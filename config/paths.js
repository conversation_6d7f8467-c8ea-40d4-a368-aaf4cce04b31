'use strict';

const path = require('path');
const fs = require('fs');
const getPublicUrlOrPath = require('react-dev-utils/getPublicUrlOrPath');

// Make sure any symlinks in the project folder are resolved:
// https://github.com/facebook/create-react-app/issues/637
const appDirectory = fs.realpathSync(process.cwd());
const resolveApp = relativePath => path.resolve(appDirectory, relativePath);

function resolveDEFAssetsConfig () {
  const project = process.env.BUILD_GIT_PROJECT
  const group = process.env.BUILD_GIT_GROUP
  const build_argv = process.env.BUILD_ARGV ? JSON.parse(process.env.BUILD_ARGV) : []
  const build_branch = process.env.BUILD_GIT_BRANCH ? process.env.BUILD_GIT_BRANCH : ''
  const [_, version] = build_branch.split('/') // branch: {feature}/{version}
  console.log(group, project, version, build_argv)

  let publishEnv = 'local'
  if (build_argv.length !== 0) {
    build_argv.forEach(item => {
      const [option, value] = item.split('=')
      if (option === '--def_publish_env') {
        publishEnv = value
      }
    })
  }
  if (!publishEnv) {
    throw new Error('publish_env not configured')
  }

  if (publishEnv === 'daily') {
    return {
      publicPath: `https://dev.g.alicdn.com/${group}/${project}/${version}/`
    }
  } else if (publishEnv === 'local') {
    return {
      publicPath: '/op-fe/kunlun-zs-webapp'
    }
  } else {
    // product
    return {
      publicPath: `https://g.alicdn.com/${group}/${project}/${version}/`
    }
  }
}

const defAssetsConfig = resolveDEFAssetsConfig()

// We use `PUBLIC_URL` environment variable or "homepage" field to infer
// "public path" at which the app is served.
// webpack needs to know it to put the right <script> hrefs into HTML even in
// single-page apps that may serve index.html for nested URLs like /todos/42.
// We can't use a relative path in HTML because we don't want to load something
// like /todos/42/static/js/bundle.7289d.js. We have to know the root.
const publicUrlOrPath = getPublicUrlOrPath(
  process.env.NODE_ENV === 'development',
  require(resolveApp('package.json')).homepage,
  // process.env.PUBLIC_URL
  defAssetsConfig.publicPath
);

const buildPath = process.env.BUILD_PATH || 'build';

const moduleFileExtensions = [
  'web.mjs',
  'mjs',
  'web.js',
  'js',
  'web.ts',
  'ts',
  'web.tsx',
  'tsx',
  'json',
  'web.jsx',
  'jsx',
];

// Resolve file paths in the same order as webpack
const resolveModule = (resolveFn, filePath) => {
  const extension = moduleFileExtensions.find(_extension =>
    fs.existsSync(resolveFn(`${filePath}.${_extension}`))
  );

  if (extension) {
    return resolveFn(`${filePath}.${extension}`);
  }

  return resolveFn(`${filePath}.js`);
};

// config after eject: we're in ./config/
module.exports = {
  dotenv: resolveApp('.env'),
  appPath: resolveApp('.'),
  appBuild: resolveApp(buildPath),
  appPublic: resolveApp('public'),
  appHtml: resolveApp('public/index.html'),
  appIndexJs: resolveModule(resolveApp, 'src/index'),
  appLifecycleJs: resolveModule(resolveApp, 'src/lifecycle'),
  appPackageJson: resolveApp('package.json'),
  appSrc: resolveApp('src'),
  appTsConfig: resolveApp('tsconfig.json'),
  appJsConfig: resolveApp('jsconfig.json'),
  yarnLockFile: resolveApp('yarn.lock'),
  testsSetup: resolveModule(resolveApp, 'src/setupTests'),
  proxySetup: resolveApp('src/setupProxy.js'),
  appNodeModules: resolveApp('node_modules'),
  swSrc: resolveModule(resolveApp, 'src/service-worker'),
  publicUrlOrPath,
};



module.exports.moduleFileExtensions = moduleFileExtensions;
