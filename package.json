{"name": "kunlun-zs-webapp", "description": "kunlun-zs-webapp", "version": "0.1.0", "private": true, "dependencies": {"@ali/alsc-gateway-web-client": "^1.1.3", "@ali/boreas2": "0.11.1", "@ali/monitor-fusion": "1.0.2", "@alifd/next": "^1.22.11", "@alife/carbon-biz-kunlun-zs": "1.4.63", "@alife/starship-sdk": "^0.1.2", "@alife/theme-24712": "^0.3.10", "@alife/theme-nr-op": "^1.11.2", "@babel/core": "7.25.7", "@ice/stark-module": "^1.5.0", "@pmmmwh/react-refresh-webpack-plugin": "0.4.3", "@svgr/webpack": "5.5.0", "@testing-library/jest-dom": "^5.11.4", "@testing-library/react": "^11.1.0", "@testing-library/user-event": "^12.1.10", "@types/debug": "^4.1.7", "@types/jest": "^26.0.15", "@types/node": "^12.0.0", "@types/react": "^17.0.0", "@types/react-dom": "^17.0.0", "@typescript-eslint/eslint-plugin": "^4.5.0", "@typescript-eslint/parser": "^4.5.0", "ahooks": "^3.7.8", "axios": "^1.3.6", "babel-eslint": "^10.1.0", "babel-jest": "^26.6.0", "babel-loader": "8.1.0", "babel-plugin-named-asset-import": "^0.3.7", "babel-preset-react-app": "^10.0.0", "bfj": "^7.0.2", "bignumber.js": "^9.0.2", "camelcase": "^6.1.0", "case-sensitive-paths-webpack-plugin": "2.3.0", "classnames": "^2.5.1", "css-loader": "4.3.0", "debug": "^4.3.3", "dotenv": "8.2.0", "dotenv-expand": "5.1.0", "eslint": "^7.11.0", "eslint-config-react-app": "^6.0.0", "eslint-plugin-flowtype": "^5.2.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jest": "^24.1.0", "eslint-plugin-jsx-a11y": "^6.3.1", "eslint-plugin-react": "^7.21.5", "eslint-plugin-react-hooks": "^4.2.0", "eslint-plugin-testing-library": "^3.9.2", "eslint-webpack-plugin": "^2.5.2", "file-loader": "6.1.1", "fs-extra": "^9.0.1", "html-webpack-plugin": "4.5.0", "identity-obj-proxy": "3.0.0", "jest": "26.6.0", "jest-circus": "26.6.0", "jest-resolve": "26.6.0", "jest-watch-typeahead": "0.6.1", "mini-css-extract-plugin": "0.11.3", "moment": "^2.29.1", "optimize-css-assets-webpack-plugin": "5.0.4", "pnp-webpack-plugin": "1.6.4", "postcss-flexbugs-fixes": "4.2.1", "postcss-loader": "3.0.0", "postcss-normalize": "8.0.1", "postcss-preset-env": "6.7.0", "postcss-safe-parser": "5.0.2", "prompts": "2.4.0", "react": "^17.0.1", "react-app-polyfill": "^2.0.0", "react-dev-utils": "^11.0.3", "react-dom": "^17.0.1", "react-error-boundary": "^3.1.3", "react-refresh": "^0.8.3", "react-router": "^5.2.1", "react-router-dom": "^5.2.1", "resolve": "1.18.1", "resolve-url-loader": "^3.1.2", "sass": "^1.56.1", "sass-loader": "^10.0.5", "semver": "7.3.2", "style-loader": "1.3.0", "terser-webpack-plugin": "4.2.3", "ts-pnp": "1.2.0", "typescript": "4.6.4", "url-loader": "4.1.1", "uuid": "^8.3.2", "web-vitals": "^1.0.1", "webpack": "4.44.2", "webpack-dev-server": "3.11.1", "webpack-manifest-plugin": "2.2.0", "workbox-webpack-plugin": "5.1.4", "yargs-parser": "^21.1.1"}, "devDependencies": {"@ali/alsc-test-coverage-webpack-plugin": "^1.4.1", "@alife/starship-alpha-sdk": "0.0.1", "@types/react-router": "^5.1.8", "@types/react-router-dom": "^5.1.6", "@types/uuid": "^8.3.1", "axios-mock-adapter": "^1.21.4", "webpack-bundle-analyzer": "^4.5.0"}, "scripts": {"start": "NODE_OPTIONS=--openssl-legacy-provider HOST=test.alibaba-inc.com node scripts/start.js", "build": "NODE_OPTIONS=--openssl-legacy-provider node --max-old-space-size=4096 scripts/build.js", "build:analyze": "WEBPACK_ANALYZE=on node --max-old-space-size=4096 scripts/build.js", "test": "node scripts/test.js", "ci:test": "CI=true node scripts/test.js --coverage"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "jest": {"roots": ["<rootDir>/src"], "collectCoverageFrom": ["src/**/{formatUtils,helper,utils}.tsx", "!src/**/*.d.ts"], "setupFiles": ["react-app-polyfill/jsdom"], "setupFilesAfterEnv": ["<rootDir>/src/setupTests.ts"], "testMatch": ["<rootDir>/src/**/__tests__/**/*.{js,jsx,ts,tsx}", "<rootDir>/src/**/*.{spec,test}.{js,jsx,ts,tsx}"], "testEnvironment": "jsdom", "testRunner": "<rootDir>/node_modules/jest-circus/runner.js", "transform": {"^.+\\.(js|jsx|mjs|cjs|ts|tsx)$": "<rootDir>/config/jest/babelTransform.js", "^.+\\.css$": "<rootDir>/config/jest/cssTransform.js", "^(?!.*\\.(js|jsx|mjs|cjs|ts|tsx|css|json)$)": "<rootDir>/config/jest/fileTransform.js"}, "transformIgnorePatterns": ["^.+\\.module\\.(css|sass|scss)$"], "modulePaths": [], "moduleNameMapper": {"^@ali/alsc-gateway-web-client": "<rootDir>/__mocks__/@ali/alsc-gateway-web-client.js", "^react-native$": "react-native-web", "^.+\\.module\\.(css|sass|scss)$": "identity-obj-proxy"}, "moduleFileExtensions": ["web.js", "js", "web.ts", "ts", "web.tsx", "tsx", "json", "web.jsx", "jsx", "node"], "watchPlugins": ["jest-watch-typeahead/filename", "jest-watch-typeahead/testname"], "resetMocks": true, "coverageDirectory": "./.ci/", "coverageReporters": ["json", "html"]}, "babel": {"presets": ["react-app"]}, "repository": "http://gitlab.alibaba-inc.com/op-fe/kunlun-zs-webapp.git", "overrides": {"@types/react": "$@types/react", "react": "$react", "react-dom": "$react-dom", "@adobe/css-tools": "4.0.1"}}