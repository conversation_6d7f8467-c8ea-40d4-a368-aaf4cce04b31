## 开发前必读

在开发前，请确定所在主应用的应用 ID 以及子应用的「路径前缀」，正确配置到 config.ts 当中。

其他注意事项参考文档：https://yuque.antfin-inc.com/docs/share/004dee4c-0a1c-4afc-b4aa-9ba2f5b4d668

### 启动方式

```bash
$ npm start
```

### 其他配置
1. 配置host：
  `127.0.0.1   test.alibaba.net`
2. 本地开代理 caddy(https://yuque.antfin-inc.com/bfe/gkgt5o/uxsldv)



~~**注意**：请使用 test.ele.me 域名来查看页面，不用使用 localhost。~~   
↑↑↑（接入云鼎<font color=red>前</font>使用）↑↑↑  

↓↓↓（接入云鼎<font color=greed>后</font>使用）↓↓↓   
**注意**：请使用 test.alibaba.net 域名来查看页面，不用使用 localhost。

**注意**：node版本升级到20，注意使用tnpm 用版本 10
[https://anpm.alibaba-inc.com/docs/cli/9to10](https://anpm.alibaba-inc.com/docs/cli/9to10)

### 文件结构

```
.
├── README.md
├── abc.json
├── config // 构建配置
├── package.json
├── public
│   └── index.html
├── scripts // 构建脚本
│   ├── build.js
│   ├── start.js
│   └── test.js
├── src
│   ├── App.css
│   ├── App.tsx
│   ├── api.ts
│   ├── common.tsx
│   ├── components // 组件
│   │   └── Layout
│   │       └── index.tsx
│   ├── config.ts
│   ├── index.tsx
│   ├── lifecycle.tsx // 微前端 npm 包入口，谨慎修改
│   ├── pages // 页面
│   │   └── HomePage
│   │       └── index.tsx
│   ├── react-app-env.d.ts
│   ├── reportWebVitals.ts
│   └── style.scss
└── tsconfig.json
```





