let mockHandlerMap = {};
class MockAlscGatewayWebClient {
  constructor() {}

  async fetch(req) {
    const handler = mockHandlerMap[req.apiKey];
    if (!handler) {
      throw new Error(`handler not found for apiKey: ${req.apiKey}`);
    }
    return handler(req);
  }
}

export const AlscGatewayWebClient = MockAlscGatewayWebClient;

export const handleAPI = (apiKey, handler) => {
  mockHandlerMap[apiKey] = handler;
};

export const clearAPI = () => {
  mockHandlerMap = {};
};

export const RequestSource = {
  BUC: "buc",
};

export const EnvType = {
  DAILY: "daily",
  PRE: "pre",
  PROD: "prod",
};
