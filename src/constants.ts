// 主活动枚举
export enum InviteActivityTypeEnum {
  SINGLE_ACTIVITY = 0, // 单活动
  MULTI_ACTIVITY = 1, // 多活动
}

export enum InviteActivityTypeEnumStr {
  '单活动' = 0, // 单活动
  '多活动' = 1, // 多活动
}

export enum SubsidyTypeMap {
  "p" = "P补",
  "b" = "B补",
  "c" = "C补",
  "d" = "D补"
}

export type Subsidy = "p" | "b" | "c" | "d";

// 活动类型枚举值
export enum ActivityTypeEnum {
  MKT_NR_ACT_TYPE_J_JIAN = 1000004, // "品类满减"
  SKU_TE_BASE = 1000005, // "商品特价"
  MKT_SALE_BASE = 1000009, // "商品折扣" 老招商活动，等老招商活动全部下线之后需要删掉
  SKU_REAL_DISCOUNT = 1000024, // 真折扣
  STORE_REDUCTION = 1000012, // "全店满减券"
  STORE_FULL_REDUCTION_BEANS_EXCHANGE_COUPON = 1000019, // 吃货豆
  STORE_FULL_SHARE_COUPON = 1000020, // 分享领券
  N_YUAN_ACTIVITY = 1000013, // "N元购"
  WHOLE_STORE_FULL_REDUCTION = 1000014, // "全店满减"
  DELIVERY_REDUCE = 1000016, // 运费满减
  ITEM_DELIVERY_REDUCTION = 1000039, // 商品运费满减(反圈)
  DELIVERY_REDUCE_TABLE_WINE = 1000043, //运费满减（餐酒）
  DELIVERY_REDUCE_EASY_PURCHASE = 1000044, //运费满减（顺手买）
  BUSINESS_CATERING_ALLIANCE = 1000017,// 业态红包
  ITEM_CATEGORY_COUPON = 1000018, //商品品类券
  ITEM_SPECIAL_COUPON = 1000021, //单品券
  STORE_REDUCTION_MULTI_COUPON = 1000022, //全店满减组合红包
  MERCHANT_ENTER_COUPON = 1000023, //商家入会礼
  LARGE_COUPON = 1000026, // 业态大额券
  TRIPARTITE_MIXTURE = 1000025, //三方混补
  N_CHOOSE_M = 1000029, // N选M
  SEND_SHOPPING_MONEY = 1000032, // 外投购物金
  ITEM_OVERLAY_COUPON = 1000031, // 单品减钱券
  ITEM_SLUMP = 1000033, // 商品直降
  BAO_HONG_BAO = 1000035, // 爆红包
  BUY_GIFT = 1000036, // 买赠
  PERIOD_INTEREST_FREE = 1000037, // 分期免息
  BAO_DAN_HONG_BAO = 1000038, // 爆单红包
  ITEM_RXCHANGE = 1000042, // 单品换购
  THREE_SUBSIDY_ITEM_PROMOTION = 1000040, // 单品一口价
  EXPLOSIVE_ORDER_COUPON = 1000045, // 爆单红包（多活动）
  MATCH_AS_YOU_PLEASE = 1000046, // 随心配
  SINGLE_ITEM_FREE_SHIPPING = 1000047, // 单品免运费券
}

// 活动类型名称
export enum ActivityTypeEnumStr {
  "品类满减" = 1000004,
  "商品特价" = 1000005,
  "商品折扣 " = 1000009,
  "商品折扣" = 1000024,
  "全店满减券" = 1000012,
  "N元购" = 1000013,
  "全店满减" = 1000014,
  "跨店搭售" = 1000015, // TODO: 老活动彻底下线后去掉改活动
  "运费满减" = 1000016,
  "运费满减（商品类）" = 1000039,
  "运费满减（餐酒）" = 1000043,
  "运费满减（顺手买）" = 1000044,
  "业态吃货联盟" = 1000017,
  "商品品类券" = 1000018,
  "单品特价券" = 1000021,
  "吃货豆兑换券" = 1000019,
  "分享领券" = 1000020,
  "全店满减组合红包" = 1000022,
  "商家会员全店红包" = 1000023,
  "业态大额券" = 1000026,
  "超级品牌券" = 1000025,
  "N选M" = 1000029,
  "外投购物金" = 1000032,
  "单品减钱券" = 1000031,
  "商品直降" = 1000033,
  "爆红包" = 1000035,
  "买赠" = 1000036,
  "分期免息" = 1000037,
  "爆单红包" = 1000038,
  "单品换购" = 1000042,
  "品牌商品特价" = 1000040,
  "爆单红包（新）" = 1000045,
  "随心配" = 1000046,
  "单品免运费券" = 1000047,
}

export type ActivitySignType = 'FORWARD_SIGN' | 'REVERSE_SIGN';

export enum ActivitySignTypeEnumStr {
  'FORWARD_SIGN' = '正招',
  'REVERSE_SIGN' = '反圈'
}

export enum InvestmentTypeEnum {
  LANMU = 1,
  RICHANG = 2,
  DACU = 3,
  SUANFA = 4,
  CHANNEL_INVESTMENT = 5,
  MARKETING_IP_INVESTMENT = 7,
}

export enum InvestmentTypeEnumStr {
  '栏目招商' = 1,
  '日常招商' = 2,
  '大促招商' = 3,
  '算法招商' = 4,
  '频道招商' = 5,
  '营销IP招商' = 7
}
export enum InvestmentSchemaEnum {
  RICHANG = 0,
  SHILING_HAOHUO = 1,
  ZHENSHIHUI = 2,
  DACU = 3,
  XIANSHI_QIANGGOU = 5,
  BAIYI_BUTIE = 6,
  SANDANLI = 7,
  SUPER_MEMBER_COUPON = 8,

  VIP_GREETING = 9,
  VIP_MONTHLY_GIFT = 10,
  EMALL = 11,
  MERCHANTS_MEMBER = 12,
  MUTI_COMMDITY = 13,
  ITEM_MUTUAL_EXCLUSION_INVESTMENT = 18,

  EMALL_PINTUAN = 17,
  WEEKEND_SPLIT_PURCHASE = 20,
  DRUG_FREE = 19,
  HOT_VALUE_PRICE = 21,
  HOT_DISCOUNT = 22,
  BUY_ONE_GIFT_ONE = 23,
  COMMODITY_EXCHANGE_CARD = 24,
  CARD_SALES = 25,
  PERIOD_INTEREST_FREE = 26,
  EXPLOSIVE_ORDER_COUPON = 27,
  FOODIE_BEANS = 28,
  BRAND_PROJECT_INVESTMENT = 29,
  SUPER_COMMODITY_GOOD_PRICE = 32,  // 爆好价
  FREE_ORDER_COUPON = 33, // 单品免单券/单品减钱券
  FINAL_DEAL_PRICE = 34, // 一口价
}

export {
  InvestmentTypeEnum as ITY,
  InvestmentSchemaEnum as ISCH,
}

export enum InvestmentSchemaEnumStr {
  '日常招商' = 0,
  '时令好货' = 1,
  '真实惠' = 2,
  '大促招商' = 3,
  '限时抢购' = 5,
  '百亿补贴' = 6,
  '商家品牌三单礼' = 7,
  '超级吃货卡' = 8,  // 超会红包

  '商家入会红包' = 9,
  '商家会员月度全店红包' = 10,
  '全能超市' = 11,
  '商家会员' = 12,
  '多商品' = 13,
  '商品互斥招商' = 18,

  '全能拼团' = 17,
  '周末一分购' = 20,
  '零售医药免单' = 19,
  '爆款超值价' = 21,
  '爆款折扣' = 22,
  '买一赠一' = 23,
  '商品通兑卡' = 24,
  '站外卡券售卖' = 25,
  '花呗分期免息' = 26,
  '爆单红包' = 27,
  '吃货豆' = 28,
  '品牌项目招商' = 29,

  '爆好价' = 32,
  '单品减钱券' = 33,
  '一口价' = 34,
}

export enum TouchAprvStatusEnumStr {
  '无需审核' = 0,
  '审核中' = 10,
  '审核通过' = 20,
  '审核驳回' = 30,
}

/**
 * InvestmentRecipe 「活动创建配方」，用来枚举每种活动创建组合(招商类型、招商模板、活动类型...)
 * 之所以枚举所有活动创建组合，是因为这些组合关系没有明确搭配规律，基本各种搭配都有可能，只能枚举
 */
export interface InvestmentRecipe {
  investmentActivityType: number; // 招商活动类型，0: 单活动, 1: 主子活动
  investmentType: number; // 招商类型
  subsidyType: "p" | "b" | "c" | "d"; // 补贴类型
  investmentSchema: number; // 招商模板
  activityType: number; // 活动类型，子活动配方才有 activityType
  remark: string;
  available: boolean;
  activitySignType: ActivitySignType;

  // 优品计划ID，目前只在 investmentSchema 是爆好价的时候不为空
  qualityProductPlanId?: number;
}

function recipe(
  investmentActivityType: number,
  investmentType: number,
  investmentSchema: number,
  activityType: number,
  activitySignType: ActivitySignType,
  remark?: string,
  available?: boolean
): any {
  return {
    investmentActivityType,
    investmentType,
    investmentSchema,
    activityType,
    activitySignType,
    remark: remark || '',
    available: available === undefined ? true : available
  }
}

export const investmentRecipes: InvestmentRecipe[] = [
  // 主子活动，栏目招商，时令好货: 品类满减、商品特价、商品折扣
  recipe(InviteActivityTypeEnum.MULTI_ACTIVITY, InvestmentTypeEnum.LANMU, InvestmentSchemaEnum.SHILING_HAOHUO, ActivityTypeEnum.MKT_NR_ACT_TYPE_J_JIAN, 'FORWARD_SIGN', "不再支持栏目招商", false),
  recipe(InviteActivityTypeEnum.MULTI_ACTIVITY, InvestmentTypeEnum.LANMU, InvestmentSchemaEnum.SHILING_HAOHUO, ActivityTypeEnum.SKU_TE_BASE, 'FORWARD_SIGN'),
  recipe(InviteActivityTypeEnum.MULTI_ACTIVITY, InvestmentTypeEnum.LANMU, InvestmentSchemaEnum.SHILING_HAOHUO, ActivityTypeEnum.SKU_REAL_DISCOUNT, 'FORWARD_SIGN', "不再支持栏目招商", false),
  // 主子活动，栏目招商，真实惠
  recipe(InviteActivityTypeEnum.MULTI_ACTIVITY, InvestmentTypeEnum.LANMU, InvestmentSchemaEnum.ZHENSHIHUI, ActivityTypeEnum.MKT_NR_ACT_TYPE_J_JIAN, 'FORWARD_SIGN', '新版招商不再支持真实惠', false),
  recipe(InviteActivityTypeEnum.MULTI_ACTIVITY, InvestmentTypeEnum.LANMU, InvestmentSchemaEnum.ZHENSHIHUI, ActivityTypeEnum.SKU_TE_BASE, 'FORWARD_SIGN', '新版招商不再支持真实惠', false),
  recipe(InviteActivityTypeEnum.MULTI_ACTIVITY, InvestmentTypeEnum.LANMU, InvestmentSchemaEnum.ZHENSHIHUI, ActivityTypeEnum.MKT_SALE_BASE, 'FORWARD_SIGN', '新版招商不再支持真实惠', false),
  // 主子活动，日常招商，日常模板
  recipe(InviteActivityTypeEnum.MULTI_ACTIVITY, InvestmentTypeEnum.RICHANG, InvestmentSchemaEnum.RICHANG, ActivityTypeEnum.MKT_NR_ACT_TYPE_J_JIAN, 'FORWARD_SIGN', "202404下线，产品林漠", false), //品类满减
  recipe(InviteActivityTypeEnum.MULTI_ACTIVITY, InvestmentTypeEnum.RICHANG, InvestmentSchemaEnum.RICHANG, ActivityTypeEnum.SKU_TE_BASE, 'FORWARD_SIGN'), //商品特价
  recipe(InviteActivityTypeEnum.MULTI_ACTIVITY, InvestmentTypeEnum.RICHANG, InvestmentSchemaEnum.RICHANG, ActivityTypeEnum.SKU_REAL_DISCOUNT, 'FORWARD_SIGN'), // 真折扣
  recipe(InviteActivityTypeEnum.MULTI_ACTIVITY, InvestmentTypeEnum.RICHANG, InvestmentSchemaEnum.RICHANG, ActivityTypeEnum.DELIVERY_REDUCE, 'FORWARD_SIGN'), //运费满减
  recipe(InviteActivityTypeEnum.MULTI_ACTIVITY, InvestmentTypeEnum.RICHANG, InvestmentSchemaEnum.RICHANG, ActivityTypeEnum.BUSINESS_CATERING_ALLIANCE, 'FORWARD_SIGN', '不再发起业态红包的活动', false), //业态红包
  recipe(InviteActivityTypeEnum.MULTI_ACTIVITY, InvestmentTypeEnum.RICHANG, InvestmentSchemaEnum.RICHANG, ActivityTypeEnum.ITEM_CATEGORY_COUPON, 'FORWARD_SIGN'), //品类券
  recipe(InviteActivityTypeEnum.MULTI_ACTIVITY, InvestmentTypeEnum.RICHANG, InvestmentSchemaEnum.RICHANG, ActivityTypeEnum.ITEM_SPECIAL_COUPON, 'FORWARD_SIGN'), //单品特价券
  recipe(InviteActivityTypeEnum.MULTI_ACTIVITY, InvestmentTypeEnum.RICHANG, InvestmentSchemaEnum.RICHANG, ActivityTypeEnum.N_CHOOSE_M, 'FORWARD_SIGN'), //N选M

  // 主子活动，日常招商，爆单红包
  recipe(InviteActivityTypeEnum.MULTI_ACTIVITY, InvestmentTypeEnum.RICHANG, InvestmentSchemaEnum.EXPLOSIVE_ORDER_COUPON, ActivityTypeEnum.EXPLOSIVE_ORDER_COUPON, 'FORWARD_SIGN'), //新爆单红包

  // 主子活动，日常招商，百亿补贴
  recipe(InviteActivityTypeEnum.MULTI_ACTIVITY, InvestmentTypeEnum.RICHANG, InvestmentSchemaEnum.BAIYI_BUTIE, ActivityTypeEnum.SKU_TE_BASE, 'FORWARD_SIGN'),
  recipe(InviteActivityTypeEnum.MULTI_ACTIVITY, InvestmentTypeEnum.RICHANG, InvestmentSchemaEnum.BAIYI_BUTIE, ActivityTypeEnum.SKU_REAL_DISCOUNT, 'FORWARD_SIGN'),

  // 主子活动，日常招商，商家会员
  recipe(InviteActivityTypeEnum.MULTI_ACTIVITY, InvestmentTypeEnum.RICHANG, InvestmentSchemaEnum.MERCHANTS_MEMBER, ActivityTypeEnum.SKU_TE_BASE, 'FORWARD_SIGN'),
  // 主子活动，日常招商，全能拼团
  recipe(InviteActivityTypeEnum.MULTI_ACTIVITY, InvestmentTypeEnum.RICHANG, InvestmentSchemaEnum.EMALL_PINTUAN, ActivityTypeEnum.SKU_TE_BASE, 'FORWARD_SIGN'),
  // 主子活动，日常招商，多商品
  recipe(InviteActivityTypeEnum.MULTI_ACTIVITY, InvestmentTypeEnum.RICHANG, InvestmentSchemaEnum.MUTI_COMMDITY, ActivityTypeEnum.SKU_TE_BASE, 'FORWARD_SIGN'),
  // 主子活动，大促招商，大促模板
  recipe(InviteActivityTypeEnum.MULTI_ACTIVITY, InvestmentTypeEnum.DACU, InvestmentSchemaEnum.DACU, ActivityTypeEnum.MKT_NR_ACT_TYPE_J_JIAN, 'FORWARD_SIGN', "不再支持大促招商", false), //品类满减
  recipe(InviteActivityTypeEnum.MULTI_ACTIVITY, InvestmentTypeEnum.DACU, InvestmentSchemaEnum.DACU, ActivityTypeEnum.SKU_TE_BASE, 'FORWARD_SIGN', "不再支持大促招商", false), //商品特价
  recipe(InviteActivityTypeEnum.MULTI_ACTIVITY, InvestmentTypeEnum.DACU, InvestmentSchemaEnum.DACU, ActivityTypeEnum.SKU_REAL_DISCOUNT, 'FORWARD_SIGN', "不再支持大促招商", false), // 真折扣
  recipe(InviteActivityTypeEnum.MULTI_ACTIVITY, InvestmentTypeEnum.DACU, InvestmentSchemaEnum.DACU, ActivityTypeEnum.DELIVERY_REDUCE, 'FORWARD_SIGN', "不再支持大促招商", false), //运费满减
  recipe(InviteActivityTypeEnum.MULTI_ACTIVITY, InvestmentTypeEnum.DACU, InvestmentSchemaEnum.DACU, ActivityTypeEnum.BUSINESS_CATERING_ALLIANCE, 'FORWARD_SIGN', '不再发起业态红包的活动', false), //业态红包
  recipe(InviteActivityTypeEnum.MULTI_ACTIVITY, InvestmentTypeEnum.DACU, InvestmentSchemaEnum.DACU, ActivityTypeEnum.ITEM_CATEGORY_COUPON, 'FORWARD_SIGN', "不再支持大促招商", false), //品类券
  recipe(InviteActivityTypeEnum.MULTI_ACTIVITY, InvestmentTypeEnum.DACU, InvestmentSchemaEnum.DACU, ActivityTypeEnum.ITEM_SPECIAL_COUPON, 'FORWARD_SIGN', "不再支持大促招商", false), //单品特价券

  // 主子活动，频道招商，全能超市
  recipe(InviteActivityTypeEnum.MULTI_ACTIVITY, InvestmentTypeEnum.CHANNEL_INVESTMENT, InvestmentSchemaEnum.EMALL, ActivityTypeEnum.SKU_TE_BASE, 'FORWARD_SIGN', '全能超时不再支持', false), // 商品特价
  recipe(InviteActivityTypeEnum.MULTI_ACTIVITY, InvestmentTypeEnum.CHANNEL_INVESTMENT, InvestmentSchemaEnum.EMALL, ActivityTypeEnum.SKU_REAL_DISCOUNT, 'FORWARD_SIGN', '全能超市不再支持', false), // 真折扣
  recipe(InviteActivityTypeEnum.MULTI_ACTIVITY, InvestmentTypeEnum.CHANNEL_INVESTMENT, InvestmentSchemaEnum.EMALL, ActivityTypeEnum.ITEM_CATEGORY_COUPON, 'FORWARD_SIGN', '全能超市不再支持', false), // 商品品类券


  // 单活动，栏目招商，时令好货: 品类满减，商品特价，商品折扣
  recipe(InviteActivityTypeEnum.SINGLE_ACTIVITY, InvestmentTypeEnum.LANMU, InvestmentSchemaEnum.SHILING_HAOHUO, ActivityTypeEnum.MKT_NR_ACT_TYPE_J_JIAN, 'FORWARD_SIGN', "栏目招商不再支持", false),
  recipe(InviteActivityTypeEnum.SINGLE_ACTIVITY, InvestmentTypeEnum.LANMU, InvestmentSchemaEnum.SHILING_HAOHUO, ActivityTypeEnum.SKU_TE_BASE, 'FORWARD_SIGN'),
  recipe(InviteActivityTypeEnum.SINGLE_ACTIVITY, InvestmentTypeEnum.LANMU, InvestmentSchemaEnum.SHILING_HAOHUO, ActivityTypeEnum.SKU_REAL_DISCOUNT, 'FORWARD_SIGN', "栏目招商不再支持", false),
  // 单活动，栏目招商，真实惠: 品类满减，商品特价，商品折扣
  recipe(InviteActivityTypeEnum.SINGLE_ACTIVITY, InvestmentTypeEnum.LANMU, InvestmentSchemaEnum.ZHENSHIHUI, ActivityTypeEnum.MKT_NR_ACT_TYPE_J_JIAN, 'FORWARD_SIGN', '新版招商不再支持真实惠', false),
  recipe(InviteActivityTypeEnum.SINGLE_ACTIVITY, InvestmentTypeEnum.LANMU, InvestmentSchemaEnum.ZHENSHIHUI, ActivityTypeEnum.SKU_TE_BASE, 'FORWARD_SIGN', '新版招商不再支持真实惠', false),
  recipe(InviteActivityTypeEnum.SINGLE_ACTIVITY, InvestmentTypeEnum.LANMU, InvestmentSchemaEnum.ZHENSHIHUI, ActivityTypeEnum.MKT_SALE_BASE, 'FORWARD_SIGN', '新版招商不再支持真实惠', false),

  // 单活动，日常招商，日常招商模板
  recipe(InviteActivityTypeEnum.SINGLE_ACTIVITY, InvestmentTypeEnum.RICHANG, InvestmentSchemaEnum.RICHANG, ActivityTypeEnum.MKT_NR_ACT_TYPE_J_JIAN, 'FORWARD_SIGN', "202404下线，产品林漠", false), // 品类满减
  recipe(InviteActivityTypeEnum.SINGLE_ACTIVITY, InvestmentTypeEnum.RICHANG, InvestmentSchemaEnum.RICHANG, ActivityTypeEnum.SKU_TE_BASE, 'FORWARD_SIGN'), // 商品特价
  recipe(InviteActivityTypeEnum.SINGLE_ACTIVITY, InvestmentTypeEnum.RICHANG, InvestmentSchemaEnum.RICHANG, ActivityTypeEnum.SKU_REAL_DISCOUNT, 'FORWARD_SIGN'), // 真折扣 
  recipe(InviteActivityTypeEnum.SINGLE_ACTIVITY, InvestmentTypeEnum.RICHANG, InvestmentSchemaEnum.RICHANG, ActivityTypeEnum.STORE_REDUCTION, 'FORWARD_SIGN'), // 全店满减红包
  recipe(InviteActivityTypeEnum.SINGLE_ACTIVITY, InvestmentTypeEnum.RICHANG, InvestmentSchemaEnum.RICHANG, ActivityTypeEnum.STORE_REDUCTION, 'REVERSE_SIGN'), // 全店满减红包
  recipe(InviteActivityTypeEnum.SINGLE_ACTIVITY, InvestmentTypeEnum.RICHANG, InvestmentSchemaEnum.RICHANG, ActivityTypeEnum.STORE_FULL_REDUCTION_BEANS_EXCHANGE_COUPON, 'FORWARD_SIGN', "吃货豆不再支持", false), // 全店满减券-吃货豆
  recipe(InviteActivityTypeEnum.SINGLE_ACTIVITY, InvestmentTypeEnum.RICHANG, InvestmentSchemaEnum.RICHANG, ActivityTypeEnum.STORE_FULL_SHARE_COUPON, 'FORWARD_SIGN'), // 全店满减券-分享领券
  recipe(InviteActivityTypeEnum.SINGLE_ACTIVITY, InvestmentTypeEnum.RICHANG, InvestmentSchemaEnum.RICHANG, ActivityTypeEnum.N_YUAN_ACTIVITY, 'FORWARD_SIGN', "N元购不在支持9.22 @敏娅", false), // N 元购
  recipe(InviteActivityTypeEnum.SINGLE_ACTIVITY, InvestmentTypeEnum.RICHANG, InvestmentSchemaEnum.RICHANG, ActivityTypeEnum.WHOLE_STORE_FULL_REDUCTION, 'FORWARD_SIGN'), // 全店满减
  recipe(InviteActivityTypeEnum.SINGLE_ACTIVITY, InvestmentTypeEnum.RICHANG, InvestmentSchemaEnum.RICHANG, ActivityTypeEnum.DELIVERY_REDUCE, 'FORWARD_SIGN'), // 运费满减
  recipe(InviteActivityTypeEnum.SINGLE_ACTIVITY, InvestmentTypeEnum.RICHANG, InvestmentSchemaEnum.RICHANG, ActivityTypeEnum.ITEM_DELIVERY_REDUCTION, 'REVERSE_SIGN'), // 商品运费满减-反圈
  recipe(InviteActivityTypeEnum.SINGLE_ACTIVITY, InvestmentTypeEnum.RICHANG, InvestmentSchemaEnum.RICHANG, ActivityTypeEnum.DELIVERY_REDUCE_TABLE_WINE, 'REVERSE_SIGN'), // 运费满减-餐酒 反圈
  recipe(InviteActivityTypeEnum.SINGLE_ACTIVITY, InvestmentTypeEnum.RICHANG, InvestmentSchemaEnum.RICHANG, ActivityTypeEnum.DELIVERY_REDUCE_EASY_PURCHASE, 'REVERSE_SIGN'), // 运费满减-顺手买 反圈
  recipe(InviteActivityTypeEnum.SINGLE_ACTIVITY, InvestmentTypeEnum.RICHANG, InvestmentSchemaEnum.RICHANG, ActivityTypeEnum.BUSINESS_CATERING_ALLIANCE, 'FORWARD_SIGN', '不再发起业态红包的活动', false), // 业态红包
  recipe(InviteActivityTypeEnum.SINGLE_ACTIVITY, InvestmentTypeEnum.RICHANG, InvestmentSchemaEnum.RICHANG, ActivityTypeEnum.ITEM_CATEGORY_COUPON, 'FORWARD_SIGN'), // 商品品类券
  recipe(InviteActivityTypeEnum.SINGLE_ACTIVITY, InvestmentTypeEnum.RICHANG, InvestmentSchemaEnum.RICHANG, ActivityTypeEnum.ITEM_SPECIAL_COUPON, 'FORWARD_SIGN'), // 单品特价券
  recipe(InviteActivityTypeEnum.SINGLE_ACTIVITY, InvestmentTypeEnum.RICHANG, InvestmentSchemaEnum.RICHANG, ActivityTypeEnum.LARGE_COUPON, 'FORWARD_SIGN'), // 业态大额券
  recipe(InviteActivityTypeEnum.SINGLE_ACTIVITY, InvestmentTypeEnum.RICHANG, InvestmentSchemaEnum.RICHANG, ActivityTypeEnum.MERCHANT_ENTER_COUPON, 'FORWARD_SIGN', '商家会员三期之后不再支持日常模板创建入会礼红包', false), // 商家入会礼
  recipe(InviteActivityTypeEnum.SINGLE_ACTIVITY, InvestmentTypeEnum.RICHANG, InvestmentSchemaEnum.RICHANG, ActivityTypeEnum.N_CHOOSE_M, 'FORWARD_SIGN'), // N选M
  recipe(InviteActivityTypeEnum.SINGLE_ACTIVITY, InvestmentTypeEnum.RICHANG, InvestmentSchemaEnum.RICHANG, ActivityTypeEnum.SEND_SHOPPING_MONEY, 'FORWARD_SIGN'), // 外投购物金
  recipe(InviteActivityTypeEnum.SINGLE_ACTIVITY, InvestmentTypeEnum.RICHANG, InvestmentSchemaEnum.RICHANG, ActivityTypeEnum.ITEM_OVERLAY_COUPON, 'REVERSE_SIGN', '根据于欣需求，单品叠加券创建入口关闭', false), // 单品叠加券
  recipe(InviteActivityTypeEnum.SINGLE_ACTIVITY, InvestmentTypeEnum.RICHANG, InvestmentSchemaEnum.RICHANG, ActivityTypeEnum.BAO_HONG_BAO, 'FORWARD_SIGN'), // 爆红包
  // 单活动，日常招商，限时抢购模板
  recipe(InviteActivityTypeEnum.SINGLE_ACTIVITY, InvestmentTypeEnum.RICHANG, InvestmentSchemaEnum.XIANSHI_QIANGGOU, ActivityTypeEnum.SKU_TE_BASE, 'FORWARD_SIGN', "限时抢购不再支持", false), // 商品特价
  // 单活动，日常招商，商家会员模板
  recipe(InviteActivityTypeEnum.SINGLE_ACTIVITY, InvestmentTypeEnum.RICHANG, InvestmentSchemaEnum.MERCHANTS_MEMBER, ActivityTypeEnum.SKU_TE_BASE, 'FORWARD_SIGN'), // 商品特价
  // 单活动，日常招商，多商品模板
  recipe(InviteActivityTypeEnum.SINGLE_ACTIVITY, InvestmentTypeEnum.RICHANG, InvestmentSchemaEnum.MUTI_COMMDITY, ActivityTypeEnum.SKU_TE_BASE, 'FORWARD_SIGN'), // 商品特价
  // 但活动，日常招商，全能拼团
  recipe(InviteActivityTypeEnum.SINGLE_ACTIVITY, InvestmentTypeEnum.RICHANG, InvestmentSchemaEnum.EMALL_PINTUAN, ActivityTypeEnum.SKU_TE_BASE, 'FORWARD_SIGN'), // 商品特价
  // 单活动，日常招商，百亿补贴
  recipe(InviteActivityTypeEnum.SINGLE_ACTIVITY, InvestmentTypeEnum.RICHANG, InvestmentSchemaEnum.BAIYI_BUTIE, ActivityTypeEnum.SKU_TE_BASE, 'FORWARD_SIGN'), // 商品特价
  recipe(InviteActivityTypeEnum.SINGLE_ACTIVITY, InvestmentTypeEnum.RICHANG, InvestmentSchemaEnum.BAIYI_BUTIE, ActivityTypeEnum.SKU_REAL_DISCOUNT, 'FORWARD_SIGN'), // 真折扣 
  // 单活动，日常招商：三单礼模板、超级吃货卡
  recipe(InviteActivityTypeEnum.SINGLE_ACTIVITY, InvestmentTypeEnum.RICHANG, InvestmentSchemaEnum.SANDANLI, ActivityTypeEnum.STORE_REDUCTION_MULTI_COUPON, 'FORWARD_SIGN'), // 全店满减组合红包
  recipe(InviteActivityTypeEnum.SINGLE_ACTIVITY, InvestmentTypeEnum.RICHANG, InvestmentSchemaEnum.SUPER_MEMBER_COUPON, ActivityTypeEnum.LARGE_COUPON, 'FORWARD_SIGN'), // 业态大额券(超会)
  // 单活动，日常招商：商品通兑卡
  recipe(InviteActivityTypeEnum.SINGLE_ACTIVITY, InvestmentTypeEnum.RICHANG, InvestmentSchemaEnum.COMMODITY_EXCHANGE_CARD, ActivityTypeEnum.ITEM_SPECIAL_COUPON, 'FORWARD_SIGN'),

  // 单活动，日常招商、商家入会红包
  recipe(InviteActivityTypeEnum.SINGLE_ACTIVITY, InvestmentTypeEnum.RICHANG, InvestmentSchemaEnum.VIP_GREETING, ActivityTypeEnum.MERCHANT_ENTER_COUPON, 'FORWARD_SIGN'),
  // 单活动，日常招商、商家会员月度全店红包
  recipe(InviteActivityTypeEnum.SINGLE_ACTIVITY, InvestmentTypeEnum.RICHANG, InvestmentSchemaEnum.VIP_MONTHLY_GIFT, ActivityTypeEnum.MERCHANT_ENTER_COUPON, 'FORWARD_SIGN'),

  // 单活动，日常招商、周末一分购
  recipe(InviteActivityTypeEnum.SINGLE_ACTIVITY, InvestmentTypeEnum.RICHANG, InvestmentSchemaEnum.WEEKEND_SPLIT_PURCHASE, ActivityTypeEnum.ITEM_SPECIAL_COUPON, 'FORWARD_SIGN', "不再支持周末一分购", false),

  // 单活动，日常招商、周末一分购：商品特价、N选M
  recipe(InviteActivityTypeEnum.SINGLE_ACTIVITY, InvestmentTypeEnum.RICHANG, InvestmentSchemaEnum.WEEKEND_SPLIT_PURCHASE, ActivityTypeEnum.N_CHOOSE_M, 'FORWARD_SIGN'),
  recipe(InviteActivityTypeEnum.SINGLE_ACTIVITY, InvestmentTypeEnum.RICHANG, InvestmentSchemaEnum.WEEKEND_SPLIT_PURCHASE, ActivityTypeEnum.SKU_TE_BASE, 'FORWARD_SIGN'),

  // 单活动，日常招商、商品互斥招商：商品直降
  recipe(InviteActivityTypeEnum.SINGLE_ACTIVITY, InvestmentTypeEnum.RICHANG, InvestmentSchemaEnum.ITEM_MUTUAL_EXCLUSION_INVESTMENT, ActivityTypeEnum.ITEM_SLUMP, 'FORWARD_SIGN'),

  // 单活动，日常招商、零售医药免单
  recipe(InviteActivityTypeEnum.SINGLE_ACTIVITY, InvestmentTypeEnum.RICHANG, InvestmentSchemaEnum.DRUG_FREE, ActivityTypeEnum.ITEM_SPECIAL_COUPON, 'FORWARD_SIGN'), // 单品特价券

  // 单活动，日常招商、站外卡券售卖
  recipe(InviteActivityTypeEnum.SINGLE_ACTIVITY, InvestmentTypeEnum.RICHANG, InvestmentSchemaEnum.CARD_SALES, ActivityTypeEnum.ITEM_CATEGORY_COUPON, 'FORWARD_SIGN'), // 商品品类券
  recipe(InviteActivityTypeEnum.SINGLE_ACTIVITY, InvestmentTypeEnum.RICHANG, InvestmentSchemaEnum.CARD_SALES, ActivityTypeEnum.ITEM_SPECIAL_COUPON, 'FORWARD_SIGN'), // 单品特价券
  recipe(InviteActivityTypeEnum.SINGLE_ACTIVITY, InvestmentTypeEnum.RICHANG, InvestmentSchemaEnum.CARD_SALES, ActivityTypeEnum.LARGE_COUPON, 'FORWARD_SIGN'), // 业态大额券

  // 单活动，日常招商，爆单红包
  recipe(InviteActivityTypeEnum.SINGLE_ACTIVITY, InvestmentTypeEnum.RICHANG, InvestmentSchemaEnum.EXPLOSIVE_ORDER_COUPON, ActivityTypeEnum.BAO_DAN_HONG_BAO, 'FORWARD_SIGN'),// 爆单红包

  // 单活动，营销IP招商，爆款超值价
  recipe(InviteActivityTypeEnum.SINGLE_ACTIVITY, InvestmentTypeEnum.MARKETING_IP_INVESTMENT, InvestmentSchemaEnum.HOT_VALUE_PRICE, ActivityTypeEnum.ITEM_SLUMP, 'FORWARD_SIGN', '0810晚紧急下线该活动——@花渊...0828重新上线 @永亮', true), // 商品直降
  // 单活动，营销IP招商，买赠
  recipe(InviteActivityTypeEnum.SINGLE_ACTIVITY, InvestmentTypeEnum.MARKETING_IP_INVESTMENT, InvestmentSchemaEnum.BUY_ONE_GIFT_ONE, ActivityTypeEnum.BUY_GIFT, 'FORWARD_SIGN', "202404下线，产品林漠", false), // 买赠
  // 单活动，营销IP招商，爆款折扣
  recipe(InviteActivityTypeEnum.SINGLE_ACTIVITY, InvestmentTypeEnum.MARKETING_IP_INVESTMENT, InvestmentSchemaEnum.HOT_DISCOUNT, ActivityTypeEnum.SKU_REAL_DISCOUNT, 'FORWARD_SIGN'), // 商品折扣
  // 单活动，花呗分期免息，分期免息
  recipe(InviteActivityTypeEnum.SINGLE_ACTIVITY, InvestmentTypeEnum.RICHANG, InvestmentSchemaEnum.PERIOD_INTEREST_FREE, ActivityTypeEnum.PERIOD_INTEREST_FREE, 'FORWARD_SIGN'), // 分期免息
  // 单活动，算法招商，日常模板
  recipe(InviteActivityTypeEnum.SINGLE_ACTIVITY, InvestmentTypeEnum.SUANFA, InvestmentSchemaEnum.RICHANG, ActivityTypeEnum.STORE_REDUCTION, 'FORWARD_SIGN'), // 全店满减红包
  recipe(InviteActivityTypeEnum.SINGLE_ACTIVITY, InvestmentTypeEnum.SUANFA, InvestmentSchemaEnum.RICHANG, ActivityTypeEnum.STORE_FULL_REDUCTION_BEANS_EXCHANGE_COUPON, 'FORWARD_SIGN', "吃货豆不再支持", false), // 全店满减券-吃货豆兑换券
  recipe(InviteActivityTypeEnum.SINGLE_ACTIVITY, InvestmentTypeEnum.SUANFA, InvestmentSchemaEnum.RICHANG, ActivityTypeEnum.STORE_FULL_SHARE_COUPON, 'FORWARD_SIGN', "算法招商去掉分享领券，日常分享领券活动支持自定义领券人数限制", false), // 全店满减券-分享领券
  recipe(InviteActivityTypeEnum.SINGLE_ACTIVITY, InvestmentTypeEnum.SUANFA, InvestmentSchemaEnum.RICHANG, ActivityTypeEnum.WHOLE_STORE_FULL_REDUCTION, 'FORWARD_SIGN', "新版算法招商不支持全店满减", false), // 全店满减

  // 单活动，频道招商，全能超市
  recipe(InviteActivityTypeEnum.SINGLE_ACTIVITY, InvestmentTypeEnum.CHANNEL_INVESTMENT, InvestmentSchemaEnum.EMALL, ActivityTypeEnum.STORE_REDUCTION, 'FORWARD_SIGN', '全能超市3.0不在支持', false), // 全店满减红包
  recipe(InviteActivityTypeEnum.SINGLE_ACTIVITY, InvestmentTypeEnum.CHANNEL_INVESTMENT, InvestmentSchemaEnum.EMALL, ActivityTypeEnum.SKU_TE_BASE, 'FORWARD_SIGN', '全能超市3.0不在支持', false), // 商品特价
  recipe(InviteActivityTypeEnum.SINGLE_ACTIVITY, InvestmentTypeEnum.CHANNEL_INVESTMENT, InvestmentSchemaEnum.EMALL, ActivityTypeEnum.SKU_REAL_DISCOUNT, 'FORWARD_SIGN', '全能超市3.0不在支持', false), // 真折扣
  recipe(InviteActivityTypeEnum.SINGLE_ACTIVITY, InvestmentTypeEnum.CHANNEL_INVESTMENT, InvestmentSchemaEnum.EMALL, ActivityTypeEnum.LARGE_COUPON, 'FORWARD_SIGN', '全能超市3.0不在支持', false), // 业态大额券
  recipe(InviteActivityTypeEnum.SINGLE_ACTIVITY, InvestmentTypeEnum.CHANNEL_INVESTMENT, InvestmentSchemaEnum.EMALL, ActivityTypeEnum.ITEM_CATEGORY_COUPON, 'FORWARD_SIGN', '全能超市3.0不在支持', false), // 商品品类券
  recipe(InviteActivityTypeEnum.SINGLE_ACTIVITY, InvestmentTypeEnum.CHANNEL_INVESTMENT, InvestmentSchemaEnum.EMALL, ActivityTypeEnum.STORE_REDUCTION_MULTI_COUPON, 'FORWARD_SIGN', '全能超市3.0不在支持', false), // 全店满减组合红包

  // 单活动，吃货豆，单品特价券
  recipe(InviteActivityTypeEnum.SINGLE_ACTIVITY, InvestmentTypeEnum.RICHANG, InvestmentSchemaEnum.FOODIE_BEANS, ActivityTypeEnum.ITEM_SPECIAL_COUPON, 'FORWARD_SIGN'), //单品特价券

  // 单活动，免单招商、单品减钱券
  recipe(InviteActivityTypeEnum.SINGLE_ACTIVITY, InvestmentTypeEnum.RICHANG, InvestmentSchemaEnum.FREE_ORDER_COUPON, ActivityTypeEnum.ITEM_OVERLAY_COUPON, 'FORWARD_SIGN'), //单品减钱券
]


export enum Permission {
  // 招商系统基础权限
  BUSINESS_INVITATION_SYSTEM_AUTHORITY = 'BUSINESS_INVITATION_SYSTEM_AUTHORITY',
  // 多活动创建权限
  ZS_PARENT_ACTIVITY_CREATION_PERMISSION = 'ZS_PARENT_ACTIVITY_CREATION_PERMISSION',
  // 算法招商活动创权限
  ZS_BRANCH_ACTIVITY_CREATION_PERMISSION = 'ZS_BRANCH_ACTIVITY_CREATION_PERMISSION',
  // 百亿补贴活动创建权限
  ZS_TEN_BILLION_SUBSIDY_ACTIVITY_PERMISSION = 'PERM_CREATE_TEN_BILLION_SUBSIDY_ACTIVITY',
  // 子活动创建权限
  ZS_CHILD_ACTIVITY_CREATION_PERMISSION = 'ZS_CHILD_ACTIVITY_CREATION_PERMISSION',
  // 业务线是否必填权限
  BIZ_LINE_OPTIONAL_PERMISSION = 'BIZ_LINE_OPTIONAL_PERMISSION',
  // 爆单红包大皮和A活动创建权限
  AYZBX_A1_MANAGE = 'AYZBX_A1_MANAGE',
  // 爆单红包Y活动创建权限
  AYZBX_Y_MANAGE='AYZBX_Y_MANAGE',
}

export const perm2ApplyURL: { [p: string]: string } = {
  [Permission.BUSINESS_INVITATION_SYSTEM_AUTHORITY]: 'https://acl.alibaba-inc.com/apply/instance/item.htm?spm=a1z5l.8422125.0.0.ap3UYZ&type=role&id=110141064',
  [Permission.ZS_PARENT_ACTIVITY_CREATION_PERMISSION]: 'https://acl.alibaba-inc.com/apply/instance/item.htm?spm=a1z5l.8422125.0.0.ap3UYZ&type=role&id=110178937',
  [Permission.ZS_BRANCH_ACTIVITY_CREATION_PERMISSION]: 'https://acl.alibaba-inc.com/apply/cart/detail.htm?rnames=ZS_BRANCH_ACTIVITY_CREATION_OPERATION',
  [Permission.ZS_TEN_BILLION_SUBSIDY_ACTIVITY_PERMISSION]: 'https://acl.alibaba-inc.com/apply/cart/detail.htm?rnames=ZS_TEN_BILLION_SUBSIDY_ACTIVITY_OPERATION',
  [Permission.ZS_CHILD_ACTIVITY_CREATION_PERMISSION]: 'https://acl.alibaba-inc.com/apply/instance/item.htm?spm=a1z5l.8422125.0.0.ap3UYZ&type=role&id=110178938',
  [Permission.AYZBX_A1_MANAGE]:'https://acl.alibaba-inc.com/apply/cart/detail.htm?rnames=ZS_AYZBX_PLATFORM_MANAGE',
  [Permission.AYZBX_Y_MANAGE]: 'https://acl.alibaba-inc.com/apply/cart/detail.htm?rnames=ZS_AYZBX_BUSINESS_MANAGE',
}

export interface UserInfo {
  realName: string;
  userid: string;
  workid: string;
}

// 核销场景
export const deliveryTypeStr = {
  1: '仅外卖可使用',
  2: '仅到店可使用',
  3: '外卖和自提都可使用',
};
