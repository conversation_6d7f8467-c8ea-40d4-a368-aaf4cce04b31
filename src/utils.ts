import { aes } from "@alife/starship-alpha-sdk";

import { ISCH, ITY } from "./constants";

export function sandbox(fn: () => any) {
  try {
    fn();
  } catch (e) {
    console.error(e);
  }
}

interface AesCustomEventREquest {
  et: string;
  c1?: string | number;
  c2?: string | number;
  c3?: string | number;
  c4?: string | number;
  c5?: string | number;
  c6?: string | number;
}

export const sendAesTracker = (key: string, options: AesCustomEventREquest) => {
  sandbox(() => {
    // @ts-ignore alpha 项目中 window 上没有 AESPluginEvent方法需要用 sdk里的 aes.event进行埋点
    if (window.AESPluginEvent) {
      // @ts-ignore
      window.AESPluginEvent(key, {
        ...options,
      });
    } else {
      // event 对应事件名
      aes.event({
        event: key,
        ...options,
      });
    }
  });
};

export function matchRecipe(
  ctx: {
    investmentType: ITY;  // 招商类型
    investmentSchema: ISCH; // 招商模版
    isSingleActivity: boolean;  // 是否是单活动
  },
  type: ITY,
  schema: ISCH,
  isSingleActivity?: boolean
): boolean {
  if (ctx.investmentType !== type || ctx.investmentSchema !== schema) {
    // 招商类型或者招商模版不匹配
    return false;
  }
  const singleActFlag = isSingleActivity as unknown;
  if (
    singleActFlag === "" ||
    singleActFlag === null ||
    singleActFlag === undefined
  ) {
    // 不限制创建的是单活动还是子活动
    return true;
  } else {
    // 明确限制活动是单活动或者子活动
    return ctx.isSingleActivity === isSingleActivity;
  }
}
