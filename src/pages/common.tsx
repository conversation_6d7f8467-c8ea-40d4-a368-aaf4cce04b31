import { useEffect, useState } from "react";
import { Dialog } from "@alifd/next";
import * as qs from "querystring";

import * as api from "../api";
import { confirmDialog, resolveEnv } from "../common";
import { ActivityTypeEnum, Permission } from "../constants";
import { InvestmentActivityDetailDTO, InvestmentTemplateDTO } from "../models";
import { getStrategy } from "../strategy";
// import PermissionDenied from './PermissionDenied'; // 待确认文件是否保留

const debug = require("debug")("zs:pages:common");

export function handleGotoActivityDetail(
  act: InvestmentActivityDetailDTO,
  history: any,
  scene: "lookup" | "examine"
) {
  if (act.fromString !== "OLD") {
    const activityId = act.activityId;
    const href = history.createHref({
      pathname: `/sub-activity/${activityId}?tab=${scene}`,
    });
    window.open(href, "_blank");
  } else {
    const strategy = getStrategy(act.activityType);
    strategy?.gotoLegacyDetail!(act, scene);
  }
}

export function handleGotoMainActivityDetail(
  act: InvestmentTemplateDTO,
  history: any
) {
  if (act.investmentTemplateDetailDTO?.fromString !== "OLD") {
    const templateId = act.investmentTemplateDetailDTO.templateId;
    const href = history.createHref({
      pathname: `/main-activity/${templateId}`,
    });
    window.open(href, "_blank");
  } else {
    const url =
      "/main/activity/newcreate/detail/" +
      act.investmentTemplateDetailDTO.templateId +
      "/";
    window.open(url, "_blank");
  }
}

export async function handleNoPermissionForActivity(
  activityId: number,
  options: { activityType?: number; isNewActivity: boolean } // 只有老活动需要传 activityType
) {
  // 用户无权限
  const ok = await confirmDialog({
    title: "提示",
    content: "您暂无该权限，是否跳转申请权限页面",
  });
  if (ok) {
    let params: any;
    if (options.isNewActivity) {
      // 新活动
      params = {
        dataPermissionModelName: "business_subactivity_permission_new",
        operationNames: "business_subactivity_update_new",
        propertyNames: "business_subactivity_new",
        business_subactivity_new: activityId,
      };
    } else if (options.activityType === ActivityTypeEnum.N_YUAN_ACTIVITY) {
      // 老 N 元购活动
      params = {
        dataPermissionModelName: "business_activity_permission",
        operationNames: "business_activity_update",
        propertyNames: "business_activity",
        business_activity: activityId,
      };
    } else {
      // 其他老活动
      params = {
        dataPermissionModelName: "business_subactivity_permission",
        operationNames: "business_subactivity_update",
        propertyNames: "business_subactivity",
        business_subactivity: activityId,
      };
    }

    const env = resolveEnv();
    const aclHost =
      env === "pre" ? "pre-acl.alibaba-inc.com" : "acl.alibaba-inc.com";
    const url = `https://${aclHost}/apply/cart/detail.htm?${qs.stringify(
      params
    )}`;
    window.open(url, "_blank");
  }
}

const env = resolveEnv() as "pre" | "prod";
const srcForEnv = {
  pre: "https://pre-invite.kunlun.alibaba-inc.com/#/kl-permission-apply/deny",
  prod: "https://invite.kunlun.alibaba-inc.com/#/kl-permission-apply/deny",
};

export function permissionRequired(
  Component: React.ComponentType<any>,
  permission: Permission
): React.FC<any> {
  const Wrap: React.FC<any> = (props) => {
    const [ready, setReady] = useState(false);

    useEffect(() => {
      api
        .checkPermission(permission)
        .then((res) => {
          if (!res.data) {
            let url = srcForEnv[env || "prod"];

            const params = {
              p: permission,
              redirect_url: window.location.href,
            };

            url = url + "?" + qs.stringify(params);

            // 无权限强制跳转，开发环境也跳，需要注意，如果不希望跳转，需要把这行注释掉。
            window.location.replace(url);
          }
        })
        .finally(() => {
          setReady(true);
        });
    }, []);

    if (!ready) {
      return null;
    }

    return <Component {...props} />;
  };

  return Wrap;
}

export async function checkForbiddenAndAlertIfNeed() {
  const forbidden = await api.checkForbidden();
  debug("forbidden?", forbidden);
  if (forbidden) {
    setTimeout(() => {
      Dialog.show({
        style: { width: 320 },
        title: "提示",
        content: "很抱歉该时间段内不支持操作",
      });
    }, 0);
  }
  return forbidden;
}

export function redirectZsV2Page() {
  const url = window.location.href;
  const urlV2 = url.replace(/\/act\//g, "/act-v2/");
  window.location.replace(urlV2);
}
