import React, { useEffect, useRef, useState } from "react";

import HudongAssetSelect from "../components/HudongAssetSelect";
import PlaydataView from "../components/PlaydataView";
import PlaydataViewForBigAmount from "../components/PlaydataViewForBigAmount";
import PlaydataViewForBlastRedPacket from "../strategy/blastRedPacket/PlaydataViewForBlastRedPacket";
import PlaydataViewForSanFang from "../components/PlaydataViewForSanFang";
import PlaydataViewForYetai from "../components/PlaydataViewForYetai";
import { PromptMessage } from "components/formControls/PromptMessage";

import PlayStepForExplosiveCoupon from "../strategy/explosiveCoupon/PlayStepForExplosiveCoupon";
import PlaydataViewForExplosiveCoupon from "../strategy/explosiveCoupon/PlaydataViewForExplosiveCoupon";
import DiscountInfoSettingForFoodBeans from "../strategy/itemSpecialCoupon/DiscountInfoSettingForFoodBeans";
import DiscountInfoViewForFoodBeans from "../strategy/itemSpecialCoupon/DiscountInfoViewForFoodBeans";
import { UserScopeEnumStr } from "../strategy/largeCoupon";
import ItemExchangePlayStep from "../strategy/itemExchange/PlayStepForItemExchange";
import ItemExchangePlaydataView from "../strategy/itemExchange/PlayViewForItemExchange";
import ThreeSubsidyPlaydataView from "../strategy/threeSubsidyItemPromotion/PlayDataView";
import PlayStepForExplosiveCoupon202409 from "../strategy/ExplosiveCoupon2014/PlayStepForExplosiveCoupon202409";
import PlayViewForExplosiveCoupon202409 from "../strategy/ExplosiveCoupon2014/PlayViewForExplosiveCoupon202409";

// 爆好价相关业务组件
import PlaydataStepForBaohaojia from "strategy/itemSale/Baohaojia/PlaydataStep";
import QualityProductConfigStep from "strategy/itemSale/Baohaojia/QualityProductConfigStep";
import PlaydataViewForBaohaojia from "strategy/itemSale/Baohaojia/PlaydataView";
import ComboConfigure from "../strategy/FollowHeartConfigure/ComboConfigure";
import PlayStepForHeart from "../strategy/FollowHeartConfigure/playStepForHeart";
import PlayViewForHeart from "../strategy/FollowHeartConfigure/PlayViewForHeart";
import PlayStepForA2 from "strategy/ExplosiveCoupon2014/StepA2/PlayStepForA2";
import PlayViewForA2 from "strategy/ExplosiveCoupon2014/StepA2/PlayViewForA2";
import NextDatePickerRange from "strategy/itemOverlayCoupon/components/NextDatePickerRange";

type UserScopeForBigAmountLikeActivity = '0' | "10" | "11" | "12" | "13" | "14" | "15"

const debug = require("debug")("zs:initCable");

// 异步初始化 CableBuilder，全局最多初始化一次，使用 initCablePromise 来跟踪初始化过程
let initCablePromise: Promise<any> | undefined = undefined;

// 异步加载 CableBuilder 和相关业务组件，并完成业务组件注册
async function setupCableAsync() {
  const [{ CableBuilder, ActivityForm }, components] = await Promise.all([
    // @ts-ignore
    import("@ali/boreas2/lib/marketing/Cable"),
    import("@alife/carbon-biz-kunlun-zs"),
  ]);

  const {
    StoreAuditView,
    StoreAuditViewV2,
    OfflineManage,
    BaseInfoView,
    CommodityRulesView,
    StockSettingView,
    SubActivityInfoView,
    AgentProgressView,
    InvitationProgressView,
    InvitationProgressViewV2,
    CommodityAuditView,
    SendShoppingMoneyPlayView,
    WeekendPointPurchasePlayView,
    CommodityRuleViewWithWeekend,
    MaterialManage,
    ManagementForFollowHear,

    BaseInfoStep,
    ScopeInfoStep,
    ItemScopeInfoStep,
    AlgoPlayDataStep,
    StockStep,
    PlayDataImportStep,
    FormatCouponRuleStep,
    ContributionTypeView,
    CommodityRulesStep,
    ExternalDeliveryChannel,
    WeeklyTimeRangeSetting,
    SendShoppingMoneyPlayStep,
    WeekendPointPurchasePlayStep,
    ConfigValidationRules,
    InstallmentSetting,
    InstallmentStockStep,
    BudgetPicker,
    ExchangeItemManagement,
  } = components;

  // 注册 boreas 组件
  CableBuilder.registerViews([
    {
      name: "kunlun.zs.BaseInfoView",
      view: {
        Component: BaseInfoView,
      },
    },
    {
      name: "kunlun.zs.CommodityRulesView",
      view: {
        Component: (props: any) => <CommodityRulesView {...props} />,
      },
    },
    {
      name: "kunlun.zs.StockSettingView",
      view: {
        Component: StockSettingView,
      },
    },
    {
      name: "kunlun.zs.PlaydataView",
      view: {
        Component: (props: any) => (
          <PlaydataView {...props} ActivityForm={ActivityForm} />
        ),
      },
    },
    {
      name: "kunlun.zs.PlaydataViewForBigAmount",
      view: {
        Component: PlaydataViewForBigAmount,
      },
    },
    {
      name: "kunlun.zs.PlaydataViewForBlastRedPacket",
      view: {
        Component: PlaydataViewForBlastRedPacket,
      },
    },
    {
      name: "kunlun.zs.PlaydataViewForSanFang",
      view: {
        Component: PlaydataViewForSanFang,
      },
    },
    {
      name: "kunlun.zs.PlaydataViewForYetai",
      view: {
        Component: PlaydataViewForYetai,
      },
    },
    {
      name: "kunlun.zs.SendShoppingMoneyPlayView",
      view: {
        Component: SendShoppingMoneyPlayView,
      },
    },
    {
      name: "kunlun.zs.SubActivityInfoView",
      view: {
        Component: SubActivityInfoView,
      },
    },
    {
      name: "kunlun.zs.StoreAuditView",
      view: {
        Component: StoreAuditView,
      },
    },
    {
      name: "kunlun.zs.StoreAuditViewV2",
      view: {
        Component: StoreAuditViewV2,
      },
    },
    {
      name: "kunlun.zs.OfflineManage",
      view: {
        Component: OfflineManage,
      },
    },
    {
      name: "kunlun.zs.AgentProgressView",
      view: {
        Component: AgentProgressView,
      },
    },
    {
      name: "kunlun.zs.InvitationProgressView",
      view: {
        Component: InvitationProgressView,
      },
    },
    {
      name: "kunlun.zs.InvitationProgressViewV2",
      view: {
        Component: InvitationProgressViewV2,
      },
    },
    {
      name: "kunlun.zs.CommodityAuditView",
      view: {
        Component: CommodityAuditView,
      },
    },
    {
      name: "kunlun.zs.ManagementForFollowHear",
      view: {
        Component: ManagementForFollowHear,
      },
    },
    {
      name: "kunlun.zs.ExchangeItemManagement",
      view: {
        Component: ExchangeItemManagement,
      },
    },
    {
      name: "kunlun.zs.WeekendPointPurchasePlayView",
      view: {
        Component: WeekendPointPurchasePlayView,
      },
    },
    {
      name: "kunlun.zs.CommodityRuleViewWithWeekend",
      view: {
        Component: CommodityRuleViewWithWeekend,
      },
    },
    {
      name: "kunlun.zs.MaterialManage",
      view: {
        Component: MaterialManage,
      },
    },
    {
      name: "kunlun.zs.PlaydataViewForExplosiveCoupon",
      view: {
        Component: PlaydataViewForExplosiveCoupon,
      },
    },
    {
      name: "kunlun.zs.DiscountInfoViewForFoodBeans",
      view: {
        Component: DiscountInfoViewForFoodBeans,
      },
    },
    {
      name: "kunlun.zs.ItemExchangePlaydataView",
      view: {
        Component: ItemExchangePlaydataView,
      },
    },
    {
      name: "kunlun.zs.ThreeSubsidyPlaydataView",
      view: {
        Component: ThreeSubsidyPlaydataView,
      },
    },
    {
      name: "kunlun.zs.PlayViewForExplosiveCoupon202409",
      view: {
        Component: PlayViewForExplosiveCoupon202409,
      },
    },
    {
      name: 'kunlun.zs.baohaojia.PlaydataView',
      view: {
        Component: PlaydataViewForBaohaojia
      }
    },
    {
      name: "kunlun.zs.PlayViewForHeart",
      view: {
        Component: PlayViewForHeart,
      },
    },
    {
      name: "kunlun.zs.PlayViewForA2",
      view: {
        Component: PlayViewForA2,
      },
    },
  ]);

  CableBuilder.registerSteps([
    {
      name: "kunlun.zs.BaseInfoStep",
      step: {
        Component: BaseInfoStep,
      },
    },
    {
      name: "kunlun.zs.ScopeInfoStep",
      step: { Component: ScopeInfoStep },
    },
    {
      name: "kunlun.zs.ItemScopeInfoStep",
      step: { Component: ItemScopeInfoStep },
    },
    {
      name: "kunlun.zs.ContributionTypeView",
      step: {
        Component: ContributionTypeView,
        onNext: async (dataStore: any, step: any) => {
          const playData = dataStore.data[step.name] || {};

          if (
            (!playData?.displayPlatformSubsidyMin &&
              playData.displayPlatformSubsidyMin !== 0) ||
            (!playData?.displayPlatformSubsidyMax &&
              playData.displayPlatformSubsidyMax !== 0)
          ) {
            return { message: "请填写商家展示平台补贴金额" };
          }

          let msg = "";

          // 校验已选中的内容是否为空
          Object.keys(playData)
            ?.filter((v) => +v >= 0)
            ?.filter((v) => playData[v])
            ?.map((key) => {
              const curItem = playData[key];
              if (curItem?.enabled) {
                if (!curItem.shopSubsidy && !msg) {
                  msg += `${
                    UserScopeEnumStr[key as UserScopeForBigAmountLikeActivity]
                  }: 商家出资不能为空\r\n`;
                } else if (!curItem.dayCountLimit && !msg) {
                  msg += `${
                    UserScopeEnumStr[key as UserScopeForBigAmountLikeActivity]
                  }: 单店每日库存最小值不能为空\r\n`;
                } else if (!curItem.dayCountLimitMax && !msg) {
                  msg += `${
                    UserScopeEnumStr[key as UserScopeForBigAmountLikeActivity]
                  }: 单店每日库存最大值不能为空\r\n`;
                } else if (
                  curItem.dayCountLimit &&
                  curItem.dayCountLimitMax &&
                  curItem.dayCountLimitMax < curItem.dayCountLimit &&
                  !msg
                ) {
                  msg += `${
                    UserScopeEnumStr[key as UserScopeForBigAmountLikeActivity]
                  }: 最大值不能小于最小值`;
                }
              }
            });

          if (!msg && playData?.userScopeContributionType === 2) {
            // 校验 以下四个档位，至少设置一档
            const types = Object.keys(playData)
              ?.filter((v) => +v >= 0)
              .filter((v) => playData[v]);
            if (types.length <= 1) {
              msg += "以下四个档位，至少设置一档";
            }
          }

          if (msg) {
            return { message: msg };
          }
        },
      },
    },
    {
      name: "kunlun.zs.AlgoPlayDataStep",
      step: {
        Component: AlgoPlayDataStep,
        onNext: async (dataStore: any, step: any) => {
          const data = dataStore.data[step.name];
          const valid =
            data && data.file && data.file.state === "done" && data.file.url;
          if (!valid) {
            return { message: "请上传玩法明细" };
          }
        },
      },
    },
    {
      name: "kunlun.zs.StockStep",
      step: {
        Component: StockStep,
        onNext: async (dataStore: any, step: any) => {
          const data = dataStore.data[step.name];
          if (data.stockType === 2) {
            if (!data.range?.start || !data.range?.end) {
              return { message: "请填写活动库范围" };
            }
          }
          if (
            data.range.start &&
            data.range.end &&
            data.range.start >= data.range.end
          ) {
            return { message: "库存最小值必须小于最大值" };
          }
        },
      },
    },
    {
      name: "kunlun.zs.PlayDataImportStep",
      step: {
        Component: PlayDataImportStep,
        onNext: async (dataStore: any, step: any) => {
          const data = dataStore.data[step.name];
          const valid =
            data && data.file && data.file.state === "done" && data.file.url;
          if (!valid) {
            return { message: "请导入玩法" };
          }
        },
      },
    },
    {
      name: "kunlun.zs.FormatCouponRuleStep",
      step: {
        Component: FormatCouponRuleStep,
        onNext: async (dataStore: any, step: any) => {
          const data = dataStore.data[step.name];
          function isEmpty(v: any) {
            return v === "" || v === null || v === undefined;
          }
          const valid =
            !isEmpty(data.dayCountLimit) &&
            !isEmpty(data.dayCountLimitMax) &&
            !isEmpty(data.shopSubsidy) &&
            !isEmpty(data.shopSubsidyMax);
          if (!valid) {
            return { message: "请填写玩法信息" };
          }
          if (data.shopSubsidy > data.shopSubsidyMax) {
            return { message: "商家出资最小值不能大于最大值" };
          }
          if (data.dayCountLimit > data.dayCountLimitMax) {
            return { message: "单门店每日可消耗数量最小值不能大于最大值" };
          }
        },
      },
    },
    {
      name: "kunlun.zs.CommodityRulesStep",
      step: {
        Component: CommodityRulesStep,
        onNext: async (dataStore: any, step: any) => {
          const data = dataStore.data[step.name];
          function isEmpty(v: any) {
            return v === "" || v === null || v === undefined;
          }
          if (data.fileUploadStatus === 2) {
            return { message: "文件正在解析中，请等待..." };
          }
          if (data.fileUploadStatus === 3) {
            return { message: "请重新上传文件" };
          }
          const valid = !isEmpty(data.checkExcel) && !isEmpty(data.uploadPath);
          if (!valid) {
            return { message: "请上传活动商品信息" };
          }
          if (
            data.checkExcel.extInfoMap.invitedUpcHasPlatformSubsidy === "true"
          ) {
            const verify = !isEmpty(data.budgetId) && !isEmpty(data.budgetName);
            if (!verify) {
              return { message: "请填写预算信息" };
            }
          }
        },
      },
    },
    {
      name: "kunlun.zs.SendShoppingMoneyPlayStep",
      step: {
        Component: SendShoppingMoneyPlayStep,
      },
    },
    {
      name: "kunlun.zs.WeekendPointPurchasePlayStep",
      step: { Component: WeekendPointPurchasePlayStep },
    },
    {
      name: "kunlun.zs.InstallmentSetting",
      step: { Component: InstallmentSetting },
    },
    {
      name: "kunlun.zs.InstallmentStockStep",
      step: { Component: InstallmentStockStep },
    },
    {
      name: "kunlun.zs.PlayStepForExplosiveCoupon",
      step: { Component: PlayStepForExplosiveCoupon },
    },
    {
      name: "kunlun.zs.DiscountInfoSettingForFoodBeans",
      step: { Component: DiscountInfoSettingForFoodBeans },
    },
    {
      name: "kunlun.zs.ItemExchangePlayStep",
      step: { Component: ItemExchangePlayStep },
    },
    {
      name: "kunlun.zs.PlayStepForExplosiveCoupon202409",
      step: { Component: PlayStepForExplosiveCoupon202409 },
    },
    {
      name: "kunlun.zs.baohaojia.QualityProductConfigStep",
      step: { Component: QualityProductConfigStep },
    },
    {
      name: "kunlun.zs.baohaojia.PlaydataStep",
      step: { Component: PlaydataStepForBaohaojia },
    },
    {
      name: 'kunlun.zs.ComboConfigure',
      step: { Component: ComboConfigure },
    },
    {
      name: 'kunlun.zs.PlayStepForHeart',
      step: { Component: PlayStepForHeart },
    },
    {
      name: 'kunlun.zs.PlayStepForExplosiveCouponA2',
      step: { Component: PlayStepForA2 },
    },
  ]);

  // 果园 N 元购互动资产选择组件
  ActivityForm.registerComponent("kunlun.zs.HudongAssetSelect", {
    component: HudongAssetSelect,
    detail: ({ value }: any) => {
      if (value && value.name) {
        return `${value.name}(${value.id || "-"})`;
      } else {
        return "";
      }
    },
    deserializeValue: (ctx: any) =>
      ctx.value ? JSON.parse(ctx.value) : ctx.value,
    serializeValue: (ctx: any) =>
      ctx.value ? JSON.stringify(ctx.value) : ctx.value,
  });

  // 限定渠道表单控件
  ActivityForm.registerComponent("kunlun.zs.ExternalDeliveryChannel", {
    component: ExternalDeliveryChannel,
    customRenderDetail: true,
    deserializeValue: (ctx: any) =>
      ctx.value ? JSON.parse(ctx.value) : ctx.value,
    serializeValue: (ctx: any) =>
      ctx.value ? JSON.stringify(ctx.value) : ctx.value,
  });

  // 中控补贴校验建议表单控件
  ActivityForm.registerComponent("kunlun.zs.ConfigValidationRules", {
    component: ConfigValidationRules,
    customRenderDetail: true,
    deserializeValue: (ctx: any) =>
      ctx.value ? JSON.parse(ctx.value) : ctx.value,
    serializeValue: (ctx: any) =>
      ctx.value ? JSON.stringify(ctx.value) : ctx.value,
  });

  // 运费满减（商品类）活动玩法表单的警告信息
  ActivityForm.registerComponent("kunlun.zs.PromptMessage", {
    component: PromptMessage,
    customRenderDetail: true,
    deserializeValue: (ctx: any) =>
      ctx.value ? JSON.parse(ctx.value) : ctx.value,
    serializeValue: (ctx: any) =>
      ctx.value ? JSON.stringify(ctx.value) : ctx.value,
  });
  // 周末一分购商品特价选择星期时段表单组件
  ActivityForm.registerComponent("kunlun.zs.WeeklyTimeRangeSetting", {
    component: WeeklyTimeRangeSetting,
    customRenderDetail: true,
    deserializeValue: (ctx: any) =>
      ctx.value ? JSON.parse(ctx.value) : ctx.value,
    serializeValue: (ctx: any) =>
      ctx.value ? JSON.stringify(ctx.value) : ctx.value,
  });

  // 分期免息玩法设置分期期数表单控件
  ActivityForm.registerComponent("kunlun.zs.InstallmentSetting", {
    component: InstallmentSetting,
    customRenderDetail: true,
    deserializeValue: (ctx: any) =>
      ctx.value ? JSON.parse(ctx.value) : ctx.value,
    serializeValue: (ctx: any) =>
      ctx.value ? JSON.stringify(ctx.value) : ctx.value,
  });

  // 预算表单控件
  ActivityForm.registerComponent("kunlun.zs.BudgetPicker", {
    component: BudgetPicker,
    customRenderDetail: true,
    deserializeValue: (ctx: any) =>
      ctx.value ? JSON.parse(ctx.value) : ctx.value,
    serializeValue: (ctx: any) =>
      ctx.value ? JSON.stringify(ctx.value) : ctx.value,
  });

  // 预算表单控件
  ActivityForm.registerComponent("kunlun.zs.NextDatePickerRange", {
    component: NextDatePickerRange,
    customRenderDetail: true,
    deserializeValue: (ctx: any) =>
      ctx.value ? JSON.parse(ctx.value) : ctx.value,
    serializeValue: (ctx: any) =>
      ctx.value ? JSON.stringify(ctx.value) : ctx.value,
  });

  return { CableBuilder, ActivityForm };
}

/**
 * initCable 是一个装饰器，保证依赖 Cable 的组件，会在 Cable 初始完成以后才启动，并将 CableBuilder 注入到被装饰的组件上
 * Cable 目前还比较重，通过 initCable 实现 Cable 延迟加载，提高页面加载速度
 * 注意：initCable 只用在顶层路由上
 */
export default function initCable(Component: React.ComponentType<any>) {
  const WrapView: React.FC<any> = (props) => {
    const CableBuilderRef = useRef();
    const ActivityFormRef = useRef();
    const [ready, setReady] = useState(false);

    useEffect(() => {
      if (!initCablePromise) {
        debug("initCable");
        initCablePromise = setupCableAsync();
      } else {
        debug("reuse initCable promise");
      }
      initCablePromise.then(({ CableBuilder, ActivityForm }) => {
        CableBuilderRef.current = CableBuilder;
        ActivityFormRef.current = ActivityForm;
        setReady(true);
      });
    }, []);

    if (!CableBuilderRef.current || !ActivityFormRef.current || !ready) {
      return null;
    }
    return (
      <Component
        {...props}
        CableBuilder={CableBuilderRef.current}
        // 子活动详情页还需要 ActivityForm
        ActivityForm={ActivityFormRef.current}
      />
    );
  };

  return WrapView;
}
