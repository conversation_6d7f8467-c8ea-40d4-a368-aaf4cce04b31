import { useMemo, useState } from "react";
import { Table, Range, Tag } from "@alifd/next";

import {
  ActivitySignType,
  ActivityTypeEnumStr,
  InvestmentSchemaEnum,
  InvestmentSchemaEnumStr,
  InvestmentTypeEnum,
  InvestmentTypeEnumStr,
  InviteActivityTypeEnum,
  investmentRecipes,
} from "../../constants";

interface RecipeIgnoreMainActivity {
  investmentType: InvestmentTypeEnum;
  investmentSchema: InvestmentSchemaEnum;
  activitySignType?: ActivitySignType;
}

export default function RecipeGuard() {
  const [threshold, setThreshold] = useState(3);

  const hotActivityTypes = useMemo(() => {
    const recipesBySchema: Record<string, RecipeIgnoreMainActivity[]> = {};
    investmentRecipes.forEach((r) => {
      if (!r.available) {
        return;
      }
      const key = `${r.activityType}`;
      const recipes: RecipeIgnoreMainActivity[] = recipesBySchema[key] || [];
      const index = recipes.findIndex((recipe) => {
        return (
          recipe.investmentSchema === r.investmentSchema &&
          recipe.investmentType === r.investmentType &&
          recipe.activitySignType === r.activitySignType
        );
      });
      if (index === -1) {
        recipes.push({
          investmentSchema: r.investmentSchema,
          investmentType: r.investmentType,
          activitySignType: r.activitySignType,
        });
      }
      recipesBySchema[key] = recipes;
    });
    const ret: {
      recipes: RecipeIgnoreMainActivity[];
      activityType: InviteActivityTypeEnum;
    }[] = [];
    Object.keys(recipesBySchema).forEach((key) => {
      const activityType = +key;
      const recipes = recipesBySchema[activityType];
      if (recipes.length >= threshold) {
        ret.push({ recipes, activityType });
      }
    });
    ret.sort((r1, r2) => {
      return r2.recipes.length - r1.recipes.length;
    });
    return ret;
  }, [threshold]);

  return (
    <div style={{ width: 1280, margin: "20px auto" }}>
      <h2>过热活动类型</h2>
      <div
        style={{
          marginBottom: 12,
          marginTop: 20,
          display: "flex",
          alignItems: "center",
        }}
      >
        阈值：
        <Range
          style={{ width: 200, marginLeft: 8 }}
          value={threshold}
          onChange={(v) => setThreshold(v as number)}
          max={10}
          min={1}
        />
      </div>
      <Table dataSource={hotActivityTypes}>
        <Table.Column
          title="活动类型"
          dataIndex="activityType"
          cell={(activityType) => {
            return <>{ActivityTypeEnumStr[activityType]}</>;
          }}
        />
        <Table.Column
          title="招商模版-招商类型组合"
          dataIndex="recipes"
          cell={(recipes) => {
            return (
              <ul>
                {recipes.map((r: RecipeIgnoreMainActivity) => {
                  return (
                    <div
                      key={`${r.investmentType}-${r.investmentSchema}`}
                      style={{ lineHeight: "22px" }}
                    >
                      招商类型：{InvestmentTypeEnumStr[r.investmentType]}
                      ，招商模版：
                      {InvestmentSchemaEnumStr[r.investmentSchema]}
                      {r.activitySignType === "FORWARD_SIGN" ? (
                        <Tag style={{ marginLeft: 8 }} size="small">
                          正圈
                        </Tag>
                      ) : (
                        <Tag
                          style={{ marginLeft: 8 }}
                          size="small"
                          color="orange"
                        >
                          反圈
                        </Tag>
                      )}
                    </div>
                  );
                })}
              </ul>
            );
          }}
        />
      </Table>
    </div>
  );
}
