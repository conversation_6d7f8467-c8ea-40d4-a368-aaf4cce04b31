import * as React from "react";
import { useState, useMemo } from "react";
import { Table, Select } from "@alifd/next";
import {
  ActivityTypeEnumStr,
  ActivitySignType,
  ActivitySignTypeEnumStr,
  InvestmentRecipe,
  investmentRecipes,
  InvestmentSchemaEnumStr,
  InvestmentTypeEnumStr,
  InviteActivityTypeEnumStr,
} from "../../constants";

function distinct(
  v: InvestmentRecipe[],
  mapper: (r: InvestmentRecipe) => number | ActivitySignType
) {
  const dict: any = {};
  investmentRecipes.forEach((_v) => {
    dict[mapper(_v)] = true;
  });
  return Object.keys(dict).map((k) => isNaN(+k) ? k : +k);
}

const DebugRecipes: React.FC<any> = () => {
  const [query, setQuery] = useState<any>({});

  const dataSource = useMemo(() => {
    return investmentRecipes
      .filter((r) => {
        if (
          query.investmentActivityType ||
          query.investmentActivityType === 0
        ) {
          return r.investmentActivityType === +query.investmentActivityType;
        } else {
          return true;
        }
      })
      .filter((r) => {
        if (query.investmentType || query.investmentType === 0) {
          return r.investmentType === +query.investmentType;
        } else {
          return true;
        }
      })
      .filter((r) => {
        if (query.investmentSchema || query.investmentSchema === 0) {
          return r.investmentSchema === +query.investmentSchema;
        } else {
          return true;
        }
      })
      .filter((r) => {
        if (query.activityType || query.activityType === 0) {
          return r.activityType === +query.activityType;
        } else {
          return true;
        }
      })
      .filter((r) => {
        if (query.activitySignType) {
          return r.activitySignType === query.activitySignType;
        } else {
          return true;
        }
      })
      .filter((r) => {
        if (query.available === true) {
          return r.available;
        } else if (query.available === false) {
          return r.available === false;
        } else {
          return true;
        }
      });
  }, [query]);

  const investmentActivityTypeValues = useMemo(() => {
    return distinct(investmentRecipes, (v) => v.investmentActivityType);
  }, []);
  const investmentTypeValues = useMemo(() => {
    return distinct(investmentRecipes, (v) => v.investmentType);
  }, []);
  const investmentSchemaValues = useMemo(() => {
    return distinct(investmentRecipes, (v) => v.investmentSchema);
  }, []);
  const activityTypeValues = useMemo(() => {
    return distinct(investmentRecipes, (v) => v.activityType);
  }, []);
  const activitySignTypeValues = useMemo(() => {
    return distinct(investmentRecipes, (v) =>  v.activitySignType);
  }, []);

  return (
    <div style={{ width: 1280, margin: "20px auto" }}>
      <div style={{ marginBottom: 20 }}>
        <Select
          style={{ minWidth: 200, marginRight: 10 }}
          showSearch
          value={query.activityType}
          placeholder="招商活动类型"
          hasClear
          onChange={(activityType) => {
            setQuery((q: any) => {
              return { ...q, activityType };
            });
          }}
        >
          {activityTypeValues.map((v) => {
            return (
              <Select.Option value={v} key={v}>
                {`${ActivityTypeEnumStr[v as number]}(${v})` || "未知"}
              </Select.Option>
            );
          })}
        </Select>
        <Select
          style={{ width: 150, marginRight: 10 }}
          value={query.investmentActivityType}
          placeholder="主活动类型"
          hasClear
          onChange={(investmentActivityType) => {
            setQuery((q: any) => {
              return { ...q, investmentActivityType };
            });
          }}
        >
          {investmentActivityTypeValues.map((v) => {
            return (
              <Select.Option value={v} key={v}>
                {InviteActivityTypeEnumStr[v as number] || "未知"}
              </Select.Option>
            );
          })}
        </Select>
        <Select
          style={{ width: 150, marginRight: 10 }}
          value={query.investmentType}
          placeholder="招商类型"
          hasClear
          onChange={(investmentType) => {
            setQuery((q: any) => {
              return { ...q, investmentType };
            });
          }}
        >
          {investmentTypeValues.map((v) => {
            return (
              <Select.Option value={v} key={v}>
                {InvestmentTypeEnumStr[v as number] || "未知"}
              </Select.Option>
            );
          })}
        </Select>
        <Select
          style={{ width: 150, marginRight: 10 }}
          showSearch
          value={query.investmentSchema}
          placeholder="招商模版"
          hasClear
          onChange={(investmentSchema) => {
            setQuery((q: any) => {
              return { ...q, investmentSchema };
            });
          }}
        >
          {investmentSchemaValues.map((v) => {
            return (
              <Select.Option value={v} key={v}>
                {InvestmentSchemaEnumStr[v as number] || "未知"}
              </Select.Option>
            );
          })}
        </Select>
        <Select
          style={{ width: 150, marginRight: 10 }}
          value={query.activitySignType}
          placeholder="招商报名类型"
          hasClear
          onChange={(activitySignType) => {
            setQuery((q: any) => {
              return { ...q, activitySignType };
            });
          }}
        >
          {activitySignTypeValues.map((v) => {
            return (
              <Select.Option value={v} key={v}>
                {ActivitySignTypeEnumStr[v as ActivitySignType] || "未知"}
              </Select.Option>
            );
          })}
        </Select>
        <Select
          style={{ width: 150, marginRight: 10 }}
          value={query.available}
          placeholder="是否可用"
          hasClear
          onChange={(available) => {
            setQuery((q: any) => {
              return { ...q, available };
            });
          }}
        >
          {[
            { label: "可用", value: true },
            { label: "不可用", value: false },
          ].map((v) => {
            return (
              <Select.Option value={v.value} key={`${v.value}`}>
                {v.label}
              </Select.Option>
            );
          })}
        </Select>
      </div>
      <Table dataSource={dataSource}>
        <Table.Column
          dataIndex="investmentActivityType"
          title="单/多活动"
          cell={(v: any) => {
            return <div>{InviteActivityTypeEnumStr[v]}</div>;
          }}
        />
        <Table.Column
          dataIndex="investmentType"
          title="招商类型"
          cell={(v: any) => {
            return <div>{InvestmentTypeEnumStr[v]}</div>;
          }}
        />
        <Table.Column
          dataIndex="investmentSchema"
          title="招商模板"
          cell={(v: any) => {
            return <div>{InvestmentSchemaEnumStr[v]}</div>;
          }}
        />
        <Table.Column
          dataIndex="activityType"
          title="活动类型"
          cell={(v: any) => {
            return <div>{ActivityTypeEnumStr[v]}({v})</div>;
          }}
        />
        <Table.Column
          dataIndex="activitySignType"
          title="活动报名类型"
          cell={(v: ActivitySignType) => {
            return <div>{ActivitySignTypeEnumStr[v]}</div>;
          }}
        />
        <Table.Column
          dataIndex="available"
          title="可用状态"
          cell={(v: any) => {
            return <div>{v ? "可用" : "不可用"}</div>;
          }}
        />
        <Table.Column
          dataIndex="remark"
          title="备注"
          cell={(v: any) => {
            return <div>{v || "无"}</div>;
          }}
        />
      </Table>
    </div>
  );
};

export default DebugRecipes;
