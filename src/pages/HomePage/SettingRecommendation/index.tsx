import './style.scss';

import React, { useEffect, useState, useCallback } from 'react';
import {
  Balloon,
  Button,
  Dialog,
  Form,
  Field,
  Icon,
  Input,
  Message,
  Radio,
} from '@alifd/next';
import {
  batchSettingActivityRecommendation,
  checkPermission,
} from '../../../api';
import { checkForbiddenAndAlertIfNeed } from '../../common';

interface ReqParams {
  recommendation: number | string;
  activityIdList: Array<number | string>;
}

const RadioGroup = Radio.Group;
const InputTextArea = Input.TextArea;
const formItemLayout = {
  labelCol: {
    fixedSpan: 6,
  },
  wrapperCol: {
    span: 18,
  },
};

function wrapCallback(_callback: any) {
  let callback = _callback;
  if (!callback) {
    callback = (msg: string) => {
      if (msg) {
        throw new Error(msg);
      }
    };
  }
  return callback;
}

interface Props {}

const RecommendationDialog: React.FC<Props> = ({}) => {
  const field = Field.useField();
  const [btnLoading, setBtnLoading] = useState(false);
  const [submitBtnLoading, setSubmitBtnLoading] = useState(false);
  const [ready, setReady] = useState(false);
  const [resFailList, setResFailList] = useState<
    { setFailReason?: string; success: boolean }[]
  >([]);
  const [successNum, setSuccessNum] = useState<number | undefined>();
  const [visible, setVisible] = useState(false);

  useEffect(() => {
    // 无申请权限入口，统一由产品添加
    checkPermission(
      'SET_RECOMMENDATION_OF_INVESTMENT_PROMOTION_ACTIVITIES'
    ).then((res) => {
      if (res.data) {
        setReady(true);
      }
    });
  }, []);

  // 关闭弹窗清空内容
  const _onClose = () => {
    setResFailList([]);
    setSuccessNum(undefined);
    setVisible(false);
  };

  const onSubmit = useCallback(async () => {
    // 校验条件
    const ret = await field.validatePromise();
    if (ret.errors) {
      return;
    }

    const values: { recommendation: string; activityIds: string } =
      field.getValues();
    const params: ReqParams = {} as ReqParams;
    params.recommendation = values?.recommendation;
    params.activityIdList = values?.activityIds
      ?.split('\n')
      .map((item) => item.trim())
      .filter(Boolean); // 去除前后空格后，去除空行

    setSubmitBtnLoading(true);
    batchSettingActivityRecommendation(params)
      .then((res) => {
        if (res?.success) {
          Message.success('设置成功');
          setResFailList(res?.data?.data?.filter((i) => !i.success) || []);
          setSuccessNum(res?.data?.successNum);
        } else {
          Message.error(res?.errorMessage || '系统异常');
        }
      })
      .catch((e) => {
        Message.error(e.message);
      })
      .finally(() => {
        setSubmitBtnLoading(false);
      });
  }, [field]);

  if (!ready) {
    return null;
  }

  return (
    <div>
      <Button
        type="primary"
        loading={btnLoading}
        data-test-id="homepage-setting-recommendation-button"
        onClick={async () => {
          setBtnLoading(true);
          if (await checkForbiddenAndAlertIfNeed()) {
            setBtnLoading(false);
            return;
          }
          setBtnLoading(false);
          setVisible(true);
        }}
      >
        设置活动推荐度
      </Button>

      <Dialog
        title="设置活动推荐度"
        visible={visible}
        data-test-id="recommendation-dialog"
        onClose={_onClose}
        footer={[
          <Button onClick={_onClose}>取消</Button>,
          <Button
            type="primary"
            style={{ marginLeft: 8 }}
            loading={submitBtnLoading}
            disabled={submitBtnLoading}
            data-test-id="homepage-setting-recommendation-submit-button"
            onClick={async () => await onSubmit()}
          >
            确认
          </Button>,
        ]}
        shouldUpdatePosition={false}
        style={{ width: 700 }}
      >
        <div>
          <div className="">
            1、只支持针对单招商/子招商ID进行设置（不支持主招商ID）
          </div>
          <div className="">
            2、不支持反圈的活动（买返购物金、优享单品立减）
          </div>
          <div className="">
            3、同一个招商活动ID多次设置时，将以最新一次的活动推荐度生效
          </div>

          <Form {...formItemLayout} style={{ marginTop: 20 }} field={field}>
            <Form.Item
              label={
                <span>
                  活动推荐度
                  <Balloon
                    v2
                    trigger={<Icon type="help" size={'small'} />}
                    triggerType="hover"
                    closable={false}
                  >
                    <div>
                      <div>
                        1、活动推荐度支持标注3档：二星/一星/无星。
                        重要性上，二星
                        {'>'}一星{'>'}无星。{' '}
                      </div>
                      <div>2、由各行业的营销接口人统一收口设置。 </div>
                      <div>
                        3、推荐度被设置为二星的活动，将获得额外的商家IM自动化触达资源（对于二星/一星活动，会在招商覆盖率和M1上存在考核要求，请谨慎设置）
                      </div>
                    </div>
                  </Balloon>
                  {' :'}
                </span>
              }
              required
            >
              <RadioGroup
                {...field.init('recommendation', {
                  rules: [
                    {
                      required: true,
                      message: '请选择活动推荐度',
                    },
                  ],
                })}
                aria-labelledby="groupId"
              >
                <Radio id="1" value="1">
                  一星
                </Radio>
                <Radio id="2" value="2">
                  二星
                </Radio>
                <Radio id="0" value="0">
                  无星级
                </Radio>
              </RadioGroup>
            </Form.Item>

            <Form.Item label="招商活动ID:" required>
              <InputTextArea
                {...field.init('activityIds', {
                  rules: [
                    {
                      required: true,
                      message: '请选填写招商活动ID',
                    },
                    {
                      validator: (rule, value: string, _callback) => {
                        let callback = wrapCallback(_callback);
                        // 去除前后空格后，去除空行，校验最多可添加20条
                        const activityList =
                          value
                            ?.split('\n')
                            .map((i) => i.trim())
                            .filter(Boolean) || [];
                        if (activityList.length > 20) {
                          callback('最多可添加20条活动id');
                        } else {
                          callback();
                        }
                      },
                    },
                  ],
                })}
                rows={5} // 不要直接用height设置多行文本框的高度, ie9 10会有兼容性问题
                placeholder={`支持从excel中直接复制粘贴到文本框，\n每行一个ID，\n示例：\n6309972042\n6309994018`}
              />
            </Form.Item>
          </Form>

          {/* 错误id展示 */}
          {resFailList && resFailList.length > 0 ? (
            <div>
              <span style={{ color: 'green' }}>{`${successNum}条成功；`}</span>
              <span
                style={{ color: 'red' }}
              >{`${resFailList.length}条失败：`}</span>
              {resFailList.map((item) => {
                return <div style={{ color: 'red' }}>{item.setFailReason}</div>;
              })}
            </div>
          ) : null}
        </div>
      </Dialog>
    </div>
  );
};

export default RecommendationDialog;
