import "./style.scss";

import * as qs from 'querystring';
import * as React from 'react';
import { useState, useCallback } from "react";
import { useHistory } from "react-router";
import { <PERSON><PERSON>, Tab, Button } from '@alifd/next';

import * as api from '../../api'
import {
  InvestmentRecipe,
  InvestmentSchemaEnum,
  InvestmentTypeEnum,
  InviteActivityTypeEnum,
  perm2ApplyURL,
  Permission,
} from "../../constants";
import CreationDialog from "./CreationDialog";
import MainActivityTab from "./MainActivityTab";
import SubActivityTab from "./SubActivityTab";
import { checkForbiddenAndAlertIfNeed, redirectZsV2Page } from "../common";

// const debug = require('debug')('zs:homepage');

// 创建活动权限检查点
interface PermissionCheckPoint {
  desc: string; // 描述
  permission: Permission; // 检查的权限名称
  match: (recipe: InvestmentRecipe) => boolean; // match 函数返回 true 时，说明该检查点需要被执行
  pri: number; // 通过 pri 来控制检查点执行的前后顺序，值越小越先执行
}


const HomePage: React.FC<any> = () => {
  const [visible, setVisible] = useState<boolean>(false)
  const history = useHistory()
  // 重定向到新页面
  redirectZsV2Page()

  const handleCopyActivity = useCallback((activityId: number | string, activityType: number, subsidyType: number | string, templateId: number | string) => {
    const href = history.createHref({
      pathname: '/create-sub-activity',
      search: '?' + qs.stringify({
        copy: true,
        activityId,
        activityType,
        subsidyType,
        templateId
      })
    })
    window.open(href, '_blank')
  }, [])
  return (
    <>
      <div className="zs-homepage-header">
        <div className="zs-homepage-header-title">招商活动</div>
        <Button
          style={{ marginRight: 20 }}
          type="primary"
          data-test-id="homepage-create-activity-button"
          onClick={async () => {
            if (await checkForbiddenAndAlertIfNeed()) {
              return;
            }
            setVisible(true)
          }}>
          创建招商活动
        </Button>
      </div>

      <div className="zs-homepage-content">
        <Tab defaultActiveKey="mainActivity" shape="wrapped">
          <Tab.Item title="按主活动查询" key="mainActivity">
            <MainActivityTab onCopyActivity={handleCopyActivity}/>
          </Tab.Item>
          <Tab.Item title="按子活动查询" key="subActivity">
            <SubActivityTab onCopyActivity={handleCopyActivity} />
          </Tab.Item>
        </Tab>
      </div>

      <CreationDialog
        visible={visible}
        onClose={() => setVisible(false)}
        onCancel={() => setVisible(false)}
        onCreate={async (_recipe: InvestmentRecipe) => {
          // step 1: 检查创建活动香相关的权限
          // 无论新老活动，创建活动的权限检查逻辑是没有区别的，相关检查点如下
          const permissionCheckPoints: PermissionCheckPoint[] = [{
            desc: '主活动创建权限检查',
            match: recipe => {
              return recipe.investmentActivityType === InviteActivityTypeEnum.MULTI_ACTIVITY
            },
            permission: Permission.ZS_PARENT_ACTIVITY_CREATION_PERMISSION,
            pri: 0,
          }, {
            desc: '算法招商活动创建权限检查',
            match: recipe => {
              return recipe.investmentType === InvestmentTypeEnum.SUANFA
            },
            permission: Permission.ZS_BRANCH_ACTIVITY_CREATION_PERMISSION,
            pri: 100
          }, {
            desc: '百亿补贴活动创建权限检查',
            match: recipe => {
              return recipe.investmentSchema === InvestmentSchemaEnum.BAIYI_BUTIE
            },
            permission: Permission.ZS_TEN_BILLION_SUBSIDY_ACTIVITY_PERMISSION,
            pri: 10
          }]

          const checkPoints = permissionCheckPoints.filter(p => p.match(_recipe)).sort((p1, p2) => p1.pri - p2.pri)
          for (let i = 0; i < checkPoints.length; i++) {
            const p = checkPoints[i]
            const res = await api.checkPermission(p.permission)
            if (!res.data) {
              const url = perm2ApplyURL[p.permission]
              Dialog.show({
                title: '提示',
                content: '您暂无该权限,是否跳转申请权限页面?',
                onOk: () => {
                  window.open(url, '_blank')
                }
              })
              return
            }
          }

          // step 2: 跳转到对应的创建活动页面
          if (_recipe.investmentActivityType === InviteActivityTypeEnum.SINGLE_ACTIVITY) {
            // 创建单活动
            const href = history.createHref({
              pathname: '/create-sub-activity',
              search: '?' + qs.stringify({ ..._recipe})
            })
            window.open(href, '_blank')
          } else { // 多活动
            const href = history.createHref({
              pathname: '/create-main-activity',
              search: '?' + qs.stringify({
                investmentType: _recipe.investmentType,
                investmentSchema: _recipe.investmentSchema
              })
            })
            window.open(href, '_blank')
          }
          setVisible(false)
        }}
      />
    </>
  )
};

export default HomePage;
