import * as React from 'react';

export const FilterItem: React.FC<any> = ({ label, children }) => {
  return (
    <div className="zs-filter-item">
      <div className="zs-filter-item-label">{label}</div>
      <div style={{ flex: 1 }}>{children}</div>
    </div>
  )
}

export const FilterRow: React.FC<any> = ({ children }) => {
  return (
    <div
      className="zs-filter-row"
      style={{ marginBottom: 15, marginTop: 15 }}>
      {children}
    </div>
  )
}