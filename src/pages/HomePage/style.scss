.zs-homepage-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  height: 55px;
  &-title {
    font-size: 20px;
    font-weight: bold;
  }
}

.zs-homepage-content {
  padding: 20px;
  background-color: #fff;
}

.zs-filter-item {
  display: flex;
  align-items: center;
  flex: 1;

  .next-input,
  .next-select {
    width: 100%;
  }

  &-label {
    margin-right: 10px;
    width: 100px;
    flex-shrink: 0;
    text-align: right;
  }
}

.zs-filter-row {
  display: flex;
  align-items: center;
}
.zs-main-activity-tab-block {
  display: flex;
  flex-direction: row;
  margin-top: 40px;
}

.zs-sub-activity-tab-block {
  display: flex;
  flex-direction: row;
  margin-top: 40px;
}

.zs-main-activity-table .next-table-cell {
  vertical-align: top;
}
