import "./style.scss";

import * as qs from "querystring";
import * as React from "react";
import { useState, useEffect, useCallback } from "react";
import { useSetState } from "ahooks";
import { useHistory } from "react-router";
import {
  Loading,
  Dialog,
  Menu,
  Dropdown,
  Tab,
  Button,
  Select,
  Input,
  Message,
  Table,
  Pagination,
} from "@alifd/next";

import { percentage, useQuery, confirmDialog, resolveEnv } from "../../common";
import BindSceneDrawer from "../../components/SubActivityTable/BindSceneDrawer";
import {
  InvestmentActivityDetailDTO,
  InvestmentTemplateDTO,
} from "../../models";
import * as api from "../../api";
import {
  ActivityTypeEnum,
  ActivityTypeEnumStr,
  InvestmentSchemaEnum,
  InvestmentSchemaEnumStr,
  InvestmentTypeEnumStr,
  InviteActivityTypeEnum,
  perm2ApplyURL,
  Permission,
  UserInfo,
} from "../../constants";
import { getStrategy } from "../../strategy";
import SubActivityCreationDialog, { Value } from "../../components/SubActivityCreationDialog";
import { FilterItem, FilterRow } from "./Filter";
import InfoItem from "../../components/InfoItem";
import {
  checkForbiddenAndAlertIfNeed,
  handleGotoActivityDetail,
  handleGotoMainActivityDetail,
  handleNoPermissionForActivity,
} from "../common";
import SettingRecommendation from "./SettingRecommendation";
import { MonitorFusion } from "@ali/monitor-fusion";

const moment = require("moment");
const debug = require("debug")("zs:homepage:MainActivityTable");

interface InvestmentTemplateQuery {
  templateId?: string; // 主活动 ID
  name?: string; // 活动名称
  status?: string; // 活动状态
  investmentType?: string; // 招商类型
  investmentSchema?: string; // 招商模板
  investmentActivityType?: string; // 活动类型
  activityType?: string; // 玩法类型
  createdUserName?: string; // 创建人
}

const OpBtn = (props: any) => (
  <div style={{ marginBottom: 10 }}>
    <Button {...props} type="primary" text />
  </div>
);

const Filter: React.FC<any> = ({ value, onChange, onSearch, onReset }) => {
  return (
    <>
      <FilterRow>
        <FilterItem label="招商活动ID">
          <Input
            placeholder="请输入招商活动ID"
            value={value?.templateId}
            onChange={(templateId) => {
              let id = templateId.replace(/\s+/g, "");
              return onChange({ ...value, templateId: id });
            }}
          />
        </FilterItem>
        <FilterItem label="活动名称">
          <Input
            placeholder="请输入招商活动名称"
            value={value?.name}
            onChange={(name) => onChange({ ...value, name })}
          />
        </FilterItem>
        <FilterItem label="活动状态">
          <Select
            placeholder="请选择"
            hasClear
            value={value?.status}
            onChange={(status) => onChange({ ...value, status })}
          >
            <Select.Option value={1}>未开始</Select.Option>
            <Select.Option value={2}>活动中</Select.Option>
            <Select.Option value={4}>活动取消</Select.Option>
            <Select.Option value={5}>活动结束</Select.Option>
          </Select>
        </FilterItem>
        <FilterItem label="招商类型">
          <Select
            placeholder="请选择"
            hasClear
            value={value?.investmentType}
            onChange={(investmentType) =>
              onChange({ ...value, investmentType })
            }
          >
            <Select.Option value={1}>栏目招商</Select.Option>
            <Select.Option value={2}>日常招商</Select.Option>
            <Select.Option value={3}>大促招商</Select.Option>
            <Select.Option value={4}>算法招商</Select.Option>
            <Select.Option value={5}>频道招商</Select.Option>
            <Select.Option value={7}>营销IP招商</Select.Option>
          </Select>
        </FilterItem>
      </FilterRow>
      <FilterRow>
        <FilterItem label="招商模板">
          <Select
            placeholder="请选择"
            hasClear
            value={value?.investmentSchema}
            onChange={(investmentSchema) =>
              onChange({ ...value, investmentSchema })
            }
          >
            {Object.values(InvestmentSchemaEnum).filter(
              (v) => typeof v === "number"
            ).map((v: any) => {
              return (
                <Select.Option key={v} value={v}>
                  {InvestmentSchemaEnumStr[v]}
                </Select.Option>
              );
            })}
          </Select>
        </FilterItem>
        <FilterItem label="活动类型">
          <Select
            placeholder="请选择"
            hasClear
            value={value?.investmentActivityType}
            onChange={(investmentActivityType) =>
              onChange({ ...value, investmentActivityType })
            }
          >
            <Select.Option value={0}>单活动</Select.Option>
            <Select.Option value={1}>多活动</Select.Option>
          </Select>
        </FilterItem>
        <FilterItem label="玩法类型">
          <Select
            placeholder="请选择"
            hasClear
            value={value?.activityType}
            onChange={(activityType) => onChange({ ...value, activityType })}
          >
            {Object.values(ActivityTypeEnum)
              .filter(
                (v) =>
                  typeof v === "number" && v !== ActivityTypeEnum.MKT_SALE_BASE
              ) // 忽略老的商品折扣活动
              .map((v: any) => {
                return (
                  <Select.Option key={v} value={v}>
                    {ActivityTypeEnumStr[v]}
                  </Select.Option>
                );
              })}
          </Select>
        </FilterItem>
        <FilterItem label="创建人">
          <Input
            placeholder="请输入创建人"
            hasClear
            value={value?.createdUserName}
            onChange={(createdUserName) =>
              onChange({ ...value, createdUserName })
            }
          />
        </FilterItem>
      </FilterRow>

      <FilterRow>
        {/* TODO: 阿尔法 - 字段值修改 */}
        {/* <FilterItem FilterItem label="活动场景ID">
          <Input placeholder="请输入活动场景ID"
            value={value?.activityId}
            onChange={activityId => onChange({ ...value, activityId })}
          />
        </FilterItem>
        <FilterItem label="活动场景名称">
          <Input placeholder="请输入活动场景名称"
            value={value?.name}
            onChange={name => onChange({ ...value, name })}
          />
        </FilterItem> */}
        <FilterItem label="">
          <Button type="primary" onClick={onSearch}>
            查询
          </Button>
          &nbsp;&nbsp;&nbsp;
          <Button onClick={onReset}>清空</Button>
        </FilterItem>
        <FilterItem />
      </FilterRow>
    </>
  );
};

const defaultPagination = {
  page: 1,
  pageSize: 15,
};

const MainActivityTab: React.FC<any> = ({ onCopyActivity }) => {
  const history = useHistory();
  const query = useQuery<InvestmentTemplateQuery>({});
  const [dataSource, setDataSource] = useState([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [tab, setTab] = useState("accessiable");
  const [subActivityCreation, updateSubActivityCreation] = useSetState<any>({
    visible: false,
    activityTypes: [],
    templateId: undefined,
  });
  const [version, setVersion] = useState(0);
  const [pagination, updatePagination] = useSetState(defaultPagination);
  const [userInfo, setUserInfo] = useState<UserInfo>({} as UserInfo);
  const [visible, setVisible] = useState(false);
  const [curActId, setCurActId] = useState<string | number>('');

  const handleNoPermission = useCallback(
    async (templateId: number | string, isNewActivity: boolean) => {
      // 用户无权限
      const ok = await confirmDialog({
        title: "提示",
        content: "您暂无该权限，是否跳转申请权限页面",
      });
      if (ok) {
        let params: any;
        if (isNewActivity) {
          params = {
            dataPermissionModelName: "business_mainactivity_permission_new",
            operationNames: "business_mainactivity_update_new",
            propertyNames: "business_mainactivity_new",
            business_mainactivity_new: templateId,
          };
        } else {
          params = {
            dataPermissionModelName: "business_mainactivity_permission",
            operationNames: "business_mainactivity_update",
            propertyNames: "business_mainactivity",
            business_mainactivity: templateId,
          };
        }

        const env = resolveEnv();
        const aclHost =
          env === "pre" ? "pre-acl.alibaba-inc.com" : "acl.alibaba-inc.com";
        const url = `https://${aclHost}/apply/cart/detail.htm?${qs.stringify(
          params
        )}`;
        window.open(url, "_blank");
      }
    },
    []
  );

  useEffect(() => {
    api.getUserInfo().then(res => {
      setUserInfo(res.data);
    })
  }, []);

  useEffect(() => {
    setLoading(true);
    const finalQuery: any = { ...query.committed };
    Object.keys(finalQuery).forEach((k: string) => {
      const value = finalQuery[k];
      if (value === "") {
        delete finalQuery[k];
      }
    });
    api.investmentTemplate
      .getList({
        ...finalQuery,
        ...pagination,
        isSelf: tab === "owner" ? 1 : 0,
      })
      .then((res) => {
        if (!res.success) {
          Message.error(res.errorMessage);
          return;
        }
        const { total: _total, data } = res;
        setTotal(_total);
        setDataSource(data as any);
      })
      .finally(() => {
        setLoading(false);
      });
  }, [query.committed, pagination, version, tab]);

  const handleAction = useCallback(
    async (
      record: InvestmentTemplateDTO,
      action:
        | "offlineMainActivity"
        | "createSubActivity"
        | "deleteMainActivity"
        | "lookup"
        | "examine"
        | "modifyStock"
        | "dingtalkLink"
        | "opt"
        | "offlineSingleActivity"
        | "copySingleActivity"
        | "bindScene"
        | "modifyPlatformSubsidy"
    ) => {
      switch (action) {
        case "createSubActivity":
          if (await checkForbiddenAndAlertIfNeed()) {
            return;
          }
          let permission = Permission.ZS_CHILD_ACTIVITY_CREATION_PERMISSION;
          const res = await api.checkPermission(permission);
          if (!res.data) {
            const url = perm2ApplyURL[permission];
            Dialog.show({
              title: "提示",
              content: "您暂无该权限,是否跳转申请权限页面?",
              onOk: () => {
                window.open(url, "_blank");
              },
            });
            return;
          }
          updateSubActivityCreation({
            visible: true,
            isNewActivity:
              record.investmentTemplateDetailDTO.fromString === "NEW",
            templateId: record.investmentTemplateDetailDTO.templateId,
            activityTypes: record.investmentTemplateDetailDTO.activityTypes,
          });
          break;
        case "lookup":
          if (record.investmentTemplateDetailDTO.investmentActivityType === 0) {
            // 单活动
            handleGotoActivityDetail(
              record.investmentActivityDetailDTO as InvestmentActivityDetailDTO,
              history,
              "lookup"
            );
          } else {
            // 多活动
            handleGotoMainActivityDetail(record, history);
          }
          break;
        case "examine":
          // 只有单活动支持审核按钮，当前活动一定是一个单活动
          handleGotoActivityDetail(
            record.investmentActivityDetailDTO as InvestmentActivityDetailDTO,
            history,
            "examine"
          );
          break;
        case "modifyStock":
          if (await checkForbiddenAndAlertIfNeed()) {
            return;
          }
          const strategy = getStrategy(
            record.investmentActivityDetailDTO?.activityType
          );
          if (!strategy || !strategy.handleModifyStock) {
            return;
          }
          strategy.handleModifyStock(
            {
              isNewActivity:
                record.investmentTemplateDetailDTO.fromString === "NEW",
              activityId: record.investmentActivityDetailDTO
                ?.activityId as number,
              viewJson: record.viewJson,
              marketPlay: record.investmentActivityDetailDTO?.marketPlay,
              investmentSchema: record.investmentTemplateDetailDTO?.investmentSchema
            },
            {
              onSuccess: () => {
                Message.success("修改成功");
                setVersion((v) => v + 1);
              },
              onFail: (msg: string) => {
                Message.error(msg || "修改失败，请稍后重试");
              },
            }
          );
          break;
        case "offlineMainActivity":
        case "offlineSingleActivity":
          if (await checkForbiddenAndAlertIfNeed()) {
            return;
          }
          Dialog.show({
            title: "提示",
            style: { width: 300 },
            content: "确认要下线活动吗？",
          onOk: async () => {
              const templateId = record.investmentTemplateDetailDTO
                ?.templateId as number;
              const isNewActivity =
                record.investmentTemplateDetailDTO?.fromString === "NEW";
              try {
                let _res: any;
                if (isNewActivity) {
                  _res = await api.investmentTemplate.offline(templateId);
                } else {
                  // 老活动
                  if (
                    record.investmentTemplateDetailDTO
                      .investmentActivityType ===
                    InviteActivityTypeEnum.SINGLE_ACTIVITY
                  ) {
                    // 老活动下的单活动
                    if (
                      record.investmentActivityDetailDTO?.activityType ===
                      ActivityTypeEnum.N_YUAN_ACTIVITY
                    ) {
                      // 如果是 N 元购活动，调用老的 N 元购单活动下线接口
                      _res = await api.legacy.offlineNBuyActivity(
                        record?.investmentActivityDetailDTO
                          ?.activityId as number
                      );
                    } else {
                      // 如果不是 N 元购活动，调用单活动下线接口
                      _res = await api.legacy.offlineSubActivity(
                        record?.investmentActivityDetailDTO
                          ?.activityId as number
                      );
                    }
                  } else {
                    // 老活动下的多活动
                    _res = await api.legacy.offlineMainActivity(templateId);
                  }
                }
                if (!_res.success) {
                  const noPermissionForTemplate =
                    _res.errorCode === "NO_AUDIT_FOR_TEMPLATE";
                  const noPermissionForActivity =
                    _res.errorCode === "NO_AUDIT_FOR_ACTIVITY";
                  if (noPermissionForTemplate) {
                    await handleNoPermission(templateId, isNewActivity);
                    throw new Error("===no Permission For Template===");
                  } else if (noPermissionForActivity) {
                    await handleNoPermissionForActivity(
                      record?.investmentActivityDetailDTO?.activityId as number,
                      {
                        activityType: record.investmentActivityDetailDTO
                          ?.activityType as number,
                        isNewActivity,
                      }
                    );
                    throw new Error("===no Permission For Activity===");
                  } else {
                    Message.error(_res.errorMessage);
                    throw new Error(_res.errorMessage);
                  }
                }
              } catch(err: any) {
                // 主活动下线异常 arms监控
                MonitorFusion?.logCustomEvent({
                  code: "offline_activity_err",
                  msg: err.message || err,
                  c1: `main activity -> isNewActivity： ${isNewActivity}, `,
                  c2: `${record.investmentActivityDetailDTO
                    ?.activityType}`,
                  c3: `${record?.investmentActivityDetailDTO?.activityId}`
                });
              } finally {
                setVersion((v) => v + 1);
              }
            },
          });
          break;
        case "dingtalkLink":
          let url = `https://nr.ele.me/op-fe/ebai-zs-h5-landing?activityId=${record.investmentActivityDetailDTO?.activityId}`;
          if (record.investmentTemplateDetailDTO.fromString === "NEW") {
            url += "&isNewActivity=true";
          }
          Dialog.show({
            title: "提示",
            content: <>报名链接：{url}</>,
            footer: false,
          });
          break;
        case "copySingleActivity":
          const selectSubsidyType = record.investmentActivityDetailDTO?.selectSubsidyType || [];
          let subsidyType: any;
          if(selectSubsidyType.length > 1 ) {
            Dialog.show({
              title: "请选择补贴类型",
              style: { width: 300 },
              content: (
                <div>
                  补贴类型：
                  <Select
                    data-test-id="creation-dialog-template-type"
                    onChange={(value) => {
                      subsidyType = value;
                    }}
                    placeholder="请选择补贴类型"
                  >
                    {selectSubsidyType.map((item) => (
                      <Select.Option value={item}>{item}</Select.Option>
                    ))}
                  </Select>
                </div>
              ),
              footer: [
                <Button
                  type="primary"
                  onClick={() => {
                    if (!subsidyType) {
                      Message.error("请选择补贴类型");
                      return;
                    }
                    onCopyActivity(
                      record.investmentActivityDetailDTO?.activityId,
                      record.investmentActivityDetailDTO?.activityType,
                      subsidyType,
                      undefined
                    );
                  }}
                >
                  复制活动
                </Button>,
              ],
            });
          } else {
            subsidyType = selectSubsidyType[0]
            onCopyActivity(
              record.investmentActivityDetailDTO?.activityId,
              record.investmentActivityDetailDTO?.activityType,
              subsidyType,
              undefined
            )
          }
          
          break;
        case "bindScene":
          setVisible(true);
          setCurActId(record.investmentActivityDetailDTO?.activityId || '');
          break;
        case "modifyPlatformSubsidy":
          if (await checkForbiddenAndAlertIfNeed()) {
            return;
          }
          const strategys = getStrategy(
            record.investmentActivityDetailDTO?.activityType
          );
          if (!strategys || !strategys.handleModifyPlatformSubsidy) {
            return;
          }
          strategys.handleModifyPlatformSubsidy(
            {
              activityId: record.investmentActivityDetailDTO
                ?.activityId as number,
              viewJson: record.viewJson,
            },
            {
              onSuccess: () => {
                Message.success("修改成功");
                setVersion((v) => v + 1);
              },
              onFail: (msg: string) => {
                Message.error(msg || "修改失败，请稍后重试");
              },
            }
          );
        break;
        case "opt":
          break;
        default:
          throw new Error("unsupported action type:" + action);
      }
    },
    []
  );

  return (
    <>
      <Filter
        value={query.value}
        onChange={(v: any) => {
          query.update(v);
          debug("on filter change", v);
        }}
        onReset={() => {
          updatePagination(defaultPagination);
          query.resetAndCommit();
        }}
        onSearch={() => {
          updatePagination(defaultPagination);
          query.commit();
        }}
      />

      <div className="zs-main-activity-tab-block">
        <Tab
          shape="wrapped"
          activeKey={tab}
          onChange={(_tab: any) => {
            updatePagination(defaultPagination);
            setTab(_tab);
          }}
        >
          <Tab.Item title="可查看的" key="accessiable" />
          <Tab.Item title="我负责的" key="owner" />
        </Tab>

        {/* 活动推荐度 */}
        <SettingRecommendation />
      </div>

      <div>
        <Table
          className="zs-main-activity-table"
          loading={loading}
          loadingComponent={() => (
            <Loading style={{ width: "100%", height: 400 }} visible={true} />
          )}
          dataSource={dataSource}
        >
          <Table.Column
            title="活动信息"
            width={420}
            cell={(_: any, index: number, record: InvestmentTemplateDTO) => {
              const {
                templateId,
                name,
                beginTime,
                endTime,
                investmentActivityType,
              } = record.investmentTemplateDetailDTO;
              const { activityId, activityType, remark } =
                (record.investmentActivityDetailDTO ||
                  {}) as InvestmentActivityDetailDTO;
              const isSingleActivity =
                investmentActivityType ===
                InviteActivityTypeEnum.SINGLE_ACTIVITY;
              const strategy = isSingleActivity
                ? getStrategy(activityType)
                : null;
              return (
                <>
                  <InfoItem label="活动ID" labelWidth={100}>
                    {!isSingleActivity ? templateId : activityId}
                  </InfoItem>
                  <InfoItem label="活动名称" labelWidth={100}>
                    {name}
                  </InfoItem>
                  {isSingleActivity && remark ? (
                    <InfoItem label="活动备注" labelWidth={100}>
                      {remark}
                    </InfoItem>
                  ) : null}
                  <InfoItem label="活动类型" labelWidth={100}>
                    {!isSingleActivity ? "多活动" : "单活动"}
                  </InfoItem>
                  <InfoItem label="活动时间" labelWidth={100}>
                    {moment(beginTime).format("YYYY-MM-DD HH:mm:ss")} ~{" "}
                    {moment(endTime).format("YYYY-MM-DD HH:mm:ss")}
                  </InfoItem>
                  {isSingleActivity &&
                  strategy &&
                  strategy.renderExtraInfoForMainActivityTable
                    ? strategy.renderExtraInfoForMainActivityTable(record)
                    : null}
                  {isSingleActivity &&
                  record.investmentAlphaInfoNodeDTOList &&
                  record.investmentAlphaInfoNodeDTOList.length > 0 ? (
                    <InfoItem label="所属活动场景" labelWidth={100}>
                      {record.investmentAlphaInfoNodeDTOList.length > 1 ? (
                        <>
                          {record.investmentAlphaInfoNodeDTOList[0].sceneName}
                          &nbsp;
                          <span style={{ color: "#999" }}>
                            等{record.investmentAlphaInfoNodeDTOList.length}个
                          </span>
                        </>
                      ) : (
                        <>
                          {record.investmentAlphaInfoNodeDTOList[0].sceneName}
                        </>
                      )}
                    </InfoItem>
                  ) : null}
                </>
              );
            }}
          />
          <Table.Column
            title="招商类型"
            width={300}
            cell={(_: any, index: number, record: InvestmentTemplateDTO) => {
              const {
                investmentActivityType,
                investmentType,
                investmentSchema,
                activityTypes,
              } = record.investmentTemplateDetailDTO;
              let ruleContent;
              if (
                investmentActivityType ===
                InviteActivityTypeEnum.SINGLE_ACTIVITY
              ) {
                const strategy = getStrategy(
                  record?.investmentActivityDetailDTO?.activityType
                );
                if (
                  record?.investmentTemplateDetailDTO?.fromString === "OLD" &&
                  record?.investmentActivityDetailDTO?.marketPlay
                ) {
                  if (strategy) {
                    try {
                      ruleContent = strategy.renderActivityRuleForTableLegacy(
                        record?.investmentActivityDetailDTO?.marketPlay
                      );
                    } catch (e) {
                      console.error(e);
                    }
                  }
                } else if (
                  record?.investmentActivityDetailDTO?.activityType &&
                  record?.viewJson
                ) {
                  if (strategy) {
                    try {
                      ruleContent = strategy.renderActivityRuleForTable(
                        record.viewJson,
                        { investmentSchema }
                      );
                    } catch (e) {
                      console.error(e);
                    }
                  }
                }
              }
              return (
                <>
                  <InfoItem label="招商类型">{InvestmentTypeEnumStr[investmentType]}</InfoItem>
                  <InfoItem label="招商模板">{InvestmentSchemaEnumStr[investmentSchema]}</InfoItem>
                  {ruleContent ?
                    <InfoItem label="优惠内容">{ruleContent}</InfoItem> :
                    null}
                  <InfoItem label="玩法类型">{activityTypes.map(t => ActivityTypeEnumStr[t]).join(',')}</InfoItem>
                </>
              );
            }}
          />
          <Table.Column
            width={280}
            title="活动报名统计"
            cell={(_: any, index: number, record: InvestmentTemplateDTO) => {
              const { activityTypes, approveStatus } =
                record.investmentTemplateDetailDTO;
              const {
                approveNum,
                waitApproveNum,
                signupNum,
                inviteSuccessNum,
                inviteTotalNum,
              } = record.investmentActivityExtendDTO || {};
              const acceptPrecent = percentage(signupNum, inviteSuccessNum);
              const approvePrecent = percentage(
                approveNum,
                (waitApproveNum as number) + (approveNum as number)
              );
              let detailInviteStatsVisible = true;
              let isHideSignUpInfo = false;
              for (let i = 0; i < activityTypes.length; i++) {
                const strategy = getStrategy(activityTypes[i]);
                isHideSignUpInfo =
                  (typeof strategy?.isHideSignUpInfo === "function"
                    ? strategy?.isHideSignUpInfo(
                       {subsidyInvestmentModel: record?.investmentActivityDetailDTO?.attributes?.subsidyInvestmentModel}
                      )
                    : strategy?.isHideSignUpInfo) || false;
                if (!strategy?.isDetailInviteStatsOnMainActivityTableVisible) {
                  detailInviteStatsVisible = false;
                  break;
                }
              }
              return (
                <>
                  {!isHideSignUpInfo && <>
                    <InfoItem labelWidth={120} label="已报名/已邀请">
                      {signupNum}/{inviteSuccessNum}
                    </InfoItem>
                    <InfoItem labelWidth={120} label="报名率">
                      {acceptPrecent}
                    </InfoItem>
                  </>
                  }
                  {detailInviteStatsVisible ? (
                    <>
                      <InfoItem labelWidth={120} label="待审核/已审核">
                        {waitApproveNum}/{approveNum}
                      </InfoItem>
                      <InfoItem labelWidth={120} label="审核通过率">
                        {approvePrecent}
                      </InfoItem>
                      {!isHideSignUpInfo && <InfoItem labelWidth={120} label="共邀请">
                        {inviteTotalNum}
                        {+approveStatus !== 20 && +approveStatus !== 40 ? (
                          <>
                            （邀请失败
                            {(inviteTotalNum as number) -
                              (inviteSuccessNum as number)}
                            ）
                          </>
                        ) : null}
                      </InfoItem>}
                    </>
                  ) : null}
                </>
              );
            }}
          />
          <Table.Column
            width={265}
            title="状态"
            cell={(_: any, index: number, record: InvestmentTemplateDTO) => {
              const {
                status,
                approveStatusStr,
                statusStr,
                failReason,
                investmentActivityType,
              } = record.investmentTemplateDetailDTO;
              if (
                investmentActivityType === InviteActivityTypeEnum.MULTI_ACTIVITY
              ) {
                // 主活动
                return (
                  <>
                    <InfoItem labelWidth={100} label="主活动状态">
                      {statusStr}
                    </InfoItem>
                    <InfoItem labelWidth={100} label="预算审批状态">
                      {approveStatusStr}
                    </InfoItem>
                    {status === 7 && (
                      <InfoItem label="失败信息">{failReason}</InfoItem>
                    )}
                  </>
                );
              } else {
                const {
                  status: _status,
                  approveStatusStr: _approveStatusStr,
                  inviteStatusStr,
                  statusStr: _statusStr,
                  failReason: _failReason,
                } = record.investmentActivityDetailDTO || {};
                // 单活动
                return (
                  <>
                    <InfoItem label="活动状态">{_statusStr}</InfoItem>
                    <InfoItem label="报名状态">{inviteStatusStr}</InfoItem>
                    <InfoItem label="审批状态">{_approveStatusStr}</InfoItem>
                    {_status === 7 && (
                      <InfoItem label="失败信息">{_failReason}</InfoItem>
                    )}
                  </>
                );
              }
            }}
          />
          <Table.Column
            width={265}
            title="创建信息"
            cell={(_: any, index: number, record: InvestmentTemplateDTO) => {
              const { createdUserName, gmtCreate, gmtModified } =
                record.investmentTemplateDetailDTO;
              return (
                <>
                  <InfoItem labelWidth={100} label="创建人">
                    {createdUserName}
                  </InfoItem>
                  <InfoItem labelWidth={100} label="创建时间">
                    {moment(gmtCreate).format("YYYY-MM-DD HH:mm:ss")}
                  </InfoItem>
                  <InfoItem labelWidth={100} label="最后修改时间">
                    {moment(gmtModified).format("YYYY-MM-DD HH:mm:ss")}
                  </InfoItem>
                </>
              );
            }}
          />
          <Table.Column
            title="操作"
            lock="right"
            width={120}
            cell={(value: any, index: any, record: InvestmentTemplateDTO) => {
              const { investmentActivityType, status, activityTypes, budgetId  } =
                record.investmentTemplateDetailDTO;
              const actStatus = record.investmentActivityDetailDTO?.status;
              enum StatusEnum {
                NOT_STARTED = 1,
                ON_GOING = 2,
                SUSPENDED = 3,
                CANCELED = 4,
                FINISHED = 5,
                BUDGET_EXHAUSTED = 8,
              }

              if (investmentActivityType === 1) {
                // 多活动
                return (
                  <>
                    <OpBtn onClick={() => handleAction(record, "lookup")}>
                      查看
                    </OpBtn>
                    {[
                      StatusEnum.NOT_STARTED,
                      StatusEnum.ON_GOING,
                      StatusEnum.BUDGET_EXHAUSTED,
                    ].includes(status) && (
                      <OpBtn
                        onClick={() =>
                          handleAction(record, "offlineMainActivity")
                        }
                      >
                        下线
                      </OpBtn>
                    )}
                    {[StatusEnum.NOT_STARTED, StatusEnum.ON_GOING].includes(
                      status
                    ) && !budgetId && (
                      <OpBtn
                        onClick={() =>
                          handleAction(record, "createSubActivity")
                        }
                      >
                        创建子活动
                      </OpBtn>
                    )}
                  </>
                );
              } else {
                const activityType = activityTypes[0];
                const strategy = getStrategy(activityType);
                const isSingleActivity = record.investmentTemplateDetailDTO.investmentActivityType === InviteActivityTypeEnum.SINGLE_ACTIVITY
                // 单活动
                // 三方混补不需要审核功能
                return (
                  <>
                    <OpBtn onClick={() => handleAction(record, "lookup")}>
                      查看
                    </OpBtn>
                    {!strategy?.checkBtnVisible && <OpBtn onClick={() => handleAction(record, "examine")}>
                      审核
                    </OpBtn>}
                    {isSingleActivity && strategy?.isSupportModifyStock({
                      isNewActivity:
                        record.investmentTemplateDetailDTO.fromString === "NEW",
                      activityId: record.investmentActivityDetailDTO
                        ?.activityId as number,
                      viewJson: record.viewJson,
                      marketPlay:
                        record.investmentActivityDetailDTO?.marketPlay,
                      investmentSchema: record.investmentActivityDetailDTO?.investmentSchema
                    }) ? (
                      <OpBtn
                        onClick={() => handleAction(record, "modifyStock")}
                      >
                        修改库存
                      </OpBtn>
                    ) : null}
                    {strategy?.isModifyPlatformSubsidy && strategy?.isModifyPlatformSubsidy(record?.viewJson) ? (
                      <OpBtn
                        onClick={() => handleAction(record, "modifyPlatformSubsidy")}
                      >
                        修改
                      </OpBtn>
                    ) : null}
                    {strategy?.isDingtalkLinkEnabled() ? (
                      <OpBtn
                        onClick={() => handleAction(record, "dingtalkLink")}
                      >
                        钉群报名链接
                      </OpBtn>
                    ) : null}

                    {[StatusEnum.NOT_STARTED, StatusEnum.ON_GOING].includes(
                      status
                    ) &&
                    userInfo?.workid?.toLowerCase() ===
                      record?.investmentActivityDetailDTO?.createdUserId?.toLowerCase()&&
                      (!strategy?.noAssociatedActScene) ? (
                      <OpBtn onClick={() => handleAction(record, 'bindScene')}>
                        关联活动场景
                      </OpBtn>
                    ) : null}

                    <Dropdown
                      trigger={
                        <div
                          style={{ display: "inline-block", color: "#FF7C4D" }}
                        >
                          更多
                          <i className="next-icon next-icon-arrow-down" />
                        </div>
                      }
                      triggerType="click"
                    >
                      <Menu>
                        <Menu.Item key="opt">
                          <OpBtn onClick={() => handleAction(record, "opt")}>
                            操作记录
                          </OpBtn>
                        </Menu.Item>
                        {strategy?.isOfflineEnabled(actStatus as number) ? (
                          <Menu.Item key="offlineSubActiivty">
                            <OpBtn
                              onClick={() =>
                                handleAction(record, "offlineSingleActivity")
                              }
                            >
                              下线
                            </OpBtn>
                          </Menu.Item>
                        ) : null}
                        {(record.investmentActivityDetailDTO?.selectSubsidyType || []).length > 0 && isSingleActivity && strategy?.isCopyEnabled?.({
                          investmentSchema: record.investmentActivityDetailDTO?.investmentSchema!,
                          investmentType: record.investmentActivityDetailDTO?.investmentType!,
                          viewJson: record.viewJson
                        }) ? (
                          <Menu.Item key="copySingleActivity">
                            <OpBtn
                              onClick={() =>
                                handleAction(record, "copySingleActivity")
                              }
                            >
                              复制
                            </OpBtn>
                          </Menu.Item>
                        ) : null}
                      </Menu>
                    </Dropdown>
                  </>
                );
              }
            }}
          />
        </Table>
      </div>

      <Pagination
        total={total}
        style={{ marginTop: 20, textAlign: "right" }}
        current={pagination.page}
        pageSize={pagination.pageSize}
        onChange={(page) => updatePagination({ page })}
      />

      <BindSceneDrawer
        visible={visible}
        onClose={() => setVisible(false)}
        activityId={curActId}
      />

      <SubActivityCreationDialog
        isNewActivity={subActivityCreation.isNewActivity}
        visible={subActivityCreation.visible}
        templateId={subActivityCreation.templateId}
        onClose={() => updateSubActivityCreation({ visible: false })}
        onCancel={() => updateSubActivityCreation({ visible: false })}
        onCreate={async (recipe: Value) => {
          if(recipe.subsidyInvestmentModel) {
            let permission = recipe.subsidyInvestmentModel === 'A' ? Permission.AYZBX_A1_MANAGE : Permission.AYZBX_Y_MANAGE; 
            const res = await api.checkPermission(permission);
            if (!res.data) {
              const url = perm2ApplyURL[permission];
              Dialog.show({
                title: "提示",
                content: "您暂无该权限,是否跳转申请权限页面?",
                onOk: () => {
                  window.open(url, "_blank");
                },
              });
              return;
            }
          }
          
            const href = history.createHref({
              pathname: "/create-sub-activity",
              search:
                "?" +
                qs.stringify({
                  templateId: subActivityCreation.templateId,
                  ...recipe,
                }),
            });
            window.open(href, "_blank");
          updateSubActivityCreation({ visible: false });
        }}
      />

    </>
  );
};

export default MainActivityTab;
