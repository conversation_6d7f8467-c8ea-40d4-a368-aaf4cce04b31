import * as React from "react";
import { useMemo } from "react";
import { Select, Dialog, Form, Field } from "@alifd/next";
import { useRequest } from "ahooks";

import {
  ActivitySignType,
  InvestmentRecipe,
  InvestmentSchemaEnum,
  InviteActivityTypeEnum,
} from "../../constants";
import * as api from "../../api";
import { QualityProductPlanSelect } from "components/QualityProductPlanSelect";

function distinct(arr: number[]) {
  return Array.from(new Set(arr));
}

const formItemLayout = {
  labelCol: {
    fixedSpan: 4,
  },
  wrapperCol: {
    span: 20,
  },
};

interface ActivityMetadata {
  activitySignTypeMap: Record<string, string>;
  activityTypeMap: Record<string, string>;
  investmentActivityTypeMap: Record<string, string>;
  investmentSchemaMap: Record<string, string>;
  investmentTypeMap: Record<string, string>;
  sceneList: Array<any>; // 用具体的类型替换 `any` 以便更准确地定义这个数组
  subsidyTypeMap: Record<string, string>;
}

interface ApiResponse {
  data: {
    data: ActivityMetadata;
  };
}

interface Props {
  visible: boolean;
  onClose: () => void;
  onCancel: () => void;
  onCreate: (recipe: InvestmentRecipe) => void;
}

const CreationDialog: React.FC<Props> = ({
  onCreate,
  visible,
  onClose,
  onCancel,
}) => {
  const { data } = useRequest(
    api.subActivity.queryInvestmentMetadata
  ) as ApiResponse;
  const {
    activitySignTypeMap = {},
    activityTypeMap = {},
    investmentSchemaMap = {},
    investmentTypeMap = {},
    sceneList = [],
    subsidyTypeMap = {},
  } = data?.data || {};
  const field = Field.useField({
    onChange: (name: string) => {
      if (name === "investmentActivityType") {
        field.setValues({
          investmentType: undefined,
          investmentSchema: undefined,
          activityType: undefined,
          activitySignType: undefined,
          subsidyType: undefined,
        });
      } else if (name === "investmentType") {
        field.setValues({
          investmentSchema: undefined,
          activityType: undefined,
          activitySignType: undefined,
          subsidyType: undefined,
        });
      } else if (name === "subsidyType") {
        field.setValues({
          investmentSchema: undefined,
          activityType: undefined,
          activitySignType: undefined,
        });
      } else if (name === "investmentSchema") {
        field.setValues({
          activityType: undefined,
          activitySignType: undefined,
          qualityProductPlanId: undefined,
        });
      } else if (name === "activityType") {
        field.setValues({
          activitySignType: undefined,
        });
      }
    },
  });

  const investmentTypeOptions = useMemo(() => {
    const investmentActivityType = field.getValue("investmentActivityType");
    if (investmentActivityType !== 0 && investmentActivityType !== 1) {
      return [];
    }
    let investmentTypes = (sceneList || [])
      .filter((r) => r.investmentActivityType === investmentActivityType)
      .map((r) => r.investmentType);
    investmentTypes = distinct(investmentTypes);
    return investmentTypes.map((value) => {
      return {
        value,
        label: investmentTypeMap[value],
      };
    });
  }, [field.getValue("investmentActivityType")]);

  const subsidyTypeOptions = useMemo(() => {
    const investmentActivityType = field.getValue("investmentActivityType");
    const investmentType = field.getValue("investmentType");
    if (
      typeof investmentType !== "number" ||
      typeof investmentActivityType !== "number"
    ) {
      return [];
    }
    let subsidyTypes = (sceneList || [])
      .filter(
        (r) =>
          r.investmentActivityType === investmentActivityType &&
          r.investmentType === investmentType
      )
      .map((r) => r.subsidyType)
      .filter(Boolean);
    subsidyTypes = distinct(subsidyTypes);
    return subsidyTypes.map((value) => {
      return {
        value,
        label: subsidyTypeMap[value],
      };
    });
  }, [
    field.getValue("investmentType"),
    field.getValue("investmentActivityType"),
  ]);

  const investmentSchemaOptions = useMemo(() => {
    const investmentActivityType = field.getValue("investmentActivityType");
    const investmentType = field.getValue("investmentType");
    const subsidyType = field.getValue("subsidyType");
    if (
      typeof investmentType !== "number" ||
      typeof investmentActivityType !== "number"
    ) {
      return [];
    }
    let investmentSchemaList = [];
    if (investmentActivityType === 0) {
      // 单活动
      if (typeof subsidyType !== "string") {
        return [];
      } else {
        investmentSchemaList = (sceneList || [])
          .filter(
            (r) =>
              r.investmentActivityType === investmentActivityType &&
              r.investmentType === investmentType &&
              r.subsidyType === subsidyType
          )
          .map((r) => r.investmentSchema);
      }
    } else {
      // 多活动
      investmentSchemaList = (sceneList || [])
        .filter(
          (r) =>
            r.investmentActivityType === investmentActivityType &&
            r.investmentType === investmentType
        )
        .map((r) => r.investmentSchema);
    }
    investmentSchemaList = distinct(investmentSchemaList);
    return investmentSchemaList.map((value) => {
      return {
        value,
        label: investmentSchemaMap[value],
      };
    });
  }, [
    field.getValue("investmentType"),
    field.getValue("investmentActivityType"),
    field.getValue("subsidyType"),
  ]);

  const activityTypeOptions = useMemo(() => {
    const investmentActivityType = field.getValue("investmentActivityType");
    const investmentType = field.getValue("investmentType");
    const subsidyType = field.getValue("subsidyType");
    const investmentSchema = field.getValue("investmentSchema");
    if (
      investmentActivityType !== 0 ||
      typeof investmentType !== "number" ||
      typeof subsidyType !== "string" ||
      typeof investmentSchema !== "number"
    ) {
      return [];
    }
    let activityTypes = (sceneList || [])
      .filter(
        (r) =>
          r.investmentActivityType === investmentActivityType &&
          r.investmentType === investmentType &&
          r.subsidyType === subsidyType &&
          r.investmentSchema === investmentSchema &&
          r.activityType
      )
      .map((r) => r.activityType as number);
    activityTypes = distinct(activityTypes);
    return activityTypes.map((value: string | number) => {
      return {
        value,
        label: activityTypeMap[value],
      };
    });
  }, [
    field.getValue("investmentType"),
    field.getValue("investmentSchema"),
    field.getValue("subsidyType"),
    field.getValue("investmentActivityType"),
  ]);

  const activitySignTypeOptions = useMemo(() => {
    const investmentActivityType = field.getValue("investmentActivityType");
    const investmentType = field.getValue("investmentType");
    const subsidyType = field.getValue("subsidyType");
    const investmentSchema = field.getValue("investmentSchema");
    const activityType = field.getValue("activityType");
    if (
      investmentActivityType !== 0 ||
      typeof investmentType !== "number" ||
      typeof subsidyType !== "string" ||
      typeof investmentSchema !== "number" ||
      typeof activityType !== "number"
    ) {
      return [];
    }
    const activitySignTypes = (sceneList || [])
      .filter(
        (r) =>
          r.investmentActivityType === investmentActivityType &&
          r.investmentType === investmentType &&
          r.subsidyType === subsidyType &&
          r.investmentSchema === investmentSchema &&
          r.activityType === activityType &&
          r.activitySignType
      )
      .map((r) => r.activitySignType as ActivitySignType);
    return activitySignTypes.map((value) => {
      return {
        value,
        label: activitySignTypeMap[value],
      };
    });
  }, [
    field.getValue("investmentType"),
    field.getValue("investmentSchema"),
    field.getValue("subsidyType"),
    field.getValue("investmentActivityType"),
    field.getValue("activityType"),
  ]);

  const isQualityProductSelectVisible =
    field.getValue("investmentActivityType") ===
      InviteActivityTypeEnum.SINGLE_ACTIVITY &&
    field.getValue("investmentSchema") === InvestmentSchemaEnum.SUPER_COMMODITY_GOOD_PRICE;

  return (
    <Dialog
      title="选择活动类型"
      visible={visible}
      data-test-id="creation-dialog"
      onCancel={onCancel}
      onClose={onClose}
      footerActions={["cancel", "ok"]}
      shouldUpdatePosition={false}
      afterClose={() => {
        field.setValues({});
        field.setErrors({});
      }}
      onOk={async () => {
        const ret = await field.validatePromise();
        console.log("form ret", ret);
        if (ret.errors) {
          return;
        }
        const values = field.getValues<InvestmentRecipe>();
        onCreate(values);
      }}
      style={{ width: 500 }}
      okProps={{
        children: "立即创建",
        // @ts-ignore
        "data-test-id": "creation-dialog-confirm-button",
      }}
    >
      <Form {...formItemLayout} style={{ marginTop: 20 }} field={field}>
        <Form.Item label="活动类型" required>
          <Select
            data-test-id="creation-dialog-template-type"
            style={{ width: 300 }}
            {...field.init("investmentActivityType", {
              rules: [{ required: true, message: "请选择活动类型" }],
            })}
            placeholder="请选择"
          >
            <Select.Option value={0}>单活动</Select.Option>
            <Select.Option value={1}>多活动(主子活动)</Select.Option>
          </Select>
        </Form.Item>
        <Form.Item label="招商类型" required>
          <Select
            data-test-id="creation-dialog-investment-type"
            style={{ width: 300 }}
            {...field.init("investmentType", {
              rules: [{ required: true, message: "请选择招商类型" }],
            })}
            placeholder="请选择"
          >
            {investmentTypeOptions.map((t) => {
              return (
                <Select.Option value={t.value} key={t.value}>
                  {t.label}
                </Select.Option>
              );
            })}
          </Select>
        </Form.Item>
        <Form.Item
          label="补贴类型"
          required={field.getValue("investmentActivityType") === 0}
          style={{
            display:
              field.getValue("investmentActivityType") === 0 ? "flex" : "none",
          }}
        >
          <Select
            data-test-id="creation-dialog-subsidy-type"
            style={{ width: 300 }}
            {...field.init("subsidyType", {
              rules: [
                {
                  required: field.getValue("investmentActivityType") === 0,
                  message: "请选择补贴类型",
                },
              ],
            })}
            placeholder="请选择"
          >
            {subsidyTypeOptions.map((t) => {
              return (
                <Select.Option value={t.value} key={t.value}>
                  {t.label}
                </Select.Option>
              );
            })}
          </Select>
        </Form.Item>
        <Form.Item label="招商模板" required>
          <Select
            style={{ width: 300 }}
            data-test-id="ceation-dialog-investment-schema"
            {...field.init("investmentSchema", {
              rules: [{ required: true, message: "请选择招商模板" }],
            })}
            placeholder="请选择"
          >
            {investmentSchemaOptions.map((t) => {
              return (
                <Select.Option value={t.value} key={t.value}>
                  {t.label}
                </Select.Option>
              );
            })}
          </Select>
        </Form.Item>
        <Form.Item
          label="玩法类型"
          required={field.getValue("investmentActivityType") === 0}
          style={{
            display:
              field.getValue("investmentActivityType") === 0 ? "flex" : "none",
          }}
        >
          <Select
            data-test-id="creation-dialog-activity-type"
            style={{ width: 300 }}
            {...field.init("activityType", {
              rules: [
                {
                  validator(_: any, value: any, _callback: any) {
                    let callback = _callback
                    if (!callback) {
                      callback = (msg: string) => {
                        if (msg) {
                          throw new Error(msg);
                        }
                      };
                    }
                    const investmentActivityType = field.getValue(
                      "investmentActivityType"
                    );
                    if (investmentActivityType === 0 && !value) {
                      callback("请选择玩法类型");
                    } else {
                      callback();
                    }
                  },
                },
              ],
            })}
            placeholder="请选择"
          >
            {activityTypeOptions.map(
              (t: {
                value: React.Key | null | undefined;
                label:
                  | boolean
                  | React.ReactChild
                  | React.ReactFragment
                  | React.ReactPortal
                  | null
                  | undefined;
              }) => {
                return (
                  <Select.Option value={t.value} key={t.value}>
                    {t.label}
                  </Select.Option>
                );
              }
            )}
          </Select>
        </Form.Item>
        <Form.Item
          label="招商方式"
          required={true}
          style={{
            display: activitySignTypeOptions?.length >= 2 ? "flex" : "none",
          }}
        >
          <Select
            data-test-id="creation-dialog-activity-sign-type"
            style={{ width: 300 }}
            {...field.init("activitySignType", {
              rules: [
                {
                  validator(r: any, value: any, _callback: any) {
                    let callback = _callback
                    if (!callback) {
                      callback = (msg: string) => {
                        if (msg) {
                          throw new Error(msg);
                        }
                      };
                    }
                    const investmentActivityType = field.getValue(
                      "investmentActivityType"
                    );
                    if (
                      investmentActivityType === 0 &&
                      activitySignTypeOptions?.length >= 2 &&
                      !value
                    ) {
                      callback("请选择招商方式");
                    } else {
                      callback();
                    }
                  },
                },
              ],
            })}
            placeholder="请选择"
          >
            {activitySignTypeOptions?.map((t) => {
              return (
                <Select.Option value={t.value} key={t.value}>
                  {t.label}
                </Select.Option>
              );
            })}
          </Select>
        </Form.Item>

        {/**
         * 针对爆好价活动，需要在进入创建活动页面之前，前置选择好优品计划。
         * 暂时简单粗暴的把优品计划选择放在这里，后面有其他类似场景的时候再看下如何收口管理
         */}
        {isQualityProductSelectVisible ? (
          <Form.Item label="优品计划" asterisk>
            <QualityProductPlanSelect
              style={{ width: 300 }}
              placeholder="请搜索优品计划"
              {...field.init("qualityProductPlanId", {
                rules: { required: true, message: "请选择优品计划" },
              })}
            />
          </Form.Item>
        ) : null}
      </Form>
    </Dialog>
  );
};

export default CreationDialog;
