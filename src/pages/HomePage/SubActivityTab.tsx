import * as React from 'react';
import { useState, useEffect } from 'react';
import { useSetState } from 'ahooks';
import { Tab, Button, Select, Input, Message, Pagination } from '@alifd/next';

import { ActivityTypeEnum, ActivityTypeEnumStr } from '../../constants';
import * as api from '../../api'
import { useQuery } from '../../common';
import { FilterItem, FilterRow } from './Filter'
import SubActivityTable from '../../components/SubActivityTable';
import SettingRecommendation from "./SettingRecommendation";

const Filter: React.FC<any> = ({ value, onChange, onSearch, onReset }) => {
  return (
    <>
      <FilterRow>
        <FilterItem label="招商活动ID">
          <Input placeholder="请输入招商活动ID"
            value={value?.activityId}
            onChange={activityId => {
              let id = activityId.replace(/\s+/g,"")
              return onChange({ ...value, activityId: id })
            }}
          />
        </FilterItem>
        <FilterItem label="活动名称">
          <Input placeholder="请输入招商活动名称"
            value={value?.name}
            onChange={name => onChange({ ...value, name })}
          />
        </FilterItem>
        <FilterItem label="活动状态">
          <Select placeholder="请选择"
            hasClear
            value={value?.status}
            onChange={status => onChange({ ...value, status })}>
            <Select.Option value={1}>未开始</Select.Option>
            <Select.Option value={2}>活动中</Select.Option>
            <Select.Option value={4}>活动取消</Select.Option>
            <Select.Option value={5}>活动结束</Select.Option>
            <Select.Option value={7}>创建失败</Select.Option>
            <Select.Option value={8}>预算熔断</Select.Option>
          </Select>
        </FilterItem>
        <FilterItem label="招商类型">
          <Select placeholder="请选择"
            hasClear
            value={value?.investmentType}
            onChange={investmentType => onChange({ ...value, investmentType })}>
            <Select.Option value={1}>栏目招商</Select.Option>
            <Select.Option value={2}>日常招商</Select.Option>
            <Select.Option value={3}>大促招商</Select.Option>
            <Select.Option value={4}>算法招商</Select.Option>
            <Select.Option value={5}>频道招商</Select.Option>
            <Select.Option value={7}>营销IP招商</Select.Option>
          </Select>
        </FilterItem>
      </FilterRow>
      <FilterRow>
        <FilterItem label="玩法类型">
          <Select placeholder="请选择"
            hasClear
            value={value?.activityType}
            onChange={activityType => onChange({ ...value, activityType })} >
            {Object.values(ActivityTypeEnum)
              .filter(v => typeof v === 'number' && v !== ActivityTypeEnum.MKT_SALE_BASE) // 排除老商品折扣活动
              .map((v: any) => {
                return (
                  <Select.Option key={v} value={v}>
                    {ActivityTypeEnumStr[v]}
                  </Select.Option>
                )
              })}
          </Select>
        </FilterItem>
        <FilterItem label="创建人">
          <Input
            placeholder="请输入创建人"
            hasClear
            value={value?.createdUserName}
            onChange={createdUserName => onChange({ ...value, createdUserName })}
          />
        </FilterItem>
        <FilterItem label="活动推荐度">
          <Select placeholder="请选择"
            hasClear
            value={value?.recommendation}
            onChange={recommendation => onChange({ ...value, recommendation })}>
            <Select.Option value={0}>无星级</Select.Option>
            <Select.Option value={1}>一星</Select.Option>
            <Select.Option value={2}>二星</Select.Option>
          </Select>
        </FilterItem>
        <FilterItem label="">
          <Button type="primary" onClick={onSearch}>查询</Button>
          &nbsp;&nbsp;&nbsp;
          <Button onClick={onReset}>清空</Button>
        </FilterItem>
        {/* TODO: 阿尔法 - 字段值修改 */}
        {/* <FilterItem label="活动场景ID">
          <Input placeholder="请输入活动场景ID"
            value={value?.activityId}
            onChange={activityId => onChange({ ...value, activityId })}
          />
        </FilterItem>
        <FilterItem label="活动场景名称">
          <Input placeholder="请输入活动场景名称"
            value={value?.name}
            onChange={name => onChange({ ...value, name })}
          />
        </FilterItem> */}
      </FilterRow>
    </>
  )
}

const defaultPagination = {
  page: 1,
  pageSize: 15
}


interface ActivityQuery {
  templateId?: string; // 主活动 ID
  name?: string; // 活动名称
  status?: string; // 活动状态
  investmentType?: string; // 招商类型
  activityType?: string; // 玩法类型
  createdUserName?: string; // 创建人
}


const SubActivityTab: React.FC<any> = ({ onCopyActivity }) => {
  const query = useQuery<ActivityQuery>({})
  const [dataSource, setDataSource] = useState([])
  const [loading, setLoading] = useState(false)
  const [total, setTotal] = useState(0)
  const [tab, setTab] = useState('accessiable')
  const [version, setVersion] = useState(0)
  const [pagination, updatePagination] = useSetState(defaultPagination)

  useEffect(() => {
    setLoading(true)
    const finalQuery: any = { ...query.committed }
    Object.keys(finalQuery).forEach((k: string) => {
      const value = finalQuery[k]
      if (value === '') {
        delete finalQuery[k]
      }
    })
    api.subActivity.getList({
      ...finalQuery,
      ...pagination,
      isSelf: tab === 'owner' ? 1 : 0
    }).then(res => {
      if (!res.success) {
        Message.error(res.errorMessage)
        return
      }
      const { total: _total, data } = res
      setTotal(_total)
      setDataSource(data as any)
    }).finally(() => {
      setLoading(false)
    })
  }, [query.committed, pagination, version, tab])

  return (
    <>
      <Filter
        value={query.value}
        onChange={(v: any) => {
          query.update(v)
        }}
        onReset={() => {
          updatePagination(defaultPagination)
          query.resetAndCommit()
        }}
        onSearch={() => {
          updatePagination(defaultPagination)
          query.commit()
        }}
      />

      <div className="zs-sub-activity-tab-block">
        <Tab
          shape="wrapped"
          activeKey={tab}
          onChange={(_tab: any) => {
            updatePagination(defaultPagination)
            setTab(_tab)
          }}>
          <Tab.Item title="可查看的" key="accessiable" />
          <Tab.Item title="我负责的" key="owner" />
        </Tab>

        {/* 活动推荐度 */}
        <SettingRecommendation />
      </div>

      <SubActivityTable
        loading={loading}
        onRefresh={() => setVersion(v => v + 1)}
        dataSource={dataSource}
        onCopyActivity={onCopyActivity}
      />
      <Pagination
        total={total}
        style={{ marginTop: 20, textAlign: 'right' }}
        current={pagination.page}
        pageSize={pagination.pageSize}
        onChange={page => updatePagination({ page })}
      />

    </>
  );
}

export default SubActivityTab
