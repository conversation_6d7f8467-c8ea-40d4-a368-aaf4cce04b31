import * as React from 'react';
import { useState, useEffect, useCallback } from 'react';
import { useDebounceEffect } from 'ahooks';
import { Select, Button, Message } from '@alifd/next';
import * as api from '../../api';

interface AuditMember {
  class?: string;
  cname: string;
  empId: string;
  lastName: string;
}

enum STATUS_ENUM {
  CANCELED = 4,
  FINISHED = 5,
}

interface Props {
  activityId: string;
  auditMemberList?: AuditMember[] | undefined;
  onSuccess: () => void;
  creatorId: string;
  status: number;
  limit?: number;
}

const AuditMemberSelector: React.FC<Props> = ({
  auditMemberList,
  activityId,
  onSuccess,
  creatorId,
  status,
  limit = 3
}) => {
  const [isCreator, setIsCreator] = useState(false); //
  const [showAuditSelect, setShowAuditSelect] = useState(false); // 编辑还是展示
  const [searchValue, setSearchValue] = useState(''); // 配合 useDebounceEffect 使用
  const [dataSource, setDataSource] = useState<AuditMember[]>([]); // 负责下拉框数据
  const [empIdList, setEmpIdList] = useState<string[]>([]); // 负责展示
  const [reqParams, setReqParams] = useState<AuditMember[]>(
    auditMemberList || []
  ); // 提交数据

  useEffect(() => {
    // 非创建人不可编辑
    api.getUserInfo().then((res) => {
      setIsCreator(
        res.data?.workid?.toLowerCase() === creatorId?.toLowerCase()
      );
    });
  }, []);

  // 设置表单数据
  useEffect(() => {
    setEmpIdList(
      auditMemberList
        ? auditMemberList
            .map((auditMember: AuditMember) => auditMember?.empId)
            .filter(Boolean)
        : []
    );
  }, [auditMemberList]);

  // 接口防抖
  useDebounceEffect(() => {
    if (searchValue) {
      api.getUserInfoBySearch(searchValue).then((res) => {
        if (Array.isArray(res)) {
          setDataSource(res);
        }
      });
    }
  }, [searchValue]);

  // 搜索框选择回调
  const handleAuditMemberChange = useCallback(
    async (v: string[]) => {
      if (v.length > limit) {
        return Message.error(`最多可填写${limit}个人`);
      }

      const oldParams: AuditMember[] = reqParams || [];
      let newParams = [];

      // 活动创建完，支持修改，仅支持增加不支持修改
      if (auditMemberList && auditMemberList.length > 0) {
        if (v?.length < auditMemberList?.length) {
          return;
        }
      }

      // 当选择编辑人自己时，时需要校验
      if (v.length > 0) {
        const empId = v.find(
          (_empId) => _empId?.toLowerCase() === creatorId?.toLowerCase()
        );
        if (empId) {
          return Message.error('请勿输入编辑人本人信息');
        }
      }

      newParams = v.map((empId) => {
        const item = dataSource.find(
          (_item) => _item.empId?.toLowerCase() === empId?.toLowerCase()
        );
        if (item) {
          return item;
        }
        return oldParams.find((member) => member?.empId === empId);
      });

      // 提交用
      setReqParams(newParams as AuditMember[]);
      // 展示用
      setEmpIdList(v);
    },
    [reqParams, auditMemberList, creatorId, dataSource]
  );

  // state 恢复默认值
  const setToDefaultState = () => {
    setDataSource([]);
    setEmpIdList(
      auditMemberList && auditMemberList.length > 0
        ? auditMemberList.map((auditMember: AuditMember) => auditMember?.empId)
        : []
    );
    setReqParams(auditMemberList || []);
    setShowAuditSelect(false);
  };

  // submit 按钮
  const onUpdateAuditMember = useCallback(() => {
    const params = reqParams.map((i) => {
      if (i.class) delete i.class;
      return i;
    });
    api.updateInvestmentActivity(params, activityId).then((res) => {
      if (res?.success) {
        onSuccess();
        setToDefaultState();
      } else {
        Message.error(res?.errorMessage || '提交失败');
      }
    });
  }, [reqParams, activityId]);

  let renderContent = (
    <Button
      type="secondary"
      onClick={() => setShowAuditSelect(true)}
      disabled={
        !isCreator ||
        [STATUS_ENUM.CANCELED, STATUS_ENUM.FINISHED].includes(status)
      }
    >
      <span>授权审核人：</span>
      {auditMemberList?.map((member: AuditMember, index: number) => {
        return (
          <span key={member.empId}>{`${member.lastName}（${member.empId}）${
            index === auditMemberList.length - 1 ? '' : '，'
          }`}</span>
        );
      })}
    </Button>
  );

  if (!auditMemberList || auditMemberList?.length === 0) {
    renderContent = (
      <Button
        onClick={() => setShowAuditSelect(true)}
        type="secondary"
        disabled={
          !isCreator ||
          [STATUS_ENUM.CANCELED, STATUS_ENUM.FINISHED]?.includes(status)
        }
      >
        审核授权人
      </Button>
    );

    // 非创建人，且这个活动没有授权人时，不展示“审核授权人”按钮
    // className 用于占位，保持样式统一
    if (!isCreator) {
      renderContent = <div className="zs-sub-activity-place-like-main" />;
    }
  }

  return (
    <div className="zs-sub-activity-audit-member-list">
      {!showAuditSelect ? (
        renderContent
      ) : (
        <div>
          <Select
            mode="multiple"
            showSearch
            placeholder={`最多可填写${limit}个人的工号`}
            value={empIdList}
            // @ts-ignore
            tagClosable={false}
            onChange={handleAuditMemberChange}
            onSearch={(v) => setSearchValue(v)}
            dataSource={dataSource.map((b: any) => {
              return {
                value: b.empId,
                label: `${b.empId}(${b.lastName})`,
              };
            })}
            style={{ minWidth: 300, marginRight: 8 }}
          />

          <Button
            text
            type="primary"
            onClick={onUpdateAuditMember}
            disabled={
              !isCreator ||
              [STATUS_ENUM.CANCELED, STATUS_ENUM.FINISHED]?.includes(status)
            }
          >
            提交
          </Button>
          <Button
            text
            type="normal"
            style={{ marginLeft: 8 }}
            onClick={setToDefaultState}
          >
            取消
          </Button>
        </div>
      )}

      <div className="zs-main-activity-AuditMember-selector"></div>
    </div>
  );
};

export default AuditMemberSelector;
