import "./style.scss";

import * as React from "react";
import { useEffect, useState, useMemo, useRef } from "react";
import { useParams } from "react-router";
import { Breadcrumb, Dialog } from "@alifd/next";
import { MonitorFusion } from "@ali/monitor-fusion";

import * as api from "../../api";
import {
  confirmDialog,
  isValueSet,
  resolveEnv,
  useRouteQuery,
} from "../../common";
import { getStrategy } from "../../strategy";
import { ActivityStrategy } from "../../models";

import { handleNoPermissionForActivity } from "../common";
import initCable from "../initCable";
import AuditMemberSelector from "./AuditMemberSelector";

const debug = require('debug')('zs:SubActivity')
// alpha 和 invite路由下，如果 main/v2/#/act/activity 页面会404
let hasMainV2 = true;
if (/alpha/.test(window.location.host) 
  || /invite/.test(window.location.host)) {
  hasMainV2 = false;
}

const SubActivityDetail: React.FC<any> = ({ CableBuilder, ActivityForm }) => {
  const { id } = useParams<any>();
  const query = useRouteQuery()
  const [detail, setDetail] = useState<any>();
  const [ready, setReady] = useState(false);
  const CableRef = useRef<React.ComponentType<any>>();
  const [version, setVersion] = useState(0);
  const [detailVersion, setDetailVersion] = useState(0);

  const isAlpha = useMemo(() => {
    const currentDomain = window.location.hostname;
    return currentDomain.includes("alpha.kunlun.alibaba-inc.com");
  }, [])
  useEffect(() => {
    api.subActivity.getDetail(id).then(res => {
      setDetail(res.data);
      setDetailVersion(dv => dv + 1)
    })
  }, [id, version]);

  const strategy = useMemo<ActivityStrategy | null>(() => {
    debug('activityType', detail?.investmentActivityDetailDTO?.activityType)
    return detail ? getStrategy(detail?.investmentActivityDetailDTO?.activityType) : null
  }, [detail]);

  
  useEffect(() => {
    // 创建 style 标签
    const styleTag = document.createElement('style');
    if(strategy?.addCustomStyle) {
      strategy.addCustomStyle(styleTag, "detail")
    }

    // 将 style 标签添加到 head 中
    document.head.appendChild(styleTag);

    // 定义清理函数以移除 style 标签
    return () => {
      if(styleTag) {
        styleTag.remove();
      }
    };
    
  }, [strategy]);

  useEffect(() => {
    const cable =
    typeof strategy?.cable === "function"
      ? strategy.cable({
          investmentSchema: Number(detail?.investmentActivityDetailDTO?.investmentSchema),
          investmentType: Number(detail?.investmentActivityDetailDTO?.investmentType),
          activitySignType: detail?.investmentActivityDetailDTO?.activitySignType,
          subsidyInvestmentModel: detail?.investmentActivityDetailDTO?.attributes?.subsidyInvestmentModel, // 只有爆单红包会使用
        })
      : strategy?.cable;
    if (!strategy) {
      return
    }
    if (CableRef.current) {
      return
    }
    CableBuilder.createWithEnv(resolveEnv()) // 环境 "daily" | "pre" | "prod"
      .buildAsync(
        cable?.workspace,
        cable?.model
      )
      .then((result: any) => {
        const { Cable } = result;
        CableRef.current = Cable;
        setReady(true)
      });
  }, [strategy]);


  const hasAgentSubsidy = useMemo(() => {
    if (!strategy) {
      // 有活动详情的时候，返回值才有意义
      return false;
    }

    let playdata: any
    try {
      playdata = JSON.parse(detail.viewJson)
      return strategy.hasAgentSubsidy!(playdata)
    } catch (e) {
      return false
    }
  }, [strategy, detail]);


  if (!ready) {
    return null
  }

  const Cable = CableRef.current as React.ComponentType<any>;
  console.log('detail', detail, '#zs-microapp-root', document.querySelector('#zs-microapp-root'))

  return (
    <div className="zs-sub-activity-page">
      {query?.activityPlanId && isAlpha ? (
        <Breadcrumb separator="/">
          <Breadcrumb.Item link="#/v2/plan/">活动计划列表</Breadcrumb.Item>
          <Breadcrumb.Item link={`#/v2/plan/${query?.activityPlanId}`}>
            活动计划详情页
          </Breadcrumb.Item>
          <Breadcrumb.Item link={`#/v2/plan/scene/${query?.activitySceneId}`}>
            活动场景详情页
          </Breadcrumb.Item>
          <Breadcrumb.Item>
            {detail?.investmentActivityDetailDTO?.name}
          </Breadcrumb.Item>
        </Breadcrumb>
      ) : (
        <Breadcrumb separator="/">
          <Breadcrumb.Item
            link={
              query.from === "explosive-coupon-act-list"
                ? "/#/explosive-coupon"
                : hasMainV2
                ? "/main/v2/#/act/activity"
                : "/#/act/activity"
            }
          >
            招商活动
          </Breadcrumb.Item>
          <Breadcrumb.Item>
            {detail?.investmentActivityDetailDTO?.name}
          </Breadcrumb.Item>
        </Breadcrumb>
      )}

      <div className="zs-sub-activity-page-content">
        <div
          className={`zs-sub-activity-header ${
            strategy?.hasAuditMemberList && strategy?.hasAuditMemberList()
              ? ""
              : "mb40"
          }`}
        >
          <div className="zs-sub-activity-header-title">
            {detail?.investmentActivityDetailDTO?.name}
            <div className="zs-subtitle">
              ID: {detail?.investmentActivityDetailDTO?.activityId}
            </div>
          </div>
          {/*
          <div className="zs-sub-activity-header-buttons">
            <Button type="normal">下线</Button>
          </div>
          */}
        </div>

        {/* 授权审核人 */}
        {strategy?.hasAuditMemberList && strategy?.hasAuditMemberList() && (
          <AuditMemberSelector
            key={detailVersion}
            activityId={detail?.investmentActivityDetailDTO?.activityId}
            auditMemberList={
              detail?.investmentActivityDetailDTO?.auditMemberList
            }
            creatorId={detail?.investmentActivityDetailDTO?.createdUserId}
            onSuccess={() => setVersion(version + 1)}
            status={detail?.investmentActivityDetailDTO?.status}
            limit={
              isValueSet(strategy?.auditMemberLimit)
                ? strategy.auditMemberLimit
                : undefined
            }
          />
        )}

        <div>
          <Cable
            env={{
              view: "detail",
              hasAgentSubsidy,
              signupShopType: `${detail.investmentActivityDetailDTO.signUpShopType}`, // 通过 signupShopType 控制是否展示代理商确认进度 tab
              investmentType:
                detail?.investmentActivityDetailDTO?.investmentType, // 通过 investmentType 控制详情中玩法信息是否通过viewJson解析
              investmentSchema:
                detail?.investmentActivityDetailDTO?.investmentSchema, // 通过 investmentSchema 来控制同一个活动类型不同模版的展示
              viewJson: detail?.viewJson,
              creatorId: detail?.investmentActivityDetailDTO?.createdUserId,
              isSupportMaterial: detail?.investmentActivityDetailDTO
                ?.creativeMaterialRules
                ? true
                : false,
              subsidyInvestmentModel:
                detail?.investmentActivityDetailDTO?.attributes
                  ?.subsidyInvestmentModel, // 通过 subsidyInvestmentModel 来控制爆单红包A和Y的展示
            }}
            channel="kunlun"
            activity={{
              activityId: id,
              templateId: detail.investmentActivityDetailDTO.activityType,
              inviteTaskId: detail.investmentActivityDetailDTO.inviteTaskId,
              status: detail?.investmentActivityDetailDTO?.status,
              isPending: detail?.isPending,
            }}
            defaultTab={query.tab}
            onEvent={async (event: any) => {
              if (event.type === "operation-forbidden") {
                if (event.armsType) {
                  MonitorFusion &&
                    MonitorFusion.logCustomEvent({
                      code: event.armsType,
                      message: "很抱歉该时间段内不支持操作",
                      c1: event.c1,
                      c2: event.c2,
                      c3: event.c3 || "",
                    });
                }
                Dialog.show({
                  style: { width: 320 },
                  title: "提示",
                  content: "很抱歉该时间段内不支持操作",
                });
              } else if (event.type === "permission-error") {
                if (event.armsType) {
                  MonitorFusion &&
                    MonitorFusion.logCustomEvent({
                      code: event.armsType,
                      message: "您暂无该权限，是否跳转申请权限页面",
                      c1: event.c1,
                      c2: event.c2,
                      c3: event.c3 || "",
                    });
                }
                // 用户无权限
                const ok = await confirmDialog({
                  title: "提示",
                  content: "您暂无该权限，是否跳转申请权限页面",
                });
                if (ok) {
                  await handleNoPermissionForActivity(
                    detail?.investmentActivityDetailDTO?.activityId,
                    { isNewActivity: true }
                  );
                }
              } else if (
                event.type === "stores_offline_err" ||
                event.type === "shop_audit_err"
              ) {
                MonitorFusion &&
                  MonitorFusion.logCustomEvent({
                    code: event.type,
                    message: event.msg,
                    c1: event.c1,
                    c2: event.c2,
                    c3: event.c3,
                  });
              } else if (event.type === "reload") {
                window.location.reload();
              }
            }}
            options={{
              views: {
                lookup: {
                  boreasEnv: {
                    investmentTemplate:
                      detail?.investmentActivityDetailDTO.investmentSchema,
                    env: resolveEnv(),
                    detailContext: {
                      activityType:
                        detail?.investmentActivityDetailDTO.activityType,
                      activityId:
                        detail?.investmentActivityDetailDTO.activityId,
                      creatorId:
                        detail?.investmentActivityDetailDTO?.createdUserId,
                    },
                  },
                  ActivityForm: ActivityForm,
                  marketPlayMapper: strategy?.marketPlayMapper
                    ? (viewJson: any, baseInfo: any) => {
                        const _strategy = getStrategy(
                          detail?.investmentActivityDetailDTO?.activityType
                        );
                        return (
                          _strategy?.marketPlayMapper &&
                          _strategy?.marketPlayMapper(viewJson, baseInfo, {
                            investmentSchema:
                              detail?.investmentActivityDetailDTO
                                ?.investmentSchema,
                          })
                        );
                      }
                    : null,
                },
                examine: {
                  scrollElement:
                    /(invite|alpha)\.kunlun\.alibaba-inc\.com/.test(
                      window.location.hostname
                    )
                      ? document.querySelector(".ss-right-content")
                      : document.querySelector("#zs-microapp-root") || window,
                  // 报名管理扩展项
                  renderGroupItemExtView: (
                    record: any,
                    activityId: number,
                    shopStatus: string,
                    activityStatus: number
                  ) => {
                    const _strategy = getStrategy(
                      detail?.investmentActivityDetailDTO?.activityType
                    );
                    return (
                      _strategy?.renderGroupItemExtView &&
                      _strategy?.renderGroupItemExtView(
                        record,
                        activityId,
                        shopStatus,
                        activityStatus
                      )
                    );
                  },
                },
                offline: {
                  scrollElement: /invite.kunlun.alibaba-inc.com/.test(
                    window.location.hostname
                  )
                    ? document.querySelector(".ss-right-content")
                    : document.querySelector("#zs-microapp-root") || window,
                  renderGroupItemExtView: (params: {
                    record: any;
                    activityId: number;
                    activityStatus: number;
                  }) => {
                    // const strategy = getStrategy(detail?.investmentActivityDetailDTO?.activityType);
                    return (
                      strategy?.renderOfflineGroupItemExtView &&
                      strategy?.renderOfflineGroupItemExtView(params)
                    );
                  },
                },
                itemManagement: {
                  scrollElement: /invite.kunlun.alibaba-inc.com/.test(
                    window.location.hostname
                  )
                    ? document.querySelector(".ss-right-content")
                    : document.querySelector("#zs-microapp-root") || window,
                },
              },
            }}
          />
        </div>
      </div>
    </div>
  );
};

export default initCable(SubActivityDetail)
