/**
 * 阿尔法创建活动页面
 */
import "./index.scss";

import * as React from "react";
import { useEffect, useMemo } from "react";
import { useHistory } from "react-router";
import { Select, Field, Form, Button } from "@alifd/next";
import * as qs from "querystring";

import { InviteActivityTypeEnum, InvestmentSchemaEnum } from "../../constants";
import { useRouteQuery } from "../../common";
import { sendAesTracker } from "../../utils";
import * as Api from "../../api";
import AlphaSceneInfo from "components/AlphaSceneInfo";
import { QualityProductPlanSelect } from "components/QualityProductPlanSelect";

const formItemLayout = {
  labelCol: {
    fixedSpan: 4,
  },
  wrapperCol: {
    span: 20,
  },
};

function distinct(arr: number[]) {
  return Array.from(new Set(arr));
}

interface Props {}

const CreateActivityForAlpha: React.FC<Props> = () => {
  const field = Field.useField({
    onChange: (name: string) => {
      if (name === "investmentActivityType") {
        field.setValues({
          investmentType: undefined,
          investmentSchema: undefined,
          activityType: undefined,
          activitySignType: undefined,
          subsidyType: undefined,
        });
      } else if (name === "investmentType") {
        field.setValues({
          investmentSchema: undefined,
          activityType: undefined,
          activitySignType: undefined,
          subsidyType: undefined,
        });
      } else if (name === "subsidyType") {
        field.setValues({
          investmentSchema: undefined,
          activityType: undefined,
          activitySignType: undefined,
        });
      } else if (name === "investmentSchema") {
        field.setValues({
          activityType: undefined,
          activitySignType: undefined,
          qualityProductPlanId: undefined,
        });
      } else if (name === "activityType") {
        field.setValues({
          activitySignType: undefined,
        });
      }
    },
  });
  const history = useHistory();
  const query = useRouteQuery();

  const [sceneInfo, setSceneInfo] = React.useState<any>();

  useEffect(() => {
    field.setValue(
      "investmentActivityType",
      InviteActivityTypeEnum.SINGLE_ACTIVITY
    );
  }, []);

  useEffect(() => {
    if (!query.sceneId) {
      return;
    }
    Api.alpha
      .getSceneBySceneId(query?.sceneId as string)
      .then((res) => {
        const data = { ...res.data };
        if (res?.data?.activityTypes) {
          try {
            data.activityTypes = JSON.parse(res?.data?.activityTypes);
          } catch (e) {
            data.activityTypes = [];
          }

          try {
            data.commodityCategoryInfo = JSON.parse(
              res?.data?.commodityCategoryInfo
            )?.value;
          } catch (e) {
            data.commodityCategoryInfo = [];
          }
        }
        setSceneInfo(data);
      })
      .catch((e) => {
        console.log("e=======", e);
      });
  }, [query]);

  const onCancel = () => {
    const _sceneInfo: any = sceneInfo;
    const eventParams = {
      et: "CLK",
      c1: _sceneInfo?.planId,
      c2: _sceneInfo?.planName,
      c3: _sceneInfo?.sceneId,
      c4: _sceneInfo?.name,
      c5: `create-sub-activity-for-alpha?sceneId=${query.sceneId}`,
    };

    // 发送AES埋点
    sendAesTracker("cancel_create_activity_for_alpha", eventParams);

    history.go(-1);
  };

  const onCreate = async () => {
    const eventParams = {
      et: "CLK",
      c1: sceneInfo?.planId,
      c2: sceneInfo?.planName,
      c3: sceneInfo?.sceneId,
      c4: sceneInfo?.name,
      c5: `create-sub-activity-for-alpha?sceneId=${query.sceneId}`,
    };

    // 发送AES埋点
    sendAesTracker("go_create_activity_for_alpha", eventParams);

    const ret = await field.validatePromise();

    if (ret.errors) {
      return;
    }

    const values: any = field.getValues() || {};
    // 跳转到对应的创建活动页面
    const params = {
      ...values,
      activitySceneId: query.sceneId,
      activityPlanId: sceneInfo?.planId,
    };

    history.replace(`/create-sub-activity?${qs.stringify({ ...params })}`);
  };
  const investmentTypeOptions = useMemo(() => {
    const investmentActivityType = field.getValue("investmentActivityType");
    if (investmentActivityType !== 0 && investmentActivityType !== 1) {
      return [];
    }
    let investmentTypes = (sceneInfo?.investmentMetadata?.sceneList || [])
      .filter((r: any) => r.investmentActivityType === investmentActivityType)
      .map((r: any) => r.investmentType);
    investmentTypes = distinct(investmentTypes);
    return investmentTypes.map((value: number) => {
      return {
        value,
        label: sceneInfo?.investmentMetadata?.investmentTypeMap[value],
      };
    });
  }, [field.getValue("investmentActivityType"), sceneInfo]);

  const subsidyTypeOptions = useMemo(() => {
    const { sceneList, subsidyTypeMap } = sceneInfo?.investmentMetadata || {};
    const investmentActivityType = field.getValue("investmentActivityType");
    const investmentType = field.getValue("investmentType");
    if (
      typeof investmentType !== "number" ||
      typeof investmentActivityType !== "number"
    ) {
      return [];
    }

    let subsidyTypes = (sceneList || [])
      .filter(
        (r: any) =>
          r.investmentActivityType === investmentActivityType &&
          r.investmentType === investmentType
      )
      .map((r: any) => r.subsidyType)
      .filter(Boolean);
    subsidyTypes = distinct(subsidyTypes);
    return subsidyTypes.map((value: string) => {
      return {
        value,
        label: subsidyTypeMap[value],
      };
    });
  }, [
    field.getValue("investmentType"),
    field.getValue("investmentActivityType"),
    sceneInfo,
  ]);

  const investmentSchemaOptions = useMemo(() => {
    const { sceneList, investmentSchemaMap } =
      sceneInfo?.investmentMetadata || {};
    const investmentActivityType = field.getValue("investmentActivityType");
    const investmentType = field.getValue("investmentType");
    const subsidyType = field.getValue("subsidyType");
    if (
      typeof investmentType !== "number" ||
      typeof investmentActivityType !== "number"
    ) {
      return [];
    }
    let investmentSchemaList = [];
    // 单活动
    if (typeof subsidyType !== "string") {
      return [];
    } else {
      investmentSchemaList = (sceneList || [])
        .filter(
          (r: any) =>
            r.investmentActivityType === investmentActivityType &&
            r.investmentType === investmentType &&
            r.subsidyType === subsidyType
        )
        .map((r: any) => r.investmentSchema);
    }

    investmentSchemaList = distinct(investmentSchemaList);
    return investmentSchemaList.map((value) => {
      return {
        value,
        label: investmentSchemaMap[value],
      };
    });
  }, [
    field.getValue("investmentType"),
    field.getValue("investmentActivityType"),
    field.getValue("subsidyType"),
    sceneInfo,
  ]);

  const activityTypeOptions = useMemo(() => {
    const { sceneList, activityTypeMap } = sceneInfo?.investmentMetadata || {};
    const investmentActivityType = field.getValue("investmentActivityType");
    const investmentType = field.getValue("investmentType");
    const subsidyType = field.getValue("subsidyType");
    const investmentSchema = field.getValue("investmentSchema");
    if (
      investmentActivityType !== 0 ||
      typeof investmentType !== "number" ||
      typeof subsidyType !== "string" ||
      typeof investmentSchema !== "number"
    ) {
      return [];
    }
    let activityTypes = (sceneList || [])
      .filter(
        (r: any) =>
          r.investmentActivityType === investmentActivityType &&
          r.investmentType === investmentType &&
          r.subsidyType === subsidyType &&
          r.investmentSchema === investmentSchema &&
          r.activityType
      )
      .map((r: any) => r.activityType as number);
    activityTypes = distinct(activityTypes);
    return activityTypes.map((value: string | number) => {
      return {
        value,
        label: activityTypeMap[value],
      };
    });
  }, [
    field.getValue("investmentType"),
    field.getValue("investmentSchema"),
    field.getValue("subsidyType"),
    field.getValue("investmentActivityType"),
    sceneInfo,
  ]);

  const isQualityProductSelectVisible =
    field.getValue("investmentActivityType") ===
      InviteActivityTypeEnum.SINGLE_ACTIVITY &&
    field.getValue("investmentSchema") === InvestmentSchemaEnum.SUPER_COMMODITY_GOOD_PRICE;

  return (
    <div className="zs-create-act-for-alpha-page">
      <AlphaSceneInfo
        sceneId={query.sceneId as string}
        planId={sceneInfo?.planId as string}
      />
      <div className="zs-block">
        <div className="zs-h4">选择招商活动类型</div>

        <Form {...formItemLayout} style={{ marginTop: 20 }} field={field}>
          <Form.Item label="活动类型" required>
            <Select
              disabled
              style={{ width: 300 }}
              {...field.init("investmentActivityType", {
                rules: [{ required: true, message: "请选择活动类型" }],
              })}
              placeholder="请选择"
            >
              <Select.Option value={0}>单活动</Select.Option>
              <Select.Option value={1}>多活动(主子活动)</Select.Option>
            </Select>
          </Form.Item>
          <Form.Item label="招商类型" required>
            <Select
              style={{ width: 300 }}
              {...field.init("investmentType", {
                rules: [{ required: true, message: "请选择招商类型" }],
              })}
              placeholder="请选择"
            >
              {investmentTypeOptions.map((t: any) => {
                return (
                  <Select.Option value={t.value} key={t.value}>
                    {t.label}
                  </Select.Option>
                );
              })}
            </Select>
          </Form.Item>
          <Form.Item
            label="补贴类型"
            required={field.getValue("investmentActivityType") === 0}
          >
            <Select
              style={{ width: 300 }}
              {...field.init("subsidyType", {
                rules: [
                  {
                    required: field.getValue("investmentActivityType") === 0,
                    message: "请选择补贴类型",
                  },
                ],
              })}
              placeholder="请选择"
            >
              {subsidyTypeOptions.map((t: any) => {
                return (
                  <Select.Option value={t.value} key={t.value}>
                    {t.label}
                  </Select.Option>
                );
              })}
            </Select>
          </Form.Item>
          <Form.Item label="招商模板" required>
            <Select
              style={{ width: 300 }}
              {...field.init("investmentSchema", {
                rules: [{ required: true, message: "请选择招商模板" }],
              })}
              placeholder="请选择"
            >
              {investmentSchemaOptions.map((t) => {
                return (
                  <Select.Option value={t.value} key={t.value}>
                    {t.label}
                  </Select.Option>
                );
              })}
            </Select>
          </Form.Item>
          <Form.Item
            label="玩法类型"
            required={field.getValue("investmentActivityType") === 0}
            style={{
              display:
                field.getValue("investmentActivityType") === 0
                  ? "flex"
                  : "none",
            }}
          >
            <Select
              style={{ width: 300 }}
              {...field.init("activityType", {
                rules: [
                  {
                    validator(r: any, value: any, _callback: any) {
                      let callback = _callback;
                      if (!callback) {
                        callback = (msg: string) => {
                          if (msg) {
                            throw new Error(msg);
                          }
                        };
                      }
                      const investmentActivityType = field.getValue(
                        "investmentActivityType"
                      );
                      if (investmentActivityType === 0 && !value) {
                        callback("请选择玩法类型");
                      } else {
                        callback();
                      }
                    },
                  },
                ],
              })}
              placeholder="请选择"
            >
              {activityTypeOptions.map((t: any) => {
                return (
                  <Select.Option value={t.value} key={t.value}>
                    {t.label}
                  </Select.Option>
                );
              })}
            </Select>
          </Form.Item>
          {/**
           * 针对爆好价活动，需要在进入创建活动页面之前，前置选择好优品计划。
           * 同 CreationDialog，暂时简单粗暴的把优品计划选择放在这里，后面有其他类似场景的时候再看下如何收口管理
           */}
          {isQualityProductSelectVisible ? (
            <Form.Item label="优品计划" asterisk>
              <QualityProductPlanSelect
                style={{ width: 300 }}
                placeholder="请搜索优品计划"
                {...field.init("qualityProductPlanId", {
                  rules: { required: true, message: "请选择优品计划" },
                })}
              />
            </Form.Item>
          ) : null}

          <Form.Item className="zs-block-btns">
            <Button onClick={onCancel}>取消</Button>
            <Button
              style={{ marginLeft: 24 }}
              type="primary"
              onClick={onCreate}
            >
              立即创建
            </Button>
          </Form.Item>
        </Form>
      </div>
    </div>
  );
};

export default CreateActivityForAlpha;
