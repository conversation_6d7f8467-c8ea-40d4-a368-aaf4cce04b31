import "./style.scss";

import { v4 as uuid4 } from 'uuid';
import * as qs from 'querystring';
import React, { useEffect, useRef, useMemo, useState } from 'react';
import { useHistory, useLocation } from 'react-router';

import CreateSubActHeader from "./Header";
import { getStrategy } from '../../strategy';
import { resolveEnv } from '../../common';
import { ActivityStrategy, CableInst } from "../../models";
import { CreationContext, UserInfo, ZsCableProps } from "../../models";
import * as api from '../../api';
import { ActivitySignType, ActivityTypeEnumStr } from "../../constants";
import globalState from "../../globalState";
import initCable from "../initCable";
import { sendAesTracker } from "../../utils";
import AlphaSceneInfo from "../../components/AlphaSceneInfo";
import { MonitorFusion } from "@ali/monitor-fusion";

const debug = require('debug')('zs:create-sub-activity')

interface ContextFromQuery {
  copy?: string;
  activityId?: string;
  templateId?: string;
  investmentType?: string;
  investmentSchema?: string;
  activityType: string;
  activitySceneId?: number | string;
  activityPlanId?: number | string;
  activitySignType?: ActivitySignType;
  subsidyType: string;
  subsidyInvestmentModel?: string;

  // 优品投放计划 id，爆好价项目引入
  qualityProductPlanId?: number | string;
}

// @ts-ignore
async function resolveCreationContext(q: ContextFromQuery, strategy: ActivityStrategy): CreationContext {
  const startTime = Date.now();
  const createActivitySessionId = uuid4();

  let ret: CreationContext;
  if (q.templateId) {
    // 目前只有主子活动商品特价才会有复制场景，所以 copy 在这里可能为 true，如果不是主子活动，目前一定是 false
    // const copy = q.copy === 'true'
    const res = await api.investmentTemplate.getDetail(+q.templateId)

    let reviewRuleInfo :any;
    if (res?.data?.investmentTemplateDetailDTO?.reviewRuleId) {
      // 如果主活动有机审规则，需要获取机审规则详细信息，自动审核功能是否开启依赖机审详细信息
      try {
        const r = await api.getAuditRuleById(res?.data?.investmentTemplateDetailDTO?.reviewRuleId)
        reviewRuleInfo = r?.auditRulesInfoDto
      } catch(e) {
        console.error(e)
      }
    }
    
    ret = {
      copy: false,
      createActivitySessionId,
      createActiivtyStartTime: startTime,
      templateId: +(q.templateId as string),
      isSingleActivity: false,
      activityType: +q.activityType,
      activitySignType: q.activitySignType,
      investmentSchema: res.data.investmentTemplateDetailDTO.investmentSchema,
      investmentType: res.data.investmentTemplateDetailDTO.investmentType,
      fineGrainedActivityTimeDisabled: true,
      investmentTemplate: {
        beginTime: res.data.investmentTemplateDetailDTO.beginTime,
        endTime: res.data.investmentTemplateDetailDTO.endTime,
        minCycleTime: res.data.investmentTemplateDetailDTO.signUpMinCycleTime,
        maxCycleTime: res.data.investmentTemplateDetailDTO.signUpMaxCycleTime,
        activityTimeEditable: res.data.investmentTemplateDetailDTO.timeMode === 1,
        reviewRuleId: res.data.investmentTemplateDetailDTO?.reviewRuleId,
        reviewRuleName: res.data.investmentTemplateDetailDTO?.reviewRuleName,
        reviewRuleInfo,
        budget: res.data.investmentTemplateDetailDTO.budgetId ? {
          id: res.data.investmentTemplateDetailDTO.budgetId,
          name: res.data.investmentTemplateDetailDTO.budgetName as string
        } : undefined
      },
      activitySceneId: q?.activitySceneId,
      activityPlanId: q?.activityPlanId,
      subsidyType: q?.subsidyType, // 补贴场景
      subsidyInvestmentModel: q?.subsidyInvestmentModel, // 招商投资模式，目前只有 爆单红包主活动会使用
    }
  } else {
    ret = {
      copy: false,
      createActiivtyStartTime: startTime,
      createActivitySessionId,
      templateId: undefined,
      isSingleActivity: true,
      activityType: +q.activityType,
      activitySignType: q.activitySignType,
      investmentType: +(q.investmentType as string),
      investmentSchema: +(q.investmentSchema as string),
      fineGrainedActivityTimeDisabled: true,
      activitySceneId: q?.activitySceneId,
      activityPlanId: q?.activityPlanId,
      subsidyType: q.subsidyType, // 补贴场景
    };
  }

  if (q.copy === 'true' && q.activityId) {
    // 复制场景，补充被复制活动信息
    const r = await api.subActivity.getDetail(+(q.activityId as string))
    if (r?.data?.investmentActivityDetailDTO) {
      ret.copy = true
      ret.activity = {
        activityId: r.data.investmentActivityDetailDTO.activityId,
        createdUserId: r.data.investmentActivityDetailDTO.createdUserId,
        createdUserName: r.data.investmentActivityDetailDTO.createdUserName,
        name: r.data.investmentActivityDetailDTO.name,
        remark: r.data.investmentActivityDetailDTO.remark,
        description: r.data.investmentActivityDetailDTO.description,
        beginTime: r.data.investmentActivityDetailDTO.beginTime,
        endTime: r.data.investmentActivityDetailDTO.endTime,
        signUpStartTime: r.data.investmentActivityDetailDTO.signUpStartTime,
        signUpEndTime: r.data.investmentActivityDetailDTO.signUpEndTime,
        allowCancel: r.data.investmentActivityDetailDTO.allowCancel,
        signUpShopType: r.data.investmentActivityDetailDTO.signUpShopType,
        shopPoolId: r.data.investmentActivityDetailDTO.shopPoolId,
        shopPoolName: r.data.investmentActivityDetailDTO.shopPoolName,
        reviewRuleId: r.data.investmentActivityDetailDTO.reviewRuleId,
        reviewRuleName: r.data.investmentActivityDetailDTO.reviewRuleName,
        marketPlay: r.data.investmentActivityDetailDTO.marketPlay,
        signUpMinCycleTime: r.data.investmentActivityDetailDTO.signUpMinCycleTime,
        signUpMaxCycleTime: r.data.investmentActivityDetailDTO.signUpMaxCycleTime,
        stockType: r.data.investmentActivityDetailDTO.stockType,
        businessLineList: r.data.investmentActivityDetailDTO?.businessLineList,
        businessFormList: r.data.investmentActivityDetailDTO?.businessFormList,
        auditMemberList: (r.data.investmentActivityDetailDTO.auditMemberList || []).map((m: any) => {
          return {
            empId: m.empId,
            lastName: m.lastName
          }
        }),
        viewJson: r.data.viewJson,
      }
      // 如果是复制活动，招商模板和招商类型应该和被复制活动保持一致（无论是复制单活动，还是复制子活动）
      ret.investmentSchema = r.data.investmentActivityDetailDTO.investmentSchema
      ret.investmentType = r.data.investmentActivityDetailDTO.investmentType
      if(q.activitySceneId){
        ret.activitySceneId = q.activitySceneId
        ret.activityPlanId = q?.activityPlanId
      }
    }
  }

  ret.customAttributes = strategy.customAttributesForCreationContext ? await strategy.customAttributesForCreationContext({
    activityType: ret.activityType,
    investmentType: ret.investmentType,
    investmentSchema: ret.investmentSchema,
  }) : {};

  const s = getStrategy(ret.activityType)
  if (typeof s?.generateExtraInfoForCreationContext === 'function') {
    const extraInfo = await s.generateExtraInfoForCreationContext(ret, q as any)
    ret.extra = extraInfo
  }

  return ret
}

const CreateSubActivity: React.FC<any> = ({ CableBuilder }) => {
  const location = useLocation()
  const history = useHistory()
  const checkNextRef = useRef(false);
  const sceneRef = useRef(null);
  const cableInstRef = useRef<CableInst>();
  const CableRef = useRef<React.ComponentType<ZsCableProps>>();
  const [copyData, setCopyData] = useState();
  const [partialCreateData, setPartialCreateData] = useState();
  const [creationContext, setCreationContext] = useState<CreationContext>();
  const [userInfo, setUserInfo] = useState<UserInfo>();

  const [ready, setReady] = useState(false);

  const query = useMemo<ContextFromQuery>(() => {
    return qs.parse(location.search.slice(1)) as any
  }, [])

  debug('query', query)

  const strategy = useMemo(() => {
    const activityType = +query.activityType
    return getStrategy(activityType)
  }, [query.activityType])

  useEffect(() => {
    // 创建 style 标签
    const styleTag = document.createElement('style');
    if(strategy?.addCustomStyle) {
      strategy.addCustomStyle(styleTag, "create")
    }

    // 将 style 标签添加到 head 中
    document.head.appendChild(styleTag);

    // 定义清理函数以移除 style 标签
    return () => {
      if(styleTag) {
        styleTag.remove();
      }
    };
    
  }, [strategy]);


  useEffect(() => {
    if (!strategy) {
      return
    }
    
    Promise.all([
      api.getUserInfo(),
      resolveCreationContext(query, strategy)
    ]).then(async ([userInfoRes, _creationContext]) => {
      setUserInfo(userInfoRes.data);
      setCreationContext(_creationContext)
      if (_creationContext.copy) {
        if (strategy.resolveCopyData) {
          // 复制活动场景
          const _copyData = await strategy.resolveCopyData(_creationContext)
          debug('copyData resolved', _copyData)
          setCopyData(_copyData)
        }
      } else {
        // 普通活动创建场景
        if (strategy.resolvePartialCreateData) {
          const data = await strategy.resolvePartialCreateData(_creationContext)
          debug('partialCreateData resolved', data)
          setPartialCreateData(data)
        }
      }
      
      let cableInst =
        typeof strategy?.cable === "function"
          ? strategy.cable({
              investmentSchema: Number(_creationContext?.investmentSchema),
              investmentType: Number(_creationContext?.investmentType),
              activitySignType: _creationContext?.activitySignType,
              subsidyInvestmentModel: _creationContext?.subsidyInvestmentModel,
              extra: _creationContext.extra,
            })
          : strategy?.cable;
      cableInstRef.current = cableInst;

     const result = await CableBuilder
      .createWithEnv(resolveEnv())
      .buildAsync(cableInst?.workspace, cableInst?.model);

      const { Cable } = result;
      CableRef.current = Cable;
      setTimeout(() => setReady(true), 300);
    });
  }, [strategy]);

  const userForBoreas = useMemo(() => {
    if (!ready) {
      return null
    } else {
      return {
        id: userInfo?.workid,
        prefix: userInfo?.userid,
        name: userInfo?.realName
      }
    }
  }, [ready, userInfo])

  const dataForCable = useMemo(() => {
    if (!creationContext) {
      return undefined
    }
    if (creationContext.copy) {
      return copyData || undefined
    } else {
      return partialCreateData || undefined
    }
  }, [creationContext, copyData, partialCreateData])

  if (!ready) {
    return null;
  }

  const Cable = CableRef.current as React.ComponentType<ZsCableProps>;
  debug('userForBoreas', userForBoreas, 'creationContext', creationContext, 'dataForCable', dataForCable);

  // 招商在玩法组件当中的服务，不再直接使用组件内置的服务链路
  const zsComponentServiceMap: any = {
    couponImageService: {
      upload(content: string) {
        return api.uploadPictureForComponentForm(content)
      }
    }
  };
  zsComponentServiceMap.budgetService = {
    async getBudgets(bizDate: number, resource: { modelType: string; }) {
      const typeMapping: { [modelType: string]: 0 | 1 | 2; } = {
        'ACTIVITY': 1,
        'COUPON': 2
      };
      const res = await api.getBudgetList({
        bizDate,
        type: typeMapping[resource.modelType]
      });
      if (creationContext?.investmentTemplate?.budget?.id) {
        // 子活动创建场景，补充主活动预算（如果预算列表当中未包含的话）
        const budgets = res?.data || []
        const index = budgets.findIndex(b => b.budgetId === creationContext?.investmentTemplate?.budget?.id)
        if (index === -1) {
          res.data = [{
            
            budgetId: creationContext?.investmentTemplate?.budget?.id,
            budgetPoolName: creationContext?.investmentTemplate?.budget?.name,
            isAvailable: true
          }, ...budgets]
        }
      }
      return res
    }
  };

  return (
    <div
      className={`zs-create-sub-act-page ${
        query?.activitySceneId ? 'zs-alpha-page' : ''
      }`}
    >
      {/* 阿尔法需要在左侧展示alpha详情 */}
      {!!query?.activitySceneId
        ? <AlphaSceneInfo sceneId={query.activitySceneId as string} planId={query.activityPlanId} /> 
        : null}

      <div className="zs-create-sub-activity">
        <CreateSubActHeader
          showActInfo={!!query?.activitySceneId}
          actConfigInfo={{ ...query } as any}
        />

        <div className="zs-content">
          <Cable
            data={dataForCable}
            env={{
              view: 'edit',
              investmentTemplate: creationContext?.investmentSchema, // 兼容玩法配置对 env 的使用
              creationContext,
            }}
            channel="kunlun"
            onCancel={() => {
              const sceneInfo: any = sceneRef.current || {};
              const aesParams = {
                et: 'CLK',
                c1: sceneInfo?.planId,
                c2: sceneInfo?.planName,
                c3: sceneInfo?.sceneId,
                c4: sceneInfo?.name,
                c6: 'cancel —— 活动创建流程中点击取消'
              }

              // 发送AES埋点
              sendAesTracker('on_create_activity_step_change_or_cancel', aesParams)

              if (query?.activitySceneId || creationContext?.subsidyInvestmentModel === "STEP_A") {
                history.go(-1);
              } else {
                const href = history.createHref({ pathname: '/activity' });
                window.location.href = href;
              }
            }}
            user={userForBoreas as any}
            onEvent={(event) => {
              const { finished } = cableInstRef.current?.onEvent(event);
              if (finished) {
                return;
              }
              debug('onEvent', event);
            }}
            // @ts-ignore
            onStepChange={(step: any) => {
              const sceneInfo: any = sceneRef.current || {};
              const aesParams = {
                et: 'CLK',
                c1: sceneInfo?.planId,
                c2: sceneInfo?.planName,
                c3: sceneInfo?.sceneId,
                c4: sceneInfo?.name,
                c5: step?.name + '_' + step?.title,
                c6: '活动创建流程中步骤切换时触发（step是当前页面所在的步骤）',
              }

              // 发送AES埋点
              sendAesTracker('on_create_activity_step_change_or_cancel', aesParams);
            }}
            checkNext={async (ctx) => {
              const sceneInfo: any = sceneRef.current || {};

              if (checkNextRef.current) {
                return;
              }
              checkNextRef.current = true;

              try {
                globalState.save();
                globalState.createActivitySessionId =
                  creationContext?.createActivitySessionId;
                globalState.createActivityStartTime =
                  creationContext?.createActiivtyStartTime;
                globalState.activitySceneId = creationContext?.activitySceneId; // alpha 活动场景id
                globalState.activityPlanId =  creationContext?.activityPlanId; // alpha 活动计划id
                globalState.sceneInfo = sceneInfo; // alpha 活动场景id
                if (creationContext?.copy) {
                  globalState.copyActivityId =
                    creationContext?.activity?.activityId; // 被复制活动 id
                  globalState.copy = creationContext?.copy;
                }
                const res = await cableInstRef.current?.checkNext(
                  ctx,
                  creationContext as CreationContext,
                  history
                );
                return res;
              } catch (err: any) {
                console.error(err)
                // 子活动创建异常 arms监控
                MonitorFusion?.logCustomEvent({
                  code: 'activity_create_err',
                  message: err.message || err,
                  c1: 'sub activity',
                  c2: `${
                    ActivityTypeEnumStr[creationContext?.activityType as number]
                  }：${creationContext?.activityType}`,
                  c3: JSON.stringify(creationContext),
                });
              } finally {
                globalState.restore();
                checkNextRef.current = false;
              }
            }}
            options={{
              steps: cableInstRef.current?.stepOptions || {
                playdata: {
                  serviceMap: zsComponentServiceMap,
                },
              },
            }}
          />
        </div>
      </div>
    </div>
  );
};

export default initCable(CreateSubActivity)
