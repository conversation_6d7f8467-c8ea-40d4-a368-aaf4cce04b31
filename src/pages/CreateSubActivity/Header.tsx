import './Header.scss';

import React from 'react';
import { Breadcrumb } from '@alifd/next';

import {
  InviteActivityTypeEnum, // 活动类型/主活动枚举
  InviteActivityTypeEnumStr,
  InvestmentTypeEnum, // 招商类型
  InvestmentTypeEnumStr,
  InvestmentSchemaEnum, // 招商模板
  InvestmentSchemaEnumStr,
  ActivityTypeEnum, // 玩法类型
  ActivityTypeEnumStr,
} from '../../constants';

// alpha 和 invite路由下，如果 main/v2/#/act/activity 页面会404
let hasMainV2 = true;
if (/alpha/.test(window.location.host) 
  || /invite/.test(window.location.host)) {
  hasMainV2 = false;
}

interface ActConfigInfo {
  investmentActivityType: InviteActivityTypeEnum;
  investmentType: InvestmentTypeEnum;
  investmentSchema: InvestmentSchemaEnum;
  activityType: ActivityTypeEnum;
  activitySceneId?: string;
  activityPlayId?: string;
  activitySceneName?: string;
}

interface CreateSubActHeaderTS {
  showActInfo?: boolean;
  actConfigInfo: ActConfigInfo;
}

const CreateSubActHeader: React.FC<CreateSubActHeaderTS> = ({
  showActInfo = false,
  actConfigInfo,
}) => {
  if (showActInfo) {
    // 阿尔法
    return (
      <div className="zs-create-sub-act-header">
        活动类型：{InviteActivityTypeEnumStr[actConfigInfo.investmentActivityType]}　|　
        招商类型：{InvestmentTypeEnumStr[actConfigInfo.investmentType]}　|　
        招商模板：{InvestmentSchemaEnumStr[actConfigInfo.investmentSchema]}　|　
        玩法类型：{ActivityTypeEnumStr[actConfigInfo.activityType]}
      </div>
    );
  }

  // 招商默认渲染
  return (
    <Breadcrumb separator="/">
      <Breadcrumb.Item link={hasMainV2 ? '/main/v2/#/act/activity' : '/#/act/activity'} >
        招商活动
      </Breadcrumb.Item>
      <Breadcrumb.Item>
        创建{ActivityTypeEnumStr[actConfigInfo.activityType]}活动
      </Breadcrumb.Item>
    </Breadcrumb>
  );
};

export default CreateSubActHeader;
