.zs-create-sub-act-page {
  display: flex;
  &.zs-alpha-page {
    width: 1280px;
    margin: 16px auto;
  }
  .zs-create-sub-activity {
    width: 100%;
    .zs-header {
      padding: 4px 0px 20px;
      background-color: #fff;
      margin-top: 20px;
      margin-bottom: 20px;
      border-radius: 4px 4px 0 0;
      .zs-header-scene-title {
        box-shadow: inset 0 -1px 0 0 rgba(0, 0, 0, 0.06);
        margin-bottom: 1px;
        padding-left: 20px;
        padding-bottom: 10px;
      }
      .zs-header-line {
        width: 100%;
        height: 1px;
        background: #eee;
      }
      .zs-header-content {
        margin-top: 20px;
        margin-left: 20px;
        .zs-header-scene-tip {
          font-size: 14px;
          color: #505050;
        }
        .zs-header-content-col-line {
          margin-left: 10px;
          margin-right: 10px;
        }
        .zs-header-scene-name {
          margin-left: 54px;
        }
        .zs-header-scene-text {
          font-size: 14px;
          color: #222222;
        }
      }
    }
    .zs-content {
      padding: 20px;
      background-color: #fff;
    }
    .std-page {
      width: 800px;
      margin: 0 auto;
    }
  }
}

.hide-prompt-after .label:after {
  display: none !important;
}
