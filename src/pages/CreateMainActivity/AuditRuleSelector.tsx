import * as React from "react";
import { useState } from "react";
import { useDebounceEffect } from "ahooks";
import { Balloon, Icon, Select } from "@alifd/next";
import {
  AlscGatewayWebClient,
  EnvType,
  RequestSource,
} from "@ali/alsc-gateway-web-client";

interface AuditRule {
  value: number;
  originName: string;
  label: string;
}

class RuleQueryService {
  env: string;

  constructor(env: string) {
    this.env = env;
  }

  async queryViaYunding(search: string): Promise<AuditRule[]> {
    const aslcEnvMapping: any = {
      daily: EnvType.DAILY,
      pre: EnvType.PRE,
      prod: EnvType.PROD,
    };
    const aslcClient = new AlscGatewayWebClient({
      env: aslcEnvMapping[this.env],
      source: RequestSource.BUC,
    });
    const res = await aslcClient.fetch({
      apiKey:
        "ele-newretail-investment.NewretailInvestmentAuditRuleYundingService.getInvestmentAuditRuleBySearch",
      params: [search],
    });
    return res;
  }

  async query(search: string): Promise<AuditRule[]> {
    return this.queryViaYunding(search)
  }
}

interface Props {
  disabled: boolean;
  env: "pre" | "prod" | "daily";
  value?: AuditRule | undefined;
  onCreate?: () => void;
  onChange: (v: AuditRule | undefined) => void;
}

const AuditRuleSelector: React.FC<Props> = ({
  env,
  value,
  onChange,
  onCreate,
  disabled,
}) => {
  const [search, setSearch] = useState<string>();
  const [dataSource, setDataSource] = useState<AuditRule[]>([]);

  useDebounceEffect(() => {
    if (search) {
      new RuleQueryService(env).query(search).then((res) => {
        // @ts-ignore
        const rules = res.data || [];
        if (Array.isArray(rules)) {
          setDataSource(
            rules
              .map((item) => {
                return {
                  value: item.ruleId,
                  originName: item.name,
                  label: item.name,
                };
              })
              .map((item: AuditRule) => {
                return {
                  value: item.value,
                  originName: item.originName,
                  label: item.label + "(" + item.value + ")",
                };
              })
          );
        }
      });
    }
  }, [env, search]);

  const jumpRule = () =>{
    if(!value?.value){
      return
    }
    window.open(`#/review-rule/?ruleId=${value.value}`, '_blank');
  }

  return (
    <div className="zs-main-activity-rule-selector">
      <div style={{position: 'relative'}}>
        <Select
          disabled={disabled}
          showSearch
          hasClear
          value={value?.value}
          filterLocal={false}
          dataSource={dataSource}
          onSearch={(v) => setSearch(v)}
          style={{ width: 360, marginRight: 5 }}
          placeholder="请输入报名规则ID"
          onChange={(_value) => {
            if (!_value) {
              onChange(undefined);
            } else {
              const item = dataSource.find((_item) => _item.value === _value);
              if (item) {
                onChange(item);
              }
            }
          }}
        />
        <Balloon
          needAdjust
          trigger={
            <Icon type="prompt" size="medium" style={{ color: "#999" }} />
          }
          align="b"
          alignEdge
          triggerType="click"
          style={{ width: 200 }}
        >
          <div>请输入报名规则ID</div>
        </Balloon>
        {value?.value && <span style={{color: '#409EFF', position: 'absolute', right: '-26px', top: 8, cursor: 'pointer'}} onClick={jumpRule}>查看详情</span>}
      </div>
      <div
        className="zs-rule-selector-tip"
        onClick={() => {
          onCreate && onCreate();
        }}
      >
        无可用规则？点击创建
      </div>
    </div>
  );
};

export default AuditRuleSelector;
