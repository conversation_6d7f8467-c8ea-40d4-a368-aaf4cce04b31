import * as React from 'react';
import { useMemo } from 'react';
import { Checkbox } from '@alifd/next'

import { ActivityTypeEnumStr } from "../../constants";
import ZsTip from '../../components/ZsTip';

interface Props {
  value?: number[] | undefined;
  activityTypes: number[];
  onChange: (v: undefined | number[]) => void;
}

const ActivityTypeSelector: React.FC<Props> = ({ value, onChange, activityTypes }) => {
  const activityTypeOptions = useMemo(() => {
    return activityTypes
      .map(t => {
        return {
          value: t,
          label: ActivityTypeEnumStr[t]
        }
      })
  }, [activityTypes])

  return (
    <>
      <div>
        <Checkbox.Group
          value={value}
          onChange={v => {
            if (!v) {
              onChange(undefined)
            } else {
              onChange(v.map(item => +item))
            }
          }}>
          {activityTypeOptions.map(option => {
            return (
              <Checkbox
                value={option.value}
                key={option.value}>
                {option.label}
              </Checkbox>
            )
          })}
        </Checkbox.Group>
      </div>
      <ZsTip tip="对应⼦活动玩法类型只可选择主活动包含的玩法类型" />
    </>
  )
}

export default ActivityTypeSelector
