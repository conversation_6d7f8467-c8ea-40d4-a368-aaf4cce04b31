import * as React from 'react';
import { NumberPicker } from '@alifd/next';
import ZsTip from '../../components/ZsTip';


interface Value {
  min?: number;
  max?: number;
}

interface Props {
  value?: Value | undefined;
  onChange: (value: Value | undefined) => void;
  disabled: boolean;
  max?: number;
}



const CircleTimeInput: React.FC<Props> = ({ value, onChange, disabled, max }) => {
  const commonProps = {
    disabled,
    min: 1,
    max,
    placeholder: '请输入正整数',
    style: { width: 150 },
  }

  return (
    <>
      <div>
        <NumberPicker
          {...commonProps}
          value={value?.min}
          onChange={v => {
            onChange({ ...value, min: v })
          }}
        />
        &nbsp;&nbsp;-&nbsp;&nbsp;
        <NumberPicker
          {...commonProps}
          value={value?.max}
          onChange={v => {
            onChange({ ...value, max: v })
          }}
        />
        &nbsp;&nbsp;天
      </div>
      <ZsTip tip="对应子活动时间不能超出主活动时间段" />
    </>
  )
}

export default CircleTimeInput
