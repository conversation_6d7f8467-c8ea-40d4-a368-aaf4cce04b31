import "./style.scss";

import * as React from "react";
import { useMemo, useRef } from "react";
import { useHistory } from "react-router";
import {
  Message,
  Button,
  Radio,
  Form,
  Field,
  Input,
  DatePicker,
  Breadcrumb,
  Dialog,
} from "@alifd/next";

import * as api from "../../api";
import { investmentRecipes, InvestmentSchemaEnum, perm2ApplyURL, Permission } from "../../constants";
import { maskMS, resolveEnv, useRouteQuery } from "../../common";
import ZsTip from "../../components/ZsTip";
import AuditRuleSelector from "./AuditRuleSelector";
import ActivityTypeSelector from "./ActivityTypeSelector";
import CircleTimeInput from "./CircleTimeInput";
import { checkForbiddenAndAlertIfNeed } from "../common";
import { MonitorFusion } from "@ali/monitor-fusion";

const moment = require("moment");

// alpha 和 invite路由下，如果 main/v2/#/act/activity 页面会404
let hasMainV2 = true;
if (/alpha/.test(window.location.host) || /invite/.test(window.location.host)) {
  hasMainV2 = false;
}

const formItemLayout = {
  labelCol: { fixedSpan: 7 },
  wrapperCol: { span: 17 },
};

function sandbox(fn: any) {
  try {
    fn();
  } catch (e) {
    console.error(e);
  }
}

function wrapCallback(callback: any) {
  if (callback) {
    return callback;
  } else {
    return (msg: string) => {
      if (msg) {
        throw new Error(msg);
      }
    };
  }
}

function validateActivityTypes(
  rule: any,
  value: number[] | undefined,
  _callback: any
) {
  let callback = wrapCallback(_callback);
  if (!value || value.length <= 0) {
    callback("请选择玩法类型");
  } else {
    callback();
  }
}

function validateActivityTime(rule: any, value: any, _callback: any) {
  let callback = wrapCallback(_callback);
  if (value && value[0] && value[1]) {
    callback();
  } else {
    callback("请设置活动时间");
  }
}

function validateCircleTime(rule: any, value: any, _callback: any) {
  let callback = wrapCallback(_callback);
  if (!value || !value.min || !value.max) {
    callback("请设置活动时间长度");
  } else if (value.min > value.max) {
    callback("最小值不能大于最大值");
  } else {
    callback();
  }
}

function resolveDays(start: any, end: any) {
  if (start && end) {
    const _start = start.clone().startOf("day");
    const _end = end.clone().startOf("day");
    return _end.diff(_start, "days") + 1;
  } else {
    return null;
  }
}

interface MainActivityData {
  investmentActivityType: number;
  investmentType: number;
  investmentSchema: number;
  investmentWay: 0 | 1; // 0: 正向招商 1: 反向招商

  name: string;
  description: string;
  beginTime: number;
  endTime: number;
  timeMode: 0 | 1; // 是否允许子活动修改活动时间, 0: 不允许, 1: 允许
  activityTypes: number[];
  reviewRuleId?: number;
  reviewRuleName?: string;
  signUpMinCycleTime?: number;
  signUpMaxCycleTime?: number;
  isLongTermActivity?: boolean;  // 是否长期活动
}

const CreateMainActivity: React.FC<any> = () => {
  const startTimeRef = useRef<number>(Date.now());
  const history = useHistory();
  const query = useRouteQuery();
  const activityTypes = useMemo(() => {
    const { investmentType, investmentSchema } = query;
    return investmentRecipes
      .filter(
        (r) =>
          r.investmentActivityType === 1 &&
          r.investmentType === +investmentType &&
          r.investmentSchema === +investmentSchema &&
          r.available
      )
      .map((r) => r.activityType);
  }, [query]);

  const needCycleTime = useMemo(() => {
    // 招商类型是栏目招商时，需要设置招商活动时间长度
    return +query.investmentType === 1;
  }, [query.investmentType]);

  const auditRuleVisible = useMemo(() => {
    // 爆单红包不展示上报名规则
    return (
      +query.investmentSchema !== InvestmentSchemaEnum.EXPLOSIVE_ORDER_COUPON
    );
  }, [query.investmentSchema]);

  const modifyActivityTimeVisible = useMemo(() => {
    // 爆单红包不展示上是否可修改活动时间，默认不能
    return (
      +query.investmentSchema !== InvestmentSchemaEnum.EXPLOSIVE_ORDER_COUPON
    );
  }, [query.investmentSchema]);

  const supportLongActivityTime = useMemo(() => {
    // 爆单红包支持长期活动
    return (
      +query.investmentSchema === InvestmentSchemaEnum.EXPLOSIVE_ORDER_COUPON
    );
  }, [query.investmentSchema]);

  const field = Field.useField({
    values: {
      timeMode: 0,
    },
    onChange(name: string, value: any) {
      if (name === "activityTime") {
        if (needCycleTime) {
          // 根据活动时间修改商家活动时间长度
          const [start, end] = value || [];
          const days = resolveDays(start, end);
          if (days === null) {
            // 活动时间被清空或者不全
            field.setValue("circleTime", undefined);
          } else {
            // 活动时间完整
            let cycleTime: any = field.getValue("cycleTime");
            if (cycleTime) {
              cycleTime = { ...cycleTime };
              if (cycleTime.min > days) {
                cycleTime.min = days;
              }
              if (cycleTime.max > days) {
                cycleTime.max = days;
              }
              field.setValue("cycleTime", cycleTime);
            }
          }
        }
      }
    },
  });

  const maxDays = useMemo(() => {
    const activityTime: any = field.getValue("activityTime");
    if (activityTime && activityTime[0] && activityTime[1]) {
      let [start, end] = activityTime;
      start = start.clone().startOf("day");
      end = end.clone().startOf("day");
      return end.diff(start, "days") + 1;
    } else {
      return null;
    }
  }, [field.getValue("activityTime")]);

  const href = history.createHref({
    pathname: "/activity",
  });

  return (
    <div className="zs-create-main-activity">
      <Breadcrumb separator="/">
        <Breadcrumb.Item link={href}>招商活动</Breadcrumb.Item>
        <Breadcrumb.Item>创建主子活动</Breadcrumb.Item>
      </Breadcrumb>
      <div className="zs-content-wrap">
        <div className="zs-content">
          <Form field={field} {...formItemLayout}>
            <div className="zs-title">基本信息</div>
            <Form.Item label="主活动名称" required>
              <Input
                style={{ width: 400 }}
                maxLength={20}
                showLimitHint
                placeholder="请输入活动名称，会在商家端展示，不能超过20个字"
                {...field.init("name", {
                  rules: [{ required: true, message: "请输入主活动名称" }],
                })}
              />
            </Form.Item>
            <Form.Item label="主活动描述" required>
              <Input.TextArea
                rows={5}
                style={{ width: 400 }}
                maxLength={300}
                showLimitHint
                placeholder="请输入活动描述，；该信息在商家端展示，不能超过300字"
                {...field.init("description", {
                  rules: [{ required: true, message: "请输入主活动描述" }],
                })}
              />
            </Form.Item>
            <Form.Item label="活动时间" required>
              {supportLongActivityTime ? (
                <div className="zs-content-value">默认长期有效</div>
              ) : (
                <DatePicker.RangePicker
                  style={{ width: 400 }}
                  {...field.init("activityTime", {
                    rules: [
                      { required: true, message: "请设置活动时间" },
                      { validator: validateActivityTime },
                    ],
                  })}
                  disabledDate={(m) => {
                    const [start] =
                      field.getValue<[any, any]>("activityTime") || [];
                    const today = moment().startOf("day");
                    const _maxDays = 365;
                    if (
                      m.diff(today) < 0 ||
                      m.diff(
                        (start || today).clone().add(_maxDays - 1, "day")
                      ) > 0
                    ) {
                      return true;
                    } else {
                      return false;
                    }
                  }}
                />
              )}
            </Form.Item>
            {modifyActivityTimeVisible && (
              <Form.Item label="可修改子活动时间" required>
                <Radio.Group {...field.init("timeMode")}>
                  <Radio value={1}>是</Radio>
                  <Radio value={0}>否</Radio>
                </Radio.Group>
              </Form.Item>
            )}
            {/* 是否需要商家活动时间长度是 querystring 所带的上下文决定的，表单使用过程当中不会发生变化，所以直接控制 Form.Item 展示还是不展示就行了 */}
            {needCycleTime && (
              <Form.Item label="商家活动时间长度" required>
                <CircleTimeInput
                  max={maxDays}
                  disabled={maxDays === null}
                  {...field.init<any>("cycleTime", {
                    rules: [{ validator: validateCircleTime }],
                  })}
                />
              </Form.Item>
            )}
            <Form.Item label="玩法类型" required>
              <ActivityTypeSelector
                activityTypes={activityTypes}
                {...field.init<number[] | undefined>("activityTypes", {
                  rules: [{ validator: validateActivityTypes }],
                })}
              />
            </Form.Item>
            {auditRuleVisible ? (
              <>
                <div className="zs-title">报名规则</div>
                <Form.Item label="商品报名规则ID">
                  <AuditRuleSelector
                    disabled={false}
                    env={resolveEnv()}
                    {...field.init<any>("auditRule")}
                    onCreate={() => {
                      const env = resolveEnv();
                      if (env === "pre") {
                        window.open(
                          "https://pre-zs.kunlun.alibaba-inc.com/main/newPromotion?createRule=true",
                          "_blank"
                        );
                      } else if (env === "prod") {
                        window.open(
                          "https://zs.kunlun.alibaba-inc.com/main/newPromotion?createRule=true",
                          "_blank"
                        );
                      }
                    }}
                  />
                  <ZsTip tip="对应子活动报名规则范围只可选择主活动包含的报名规则" />
                </Form.Item>
              </>
            ) : null}

            <Form.Item label=" " style={{ marginTop: 40 }}>
              <Button
                onClick={() => {
                  if (hasMainV2) {
                    window.location.href = "/main/v2/#/act/activity";
                  } else {
                    window.location.href = "/#/act/activity";
                  }
                }}
              >
                取消
              </Button>
              &nbsp;&nbsp;&nbsp;
              <Button
                onClick={async () => {
                  if (await checkForbiddenAndAlertIfNeed()) {
                    return;
                  }
                  if(+query.investmentSchema === InvestmentSchemaEnum.EXPLOSIVE_ORDER_COUPON) {
                    let permission = Permission.AYZBX_A1_MANAGE; 
                    const res = await api.checkPermission(permission);
                    if (!res.data) {
                      const url = perm2ApplyURL[permission];
                      Dialog.show({
                        title: "提示",
                        content: "您暂无该权限,是否跳转申请权限页面?",
                        onOk: () => {
                          window.open(url, "_blank");
                        },
                      });
                      return;
                    }
                  }
                  const ret = await field.validatePromise();
                  if (ret.errors) {
                    const key = Object.keys(ret.errors)[0];
                    // @ts-ignore
                    const errMesage = ret.errors[key].errors[0];
                    Message.error(errMesage);
                    return;
                  }

                  sandbox(() => {
                    const endTime = Date.now();
                    const _startTime = startTimeRef.current;
                    // @ts-ignore
                    if (window.AESPluginEvent) {
                      // @ts-ignore
                      window.AESPluginEvent("create-main-activity", {
                        et: "CLK",
                        c1: endTime - _startTime,
                        c2: +query.investmentType,
                        c3: +query.investmentSchema,
                      });
                    }
                  });

                  const formData = ret.values;
                  const activityData: MainActivityData = {
                    investmentActivityType: 1, // 写死多活动
                    investmentType: +query.investmentType,
                    investmentSchema: +query.investmentSchema,
                    investmentWay: 0, // 当前没有反向招商，直接写成成正向招商
                    name: formData.name,
                    description: formData.description,
                    beginTime: formData.activityTime?.[0]
                      .clone()
                      .startOf("day")
                      .toDate()
                      .getTime(),
                    endTime: maskMS(
                      formData.activityTime?.[1]
                        .clone()
                        .endOf("day")
                        .toDate()
                        .getTime()
                    ),
                    timeMode: +query.investmentSchema === InvestmentSchemaEnum.EXPLOSIVE_ORDER_COUPON ? 1 :formData.timeMode,
                    activityTypes: formData.activityTypes,
                    reviewRuleId: formData.auditRule?.value,
                    reviewRuleName: formData.auditRule?.originName,
                    isLongTermActivity: supportLongActivityTime,
                  };
                  if (needCycleTime) {
                    activityData.signUpMinCycleTime = formData.cycleTime.min;
                    activityData.signUpMaxCycleTime = formData.cycleTime.max;
                  }

                  try {
                    const res = await api.investmentTemplate.create(
                      activityData
                    );
                    if (res.success) {
                      history.push("/main-activity/" + res.data);
                      sandbox(() => {
                        // 主活动创建 
                        MonitorFusion?.logCustomEvent({
                          code: "creat_action",
                          msg: res,
                          c1: "main activity",
                          c2: `主活动创建：${formData.activityTypes}`,
                          c3: JSON.stringify(activityData),
                        });
                      });
                    } else {
                      Message.error(res.errorMessage);
                      throw new Error(res.errorMessage);
                    }
                  } catch (err: any) {
                    // 主活动创建异常 arms监控
                    MonitorFusion?.logCustomEvent({
                      code: "activity_create_err",
                      msg: err.message || err,
                      c1: "main activity",
                      c2: `主活动创建：${formData.activityTypes}`,
                      c3: JSON.stringify(activityData),
                    });
                  }
                }}
                type="primary"
              >
                下一步
              </Button>
            </Form.Item>
          </Form>
        </div>
      </div>
    </div>
  );
};

export default CreateMainActivity;
