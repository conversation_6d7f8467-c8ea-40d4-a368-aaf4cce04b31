import * as React from 'react';
import { useState } from 'react';
import { useDebounceEffect } from 'ahooks'
import { Select } from '@alifd/next';
import { AlscGatewayWebClient, EnvType, RequestSource } from '@ali/alsc-gateway-web-client';

interface Budget {
  budgetId: number;
  budgetPoolName: string;
}

interface BudgetQueryService {
  query(startTime: number): Promise<Budget[]>
}

class BudgetQueryServiceImpl implements BudgetQueryService {
  env: string;

  constructor(env: string) {
    this.env = env;
  }

  async queryViaYunding(bizDate: number | undefined): Promise<Budget[]> {
    const aslcEnvMapping: any = {
      daily: EnvType.DAILY,
      pre: EnvType.PRE,
      prod: EnvType.PROD,
    };
    const aslcClient = new AlscGatewayWebClient({
      env: aslcEnvMapping[this.env],
      source: RequestSource.BUC,
    });
    const res = await aslcClient.fetch({
      apiKey: 'ele-newretail-investment.NewretailInvestmentBudgetYundingService.getBudgetList',
      params: [{ bizDate }]
    })
    return res?.data || []
  }

  async query(bizDate: number | undefined): Promise<Budget[]> {
    return await this.queryViaYunding(bizDate)
  }
}

interface Props {
  startTime?: number;
  env: 'pre2' | 'pre' | 'prod' | 'daily';
  value?: Budget | undefined;
  onChange: (v: Budget | undefined) => void;
}

const BudgetSelector: React.FC<Props> = ({
  env,
  startTime,
  value,
  onChange
}) => {
  const [dataSource, setDataSource] = useState<Budget[]>([])

  useDebounceEffect(() => {
    if (startTime) {
      new BudgetQueryServiceImpl(env)
        .query(startTime)
        .then(res => {
          if (Array.isArray(res)) {
            setDataSource(res)
          }
        })
    }
  }, [env, startTime])

  return (
    <div className="zs-main-activity-budget-selector">
      <Select
        showSearch
        hasClear
        value={value?.budgetId}
        dataSource={dataSource.map((b: any) => {
          return {
            value: b.budgetId,
            label: `${b.budgetPoolName}(${b.budgetId})`
          }
        })}
        style={{ width: 360, marginRight: 5 }}
        placeholder="请选择预算"
        onChange={_value => {
          if (!_value) {
            onChange(undefined)
          } else {
            const item = dataSource.find(_item => _item.budgetId === _value)
            if (item) {
              onChange(item)
            }
          }
        }}
      />
    </div>
  )
}

export default BudgetSelector
