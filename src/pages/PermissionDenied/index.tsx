import React from "react";
import { Button } from "@alifd/next";
import NoPermissionIcon from "../../assets/no-permission.svg";
import { isInMicro } from "@alife/starship-alpha-sdk";

const PermissionDenied: React.FC<any> = ({ url }) => {
  return (
    <div
      style={{
        minHeight: "80vh",
        background: "#fff",
        display: "flex",
        flexDirection: "column",
        alignItems: "center",
        justifyContent: "center",
      }}
    >
      <div style={{ display: "flex", alignItems: "center", marginBottom: 40 }}>
        <div style={{ marginRight: 12 }}>
          <img src={NoPermissionIcon} alt="" />
        </div>
        <div>
          <div
            style={{
              fontWeight: 500,
              lineHeight: "32px",
              fontSize: 24,
              color: "#222",
              marginBottom: 12,
            }}
          >
            暂无访问权限
          </div>
          <div
            style={{
              lineHeight: "20px",
              fontSize: 14,
            }}
          >
            请先申请权限吧
          </div>
        </div>
      </div>
      <div>
        <Button
          onClick={() => window.history.go(-1)}
          style={{ marginRight: 24, display: isInMicro() ? "block" : "none" }}
        >
          返回
        </Button>
        <Button
          type="primary"
          onClick={() => {
            window.open(url, "_blank");
          }}
        >
          申请权限
        </Button>
      </div>
    </div>
  );
};

export default PermissionDenied;
