import "./style.scss";

import * as React from 'react';
import { useEffect, useState, useCallback } from "react";
import * as qs from 'querystring';
import { Dialog, Pagination, Tab, Breadcrumb, Button } from '@alifd/next';
import { useHistory, useRouteMatch } from 'react-router';
import { Link } from "react-router-dom";
import { useSetState } from 'ahooks';

import * as api from '../../api';
import { InvestmentTemplateDTO } from "../../models";
import { ActivityTypeEnumStr, perm2ApplyURL, Permission } from "../../constants";
import SubActivityCreationDialog, { Value } from "../../components/SubActivityCreationDialog";
import SubActivityTable from "../../components/SubActivityTable";
import { checkForbiddenAndAlertIfNeed, redirectZsV2Page } from "../common";

const moment = require('moment')
// const debug = require('debug')('zs:MainActivity')

const defaultPagination = {
  page: 1,
  pageSize: 15
}

const MainActivity: React.FC<any> = () => {
   // 重定向到新页面
  redirectZsV2Page()
  const match = useRouteMatch<any>()
  const history = useHistory()
  const [loading, setLoading] = useState(false)
  const [investmentTemplate, setInvestmentTemplate] = useState<InvestmentTemplateDTO>()
  const [visible, setVisible] = useState(false)

  // 子活动列表相关状态
  const [subActivityList, setSubActivityList] = useState([])
  const [total, setTotal] = useState(0);
  const [version, setVersion] = useState(0)
  const [tab, setTab] = useState('accessible')
  const [pagination, updatePagination] = useSetState(defaultPagination)

  useEffect(() => {
    api.investmentTemplate.getDetail(match.params.id).then(res => {
      setInvestmentTemplate(res.data as any)
    })
  }, [match.params.id])

  useEffect(() => {
    setLoading(true)
    api.subActivity.getList({
      isSelf: tab === 'owner' ? 1 : 0,
      templateId: match.params.id,
      ...pagination
    }).then(res => {
      setTotal(res.total)
      setSubActivityList(res.data)
    }).finally(() => {
      setLoading(false)
    })
  }, [tab, version, pagination, match])

  const templateName = React.useMemo(() => {
    return investmentTemplate?.investmentTemplateDetailDTO?.name || ''
  }, [investmentTemplate])

  const activityNum = React.useMemo(() => {
    return investmentTemplate ?
      investmentTemplate?.investmentActivityExtendDTO?.activityNum :
      ''
  }, [investmentTemplate])

  const descElements = React.useMemo(() => {
    const desc = investmentTemplate?.investmentTemplateDetailDTO?.description || ''
    return desc.split('\n').map((t, i) => {
      return (
        <div key={i} style={{ marginBottom: 15 }}>
          {t}
        </div>
      )
    })
  }, [investmentTemplate])

  const handleCopyActivity = useCallback(async (activityId, activityType, subsidyType) => {
    if (!investmentTemplate) {
      console.error('investmentTemplate is empty');
      return
    }
    const href = history.createHref({
      pathname: '/create-sub-activity',
      search: '?' + qs.stringify({
        copy: true,
        activityId,
        activityType,
        subsidyType,
        templateId: investmentTemplate?.investmentTemplateDetailDTO.templateId,
      })
    })
    window.open(href, '_blank')
  }, [investmentTemplate])

  const jumpRule = () =>{
    if(!investmentTemplate?.investmentTemplateDetailDTO?.reviewRuleId){
      return
    }
    window.open(`#/review-rule/?ruleId=${investmentTemplate?.investmentTemplateDetailDTO?.reviewRuleId}`, '_blank');
  }

  return (
    <div>
      <Breadcrumb style={{ margin: '10px 0px' }} separator='/'>
        <Breadcrumb.Item><Link to="/activity">招商活动</Link></Breadcrumb.Item>
        <Breadcrumb.Item >{templateName}</Breadcrumb.Item>
      </Breadcrumb>
      <div className="main-activity-detail">
        <div className="main-activity-header">
          <div className="main-activity-header-title">
            {templateName}
          </div>
          {/* 营销治理将主活动的预算隐藏，老的带预算的主活动不允许再创建子活动 */}
          {!investmentTemplate?.investmentTemplateDetailDTO.budgetId ? <Button type="primary"
            onClick={async () => {
              if (await checkForbiddenAndAlertIfNeed()) {
                return
              }
              const permission = Permission.ZS_CHILD_ACTIVITY_CREATION_PERMISSION
              const res = await api.checkPermission(permission)
              if (!res.data) {
                const url = perm2ApplyURL[permission]
                Dialog.show({
                  title: '提示',
                  content: '您暂无该权限,是否跳转申请权限页面?',
                  onOk: () => {
                    window.open(url, '_blank')
                  }
                })
                return
              }
              setVisible(true)
            }}>
            创建子活动
          </Button> : null}
        </div>

        <div className="main-activity-content">
          <Tab>
            <Tab.Item title="活动详情" key="detail">
              <div className="zs-main-activity-info-table">
                <div className="zs-main-activity-info-table-title">基本信息</div>
                <table>
                  <tr>
                    <td>活动ID</td>
                    <td>{investmentTemplate?.investmentTemplateDetailDTO.templateId}</td>
                  </tr>
                  <tr>
                    <td>活动名称</td>
                    <td>{investmentTemplate?.investmentTemplateDetailDTO.name}</td>
                  </tr>
                  <tr>
                    <td>活动描述</td>
                    <td>{descElements}</td>
                  </tr>
                  <tr>
                    <td>活动时间</td>
                    {investmentTemplate?.investmentTemplateDetailDTO
                      ?.isLongTermActivity ? (
                      <td>长期有效</td>
                    ) : (
                      <td>
                        {moment(
                          investmentTemplate?.investmentTemplateDetailDTO
                            .beginTime
                        ).format("YYYY-MM-DD HH:mm:ss")}
                        &nbsp; ~&nbsp;
                        {moment(
                          investmentTemplate?.investmentTemplateDetailDTO
                            .endTime
                        ).format("YYYY-MM-DD HH:mm:ss")}
                    </td>)}
                  </tr>
                  <tr>
                    <td>可修改子活动时间</td>
                    <td>
                      {investmentTemplate?.investmentTemplateDetailDTO.timeMode === 0 ? '否' : '是'}
                    </td>
                  </tr>
                  <tr>
                    <td>玩法类型</td>
                    <td>
                      {investmentTemplate?.investmentTemplateDetailDTO.activityTypes
                        .map(t => ActivityTypeEnumStr[t])
                        .join(', ')}
                    </td>
                  </tr>
                  <tr>
                    <td>报名规则</td>
                    <td style={{color: '#409EFF', cursor: 'pointer'}} onClick={jumpRule}>
                      {investmentTemplate?.investmentTemplateDetailDTO.reviewRuleId ?
                        <div>
                          {investmentTemplate.investmentTemplateDetailDTO.reviewRuleName}
                          &nbsp; ({investmentTemplate.investmentTemplateDetailDTO.reviewRuleId})
                        </div> :
                        <></>}
                    </td>
                  </tr>
                </table>

                <div className="zs-main-activity-info-table-title">触达信息</div>
                <table>
                  <tr>
                    <td>触达形式</td>
                    <td></td>
                  </tr>
                </table>
                <div className="zs-main-activity-info-table-title">创建信息</div>
                <table>
                  <tr>
                    <td>创建人</td>
                    <td>{investmentTemplate?.investmentTemplateDetailDTO.createdUserName}</td>
                  </tr>
                  <tr>
                    <td>创建时间</td>
                    <td>{moment(investmentTemplate?.investmentTemplateDetailDTO.gmtCreate).format('YYYY-MM-DD HH:mm:ss')}</td>
                  </tr>
                </table>

                {investmentTemplate?.investmentTemplateDetailDTO.budgetId &&
                  <>
                    <div className="zs-main-activity-info-table-title">预算信息</div>
                    <table>
                      <tr>
                        <td>预算名称</td>
                        <td>{investmentTemplate?.investmentTemplateDetailDTO.budgetName}</td>
                      </tr>
                      <tr>
                        <td>预算ID</td>
                        <td>{investmentTemplate?.investmentTemplateDetailDTO.budgetId}</td>
                      </tr>
                    </table>
                  </>}
              </div>

            </Tab.Item>

            {investmentTemplate ?
              // 子活动列表的复制活动功能依赖 investmentTemplate，需要等 investmentTemplate 加载完成之后才能展示
              <Tab.Item title={<>子活动列表({activityNum})</>} key="subActivityList">
                <Tab
                  style={{ marginTop: 25 }}
                  shape="wrapped"
                  activeKey={tab}
                  onChange={(t: any) => setTab(t)}>
                  <Tab.Item title="可查看的" key="accessible" />
                  <Tab.Item title="我负责的" key="owner" />
                </Tab>
                <SubActivityTable
                  onCopyActivity={handleCopyActivity}
                  loading={loading}
                  onRefresh={() => setVersion(v => v + 1)}
                  dataSource={subActivityList}
                />
                <Pagination
                  total={total}
                  style={{ marginTop: 20, textAlign: 'right' }}
                  current={pagination.page}
                  pageSize={pagination.pageSize}
                  onChange={page => updatePagination({ page })}
                />
              </Tab.Item> :
              null}
          </Tab>
        </div >
      </div >
      <SubActivityCreationDialog
        visible={visible}
        isNewActivity
        templateId={investmentTemplate?.investmentTemplateDetailDTO?.templateId}
        onCancel={() => setVisible(false)}
        onClose={() => setVisible(false)}
        onCreate={async (recipe: Value) => {
          if(recipe.subsidyInvestmentModel) {
            let permission = recipe.subsidyInvestmentModel === 'A' ? Permission.AYZBX_A1_MANAGE : Permission.AYZBX_Y_MANAGE; 
            const res = await api.checkPermission(permission);
            if (!res.data) {
              const url = perm2ApplyURL[permission];
              Dialog.show({
                title: "提示",
                content: "您暂无该权限,是否跳转申请权限页面?",
                onOk: () => {
                  window.open(url, "_blank");
                },
              });
              return;
            }
          }
          const href = history.createHref({
            pathname: '/create-sub-activity',
            search: '?' + qs.stringify({
              templateId: investmentTemplate?.investmentTemplateDetailDTO.templateId,
              ...recipe
            })
          })
          window.open(href, '_blank')
        }}
      />
    </div>
  )
}

export default MainActivity
