import React from 'react';
import ReactDOM from 'react-dom';
import { isInMicro } from '@alife/starship-alpha-sdk'

import App from './App';

/**
 * The bootstrap will only be called once when the child application is initialized.
 * The next time the child application re-enters, the mount hook will be called directly, and bootstrap will not be triggered repeatedly.
 * Usually we can do some initialization of global variables here,
 * such as application-level caches that will not be destroyed during the unmount phase.
 */
export async function bootstrap() {
  if (isInMicro()) {
    const el = document.querySelector('#__qiankun_microapp_wrapper_for_op_fe_kunlun_zs_webapp__ #root')
    if (el) {
      const div = el as HTMLDivElement
      div.style.padding =  '16px'
    }
  }

  console.log('react app bootstraped');
}
/**
 * The mount method is called every time the application enters,
 * usually we trigger the application's rendering method here.
 */
export async function mount(props: any) {
  if (isInMicro()) {
    const el = document.querySelector('#__qiankun_microapp_wrapper_for_op_fe_kunlun_zs_webapp__ #root')
    if (el) {
      const div = el as HTMLDivElement
      div.style.padding =  '16px'
    }
  }

  ReactDOM.render(
    <App />,
    props.container ? props.container.querySelector('#root') : document.getElementById('root')
  );
}
/**
 * Methods that are called each time the application is switched/unloaded,
 * usually in this case we uninstall the application instance of the subapplication.
 */
export async function unmount(props: any) {
  ReactDOM.unmountComponentAtNode(props.container ? props.container.querySelector('#root') : document.getElementById('root'));
}
/**
 * Optional lifecycle，just available with loadMicroApp way
 */
export async function update(props: any) {
  console.log('update props', props);
}
