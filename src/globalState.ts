import debugFn from "debug";

const debug = debugFn("zs:glboalState");

class GlobalState {
  createActivitySessionId?: string;
  createActivityStartTime?: number;
  activitySceneId?: string | number;
  activityPlanId?: string | number;
  sceneInfo?: {};
  copy?: boolean;
  copyActivityId?: number | string;

  private stacks: GlobalState[] = [];

  save() {
    this.stacks.push({ ...this, stacks: [] });
    debug("save", this);
  }

  restore() {
    if (this.stacks.length > 0) {
      const prevState = this.stacks[this.stacks.length - 1];
      this.stacks = this.stacks.slice(0, this.stacks.length - 1);
      // 注意！GlobalState 属性不支持复杂类型，否则使用 Object.assign 无法实现恢复
      Object.assign(this, { ...prevState, stacks: this.stacks });
      debug("restore", this);
    }
  }
}

export default new GlobalState();
