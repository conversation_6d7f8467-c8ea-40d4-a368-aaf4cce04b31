import axios from "axios";
import debugFn from "debug";
import {
  AlscGatewayWebClient,
  RequestSource,
  EnvType,
  WebclientInvokeRequest,
  WebclientOptions,
} from "@ali/alsc-gateway-web-client";

import config from "./config";
import { resolveEnv } from "./common";
import globalState from "./globalState";
import { ActivityTypeEnumStr } from "./constants";
import { CheckActDiscountParams, CheckActDiscountParamsV2 } from "./models";
import _ from "lodash";
import { MonitorFusion } from "@ali/monitor-fusion";

const debug = debugFn("zs:api");

// 校验接口返回值类型，根据 marketingRuleCheckErrorType 枚举取到具体需要的字段
interface CheckActDiscountResponse {
  success: boolean;
  errorMessage: string;
  data: {
    result: boolean; // true 符合规则、false 不符合规则
    marketingRuleCheckErrorType: 'multi_rule' | 'inconformity_rule'; // multi_rule 跨业态、 inconformity_rule 跨多条规则
    failReason?: string; // marketingRuleCheckErrorType: multi_rule 跨业态会返回
    // marketingRuleCheckErrorType: inconformity_rule 跨规则会返回
    ruleValueCheckResultList?: { 
      success: boolean;
      errorMsg: string;
      mktVerifyRuleCheckType:
        | 'threshold' // 门槛
        | 'platform_fund_propotion' // 平台出资比例
        | 'platform_fund_intensity' // 平台补贴强度
        | 'person_platform_fund'; // 单用户平台补贴总额
      demandRuleValue: string; // 规则值
      currentValue: string;
    }[];
  };
}

const trackerThrottle = _.throttle((e: any, req: any) => {
  sandbox(() => {
    MonitorFusion.logJsError(e);
  });
  sandbox(() => {
    MonitorFusion.logCustomEvent({
      code: "api_error",
      msg: "yunding api error: " + e.message,
      c1: req.apiKey,
      c2: JSON.stringify(req.params),
      c3: e.result?.gwOpsInfo?.traceId,
      c4: JSON.stringify(e.result)
    });
  });
}, 1000)

function createAlscClient() {
  const alscEnvMapping: any = {
    daily: EnvType.DAILY,
    pre: EnvType.PRE,
    prod: EnvType.PROD,
  };
  const env = resolveEnv();
  const alscClient = new AlscGatewayWebClient({
    env: alscEnvMapping[env],
    source: RequestSource.BUC,
  });

  const originFetch: any = alscClient.fetch;
  alscClient.fetch = async function <T>(
    req: Partial<WebclientInvokeRequest>,
    options?: Partial<WebclientOptions>
  ): Promise<T> {
    try {
      return await originFetch.call(alscClient, req, options);
    } catch (e: any) {
      if(e?.message === '用户未登陆') {
        trackerThrottle(e, req)
      } else {
        sandbox(() => {
          MonitorFusion.logJsError(e);
        });
        sandbox(() => {
          MonitorFusion.logCustomEvent({
            code: "api_error",
            msg: "yunding api error: " + e.message,
            c1: req.apiKey,
            c2: JSON.stringify(req.params),
            c3: e.result?.gwOpsInfo?.traceId,
            c4: JSON.stringify(e.result)
          });
        });
      }
      throw e;
    }
  };

  return alscClient;
}

export const alscClient = createAlscClient();

/**
 * @deprecated
 * 有拼写问题，应该改使用 alscClient
 */
export const aslcClient = alscClient

function resolveAPIHost() {
  const env = resolveEnv();
  return `https://${config[env].hostname}`;
}

function boreasBaseUrl() {
  const env = resolveEnv();
  let baseUrl = '';
  if (env === 'pre') {
    baseUrl = 'https://pre-boreas-service.ele.me'
  } else if (env === 'prod') {
    baseUrl = 'https://boreas-service.ele.me'

  } else if (env === 'daily') {
    baseUrl = 'https://boreas.daily.elenet.me'
  }
  return baseUrl
}

export const boreasRequest = axios.create({
  baseURL: boreasBaseUrl(),
  withCredentials: true,
});

export const msRequest = axios.create({
  baseURL:' https://alsc-retail.elemecdn.com',
  withCredentials: true,
});

// function sleep(seconds: number) {
//   return new Promise((resolve) => {
//     setTimeout(resolve, seconds * 1000);
//   });
// }

function sandbox(fn: () => any) {
  try {
    fn();
  } catch (e) {
    console.error(e);
  }
}

// zsr 函数不应该直接放在外部使用，应该 api 模块内封装好接口
export const zsr = axios.create({
  baseURL: resolveAPIHost(),
  withCredentials: true,
});
zsr.interceptors.response.use(
  (resp) => {
    sandbox(() => {
      const { errorCode, errorMessage } = resp.data;
      if (errorCode) {
        MonitorFusion.logCustomEvent({
          code: "api_error",
          msg: errorMessage,
          c1: resp.config.url,
          c2: resp.config.method,
          c3: errorCode,
          c4: JSON.stringify(resp.config.data),
        });
      }
    });
    return resp;
  },
  (error) => {
    sandbox(() => {
      MonitorFusion.logJsError(error);
    });
    return Promise.reject(error);
  }
);

export const getUserInfo = async function () {
  const res = await aslcClient.fetch({
    apiKey: "alsc-market-web.SessionQueryService.getLoginUser",
  });
  return {
    data: {
      userid: res.data.loginName,
      workid: res.data.workId,
      realName: res.data.realName,
    },
  };
};

export const legacy = {
  async updateStock(payload: any) {
    try {
      await axios({
        baseURL: resolveAPIHost(),
        method: "POST",
        withCredentials: true,
        url: "/api/v1/subactivity/coupons/update",
        data: payload,
      });
      return { success: true };
    } catch (e: any) {
      if (e?.response.data.errorMsg) {
        throw new Error(e?.response.data.errorMsg);
      } else {
        throw e;
      }
    }
  },
  async offlineNBuyActivity(activityId: number | string) {
    try {
      await axios({
        baseURL: resolveAPIHost(),
        method: "PATCH",
        withCredentials: true,
        url: "/api/v1/activity/" + activityId + "/offline",
      });
      return { success: true };
    } catch (e: any) {
      if (e.response.status === 403) {
        return {
          success: false,
          errorCode: "NO_AUDIT_FOR_ACTIVITY",
        };
      } else {
        return {
          success: false,
          errorCode: "server-error",
          errorMessage: "系统异常，请稍后重试",
        };
      }
    }
  },
  async offlineSubActivity(activityId: number | string) {
    try {
      await axios({
        baseURL: resolveAPIHost(),
        method: "POST",
        withCredentials: true,
        url: "/api/v1/subactivity/" + activityId + "/offline",
      });
      return { success: true };
    } catch (e: any) {
      if (e.response.status === 403) {
        return {
          success: false,
          errorCode: "NO_AUDIT_FOR_ACTIVITY",
        };
      } else {
        return {
          success: false,
          errorCode: "server-error",
          errorMessage: "系统异常，请稍后重试",
        };
      }
    }
  },
  async offlineMainActivity(mainActivityId: number | string) {
    try {
      await axios({
        baseURL: resolveAPIHost(),
        method: "POST",
        withCredentials: true,
        url: "/api/v1/newcreate/offline/" + mainActivityId,
      });
      return { success: true };
    } catch (e: any) {
      if (e.response.status === 403) {
        return {
          success: false,
          errorCode: "NO_AUDIT_FOR_TEMPLATE",
        };
      } else {
        return {
          success: false,
          errorCode: "server-error",
          errorMessage: "系统异常，请稍后重试",
        };
      }
    }
  },
};

interface CreateSubActivityPayloadBase {
  activityType: number;
  investmentType: number;
  investmentSchema: number;
  [prop: string]: any;
}

function trackCreateSubActivity(
  startTime: number | undefined,
  endTime: number,
  payload: CreateSubActivityPayloadBase
) {
  try {
    const { activityType, investmentType, investmentSchema, templateId } =
      payload;
    // @ts-ignore
    if (startTime && window.AESPluginEvent) {
      // @ts-ignore
      window.AESPluginEvent("create-sub-activity", {
        et: "CLK",
        c1: activityType,
        c2: endTime - startTime,
        c3: investmentType,
        c4: investmentSchema,
        c5: templateId,
        c6: JSON.stringify(globalState?.sceneInfo)
      });
    }
  } catch (e) {
    console.error(e);
  }
}

// 是否是禁写时段
const doCheckForbidden = async () => {
  return await aslcClient.fetch({
    apiKey:
      "ele-newretail-investment.NewretailInvestmentForbiddenWriteYundingService.isKunlunForbiddenWrite",
  });
};

export const checkForbidden = async () => {
  return doCheckForbidden().then((res) => res?.data);
};

export const subActivity = {
  // 获取招商活动类型信息
  queryInvestmentMetadata: async () => {
    return await aslcClient.fetch({
      apiKey:
        "ele-newretail-investment.NewretailInvestmentYundingCommonService.queryInvestmentMetadata",
    });
  },
  // 通过活动id查询已绑定的场景列表
    getInvestmentActivityBoundSceneList: async(activityId: number | string) => {
    return await aslcClient.fetch({
      apiKey: 'ele-newretail-alpha.SceneYundingService.getInvestmentActivityBoundSceneList',
      params: [{ activityId, page: 1, pageSize: 200 }],
    });
  },

  // 主活动获取 子活动类型和对应补贴类型
  getInvestmentTemplateSubsidyType: async (templateId: number) => {
    return await aslcClient.fetch({
      apiKey:
        "ele-newretail-investment.NewretailInvestmentTemplateYundingService.getInvestmentTemplateSubsidyType",
      params: [templateId],
      });
  },

  // 修改库存（玩法组件viewJson格式）
  updateStockForViewJson: async (activityId: number, totalCountLimit: number, dayCountLimit?: number, upcCode?: number) => {
      return await aslcClient.fetch({
        apiKey:
          "ele-newretail-investment.NewretailInvestmentActivityYundingService.modifySinglePromotionActivityLimitRule",
        params: [{ activityId, totalCountLimit, dayCountLimit, upcCode }],
      });
  },
  // 修改库存(玩法组件 组件格式)，增加授权审核人
  updateStock: async (activityId: number, dataMap: any) => {
      return await aslcClient.fetch({
        apiKey:
          "ele-newretail-investment.NewretailInvestmentActivityYundingService.updateInvestmentActivity",
        params: [{ activityId, dataMap }],
      });
  },

  offline: async (activityId: number | string) => {
    return await aslcClient.fetch({
      apiKey:
        "ele-newretail-investment.NewretailInvestmentActivityYundingService.offlineInvestmentActivity",
      params: [activityId],
    });
  },
  getDetail: async (activityId: number | string) => {
    const [currentTime, res] = await Promise.all([
      aslcClient
        .fetch({
          apiKey:
            "ele-newretail-investment.NewretailInvestmentYundingSupportService.getCurrentDateTime",
        })
        .then(
          (_res) => _res.data,
          () => Date.now()
        ),
      aslcClient.fetch({
        apiKey:
          "ele-newretail-investment.NewretailInvestmentActivityYundingService.getInvestmentActivity",
        params: [activityId],
      }),
    ]);
    if (res?.data) {
      const isPending =
        currentTime <
        new Date(res.data?.investmentActivityDetailDTO?.beginTime).getTime();
      // 活动开始时间是否未到
      res.data.isPending = isPending;
    }
    return res;
  },
  // 获取玩法组件对应model
  getComponentDataModel: async (workspace: string, model: string) => {
    const resp = await boreasRequest({
      method: 'GET',
      url: `/v1/metadata/workspaces/${workspace}/models/${model}`
    })
    return resp.data?.data
  },
  // 算法招商提交前校验
  doValidateBatchCreate: async (data: any) => {
    return await aslcClient.fetch({
      apiKey:
        "ele-newretail-investment.NewretailInvestmentActivityAssistantYundingService.validateBatchCreate",
      params: [ data ],
    });
  },
  async validateBatchCreate(data: any) {
    return this.doValidateBatchCreate({
      ...data,
      idempotentKey: "" + globalState.createActivitySessionId,
    });
  },
  doBatchCreateInvestmentActivity: async (data: any) => {
    return await aslcClient.fetch({
      apiKey:
        "ele-newretail-investment.NewretailInvestmentActivityAssistantYundingService.batchCreateInvestmentActivity",
      params: [ data ],
    });
  },
  // 算法招商活动创建接口
  async batchCreateInvestmentActivity(data: any) {
    trackCreateSubActivity(
      globalState.createActivityStartTime,
      Date.now(),
      data as CreateSubActivityPayloadBase
    );
    return await this.doBatchCreateInvestmentActivity({
      ...data,
      idempotentKey: "" + globalState.createActivitySessionId,
    });
  },
  doCreate: async (data: any) => {
    return await aslcClient.fetch({
      apiKey:
        "ele-newretail-investment.NewretailInvestmentActivityYundingService.createInvestmentActivity",
      params: [data],
    });
  },

  async create(data: any) {
    trackCreateSubActivity(
      globalState.createActivityStartTime,
      Date.now(),
      data as CreateSubActivityPayloadBase
    );
    const forbidden = await checkForbidden();
    if (forbidden) {
      return {
        success: false,
        errorMessage: "很抱歉该时间段内不支持操作",
      };
    }
    let params = {
      ...data,
      // TODO: 待和后端确定被复制活动 id
      copyActivityId: globalState?.copy
        ? globalState.copyActivityId
        : undefined,
      idempotentKey: "" + globalState.createActivitySessionId,
      activitySceneId: globalState.activitySceneId || undefined,
      activityPlanId: globalState.activityPlanId || undefined,
    }
    if(globalState.activitySceneId) {
      params = {
        ...params,
        activityChannel: 30, // alpha平台创建的活动
      }
    }
    const res = await this.doCreate(params);

    if (!res.success) {
      try {
        // 子活动创建异常 arms监控
        MonitorFusion?.logCustomEvent({
          code: "activity_create_err",
          msg: res.errorMessage,
          c1: "sub activity",
          c2: `${ActivityTypeEnumStr[data.activityType]}：${data.activityType}`,
          c3: JSON.stringify(data),
        });
      } catch (err) {
        debug(`埋子活动创建异常点报错：${err}`);
        console.log("埋子活动创建异常点报错：", err);
      }
    } else {
      sandbox(() => {
         //子活动创建 arms监控
         MonitorFusion?.logCustomEvent({
          code: "creat_action",
          msg: res,
          c1: "sub activity",
          c2: `${ActivityTypeEnumStr[data.activityType]}：${data.activityType}`,
          c3: JSON.stringify(data),
        });
      });
    }
    return res;
  },

  checkActDiscountByMarketingRule: async (data: CheckActDiscountParams | CheckActDiscountParamsV2): Promise<CheckActDiscountResponse> => {
    const res = await aslcClient.fetch({
      apiKey: "ele-newretail-investment.NewretailInvestmentActivityYundingService.checkActDiscountByMarketingRule",
      params: [data]
    })
    return res
  },


  getList: async (data: any) => {
    return await aslcClient.fetch({
      apiKey:
      "ele-newretail-investment.NewretailInvestmentActivityYundingService.getInvestmentActivityList",
      params: [{...data, queryActivityChannel: 20}],
    });
  },

  getCommodityExcelUrl: async (activityId: number | string) => {
    return await aslcClient.fetch({
      apiKey:
      "ele-newretail-investment.NewretailInvestmentActivityInviteYundingService.exportInviteUpc",
      params: [activityId],
    });
  },

  // 预校验被复制的活动信息
  async validateCopyActivityData(payload: {
    copyActivityId: number | string;
    [prop: string]: any;
  }): Promise<{
    errorCode?: string;
    errorMessage?: string;
    data?: {
      result: boolean;
      message?: string
    }
  }> {
    const res = await aslcClient.fetch({
      apiKey: 'ele-newretail-investment.NewretailInvestmentActivityYundingService.checkActivityCreatRequest',
      params: [payload]
    })
    return res
  },
};

/**
 * 招商主活动相关接口
 */
export const investmentTemplate = {
  async getDetail(templateId: string | number){
    const res = await aslcClient.fetch({
      apiKey: "ele-newretail-investment.NewretailInvestmentTemplateYundingService.getInvestmentTemplateInfo",
      params: [templateId]
    })
    return res
  },

  async getList(data: any){
    const res = await aslcClient.fetch({
      apiKey: 'ele-newretail-investment.NewretailInvestmentTemplateYundingService.getInvestmentTemplateList',
      params: [{...data, queryActivityChannel: 20}]
    })
    return res
  },

  async create(data: any){
    const res = await aslcClient.fetch({
      apiKey: "ele-newretail-investment.NewretailInvestmentTemplateYundingService.createInvestmentTemplate",
      params: [data]
    })
    return res
  },


  async offline(templateId: number){
    const res = await aslcClient.fetch({
      apiKey: 'ele-newretail-investment.NewretailInvestmentTemplateYundingService.offLineInvestmentTemplate',
      params: [templateId]
    })
    return res
  },
};

const doGetAuditRuleById = async (ruleId: string | number) => {
  return await aslcClient.fetch({
    apiKey: 'ele-newretail-investment.NewretailInvestmentAuditRuleYundingService.getInvestmentAuditRuleId',
    params: [ ruleId ]
  })
}

export function getAuditRuleById(ruleId: string | number) {
  return doGetAuditRuleById(ruleId).then((res) => res?.data);
}

// 活动参与库存
export const queryActivityParticipantStock = async (data: {
  sellerId: number;
  storeId: number;
  activityId: number;
}) => {
  return await aslcClient.fetch({
    apiKey:
      "ele-newretail-investment.NewretailInvestmentParticipantYundingService.queryActivityParticipantStock",
    params: [data],
  });
};

export const getShopPoolList = async (search: string) => {
  return await aslcClient.fetch({
    apiKey:
      "ele-newretail-investment.NewretailInvestmentActivityShopPoolYundingService.getShopPoolList",
    params: [search]
  });
};

export interface BindSceneParams {
  investmentActivityId: string | number;
  sceneIdList: number[];
}

export interface QuerySceneListParams {
  activityId: string | number;
  activityType?: number;
  sceneId?: number;
  sceneName?: string;
  owner?: string;
  page?: number;
  pageSize?: number;
}

export const alpha = {

  // 获取可绑定的场景列表
  async getInvestmentActivitySignUpSceneList(params: QuerySceneListParams) {
    console.log('params=======', params, );

    const res = await aslcClient.fetch({
      apiKey: 'ele-newretail-alpha.SceneYundingService.getInvestmentActivitySignUpSceneList',
      params: { ...params },
    });
    return res;
  },

  // 招商活动绑定活动场景ID
  async investmentActivityBindScene(params: BindSceneParams) {
    const res = await aslcClient.fetch({
      apiKey:
        'ele-newretail-alpha.RelationYundingService.investmentActivityBindScene',
      params: { ...params },
    });
    return res;
  },
  // 根据场景Id查场景
  async getSceneBySceneId(sceneId: string | number) {
    const res = await aslcClient.fetch({
      apiKey:
        'ele-newretail-alpha.SceneYundingService.getSceneBySceneId',
      params: [+sceneId],
    });
    return res;
  },
  
};

export async function checkPermission(permission: string) {
  const res = await aslcClient.fetch({
    apiKey: "ele-newretail-investment.NewretailInvestmentActivityYundingService.checkPermission",
    params: [permission],
  });
  return res
}

// 玩法表单图片上传接口，用于单品券和特价券活动，普通上传图片文件的功能，不要用这个接口
export async function uploadPictureForComponentForm(content: string) {
  const res = await aslcClient.fetch({
    apiKey: "ele-newretail-investment.NewretailInvestmentActivityYundingService.uploadPicture",
    params: [content],
  });
  return res
}

export interface BudgetItem {
  budgetId: string;
  budgetPoolName: string;
  isAvailable: boolean;
}

// 如果是子活动，必须传预算类型参数
export async function getBudgetList(options: {
  bizDate: number;
  type?: 0 | 1 | 2; // all：0, 活动：1， 券：2，默认是 0
}): Promise<{ data?: BudgetItem[]; }> {
  const res = await aslcClient.fetch({
    apiKey:
      "ele-newretail-investment.NewretailInvestmentBudgetYundingService.getBudgetList",
    params: [options],
  });

  return res;
}

// 通过search查询用户信息 ——授权审核人
export async function getUserInfoBySearch(
  searchKey: string | undefined
): Promise<{ data?: { empId: string; lastName: string }[] }> {
  const res = await aslcClient.fetch({
    apiKey:
      'ele-newretail-investment.NewretailInvestmentUserInfoYundingService.getUserInfoBySearch',
    params: [{ searchKey, pageSize: 20 }],
  });

  return res.data;
}

// 通过search查询用户信息 ——授权审核人
export async function updateInvestmentActivity(
  auditMemberList: { empId: string; lastName: string, cname: string }[],
  activityId: string
): Promise<{ data?: boolean; success?: boolean; errorMessage?: string }> {
  const res = await aslcClient.fetch({
    apiKey:
      'ele-newretail-investment.NewretailInvestmentActivityYundingService.updateInvestmentActivity',
    params: [{ auditMemberList, activityId }],
  });

  return res;
}


//  获得是否能使用"无需审核功能"接口
export async function checkAutoAuditPermission(): Promise<{ data?: boolean; }> {
  const res = await aslcClient.fetch({
    apiKey:
      "ele-newretail-investment.NewretailInvestmentActivityYundingService.checkAutoAuditPermission",
  });

  return res;
}

//  设置活动推荐度
export async function batchSettingActivityRecommendation(options: {
  recommendation: number | string;
  activityIdList: Array<number | string>;
}): Promise<{
  data?: {
    data: { setFailReason?: string; success: boolean; }[];
    failedNum: number;
    successNum: number;
    total: number;
  };
  errorMessage?: string;
  success: boolean;
}> {
  const res = await aslcClient.fetch({
    apiKey:
      'ele-newretail-investment.NewretailInvestmentActivityYundingService.batchSettingActivityRecommendation',
    params: [options],
  });

  return res;
}

//  单品叠加券修改库存
export async function modifyActivityLimitRule(options:any): Promise<{ data?: boolean; }> {
  const res = await aslcClient.fetch({
    apiKey:
      "ele-newretail-investment.NewretailInvestmentActivityYundingService.modifyActivityLimitRule",
    params: [options],
  });

  return res;
}

//  外投购物金修改补贴和发放数量
export async function modifySinglePromotionActivityLimitRule(options:any): Promise<{ data?: boolean; }> {
  const res = await aslcClient.fetch({
    apiKey:
      "ele-newretail-investment.NewretailInvestmentActivityYundingService.modifySinglePromotionActivityLimitRule",
    params: [options],
  });

  return res;
}

export async function uploadRichText(params: any) {
  const res = await aslcClient.fetch({
    apiKey:
      "ele-newretail-investment.NewretailInvestmentActivityYundingService.uploadActivityRichTextDescription",
    params: [params],
  });

  return res;
}
