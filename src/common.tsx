import * as qs from 'querystring';
import { useMemo, useReducer, useState } from 'react';
import { useLocation } from "react-router";
import { Dialog } from '@alifd/next';
const debug = require('debug')('zs:common');

declare global {
  interface Window {
    ENV: {
      env: string
    }
  }
}

export function resolveEnv(): 'daily' | 'pre' | 'prod' {
  // 注意：只有 resolveEnv 和 src/config.ts 可以使用 window.ENV.env，其他地方想要识别当前是什么环境必须通过 resolveEnv 方法
  const env = window.ENV.env 
  if (env === 'prod' || env === 'pre' || env === 'daily') {
    return env
  }
  if (env === 'ppe') {
    return 'pre'
  }
  if (env === '{{ nrEnv }}') {
    // window.ENV.env 未填充，说明当前是本地环境
    return 'pre'
  }

  return 'daily' // 如果 window.ENV.env 未设置，默认返回 pre
}


export function useRouteQuery() {
  const location = useLocation();
  const query = useMemo(() => {
    return qs.parse(location.search.slice(1));
  }, [location.search]);

  return query;
}

export function useQuery<T>(defaultValue: T) {
  const [state, dispatch] = useReducer((_state: any, action: any) => {
    debug('useQuery pre handle action', action);

    let ret: { value: T, committed: T; };
    switch (action.type) {
      case 'update':
        ret = {
          ..._state,
          value: { ...action.payload }
        };
        break;
      case 'commit':
        ret = {
          ..._state,
          committed: { ..._state.value }
        };
        break;
      case 'resetAndCommit':
        ret = {
          value: { ...defaultValue },
          committed: { ...defaultValue }
        };
        break;
      default:
        throw new Error('unsupproted action: ' + action.type);
    }

    debug('useQuery post handle action', action, ret);
    return ret;
  }, {
    value: defaultValue,
    committed: defaultValue
  });

  return {
    get value() {
      return state.value;
    },
    get committed() {
      return state.committed;
    },
    update(value: T) {
      dispatch({ type: 'update', payload: value });
    },
    resetAndCommit() {
      dispatch({ type: 'resetAndCommit' });
    },
    commit() {
      dispatch({ type: 'commit' });
    },
    reset() {
      dispatch({ type: 'update', payload: defaultValue });
    }
  };
}


interface PGValue {
  page: number;
  pageSize: number;
}

export interface PG {
  value: PGValue;
  reset(): void;
  changePageSize(pageSize: number): void;
  changePage(page: number): void;
}

export function usePagination(defaultValue: PGValue = { page: 1, pageSize: 20 }): PG {
  const [pagination, setPagination] = useState({ ...defaultValue });
  return {
    value: pagination,
    changePage(page: number) {
      setPagination((prev) => {
        return {
          ...prev,
          page
        };
      });
    },
    reset() {
      setPagination({ ...defaultValue });
    },
    changePageSize(pageSize: number) {
      setPagination(prev => {
        const firstIndex = (prev.page - 1) * prev.pageSize;
        let page = Math.ceil(firstIndex / pageSize);
        if (firstIndex % pageSize === 0) {
          page++;
        }
        return { page, pageSize };
      });
    }
  };
}

export function percentage(num: any, total: any) {
  num = parseFloat(num);
  total = parseFloat(total);
  if (isNaN(num) || isNaN(total)) {
    return "-";
  }
  return total <= 0
    ? "0%"
    : (Math.round((num / total) * 10000) / 100.0).toFixed(1) + "%";
}


export function confirmDialog(options: any) {
  return new Promise(resolve => {
    Dialog.show({
      ...options,
      onOk: () => resolve(true),
      onCancel: () => resolve(false)
    });
  });
}


export function maskMS(v: number) {
  return v - v % 1000;
}


export function isValueSet(v: any) {
  return v !== '' && v !== null && v !== undefined
}

export function parseJsonSafe(JSONobj: any) {
  let res;
  try {
    res = JSON.parse(JSONobj);
  } catch (e) {
    res = JSONobj
  }
  return res
}

export function renderWeekDay(weekday: string) {
  const weeks = `${weekday}`.split(',');
  const weekText = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];

  if (weeks.length === 7) {
    return '整周';
  } else {
    return weeks.sort().map((item: any) => weekText[item]).join(',');
  }
}

// 时间段渲染
export function renderTimeRange({ start, end }: any) {
  const startRes = start.split(':').slice(0, 2).join(':');
  const endRes = end.split(':').slice(0, 2).join(':');

  if (startRes === '00:00' && endRes === '23:59') {
    return '全天';
  } else {
    return `每天${startRes}~${endRes}`;
  }
}
