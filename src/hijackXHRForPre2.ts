/**
 * 如果当前在预发二套环境上，将所有的 node 层接口调用地址跳转成 pre-zs2.kunlun.alibaba-inc.com
 * 业务代码不感知二套环境
 */
export default function hijackXHRForPre2() {
  const originOpen = XMLHttpRequest.prototype.open
  XMLHttpRequest.prototype.open = function () {
    const args = Array.prototype.slice.call(arguments)
    const method = args[0]
    let url: string = args[1]
    if (url.startsWith('https://pre-zs.kunlun.alibaba-inc.com')) {
      url = url.replace('https://pre-zs', 'https://pre-zs2')
    }

    // @ts-ignore
    originOpen.apply(this, [method, url, ...args.slice(2)])
  };
}
