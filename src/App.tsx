import '@ali/boreas2/lib/marketing/std.css';
import "./style.scss";

import { MonitorFusion } from '@ali/monitor-fusion';
import { Loading } from '@alifd/next';
import React, { lazy, Suspense, useEffect } from 'react';
import { Route } from 'react-router';
import { HashRouter as Router } from 'react-router-dom';

import * as api from "./api";
import { resolveEnv } from './common';
import ZsErrorBoundary from './components/ZsErrorBoundary';
import config from './config';
import { Permission } from './constants';
import hijackXHRForPre2 from './hijackXHRForPre2';
import { permissionRequired } from './pages/common';
import { sandbox } from './utils';

if (
  window.location.hostname === 'pre-zs2.kunlun.alibaba-inc.com' ||
  /env=pre2/.test(window.location.href)) {
  hijackXHRForPre2()
}

declare global {
  interface Window { yundingFlag?: boolean; }
}

const env = resolveEnv()
try {
  if (env === 'daily') {
    // 为组件设置日常环境的接口地址
    // @ts-ignore
    window[Symbol.for('zs-node-daily-api-host')] = `https://${config.daily.hostname}`
  }
} catch(e) {
  console.error(e)
}

sandbox(() => {
  const options = {
    pid: 'kunlun-zs',
    errorEnable: true,
    version: CDN_VERSION,
    env: env,
    ignoreErrors: [
      /Reflect.ownKeys called on non-object/, 
      /Script error./
    ],
  }
  MonitorFusion.init(options)
})

function lazyImport(path: string) {
  return lazy(() => import(`${path}`));
}

// 云鼎迁移已全量，后续接口直接走云鼎
window.yundingFlag = true

function goNewHost(_App: React.ComponentType) {
  const needGoNewHost = ["pre", "prod"].includes(env) && /zs.kunlun.alibaba-inc.com/.test(window.location.host);
  if (!needGoNewHost) {
    return _App;
  } else {
    const host =
      env === "pre"
        ? "pre-invite.kunlun.alibaba-inc.com"
        : "invite.kunlun.alibaba-inc.com";
    window.location.replace(`https://${host}/${window.location.search}${window.location.hash}`)
    return () => {
      return <></>
    };
  }
}

function App() {
  useEffect(() => {
    api.getUserInfo().then((res) => {
      MonitorFusion?.updateConfig({
        uid: res.data.userid,
      });
    }).catch(() => {
    });
  }, []);
  useEffect(() => {
    sandbox(() => {
      // 监控访问域名 arms监控
      MonitorFusion?.logCustomEvent({
        msg: `${window.location.host}`,
        code: "access_domain",
        c1: `${window.location.href}`,
      });
    });
  }, []);

  return (
    <ZsErrorBoundary source="root">
      <Suspense fallback={<Loading style={{ height: 'calc(100vh - 80px)', display: 'block' }} tip="加载中..." />}>
        <Router basename={config.basename}>
          <Route path="/activity" exact component={lazyImport('./pages/HomePage')} />
          <Route path="/sub-activity/:id/:tab?" exact component={lazyImport('./pages/SubActivity')} />
          <Route path="/main-activity/:id" exact component={lazyImport('./pages/MainActivity')} />
          <Route path="/create-sub-activity" exact component={lazyImport('./pages/CreateSubActivity')} />
          <Route path="/create-main-activity" exact component={lazyImport('./pages/CreateMainActivity')} />
          <Route path="/create-sub-activity-for-alpha" exact component={lazyImport('./pages/CreateActivityForAlpha')} />

          {/* 研发会用到的页面小工具 */}
          <Route path="/tools/recipes" exact component={lazyImport('./pages/DebugRecipes')} />
          <Route path="/tools/recipe-guard" exact component={lazyImport('./pages/RecipeGuard')} />
        </Router>
      </Suspense>
    </ZsErrorBoundary>
  );
}

export default goNewHost(permissionRequired(App, Permission.BUSINESS_INVITATION_SYSTEM_AUTHORITY));
