// 变量定义
$border-color: #e6e6e6;
$background-color: #fff;
$header-background: #f2f3f7;
$primary-color: #ff6a00;
$text-color: #333;
$text-light: #999;

.fundingPlanForm {
  padding: 20px;
  background-color: $background-color;
}

.formSection {
  margin-bottom: 24px;
}

.sectionTitle {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 16px;
}

.tierTableContainer {
  border: 1px solid $border-color;
  border-radius: 4px;
  box-sizing: border-box;
  overflow-x: auto;
  background-color: #f7f8fa;
}
.tierTableContent {
    min-width: 1930px;
}

.tierTableHeader {
  padding: 8px 0;
  font-weight: 500;
  background-color: $header-background;
}

.tierTableBody {
  background-color: $background-color;
}

.tierRow {
  border-bottom: 1px solid $border-color;
  background-color: $background-color;
  &:last-child {
    border-bottom: none;
  }
}
.tierFlagContent {
  padding: 8px;
  border-right: 1px solid $border-color;
}
.tierContent {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 8px;
  border-right: 1px solid $border-color;
}

.step {
  min-width: 200px;
}
.shopSubsidy {
  min-width: 300px;
}
.platformSubsidy {
  min-width: 150px;
}
.raiseShopSubsidy {
  min-width: 300px;
}
.spareFlag {
  min-width: 360px;
}
.dayCountLimit {
  min-width: 150px;
}
.mustShow {
  min-width: 120px;
}
.budget {
  min-width: 250px;
}
.operation {
  min-width: 100px;
}

.tierName {
  margin-right: 20px;
  margin-bottom: 16px
}

.addTierButton {
  padding: 16px;
  display: flex;
  justify-content: center;
  background-color: $background-color;
  border-top: 1px solid $border-color;
}

.activityDisplay {
  margin-top: 30px;
}

.activityType {
  margin-bottom: 24px;

  h4 {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 12px;
  }
}

.activityDescription {
  margin-bottom: 16px;
  line-height: 24px;
}

.couponExamples {
  display: flex;
  gap: 16px;
  margin-top: 16px;
}
.image{
  width: 200px;
}

.coupon {
  width: 200px;
  height: 80px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  padding: 12px;
  position: relative;
  background-color: $background-color;

  &:before {
    content: "";
    position: absolute;
    right: 0;
    top: 0;
    width: 30px;
    height: 30px;
    background-color: $primary-color;
    opacity: 0.1;
    border-radius: 0 0 0 30px;
  }
}

.couponAmount {
  color: $primary-color;
  font-size: 20px;
  font-weight: bold;
}

.couponDesc {
  font-size: 14px;
  margin-top: 4px;
}

.couponCondition {
  font-size: 12px;
  color: $text-light;
  margin-top: 4px;
}

.formActions {
  display: flex;
  justify-content: center;
  margin-top: 40px;
}
