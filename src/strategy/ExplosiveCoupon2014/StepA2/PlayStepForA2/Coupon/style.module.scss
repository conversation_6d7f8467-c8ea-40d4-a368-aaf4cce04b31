// 变量定义
$primary-color: #ff5722;
$text-color: #333;
$light-gray: #f5f5f5;
$border-color: #eee;
$button-color: #ff5722;

.coupon {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  max-width: 370px;
  padding: 16px;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  border: 1px solid $border-color;
  margin-bottom: 15px;
}

.content {
  display: flex;
  align-items: center;
  flex: 1;
}

.amountWrapper {
  color: $primary-color;
  margin-right: 20px;
}

.currency {
  font-size: 14px;
  font-weight: 500;
}

.amount {
  font-size: 24px;
  font-weight: bold;
}

.info {
  display: flex;
  flex-direction: column;
}

.title {
  font-size: 14px;
  font-weight: 500;
  color: $text-color;
  margin-bottom: 4px;
}

.condition {
  font-size: 12px;
  color: #999;
}

.action {
  margin-left: 16px;
}

.claimButton {
  background-color: $button-color;
  color: white;
  border: none;
  border-radius: 20px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
}

