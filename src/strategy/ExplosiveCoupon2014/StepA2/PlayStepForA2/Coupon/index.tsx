import React from "react";
import styles from "./style.module.scss";
import { isValueSet } from "common";

interface CouponProps {
  /**
   * 红包面额
   */
  amount: number;

  /**
   * 使用门槛（满多少可用）
   */
  threshold: number;
  availableText: string;
}

/**
 * 店铺专享红包组件
 */
const Coupon: React.FC<CouponProps> = ({ amount, threshold, availableText }) => {
  const _threshold = isValueSet(threshold) ? threshold : "-";
  const _amount = isValueSet(amount) ? amount : "-";
  return (
    <div className={styles.coupon}>
      <div className={styles.content}>
        <div className={styles.amountWrapper}>
          <div>
            <span className={styles.currency}>¥</span>
            <span className={styles.amount}>{_amount}</span>
          </div>

          <div className={styles.condition}>满{_threshold}可用 </div>
        </div>
        <div className={styles.info}>
          <div className={styles.title}>店铺专享红包</div>
          <div className={styles.condition}>{availableText}</div>
        </div>
      </div>
      <div className={styles.action}>
        <button className={styles.claimButton}>领取</button>
      </div>
    </div>
  );
};

export default Coupon;
