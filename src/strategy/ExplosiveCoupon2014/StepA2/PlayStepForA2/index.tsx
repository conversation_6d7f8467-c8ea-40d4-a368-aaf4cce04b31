import * as React from "react";
import {
  Form,
  Radio,
  Button,
  Icon,
  Checkbox,
  Grid,
  Message,
  Field,
  NumberPicker,
  DatePicker,
} from "@alifd/next";
import styles from "./style.module.scss";
import { BudgetPickerEdit } from "@alife/carbon-biz-kunlun-zs/lib/components/BudgetPicker/BudgetPicker";
import { isValueSet, resolveEnv } from "common";
import Coupon from "./Coupon";
import BigNumber from "bignumber.js";
import moment from "moment";
import UseTimeSet, { TimeRangeSelectorValue } from "components/formControls/UseTimeSet";
import RadioWithInput from "components/formControls/RadioWithInput";
import {
  UserScopeEnum,
  UserScopeSelect,
} from "components/formControls/UserScopeSelect";
import { fmt, normalizeTimestampToDate } from "../helper";
import { v4 as uuid4 } from "uuid";

const { Row, Col } = Grid;
const FormItem = Form.Item;
const RadioGroup = Radio.Group;

const  BUDGET_TYPE_INDEPENDENT  = "1"
const SUPER_MARKETING_CHANNEL = "yt1_qgka";
interface Budget {
  budgetId: string;
  budgetName: string;
}
interface TierData {
  id: string;
  conditionCny?: number;
  shopSubsidyCny?: number;
  shopSubsidyMaxCny?: number;
  platformSubsidyCny?: number;
  raiseShopSubsidyPercent?: number;
  mustShow?: boolean;
  spareFlag?: boolean;
  budgetInfo?: Budget;
  // 券有效期
  couponValid?: "fromDraw" | "validRange";
  fromDraw?: number;
  validRange?: [moment.Moment, moment.Moment];
  // 使用时段
  exchangeTimeRangeList?: TimeRangeSelectorValue;
  // 领券限制
  drawLimit?: {
    totalLimit?: number;
    dayLimit?: number;
  };
  dayCountLimit?: string;
}

export interface PlayDataTS {
  budgetType: "1" | "2";
  tiers: TierData[];
  budgetInfo?: Budget;
  userScope?: number;
}
interface BudgetPickerEditProps {
  env: "prod" | "pre" | "daily";
  value?: Budget;
  onChange?: (value?: Budget) => void;
  className?: string;
  style?: React.CSSProperties;
  hasClear?: boolean;
  bizDate?: number;
  budgetType?: "ACTIVITY" | "COUPON";
  subsidyType?: "b" | "p" | "c" | "d";
  disabled?: boolean;
  templateId?: number | string;
  activityType?: number;
}

interface FundingPlanFormProps {
  value?: PlayDataTS;
  onChange?: (value: PlayDataTS) => void;
  onEvent?: (event: { type: string }) => void;
  budgetPickerProps: Omit<BudgetPickerEditProps, "env">;
  manageOrgList?: any; // 业务线
  endTime?: moment.Moment;
}

const defaultTier = (index: number): TierData => ({
  id: Date.now().toString() + index,
  conditionCny: undefined,
  shopSubsidyCny: undefined,
  shopSubsidyMaxCny: undefined,
  platformSubsidyCny: undefined,
  raiseShopSubsidyPercent: undefined,
  mustShow: false,
  spareFlag: false,
  // 券有效期默认值
  couponValid: "fromDraw",
  fromDraw: undefined,
  validRange: undefined,
  // 使用时段默认值
  exchangeTimeRangeList: {
    timeType: "allDay",
    timeRanges: [],
  },
  // 领券限制默认值
  drawLimit: {
    dayLimit: undefined,
  },
});

const defaultFormData: PlayDataTS = {
  budgetType: "1",
  tiers: [defaultTier(0)],
  userScope: 0,
};
const formItemLayout = {
  labelCol: { span: 4, style: { color: "#333" } },
  wrapperCol: { span: 20 },
};

function getFieldDisplayName(field: string): string {
  const map: Record<string, string> = {
    conditionCny: "阶梯券门槛",
    shopSubsidyCny: "商家默认补贴金额",
    shopSubsidyMaxCny: "商家补贴上限",
    platformSubsidyCny: "平台补贴金额",
  };
  return map[field] || field;
}

const PlayStepForA2: React.FC<FundingPlanFormProps> = ({
  value = defaultFormData,
  onChange,
  onEvent,
  budgetPickerProps,
  manageOrgList,
  endTime
}) => {
  const field = Field.useField({
    // @ts-ignore
    values: value,
    onChange: (name: string) => {
      if (name === "budgetType") {
        field.setValue("budgetInfo", undefined);
      }
      if (onChange) {
        onChange(field.getValues());
      }
    },
  });

  const mustShowVisible = React.useMemo(() => {
    // 超市便利行业才能看到必现券
    return manageOrgList?.some(
      (item: any) => item.code === SUPER_MARKETING_CHANNEL
    );
  }, [manageOrgList]);

  // 验证单个档位的必填字段
  const validateTier = (tier: TierData) => {
    const errors: Record<string, string> = {};

    if (!isValueSet(tier.conditionCny)) {
      errors.conditionCny = "请输入满额";
    }

    if (!isValueSet(tier.shopSubsidyCny)) {
      errors.shopSubsidyCny = "请输入默认商户补贴";
    }

    if (!isValueSet(tier.shopSubsidyMaxCny)) {
      errors.shopSubsidyMaxCny = "请输入商户最高补贴";
    }

    if (!isValueSet(tier.platformSubsidyCny)) {
      errors.platformSubsidyCny = "请输入平台补贴金额";
    }

    if (!isValueSet(tier.raiseShopSubsidyPercent) && tier.shopSubsidyCny !== tier.shopSubsidyMaxCny) {
      errors.raiseShopSubsidyPercent = "请输入加码出资比";
    }
    if (!isValueSet(tier.dayCountLimit)) {
      errors.dayCountLimit = "请填写最低每日库存限制";
    }

    if (tier.shopSubsidyCny === 0 && tier.platformSubsidyCny === 0) {
      errors.shopSubsidyCny = "商户补贴和平台补贴不能同时为0";
    }
    const shopSubsidyCny = new BigNumber(tier.shopSubsidyCny!);
    const shopSubsidyMaxCny = new BigNumber(tier.shopSubsidyMaxCny!);
    if (shopSubsidyCny.gt(shopSubsidyMaxCny)) {
      errors.shopSubsidyCny = "商户最高补贴需要大于商户默认补贴";
    }
    const shopSubsidyTotal = new BigNumber(tier.shopSubsidyCny!).plus(
      new BigNumber(tier.platformSubsidyCny!)
    );
    const condition = new BigNumber(tier.conditionCny!);
    if (shopSubsidyTotal.gt(condition) && condition.gt(0)) {
      // 在门槛等于0的时候，不校验此条规则：林漠
      errors.shopSubsidyCny = "商户补贴+平台补贴不能大于门槛";
    }
    // 当商户默认出资和商户最高出资不相等的时，商户加码出资比不能为0%
    if (
      tier.shopSubsidyCny !== tier.shopSubsidyMaxCny &&
      tier.raiseShopSubsidyPercent === 0
    ) {
      errors.raiseShopSubsidyPercent = "商户加码出资比不能为0%";
    }

    const budgetType = field.getValue("budgetType");
    if (
      budgetType === "2" &&
      !tier?.budgetInfo?.budgetId &&
      tier.platformSubsidyCny
    ) {
      errors.budgetInfo = "请输入预算";
    }

    // 兜底券相关校验
    if (tier.spareFlag) {
      // 券有效期校验
      if (!tier.couponValid) {
        errors.couponValid = "请选择券有效期类型";
      } else if (
        tier.couponValid === "fromDraw" &&
        !isValueSet(tier.fromDraw)
      ) {
        errors.fromDraw = "请输入自领取日期起生效天数";
      } else if (tier.couponValid === "validRange") {
        if (!tier.validRange?.[0] || !tier.validRange?.[1]) {
          errors.validRange = "请选择指定生效日期范围";
          return errors;
        } else if (
          budgetPickerProps?.bizDate &&
          tier.validRange?.[0]?.valueOf() < budgetPickerProps.bizDate
        ) {
          errors.validRange = "红包使用的开始日期应该大于等于活动开始日期";
          return errors;
        } else if (endTime && tier.validRange?.[1]?.valueOf() < normalizeTimestampToDate(endTime?.valueOf())) {
          errors.validRange = "红包使用的结束日期应大于等于活动结束日期";
          return errors;
        } else if ( tier.validRange?.[1]?.valueOf() - tier.validRange?.[0]?.valueOf() > 90 * 24 * 60 * 60 * 1000) {
          errors.validRange = "红包的使用日期不能大于90天";
          return errors;
        }
      }

      // 使用时段校验
      if (!tier.exchangeTimeRangeList?.timeType) {
        errors.timeType = "请选择使用时段类型";
      } else if (tier.exchangeTimeRangeList?.timeType === "custom") {
        const v = tier.exchangeTimeRangeList;
        const timeRanges = v?.timeRanges || [];
        for (let i = 0; i < timeRanges.length; i++) {
          const item = timeRanges[i];
          if (!item?.start || !item?.end) {
            errors.timeType = "请选择自定义红包使用时段";
            return errors;
          }
          const start = item.start.format("HH:mm");
          const end = item.end.format("HH:mm");
          if (start > end) {
            errors.timeType = "自定义红包使用时段开始时间不能大于结束时间";
            return errors;
          }
        }
        if (timeRanges.length === 2) {
          const r1 = timeRanges[0];
          const r2 = timeRanges[1];
          if (
            r1.end!.format("HH:mm") >= r2.start!.format("HH:mm") &&
            r2.end!.format("HH:mm") >= r1.start!.format("HH:mm")
          ) {
            errors.timeType = "自定义红包使用时段时段不能重叠";
            return errors;
          }
        }
      }

      // 领券限制校验
      if (!tier.drawLimit) {
        errors.drawLimit = "请选择领券限制类型";
      } else if ("totalLimit" in tier.drawLimit) {
        if (!isValueSet(tier.drawLimit.totalLimit)) {
          errors.drawLimit = "请输入活动期间每人限领";
          return errors;
        } else if (
          +tier.drawLimit.totalLimit! <= 0 ||
          +tier.drawLimit.totalLimit! > 30
        ) {
          errors.drawLimit = "活动期间每人限领范围是1-30";

          return errors;
        }
      }
      if ("dayLimit" in tier.drawLimit!) {
        if (!isValueSet(tier.drawLimit.dayLimit)) {
          errors.drawLimit = "请输每人每日限领";
          return errors;
        } else if (
          +tier.drawLimit.dayLimit! <= 0 ||
          +tier.drawLimit.dayLimit! > 30
        ) {
          errors.drawLimit = "每人每日限领范围是1-30";
          return errors;
        }
      }
      if (
        !("totalLimit" in tier.drawLimit!) &&
        !("dayLimit" in tier.drawLimit!)
      ) {
        errors.drawLimit = "请填写领券限制";
      }
    }

    return errors;
  };

  const budgetRequire = React.useMemo(() => {
    const budgetType = field.getValue("budgetType");
    const tableData: TierData[] = field.getValue("tiers") || [];
    return (
      budgetType === BUDGET_TYPE_INDEPENDENT &&
      tableData.some((i) => i.platformSubsidyCny && i.platformSubsidyCny > 0)
    );
  }, [JSON.stringify(field.getValue("tiers")), field.getValue("budgetType")]);

  const handleSubmit = () => {
    field.validate((errors, values) => {
      if (errors && Object.keys(errors).length > 0) {
        const key = Object.keys(errors)[0];
        const msg = errors[key].errors[0];
        Message.error(msg as string);
        return;
      }

      // 验证每个档位
      const tiers = values.tiers || [];
      let hasErrors = false;
      const tierErrors: Record<string, string>[] = [];
      
      const fieldsToValidate = [
        "conditionCny",
        "shopSubsidyCny",
        "shopSubsidyMaxCny"
      ] as const;

      const valueTrackers = fieldsToValidate.reduce(
        (acc, _field) => {
          acc[_field] = {
            lastValue: -Infinity,
            seenValues: new Set<number>(),
          };
          return acc;
        },
        {} as Record<
          (typeof fieldsToValidate)[number],
          {
            lastValue: number;
            seenValues: Set<number>;
          }
        >
      );

      (tiers as TierData[]).forEach((tier, index) => {
        const tierError = validateTier(tier);
        tierErrors[index] = tierError;

        fieldsToValidate.forEach((_field) => {
          const _value = tier[_field];
          if (_value === undefined) return;

          const tracker = valueTrackers[_field];

          // 检查重复值
          if (tracker.seenValues.has(_value)) {
            tierError[_field] = `${getFieldDisplayName(_field)}不能重复`;
            hasErrors = true;
          } else {
            tracker.seenValues.add(_value);
          }

          // 检查递增
          if (_value <= tracker.lastValue) {
            tierError[_field] = `${getFieldDisplayName(_field)}必须逐步增加`;
            hasErrors = true;
          }
          tracker.lastValue = _value;
        });

        if (Object.keys(tierError).length > 0) {
          hasErrors = true;
        }
      });

      if (hasErrors) {
        // 显示第一个错误
        for (let i = 0; i < tierErrors.length; i++) {
          const _errors = tierErrors[i];
          if (Object.keys(_errors).length > 0) {
            const firstError = Object.values(_errors)[0];
            Message.error(`档位${i + 1}: ${firstError}`);
            break;
          }
        }
        return;
      }

      if (
        values.budgetType === BUDGET_TYPE_INDEPENDENT &&
        !(values?.budgetInfo as Budget)?.budgetId &&
        budgetRequire
      ) {
        Message.error("请输入统一预算池ID");
        return;
      }
      if (onEvent) onEvent({ type: "next" });
    });
  };

  const addTier = () => {
    const tiers: TierData[] = field.getValue("tiers") || [];
    if (tiers.length >= 6) {
      Message.error("最多只能添加6个档位");
      return;
    }

    const newTiers = [...tiers, defaultTier(tiers.length)];
    field.setValue("tiers", newTiers);
  };

  const removeTier = (index: number) => {
    const tiers: TierData[] = field.getValue("tiers") || [];
    if (tiers.length <= 1) {
      Message.error("至少需要保留1个档位");
      return;
    }

    const newTiers = tiers.filter((_, i) => i !== index);
    field.setValue("tiers", newTiers);
  };
  const handleTierChange = (
    index: number,
    key: keyof TierData,
    _value: any
  ) => {
    const tiers = [...((field.getValue("tiers") as TierData[]) || [])];
    if (tiers[index]) {
      if (key === "spareFlag" && !_value) {
        // 如果取消兜底，则必现展示也取消，并清空相关字段
        tiers[index] = {
          ...tiers[index],
          [key]: _value,
          mustShow: false,
          couponValid: "fromDraw",
          fromDraw: undefined,
          validRange: undefined,
          exchangeTimeRangeList: {
            timeType: "allDay",
            timeRanges: [{ start: null, end: null, key: uuid4() }],
          },
          drawLimit: {
            totalLimit: undefined,
            dayLimit: undefined,
          },
        };
      } else if (key === "platformSubsidyCny" && !_value) {
        // 如果平台补贴没有值，预算需要清空
        const budgetType = field.getValue("budgetType");
        if (budgetType === "2") {
          tiers[index] = {
            ...tiers[index],
            [key]: _value,
            budgetInfo: undefined,
          };
        } else if (budgetType === BUDGET_TYPE_INDEPENDENT) {
          if (!budgetRequire) {
            field.setValue("budgetInfo", undefined);
          }
          tiers[index] = { ...tiers[index], [key]: _value };
        }
      } else if (key === "couponValid") {
        // 切换有效期类型时，清空另一个类型的值
        tiers[index] = {
          ...tiers[index],
          [key]: _value,
          fromDraw: _value === "fromDraw" ? tiers[index].fromDraw : undefined,
          validRange:
            _value === "validRange" ? tiers[index].validRange : undefined,
        };
      } else if (
        key === "shopSubsidyCny" &&
        _value === tiers[index].shopSubsidyMaxCny
      ) {
        // 如果商户补贴等于最高补贴，当商户出资默认值 等于 最大值时，加码出资比隐藏，不可填。加码出资比需要清空
        tiers[index] = {
          ...tiers[index],
          [key]: _value,
          raiseShopSubsidyPercent: undefined,
        };
      } else if (
        key === "shopSubsidyMaxCny" &&
        _value === tiers[index].shopSubsidyCny
      ) {
        tiers[index] = {
          ...tiers[index],
          [key]: _value,
          raiseShopSubsidyPercent: undefined,
        };
      } else {
        tiers[index] = { ...tiers[index], [key]: _value };
      }

      field.setValue("tiers", tiers);
      if (onChange) {
        onChange(field.getValues());
      }
    }
  };

  const finalBudgetPickerProps = {
    ...budgetPickerProps,
    env: resolveEnv(),
  };

  const renderTiers = () => {
    const tiers: TierData[] = field.getValue("tiers") || [];
    const budgetType = field.getValue("budgetType");

    return tiers.map((tier, index) => {
      const hasShopSubsidy = budgetType === "2" && !!tier.platformSubsidyCny;
      return (
        <div key={tier.id} className={styles.tierRow}>
          <Row>
            {/* 第一列：阶梯券门槛 */}
            <Col className={`${styles.tierContent} ${styles.step}`}>
              <div className={styles.tierName}>档位{index + 1}</div>
              <FormItem
                required
                requiredMessage="请输入门槛"
                validatorTrigger={["onBlur"]}
                validator={(_: any, v: number, callback) => {
                  if (!isValueSet(v)) {
                    callback("请输入门槛");
                    return;
                  }
                  callback();
                }}
              >
                满{" "}
                <NumberPicker
                  max={99999}
                  step={1}
                  min={0}
                  precision={0}
                  value={tier.conditionCny}
                  onChange={(v) => handleTierChange(index, "conditionCny", v)}
                />
                &nbsp;元
              </FormItem>
            </Col>

            {/* 第二列：商户补贴 */}
            <Col className={`${styles.tierContent} ${styles.shopSubsidy}`}>
              <FormItem required requiredMessage="请输入默认商户补贴">
                默认{" "}
                <NumberPicker
                  max={99999}
                  step={0.5}
                  min={0}
                  precision={1}
                  value={tier.shopSubsidyCny}
                  onChange={(v) => handleTierChange(index, "shopSubsidyCny", v)}
                />{" "}
                &nbsp;元，
              </FormItem>

              <FormItem required requiredMessage="请输入商户最高补贴">
                最高{" "}
                <NumberPicker
                  step={0.5}
                  max={99999}
                  min={0}
                  precision={1}
                  value={tier.shopSubsidyMaxCny}
                  onChange={(v) =>
                    handleTierChange(index, "shopSubsidyMaxCny", v)
                  }
                />
                &nbsp;元
              </FormItem>
            </Col>

            {/* 第三列：平台补贴金额 */}
            <Col className={`${styles.tierContent} ${styles.platformSubsidy}`}>
              <FormItem required requiredMessage="请输入平台补贴金额">
                默认&nbsp;
                <NumberPicker
                  step={0.5}
                  max={99999}
                  min={0.1}
                  precision={1}
                  value={tier.platformSubsidyCny}
                  onChange={(v) =>
                    handleTierChange(index, "platformSubsidyCny", v)
                  }
                />
                &nbsp;元
              </FormItem>
            </Col>

            {/* 第四列：加码出资比 */}
            <Col className={`${styles.tierContent} ${styles.raiseShopSubsidy}`}>
              <FormItem required requiredMessage="请输入加码出资比">
                面额每增加1元，商户分摊&nbsp;
                <NumberPicker
                  step={1}
                  min={0}
                  max={100}
                  precision={0}
                  value={tier.raiseShopSubsidyPercent}
                  disabled={tier.shopSubsidyCny === tier.shopSubsidyMaxCny}
                  onChange={(v) =>
                    handleTierChange(index, "raiseShopSubsidyPercent", v)
                  }
                />{" "}
                %
              </FormItem>
            </Col>

            <Col className={`${styles.tierFlagContent} ${styles.spareFlag}`}>
              <FormItem>
                <Checkbox
                  checked={tier.spareFlag}
                  onChange={(v) => handleTierChange(index, "spareFlag", v)}
                >
                  是否兜底
                </Checkbox>
              </FormItem>
              {tier.spareFlag && (
                <>
                  <FormItem
                    label="券有效期"
                    required
                    requiredMessage="请选择券有效期"
                  >
                    <div className={styles.radioGroup}>
                      <Radio.Group
                        value={tier.couponValid}
                        onChange={(v) =>
                          handleTierChange(index, "couponValid", v)
                        }
                      >
                        <Radio value="fromDraw">
                          自领取日期起{" "}
                          {tier.couponValid === "fromDraw" && (
                            <>
                              <NumberPicker
                                min={0}
                                max={30}
                                value={tier.fromDraw}
                                onChange={(v) =>
                                  handleTierChange(index, "fromDraw", v)
                                }
                              />{" "}
                              天内有效
                            </>
                          )}
                        </Radio>
                        <Radio value="validRange">
                          指定生效日期
                          {tier.couponValid === "validRange" && (
                            <DatePicker.RangePicker
                              value={tier.validRange}
                              onChange={(v) =>
                                handleTierChange(index, "validRange", v)
                              }
                              style={{ marginLeft: 8 }}
                            />
                          )}
                        </Radio>
                      </Radio.Group>
                    </div>
                  </FormItem>

                  <FormItem
                    label="使用时段"
                    validatorTrigger={["onBlur"]}
                    validator={(
                      _: any,
                      v: TierData["exchangeTimeRangeList"],
                      callback
                    ) => {
                      const timeType = v?.timeType;
                      if (timeType === "custom") {
                        const timeRanges = v?.timeRanges || [];
                        for (let i = 0; i < timeRanges.length; i++) {
                          const item = timeRanges[i];
                          if (!item?.start || !item?.end) {
                            callback("请选择使用时段");
                            return;
                          }
                          const start = item.start.format("HH:mm");
                          const end = item.end.format("HH:mm");
                          if (start >= end) {
                            callback("开始时间不能大于结束时间");
                            return;
                          }
                        }
                        if (timeRanges.length === 2) {
                          const r1 = timeRanges[0];
                          const r2 = timeRanges[1];
                          if (
                            r1.end!.format("HH:mm") <
                              r2.start!.format("HH:mm") ||
                            r2.end!.format("HH:m") < r1.start!.format("HH:mm")
                          ) {
                            callback("时段不能重叠");
                            return;
                          }
                        }
                        callback();
                      } else {
                        callback();
                      }
                    }}
                  >
                    <UseTimeSet
                      value={tier.exchangeTimeRangeList}
                      onChange={(v) => {
                        handleTierChange(index, "exchangeTimeRangeList", v);
                      }}
                    ></UseTimeSet>
                  </FormItem>

                  <FormItem
                    label="领券限制"
                    validatorTrigger={["onBlur"]}
                    validator={(_: any, v: TierData["drawLimit"], callback) => {
                      if (!v) {
                        callback("请填写领券限制");
                        return;
                      }
                      if ("totalLimit" in v) {
                        if (!isValueSet(v.totalLimit)) {
                          callback("请输入活动期间每人限领");
                          return;
                        } else if (
                          +v.totalLimit! <= 0 ||
                          +v.totalLimit! > 30
                        ) {
                          callback("活动期间每人限领范围是1-9999");
                          return;
                        }
                      }
                      if ("dayLimit" in v) {
                        if (!isValueSet(v.dayLimit)) {
                          callback("请输每人每日限领");
                          return;
                        } else if (+v.dayLimit! <= 0 || +v.dayLimit! > 30) {
                          callback("每人每日限领范围是1-9999");
                          return;
                        }
                      }
                      if (!("totalLimit" in v) && !("dayLimit" in v)) {
                        callback("请填写领券限制");
                      }
                      callback();
                    }}
                  >
                    <RadioWithInput
                      value={tier.drawLimit || {}}
                      onChange={(v) => handleTierChange(index, "drawLimit", v)}
                      options={[
                        { label: "每人每日限领", key: "dayLimit", min: 1, max: 30 },
                        {
                          label: "活动期间每人限领",
                          key: "totalLimit",
                          min: 1,
                          max: 30,
                        },
                      ]}
                      unit="张"
                    />
                  </FormItem>
                </>
              )}
            </Col>

            {mustShowVisible && (
              <Col className={`${styles.tierContent} ${styles.mustShow}`}>
                <FormItem>
                  <Checkbox
                    checked={tier.mustShow}
                    disabled={!tier.spareFlag}
                    onChange={(v) => handleTierChange(index, "mustShow", v)}
                  >
                    必现展示
                  </Checkbox>
                </FormItem>
              </Col>
            )}

            <Col className={`${styles.tierContent} ${styles.dayCountLimit}`}>
              <FormItem>
                最低{" "}
                <NumberPicker
                  min={1}
                  max={9999}
                  value={tier.dayCountLimit}
                  onChange={(v) => handleTierChange(index, "dayCountLimit", v)}
                />
                &nbsp;件
              </FormItem>
            </Col>

            {/* 第六列：预算池（条件渲染） */}
            {budgetType === "2" && (
              <Col className={`${styles.tierContent} ${styles.budget}`}>
                <Form.Item
                  validatorTrigger={["onBlur"]}
                  validator={(
                    _: any,
                    v: BudgetPickerEditProps["value"],
                    callback
                  ) => {
                    if (!hasShopSubsidy) {
                      callback();
                      return;
                    }
                    if (!isValueSet(v?.budgetId)) {
                      callback("请选择预算22");
                    } else {
                      callback();
                    }
                  }}
                >
                  <BudgetPickerEdit
                    {...finalBudgetPickerProps}
                    prompt="仅支持选择/输入B/C类预算,同时只可有1个补贴类型标，不可有多个"
                    extraRequestParams={{
                      subsidyInvestmentModel: "STEP_A",
                    }}
                    style={{ width: "240px" }}
                    disabled={!hasShopSubsidy}
                    hasClear
                    value={tier.budgetInfo}
                    onChange={(v) => handleTierChange(index, "budgetInfo", v)}
                  />
                </Form.Item>
              </Col>
            )}

            {/* 第七列：操作 */}
            <Col className={`${styles.tierContent} ${styles.operation}`}>
              <div className={styles.tierLabel}>&nbsp;</div>
              <Button
                type="primary"
                text
                warning
                onClick={() => removeTier(index)}
              >
                删除
              </Button>
            </Col>
          </Row>
        </div>
      );
    });
  };

  const activityEffectCoupon = React.useMemo(() => {
    const tableData: TierData[] = field.getValue("tiers") || [];

    const ret = tableData.map((i) => {
      if (i.mustShow || i.spareFlag) {
        const shopSubsidy = i.shopSubsidyCny ?? 0;
        const platformSubsidy = i.platformSubsidyCny ?? 0;
        return {
          conditionCny: i.conditionCny,
          threshold: new BigNumber(shopSubsidy).plus(new BigNumber(platformSubsidy)).toNumber(),
          availableText: isValueSet(i?.fromDraw)
          ? `自领取之日起 ${i.fromDraw} 天可用`
          : ` ${i?.validRange?.[0]?.format("YYYY-MM-DD") ?? ''} - ${
            i?.validRange?.[1]?.format("YYYY-MM-DD") ?? ''}可用`
        };
      }
    });
    return {
      checkMustShow: tableData.some((i) => i.mustShow),
      coupon: ret.filter(Boolean),
    };
  }, [JSON.stringify(field.getValue("tiers"))]);

  return (
    <div className={styles.fundingPlanForm}>
      <div className={styles.formSection}>
        <h3 className={styles.sectionTitle}>补贴规则</h3>
      </div>
      <Form
        field={field}
        labelAlign="left"
        {...formItemLayout}
        className={styles.fundingPlanForm}
      >
        <FormItem label="预算设置" required>
          <RadioGroup {...field.init("budgetType")}>
            <Radio value="1">多档位统一</Radio>
            <Radio value="2">多档位不同</Radio>
          </RadioGroup>
        </FormItem>

        <FormItem label="出资方案" required>
          <div className={`${styles.formSection} ${styles.tierTableContainer}`}>
            <div className={styles.tierTableContent}>
              <div className={styles.tierTableHeader}>
                <Row>
                  <Col className={`${styles.tierContent} ${styles.step}`}>
                    阶梯券门槛
                  </Col>
                  <Col
                    className={`${styles.tierContent} ${styles.shopSubsidy}`}
                  >
                    商户补贴
                  </Col>
                  <Col
                    className={`${styles.tierContent} ${styles.platformSubsidy}`}
                  >
                    平台补贴金额
                  </Col>
                  <Col
                    className={`${styles.tierContent} ${styles.raiseShopSubsidy}`}
                  >
                    加码出资比
                  </Col>
                  <Col className={`${styles.tierContent} ${styles.spareFlag}`}>
                    兜底券
                  </Col>
                  {mustShowVisible && (
                    <Col className={`${styles.tierContent} ${styles.mustShow}`}>
                      必现券
                    </Col>
                  )}
                  <Col
                    className={`${styles.tierContent} ${styles.dayCountLimit}`}
                  >
                    每日库存
                  </Col>
                  {field.getValue("budgetType") === "2" && (
                    <Col className={`${styles.tierContent} ${styles.budget}`}>
                      预算池
                    </Col>
                  )}
                  <Col className={`${styles.tierContent} ${styles.operation}`}>
                    操作
                  </Col>
                </Row>
              </div>

              <div className={styles.tierTableBody}>{renderTiers()}</div>

              <div className={styles.addTierButton}>
                <Button type="primary" onClick={addTier}>
                  <Icon type="add" /> 添加档位
                </Button>
              </div>
            </div>
          </div>
        </FormItem>

        <Form.Item label="限制人群:" required={true}>
          <UserScopeSelect
            {...field.init("userScope")}
            options={[
              { label: "全部顾客", value: UserScopeEnum.ALL },
              { label: "门店新客", value: UserScopeEnum.STORE_NEW },
              { label: "商家品牌新客", value: UserScopeEnum.BRAND_NEW },
              { label: "学生人群", value: UserScopeEnum.STUDENT },
            ]}
          />
        </Form.Item>

        {field.getValue("budgetType") === BUDGET_TYPE_INDEPENDENT && (
          <Form.Item
            label="预算"
            name="budgetInfo"
            validatorTrigger={["onBlur"]}
            validator={(_: any, v: Budget, callback) => {
              if (!budgetRequire || field.getValue("budgetType") === "2") {
                callback();
                return;
              }

              if (!v?.budgetId) {
                callback("请选择预算");
                return;
              }
              callback();
            }}
          >
            <BudgetPickerEdit
              {...finalBudgetPickerProps}
              hasClear
              prompt="仅支持选择/输入B/C类预算,同时只可有1个补贴类型标，不可有多个"
              disabled={!budgetRequire}
              extraRequestParams={{
                subsidyInvestmentModel: "STEP_A",
              }}
            />
          </Form.Item>
        )}

        {activityEffectCoupon.coupon.length > 0 && (
          <FormItem label="活动展示">
            <div className={styles.activityDisplay}>
              {activityEffectCoupon.checkMustShow && (
                <div className={styles.activityType}>
                  <h4>消费者有可爆涨券进店时</h4>
                  <div className={styles.activityDescription}>
                    如勾选了必现券，当用户进店时，可通过一爆三惊喜兑换出来的必须展示的券
                  </div>
                  <div className={styles.couponExamples}>
                    <img
                      src="https://img.alicdn.com/imgextra/i4/O1CN01LTynsa1gmma3Fsygh_!!6000000004185-0-tps-662-1432.jpg"
                      alt="暴涨券示例"
                      className={styles.image}
                    />
                    <img
                      src="https://img.alicdn.com/imgextra/i4/O1CN018CpavC1wZ4ZlUjMfq_!!6000000006321-0-tps-662-1432.jpg"
                      alt="暴涨券示例"
                      className={styles.image}
                    />
                  </div>
                </div>
              )}

              <div className={styles.activityType}>
                <h4>消费者无可爆涨券进店时</h4>
                <div className={styles.activityDescription}>
                  如勾选了兜底券或者必现券，当用户进店时，兜底/必现券将以全店满减券的方式在店内展示，消费者可主动领取使用
                </div>
                <div>
                  {activityEffectCoupon.coupon.map((c: any) => {
                    return (
                      <Coupon
                        key={c.conditionCny}
                        amount={c.threshold}
                        threshold={c.conditionCny}
                        availableText={c.availableText}
                      />
                    );
                  })}
                </div>
              </div>
            </div>
          </FormItem>
        )}
        <FormItem label=" ">
          <Button
            data-test-id="scope-info-step-cancel-btn"
            onClick={() => onEvent && onEvent({ type: "cancel" })}
          >
            取消
          </Button>
          &nbsp;&nbsp;
          <Button
            data-test-id="scope-info-step-prev-btn"
            onClick={() => onEvent && onEvent({ type: "prev" })}
          >
            上一步
          </Button>
          &nbsp;&nbsp;
          <Button type="primary" onClick={handleSubmit}>
            发布
          </Button>
        </FormItem>
      </Form>
    </div>
  );
};

export default PlayStepForA2;
