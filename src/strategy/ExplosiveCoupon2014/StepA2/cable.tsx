import { Form } from "@alifd/next";

import { ActivityTypeEnum } from "../../../constants";
import { CableInst } from "models";
import loadingDialog from "components/LoadingDialog";

import { CableBase } from "strategy/base";
import {
  checkAndUploadRichDescription,
  genericCreateActivity,
} from "strategy/mixins";

import { auditServiceForCoupon } from "strategy/service";
import { maskMS } from "common";
import { transformPlayStepToViewJson } from "./helper";

export function cable(): CableInst {
  return {
    ...CableBase,
    workspace: "ExukovQrae6xWn2rt4NgG",
    model: "7pglvc8oczU8cTya3TXjF0",
    async checkNext({ step, dataStore: _dataStore }, creationContext, history) {
      let dataStore = _dataStore;
      if (!dataStore._session) {
        dataStore._session = {};
      }
      if (step.name === "baseinfo") {
        if (dataStore.data.baseinfo?.richDescription) {
          const { hide } = loadingDialog.show({
            message: "活动信息检查中...",
          });
          try {
            const error = await checkAndUploadRichDescription({
              richDescription: dataStore.data.baseinfo?.richDescription,
              session: dataStore._session,
            });
            if (error) {
              return error;
            }
          } finally {
            hide();
          }
        }
      }
      if (step.name === "playdata") {
        const { baseinfo, scope } = dataStore.data;
        const viewJson = transformPlayStepToViewJson({
          playdata: dataStore?.data?.playdata,
          baseinfo,
        });

        const payload: any = {
          // 活动类型信息
          templateId: creationContext.templateId,
          investmentType: creationContext.investmentType,
          investmentSchema: creationContext.investmentSchema,
          subsidyInvestmentModel: creationContext.subsidyInvestmentModel,
          activityType: ActivityTypeEnum.EXPLOSIVE_ORDER_COUPON,
          // 基本信息
          name: baseinfo.name,
          description: baseinfo.description,
          remark: baseinfo.remark,
          beginTime: baseinfo.activityTime.start.toDate().getTime(),
          endTime: maskMS(baseinfo.activityTime.end.toDate().getTime()),
          allowCancel: baseinfo.allowCancel,

          signUpShopType: baseinfo.signUpShopType,
          signUpStartTime: baseinfo.signupTime.start.toDate().getTime(),
          signUpEndTime: maskMS(baseinfo.signupTime.end.toDate().getTime()),
          subsidyType: creationContext?.subsidyType,
          manageOrgList: baseinfo?.manageOrgList,

          // 授权审核人
          auditMemberList:
            baseinfo.auditMemberList?.length > 0
              ? baseinfo.auditMemberList
              : null,

          // 招商范围信息
          shopPoolId: scope.pool.value,
          shopPoolName: scope.pool.label,

          // 玩法信息
          viewJson: JSON.stringify(viewJson),
        };
        if (dataStore._session?.ossURL) {
          const ossURL = dataStore._session?.ossURL;
          payload.description = baseinfo.richDescription.text;
          payload.attributes = {
            hasRichDescription: "on",
            richDescriptionURL: ossURL,
          };
        }

        return await genericCreateActivity(payload, history, creationContext);
      }
    },
    auditService: auditServiceForCoupon,
    stepOptions: {
      baseinfo: {
        slotBeforeActivityTime: (
          <Form.Item label="招商类型">
            <p>阶梯档位</p>
          </Form.Item>
        ),
      },
    },
  };
}
