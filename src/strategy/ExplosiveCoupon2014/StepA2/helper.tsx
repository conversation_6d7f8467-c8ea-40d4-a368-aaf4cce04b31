import moment from "moment";
import { ActivityTypeEnum } from "../../../constants";
import { PlayDataTS } from "./PlayStepForA2";

const BUDGET_TYPE_INDEPENDENT = "1";
const SUPER_MARKETING_CHANNEL = "yt1_qgka"; // 全国KA行业

export const transformPlayStepToViewJson = (options: {
  playdata: PlayDataTS;
  baseinfo: {
    manageOrgList: any[];
    activityTime: {
      weeks: number[];
      batchRange: {
        type: "all" | "custom";
        list: { start: string; end: string }[];
      };
    };
  };
}) => {
  return {
    activityType: ActivityTypeEnum.EXPLOSIVE_ORDER_COUPON,
    activityRule: [
      {
        amountScene: "A_WITH_STEP",
        activityMarketingInfo: {
          userScope: options.playdata.userScope,
        },
        couponRule: options.playdata.tiers.map((tier) => {
          let ret: any = {
            conditionCny: tier.conditionCny,
            shopSubsidyCny: tier.shopSubsidyCny,
            shopSubsidyMaxCny: tier.shopSubsidyMaxCny,
            platformSubsidyCny: tier.platformSubsidyCny,
            raiseShopSubsidyPercent: tier.raiseShopSubsidyPercent,
            mustShow: tier.mustShow,
            spareFlag: tier.spareFlag,
            dayCountLimit: tier?.dayCountLimit,
          };
          if (options.playdata.budgetType === BUDGET_TYPE_INDEPENDENT) {
            if (tier.platformSubsidyCny) {
              // 有平台出资的情况再给预算
              ret = {
                ...ret,
                ...options.playdata.budgetInfo,
              };
            }
          } else {
            ret = {
              ...ret,
              ...tier?.budgetInfo,
            };
          }
          const hasPermission = options.baseinfo?.manageOrgList?.some(
            (item) => item.code === SUPER_MARKETING_CHANNEL
          );
          // 超市便利行业才能看到必现券,为了避免先选择超市便利，勾选必现券，然后切换行业的情况，这里需要把必现券置为false
          ret.mustShow = hasPermission ? ret.mustShow : false;
          if (tier.spareFlag) {
            const couponValid = tier.couponValid;
            ret = {
              ...ret,
              availableDays:
                couponValid === "fromDraw" ? tier.fromDraw : undefined,
              availableStartTime:
                couponValid === "validRange"
                  ? moment(
                      tier?.validRange?.[0],
                      "YYYY-MM-DD HH:mm:ss"
                    ).valueOf()
                  : undefined,
              availableEndTime:
                couponValid === "validRange"
                  ? moment(
                      tier?.validRange?.[1],
                      "YYYY-MM-DD HH:mm:ss"
                    ).endOf("day").unix() * 1000
                  : undefined,
              userTotalCountLimit: tier.drawLimit?.totalLimit,
              userDayCountLimit: tier.drawLimit?.dayLimit,
              exchangePeriodList:
                tier.exchangeTimeRangeList?.timeType === "custom"
                  ? tier.exchangeTimeRangeList?.timeRanges?.map((item) => {
                      return {
                        openTime:
                          item.start?.format("HH:mm").slice(0, "HH:mm".length) +
                          ":00",
                        closeTime:
                          item.end?.format("HH:mm").slice(0, "HH:mm".length) +
                          ":59",
                      };
                    }) : undefined,
            };
          }
          return ret;
        }),
      },
    ],

    weekday: options.baseinfo.activityTime.weeks.join(","),
    periodList: options.baseinfo?.activityTime?.batchRange?.list?.map(
      (item) => {
        return {
          openTime: item.start.slice(0, "HH:mm".length) + ":00",
          closeTime: item.end.slice(0, "HH:mm".length) + ":59",
        };
      }
    ),
  };
};

export function normalizeTimestampToDate(timestamp: number) {
  // 创建日期对象
  const date = new Date(timestamp);
  // 将时间设置为当天的00:00:00
  date.setHours(0, 0, 0, 0);
  return date.getTime(); // 返回 normalized timestamp
}

export const fmt = (d: any) => (d ? moment(d).format("YYYY-MM-DD") : " ");
