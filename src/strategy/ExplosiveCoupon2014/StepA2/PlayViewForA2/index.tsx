import type React from "react";
import styles from "./style.module.scss";
import { useEffect, useState } from "react";
import * as api from "../../../../api";
import { BudgetPickerDetail } from "@alife/carbon-biz-kunlun-zs/lib/components/BudgetPicker/BudgetPicker";
import { isValueSet, resolveEnv } from "common";
import moment from "moment";
import { UserBuyLimitView } from "components/formControls/UserBuyLimit";
import { UserScopeView } from "components/formControls/UserScopeSelect";
import { fmt } from "../helper";

interface CouponRule {
  budgetId: string;
  budgetName: string;
  conditionCny: string;
  mustShow: boolean;
  platformSubsidyCny: string;
  raiseShopSubsidyPercent: number;
  shopSubsidyCny: string;
  shopSubsidyMaxCny: string;
  spareFlag: boolean;
  availableDays?: number;
  availableStartTime?: string;
  availableEndTime?: string;
  dayCountLimit?: number;
  exchangePeriodList?: {
    openTime: string;
    closeTime: string;
  }[];
  userTotalCountLimit?: number;
  userDayCountLimit?: number;
}

interface ActivityLimitRule {
  dayCountLimit: number;
}

interface ActivityRule {
  activityLimitRule: ActivityLimitRule;
  activityMarketingInfo: {
    userScope?: number;
  };
  amountScene: string;
  couponRule: CouponRule[];
}

interface ActivityFrontConfig {
  platformSubsidyMinCny: string;
}

interface Period {
  openTime: string;
  closeTime: string;
}

interface ViewJsonData {
  activityFrontConfig: ActivityFrontConfig;
  activityRule: ActivityRule[];
  activityType: number;
  periodList: Period[];
  weekday: string;
}

interface FundingPlanViewProps {
  viewJson: string;
  activityId: number;
  isStepLimit: boolean;
}



const PlayViewForA2: React.FC<FundingPlanViewProps> = ({
  viewJson,
  activityId,
  isStepLimit
}) => {
  if (!viewJson) {
    return null;
  }
  const { activityRule } = JSON.parse(
    viewJson
  ) as ViewJsonData;
  const couponRules = activityRule[0]?.couponRule || [];

  return (
    <div className={styles.fundingPlanView}>
      <div className={styles.section}>
        <h3 className={styles.sectionTitle}>补贴规则</h3>
      </div>
      <div className={styles.formItem}>
        <div className={styles.label}>出资方案:</div>
        <div className={styles.value}>
          {couponRules.map((rule, index) => (
            <div key={index} className={styles.tierSection}>
              <h4 className={styles.tierTitle}>档位{index + 1}</h4>

              <div className={styles.tierContent}>
                <div className={styles.tierItem}>
                  <div className={styles.tierLabel}>阶梯券门槛</div>
                  <div className={styles.tierValue}>
                    满 {rule.conditionCny} 元
                  </div>
                </div>

                <div className={styles.tierItem}>
                  <div className={styles.tierLabel}>商户补贴</div>
                  <div className={styles.tierValue}>
                    <div>
                      默认 {rule.shopSubsidyCny} 元，最高{" "}
                      {rule.shopSubsidyMaxCny} 元
                    </div>
                  </div>
                </div>

                <div className={styles.tierItem}>
                  <div className={styles.tierLabel}>平台补贴金额</div>
                  <div className={styles.tierValue}>
                    {rule.platformSubsidyCny} 元
                  </div>
                </div>

                {isValueSet(rule.raiseShopSubsidyPercent) && <div className={styles.tierItem}>
                  <div className={styles.tierLabel}>加码出资比</div>
                  <div className={styles.tierValue}>
                    {rule.raiseShopSubsidyPercent}%
                  </div>
                </div>}

                <div className={styles.tierItem}>
                  <div className={styles.tierLabel}>兜底券</div>
                  <div className={styles.tierValue}>
                    {rule.spareFlag ? "是兜底券" : "非兜底券"}
                    {rule.spareFlag && isStepLimit && (
                      <>
                        <div className={styles.flagContent}>
                          <div className={styles.flagContentLabel}>
                            券有效期:
                          </div>
                          <div className={styles.flagContentValue}>
                            {isValueSet(rule?.availableDays)
                              ? `自领取之日起 ${rule.availableDays} 天可用`
                              : ` ${fmt(rule.availableStartTime)} - ${fmt(
                                  rule.availableEndTime
                                )}`}
                          </div>
                        </div>
                        <div className={styles.flagContent}>
                          <div className={styles.flagContentLabel}>
                            使用时段:
                          </div>
                          <div className={styles.flagContentValue}>
                            {(rule?.exchangePeriodList || []).length > 0
                              ? `${rule
                                  .exchangePeriodList!.map(
                                    (item) =>
                                      `${item.openTime.slice(0, "HH:mm".length)}～${item.closeTime.slice(0, "HH:mm".length)}`
                                  )
                                  .join("，")} 可用`
                              : "全天可用"}
                          </div>
                        </div>
                        <div className={styles.flagContent}>
                          <div className={styles.flagContentLabel}>
                            领券限制:
                          </div>
                          <div className={styles.flagContentValue}>
                            <UserBuyLimitView
                              value={rule}
                              userDayCountLimitText="每人每日限领"
                              userTotalCountLimitText="活动期间限领"
                              unit="张"
                            />
                          </div>
                        </div>
                      </>
                    )}
                  </div>
                </div>

                <div className={styles.tierItem}>
                  <div className={styles.tierLabel}>必现券</div>
                  <div className={styles.tierValue}>
                    {rule.mustShow ? "是必现券" : "非必现券"}
                  </div>
                </div>
                {rule.dayCountLimit && <div className={styles.tierItem}>
                  <div className={styles.tierLabel}>每日库存最小值</div>
                  <div className={styles.tierValue}>
                    {rule.dayCountLimit} 件
                  </div>
                </div>}

                <div className={styles.tierItem}>
                  <div className={styles.tierLabel}>预算池</div>
                  <div className={styles.tierValue}>
                    {rule.budgetId ? (
                      <BudgetPickerDetail
                        env={{
                          env: resolveEnv(),
                          view: "detail",
                          detailContext: {
                            activityId: activityId as number,
                          },
                        }}
                        value={{
                          budgetId: rule.budgetId,
                          budgetName: rule.budgetName,
                        }}
                      />
                    ) : (
                      "-"
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
      {isValueSet(activityRule[0]?.activityMarketingInfo?.userScope) && (
        <div className={styles.formItem}>
          <div className={styles.label}>生效人群</div>
          <div className={styles.value}>
            <UserScopeView value={activityRule[0]?.activityMarketingInfo?.userScope!} />
          </div>
        </div>
      )}

      {isValueSet(activityRule[0]?.activityLimitRule?.dayCountLimit) && (
        <div className={styles.formItem}>
          <div className={styles.label}>每日库存最小值:</div>
          <div className={styles.value}>
            {activityRule[0]?.activityLimitRule?.dayCountLimit} 件
          </div>
        </div>
      )}
    </div>
  );
};

const PlayViewForA2Wrap: React.FC<any> = ({ activityInfo }) => {
  const [detail, setDetail] = useState<any>();

  useEffect(() => {
    api.subActivity.getDetail(activityInfo.activityId).then((res) => {
      setDetail(res.data);
    });
  }, [activityInfo.activityId]);
  return (
    <div className="zs-act-info">
      <div className="zs-act-area">
        <div className="zs-act-area-title" style={{ marginBottom: 30 }}>
          玩法设置
        </div>
        <div className="zs-act-section">
          <PlayViewForA2
            viewJson={detail?.viewJson}
            activityId={activityInfo?.activityId}
            isStepLimit={detail?.investmentActivityDetailDTO?.attributes?.isStepLimit === "true"}
          />
        </div>
      </div>
    </div>
  );
};

export default PlayViewForA2Wrap;
