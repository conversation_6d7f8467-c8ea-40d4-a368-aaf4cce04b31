// 变量定义
$border-color: #e6e6e6;
$background-color: #fff;
$header-background: #f7f8fa;
$text-color: #333;
$text-light: #999;
$primary-color: #ff6a00;

.fundingPlanView {
  padding: 20px;
  background-color: $background-color;
  font-size: 14px;
  color: $text-color;
}

.section {
  margin-bottom: 24px;
}

.sectionTitle {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 16px;
  color: $text-light;
}

.formItem {
  display: flex;
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }
}

.label {
  width: 120px;
  text-align: right;
  flex-shrink: 0;
  margin-right: 16px;
}

.value {
  flex: 1;
}

.tierSection {
  margin-bottom: 24px;
  padding: 16px;
  border: 1px solid $border-color;
  border-radius: 4px;
  background-color: $background-color;

  &:last-child {
    margin-bottom: 24px;
  }
}

.tierTitle {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid $border-color;
}

.tierContent {
  display: flex;
  flex-wrap: wrap;
}
.flagContent {
  display: flex;
  .flagContentLabel {
    margin-right: 10px;
    color: rgba(0, 0, 0, 0.8);
    min-width: 15%;
  }
}

.tierItem {
  width: 100%;
  margin-bottom: 16px;
  padding-right: 16px;
  box-sizing: border-box;
  display: flex;
  line-height: 32px;

  @media (max-width: 768px) {
    width: 50%;
  }

  @media (max-width: 480px) {
    width: 100%;
  }
}

.tierLabel {
  color: $text-light;
  min-width: 30%;
  text-align: right;
  margin-right: 10px;
}

.tierValue {
  color: $text-color;

  & > div {
    &:last-child {
      margin-bottom: 0;
    }
  }
}

.budgetName {
  color: $text-light;
  font-size: 12px;
}
