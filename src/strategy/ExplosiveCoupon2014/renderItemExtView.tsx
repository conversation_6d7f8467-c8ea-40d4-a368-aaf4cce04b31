import { <PERSON><PERSON>, But<PERSON> } from "@alifd/next";
import { isValueSet } from "../../common";
import "./style.scss";
import moment from "moment";
import { UserBuyLimitView } from "components/formControls/UserBuyLimit";

type AmountScence =
  | "A_WITH_THRESHOLD" // 商户基础出资_有门槛
  | "A_WITHOUT_THRESHOLD" // 商户基础出资_无门槛
  | "Y_WITH_USER_SCOPE" // 商户追加出资_分人群追加
  | "Y_WITH_TIME" // 商户追加出资_分时段追加
  | "Y_WITH_STEP" // 商户追加出资_分阶梯追加
  | "Y_WITH_BASIC" // 商户追加出资_基础出资
  | "Y_WITH_BASE_RULE" // 商户追加出资_基础出资
  | "A_WITH_STEP"; // 商户基础出资_阶梯A
interface PlayRuleInfo {
  conditionCny?: string; // 门槛
  shopSubsidyCny?: string; // 出资
  agentSubsidyCny?: string; // 城代出资
  periodTime?: string; // 时段追加独有
}
interface PlayStepRule {
  conditionCny?: number;
  shopSubsidyCny?: number;
  shopSubsidyMaxCny?: number;
  platformSubsidyCny?: number;
  raiseShopSubsidyPercent?: number;
  mustShow?: boolean;
  spareFlag?: boolean;
  limitRuleDTO?: {
    dayCountLimit?: number; // 档位日库存
    userTotalCountLimit?: number;
    userDayCountLimit?: number;
  };
  availableDays?: number;
  availableStartTime?: string;
  availableEndTime?: string;
  usedPeriodList?: {
    openTime: string;
    closeTime: string;
  }[];
}

interface AmountRule {
  amountScene: AmountScence;
  userScope?: 4 | 11 | 28; // 4为门店新客，9是品牌新客
  failReason?: string; // 失败原因
  limitRule: {
    dayCountLimit?: number; // 库存
  };
  playRuleList?: PlayRuleInfo[];
}

interface SignUpActivityAmountSceneRuleType {
  baseAmountRule: AmountRule;
  raiseAmountRule: {
    raiseAmountRuleDetailList?: AmountRule[];
  };
}
type RenderValueKeys =
  | "baseAmountCondition"
  | "baseAmountShopSubsidy"
  | "baseAmountDailyStock"
  | "baseAmountShopSubsidyCity"
  | "baseAmountFailReason"
  | "userScope4FailReason"
  | "userScope4ShopSubsidy"
  | "userScope4DailyStockValue"
  | "userScope9ShopSubsidy"
  | "userScope9DailyStockValue"
  | "userScope28ShopSubsidy"
  | "userScope28DailyStockValue"
  | "raiseAmountPeriodTimeFailReason"
  | "raiseAmountPeriodTime"
  | "raiseAmountPeriodTimeDailyStock"
  | "raiseAmountThresholdSubsidy"
  | "raiseAmountThresholdDailyStock"
  | "baseAmountStepDailyStock"
  | "baseAmountStep"
  | "newYRuleDailyStock"
  | "newYRuleShopSubsidy"
  | "newYRuleFailReason";

type playRuleListType = PlayRuleInfo[];

const titleStyle = { fontSize: 14, margin: "10px 0", fontWeight: 500 };
const labelStyle = {
  width: 130,
  paddingRight: 10,
  fontSize: 12,
  lineHeight: "16px",
};
const valueStyle = { width: 80, fontSize: 12, lineHeight: "16px" };
const stepLabelStyle = {
  width: 100,
  fontSize: 12,
  paddingRight: 10,
  lineHeight: "24px",
  textAlign: "right" as "right",
};
const stepValueStyle = { width: 150, fontSize: 12, lineHeight: "24px" };
const fmt = (d: any) => (d ? moment(d).format("YYYY-MM-DD") : " ");
const getRuleInfoBykey = (
  key: RenderValueKeys,
  data: SignUpActivityAmountSceneRuleType
) => {
  const userScope9Info = data?.raiseAmountRule?.raiseAmountRuleDetailList?.find(
    (item) => item.userScope === 11 && item.amountScene === "Y_WITH_USER_SCOPE"
  );
  const userScope4Info = data?.raiseAmountRule?.raiseAmountRuleDetailList?.find(
    (item) => item.userScope === 4 && item.amountScene === "Y_WITH_USER_SCOPE"
  );
  const userScope28Info =
    data?.raiseAmountRule?.raiseAmountRuleDetailList?.find(
      (item) =>
        item.userScope === 28 && item.amountScene === "Y_WITH_USER_SCOPE"
    );
  const periodTimeInfo = data?.raiseAmountRule?.raiseAmountRuleDetailList?.find(
    (item) => item.amountScene === "Y_WITH_TIME"
  );
  const thresholdInfo = data?.raiseAmountRule?.raiseAmountRuleDetailList?.find(
    (item) => item.amountScene === "Y_WITH_STEP"
  );
  const newYRuleInfo = data?.raiseAmountRule?.raiseAmountRuleDetailList?.find(
    (item) => item.amountScene === "Y_WITH_BASE_RULE"
  );
  // 这三种类型不会同时出现
  const baseAmountInfo =
    data?.baseAmountRule?.amountScene === "Y_WITH_BASIC" ||
    data?.baseAmountRule?.amountScene === "A_WITH_THRESHOLD" ||
    data?.baseAmountRule?.amountScene === "A_WITHOUT_THRESHOLD"
      ? data?.baseAmountRule
      : null;
  const baseAmountStepInfo =
    data?.baseAmountRule?.amountScene === "A_WITH_STEP"
      ? data?.baseAmountRule
      : null;
  const renderObj = {
    // 基础A2门槛
    baseAmountCondition: () => baseAmountInfo?.playRuleList?.[0]?.conditionCny,
    // 基础A2出资
    baseAmountShopSubsidy: () =>
      baseAmountInfo?.playRuleList?.[0]?.shopSubsidyCny,
    baseAmountFailReason: () => baseAmountInfo?.failReason,
    // 基础A2出资城代补贴
    baseAmountShopSubsidyCity: () =>
      baseAmountInfo?.playRuleList?.[0]?.agentSubsidyCny,
    // 基础A2库存
    baseAmountDailyStock: () => baseAmountInfo?.limitRule?.dayCountLimit,

    // 基础A阶梯库存
    baseAmountStepDailyStock: () =>
      baseAmountStepInfo?.limitRule?.dayCountLimit,
    // 基础A阶梯出资
    baseAmountStep: () => baseAmountStepInfo?.playRuleList,
    // 新Y出资
    newYRuleDailyStock: () => newYRuleInfo?.limitRule?.dayCountLimit,
    newYRuleShopSubsidy: () => newYRuleInfo?.playRuleList?.[0]?.shopSubsidyCny,
    newYRuleFailReason: () => newYRuleInfo?.failReason,
    // 门店新客失败原因
    userScope4FailReason: () => {
      return userScope4Info?.failReason;
    },
    // 门店新客出资
    userScope4ShopSubsidy: () => {
      return userScope4Info?.playRuleList?.[0]?.shopSubsidyCny;
    },
    // 门店新客库存
    userScope4DailyStockValue: () => {
      return userScope4Info?.limitRule?.dayCountLimit;
    },
    // 品牌新客出资
    userScope9ShopSubsidy: () => {
      return userScope9Info?.playRuleList?.[0]?.shopSubsidyCny;
    },
    // 品牌新客库存
    userScope9DailyStockValue: () => {
      return userScope9Info?.limitRule?.dayCountLimit;
    },
    // 供应商新客
    userScope28ShopSubsidy: () => {
      return userScope28Info?.playRuleList?.[0]?.shopSubsidyCny;
    },
    // 供应商新客库存
    userScope28DailyStockValue: () => {
      return userScope28Info?.limitRule?.dayCountLimit;
    },
    // 时段追加失败原因
    raiseAmountPeriodTimeFailReason: () => {
      return periodTimeInfo?.failReason;
    },
    // 时段追加信息
    raiseAmountPeriodTime: () => {
      return periodTimeInfo?.playRuleList;
    },
    // 时段信息库存
    raiseAmountPeriodTimeDailyStock: () => {
      return periodTimeInfo?.limitRule?.dayCountLimit;
    },
    // 门槛追加
    raiseAmountThresholdSubsidy: () => {
      return thresholdInfo?.playRuleList;
    },
    raiseAmountThresholdDailyStock: () => {
      return thresholdInfo?.limitRule?.dayCountLimit;
    },
    raiseAmountThresholdFailReason: () => {
      return thresholdInfo?.failReason;
    },
  };

  return renderObj[key]();
};

export const renderPeriodTime = (
  playRuleList: playRuleListType,
  timeDailyStock: any
) => {
  if (!Array.isArray(playRuleList)) return "";

  return (
    <Balloon
      closable={false}
      triggerType="click"
      trigger={
        <Button text type="primary" size="small">
          点击查看
        </Button>
      }
      align="t"
    >
      <div style={{ display: "flex", marginBottom: 4 }}>
        <div style={labelStyle}>特殊时段</div>
        <div style={valueStyle}>追补资金</div>
      </div>
      {playRuleList.map((item) => (
        <div key={item.periodTime} style={{ display: "flex" }}>
          <div style={labelStyle}> {item.periodTime}</div>
          <div style={valueStyle}>{item.shopSubsidyCny}元</div>
        </div>
      ))}
      <div style={{ display: "flex" }}>
        <div style={labelStyle}>每日库存</div>
        <div style={valueStyle}>{timeDailyStock}个</div>
      </div>
    </Balloon>
  );
};

export function renderItemExtView({ signUpActivityAmountSceneRule }: any) {
  // 基础A2出资
  const baseAmountShopSubsidyValue = getRuleInfoBykey(
    "baseAmountShopSubsidy",
    signUpActivityAmountSceneRule
  );
  const baseAmountDailyStockValue = getRuleInfoBykey(
    "baseAmountDailyStock",
    signUpActivityAmountSceneRule
  );
  const baseAmountShopSubsidyCityValue = getRuleInfoBykey(
    "baseAmountShopSubsidyCity",
    signUpActivityAmountSceneRule
  );
  // 基础阶梯A库存
  const baseAmountStepDailyStockValue = getRuleInfoBykey(
    "baseAmountStepDailyStock",
    signUpActivityAmountSceneRule
  );
  // 基础阶梯A出资
  const baseAmountStepValue = getRuleInfoBykey(
    "baseAmountStep",
    signUpActivityAmountSceneRule
  );
  const baseAmountFailReasonValue = getRuleInfoBykey(
    "baseAmountFailReason",
    signUpActivityAmountSceneRule
  );

  // 新Y出资
  const newYRuleDailyStockValue = getRuleInfoBykey(
    "newYRuleDailyStock",
    signUpActivityAmountSceneRule
  );
  const newYRuleShopSubsidyValue = getRuleInfoBykey(
    "newYRuleShopSubsidy",
    signUpActivityAmountSceneRule
  );
  const newYRuleFailReasonValue = getRuleInfoBykey(
    "newYRuleFailReason",
    signUpActivityAmountSceneRule
  );
  // 门店新客追补
  const userScope4FailReasonValue = getRuleInfoBykey(
    "userScope4FailReason",
    signUpActivityAmountSceneRule
  );
  const userScope4ShopSubsidyValue = getRuleInfoBykey(
    "userScope4ShopSubsidy",
    signUpActivityAmountSceneRule
  );
  const userScope4DailyStockValue = getRuleInfoBykey(
    "userScope4DailyStockValue",
    signUpActivityAmountSceneRule
  );
  // 品牌新客追补
  const userScope9ShopSubsidyValue = getRuleInfoBykey(
    "userScope9ShopSubsidy",
    signUpActivityAmountSceneRule
  );
  const userScope9DailyStockValue = getRuleInfoBykey(
    "userScope9DailyStockValue",
    signUpActivityAmountSceneRule
  );
  // 供应商新客追补
  const userScope28ShopSubsidyValue = getRuleInfoBykey(
    "userScope28ShopSubsidy",
    signUpActivityAmountSceneRule
  );
  const userScope28DailyStockValue = getRuleInfoBykey(
    "userScope28DailyStockValue",
    signUpActivityAmountSceneRule
  );
  // 时段追补
  const raiseAmountPeriodTimeFailReasonValue = getRuleInfoBykey(
    "raiseAmountPeriodTimeFailReason",
    signUpActivityAmountSceneRule
  );
  const raiseAmountPeriodTimeValue: any = getRuleInfoBykey(
    "raiseAmountPeriodTime",
    signUpActivityAmountSceneRule
  );
  const raiseAmountPeriodTimeDailyStockValue: any = getRuleInfoBykey(
    "raiseAmountPeriodTimeDailyStock",
    signUpActivityAmountSceneRule
  );
  const raiseAmountThresholdSubsidyValue: any = getRuleInfoBykey(
    "raiseAmountThresholdSubsidy",
    signUpActivityAmountSceneRule
  );
  const raiseAmountThresholdDailyStockValue: any = getRuleInfoBykey(
    "raiseAmountThresholdDailyStock",
    signUpActivityAmountSceneRule
  );
  return (
    <Balloon
      closable={false}
      triggerType="click"
      trigger={
        <Button text type="primary" size="small">
          点击查看券报名信息
        </Button>
      }
      align="t"
    >
      <div
        style={{
          maxHeight: "600px",
          overflow: "hidden",
          overflowY: "auto",
        }}
      >
        {isValueSet(baseAmountShopSubsidyValue) ? (
          <>
            <div style={titleStyle}> 基础出资:</div>
            <div style={{ display: "flex" }}>
              <div style={labelStyle}>基础出资：</div>
              <div style={valueStyle}>{baseAmountShopSubsidyValue}元</div>
            </div>
            {isValueSet(baseAmountShopSubsidyCityValue) ? (
              <div style={{ display: "flex" }}>
                <div style={labelStyle}>城代补贴：</div>
                <div style={valueStyle}>{baseAmountShopSubsidyCityValue}元</div>
              </div>
            ) : null}
            <div style={{ display: "flex" }}>
              <div style={labelStyle}>每日库存</div>
              <div style={valueStyle}>{baseAmountDailyStockValue}个</div>
            </div>
          </>
        ) : null}
        {isValueSet(newYRuleShopSubsidyValue) ? (
          <>
            <div style={titleStyle}>
              通用出资:
              {isValueSet(newYRuleFailReasonValue) ? (
                <span style={{ color: "#f52743" }}>
                  &nbsp;&nbsp;({newYRuleFailReasonValue})
                </span>
              ) : null}
            </div>
            <div style={{ display: "flex" }}>
              <div style={labelStyle}>追补资金：</div>
              <div style={valueStyle}>{newYRuleShopSubsidyValue}元</div>
            </div>
            <div style={{ display: "flex" }}>
              <div style={labelStyle}>每日库存</div>
              <div style={valueStyle}>{newYRuleDailyStockValue}个</div>
            </div>
          </>
        ) : null}
        {((baseAmountStepValue as PlayStepRule[]) || []).length > 0 ? (
          <>
            <div style={titleStyle}>
              {" "}
              阶梯A出资:
              {isValueSet(baseAmountFailReasonValue) ? (
                <span style={{ color: "#f52743" }}>
                  &nbsp;&nbsp;({baseAmountFailReasonValue})
                </span>
              ) : null}
            </div>
            {baseAmountStepDailyStockValue && (
              <div style={{ display: "flex" }}>
                <div style={stepLabelStyle}>每日库存</div>
                <div style={stepValueStyle}>
                  {baseAmountStepDailyStockValue}个
                </div>
              </div>
            )}
            {((baseAmountStepValue as PlayStepRule[]) || []).map(
              (item: PlayStepRule, index: number) => (
                <div key={item.conditionCny}>
                  <h3>档位{index + 1}</h3>
                  <div style={{ display: "flex" }}>
                    <div style={stepLabelStyle}> 阶梯券门槛</div>
                    <div style={stepValueStyle}>满{item.conditionCny}元</div>
                  </div>
                  <div style={{ display: "flex" }}>
                    <div style={stepLabelStyle}> 商户补贴</div>
                    <div style={stepValueStyle}>
                      默认{item.shopSubsidyCny}元, 最高{item.shopSubsidyMaxCny}
                      元
                    </div>
                  </div>
                  <div style={{ display: "flex" }}>
                    <div style={stepLabelStyle}> 平台补贴</div>
                    <div style={stepValueStyle}>
                      {" "}
                      {item.platformSubsidyCny}元
                    </div>
                  </div>
                  {isValueSet(item.raiseShopSubsidyPercent) && (
                    <div style={{ display: "flex" }}>
                      <div style={stepLabelStyle}>加码出资比</div>
                      <div style={stepValueStyle}>
                        {" "}
                        {item.raiseShopSubsidyPercent}%
                      </div>
                    </div>
                  )}
                  <div style={{ display: "flex" }}>
                    <div style={stepLabelStyle}>兜底券</div>
                    <div style={stepValueStyle}>
                      {" "}
                      {item.spareFlag ? "是" : "否"}
                    </div>
                  </div>
                  {item.limitRuleDTO?.dayCountLimit && (
                    <div style={{ display: "flex" }}>
                      <div style={stepLabelStyle}>每日库存最小值</div>
                      <div style={stepValueStyle}>
                        {" "}
                        {item.limitRuleDTO?.dayCountLimit} 件
                      </div>
                    </div>
                  )}
                  {item.spareFlag && item.limitRuleDTO?.dayCountLimit && (
                    <>
                      <div style={{ display: "flex" }}>
                        <div style={stepLabelStyle}>券有效期:</div>
                        <div style={stepValueStyle}>
                          {item?.availableDays
                            ? `领取后${item.availableDays} 天可用`
                            : ` ${fmt(item.availableStartTime)} - ${fmt(
                                item.availableEndTime
                              )}`}
                        </div>
                      </div>
                      <div style={{ display: "flex" }}>
                        <div style={stepLabelStyle}>使用时段:</div>
                        <div style={stepValueStyle}>
                          {(item?.usedPeriodList || []).length > 0
                            ? `${item
                                .usedPeriodList!.map(
                                  (_item) =>
                                    `${_item.openTime.slice(0, "HH:mm".length)}～${_item.closeTime.slice(0, "HH:mm".length)}`
                                )
                                .join("，")} 可用`
                            : "全天可用"}
                        </div>
                      </div>
                      <div style={{ display: "flex" }}>
                        <div style={stepLabelStyle}>领券限制:</div>
                        <div style={stepValueStyle}>
                          <UserBuyLimitView
                            value={item?.limitRuleDTO}
                            userDayCountLimitText="每人每日限领"
                            userTotalCountLimitText="活动期间限领"
                            unit="张"
                          />
                        </div>
                      </div>
                    </>
                  )}
                  <div style={{ display: "flex" }}>
                    <div style={stepLabelStyle}>必现券</div>
                    <div style={stepValueStyle}>
                      {" "}
                      {item.mustShow ? "是" : "否"}
                    </div>
                  </div>
                </div>
              )
            )}
          </>
        ) : null}

        {isValueSet(userScope4ShopSubsidyValue) ? (
          <div>
            <div style={titleStyle}>
              门店新客:
              {isValueSet(userScope4FailReasonValue) ? (
                <span style={{ color: "#f52743" }}>
                  &nbsp;&nbsp;({userScope4FailReasonValue})
                </span>
              ) : null}
            </div>
            <div style={{ display: "flex" }}>
              <div style={labelStyle}>追补资金：</div>
              <div style={valueStyle}>{userScope4ShopSubsidyValue}元</div>
            </div>
            <div style={{ display: "flex" }}>
              <div style={labelStyle}>每日库存</div>
              <div style={valueStyle}>{userScope4DailyStockValue}个</div>
            </div>
          </div>
        ) : null}
        {isValueSet(userScope9ShopSubsidyValue) ? (
          <div>
            <div style={titleStyle}>品牌新客:</div>
            <div style={{ display: "flex" }}>
              <div style={labelStyle}>追补资金：</div>
              <div style={valueStyle}>{userScope9ShopSubsidyValue}元</div>
            </div>
            <div style={{ display: "flex" }}>
              <div style={labelStyle}>每日库存</div>
              <div style={valueStyle}>{userScope9DailyStockValue}个</div>
            </div>
          </div>
        ) : null}
        {isValueSet(userScope28ShopSubsidyValue) ? (
          <div>
            <div style={titleStyle}>供应商新客:</div>
            <div style={{ display: "flex" }}>
              <div style={labelStyle}>追补资金：</div>
              <div style={valueStyle}>{userScope28ShopSubsidyValue}元</div>
            </div>
            <div style={{ display: "flex" }}>
              <div style={labelStyle}>每日库存</div>
              <div style={valueStyle}>{userScope28DailyStockValue}个</div>
            </div>
          </div>
        ) : null}
        {Array.isArray(raiseAmountPeriodTimeValue) ? (
          <div>
            <div style={titleStyle}>
              特殊时段:
              {isValueSet(raiseAmountPeriodTimeFailReasonValue) ? (
                <span style={{ color: "#f52743" }}>
                  &nbsp;&nbsp;({raiseAmountPeriodTimeFailReasonValue})
                </span>
              ) : null}
            </div>
            {raiseAmountPeriodTimeValue.map((item: PlayRuleInfo) => (
              <div key={item.periodTime} style={{ display: "flex" }}>
                <div style={labelStyle}> {item.periodTime}</div>
                <div style={valueStyle}>{item.shopSubsidyCny}元</div>
              </div>
            ))}
            <div style={{ display: "flex" }}>
              <div style={labelStyle}>每日库存</div>
              <div style={valueStyle}>
                {raiseAmountPeriodTimeDailyStockValue}个
              </div>
            </div>
          </div>
        ) : null}
        {Array.isArray(raiseAmountThresholdSubsidyValue) ? (
          <div>
            <div style={titleStyle}>
              门槛追加:
              {isValueSet(raiseAmountPeriodTimeFailReasonValue) ? (
                <span style={{ color: "#f52743" }}>
                  &nbsp;&nbsp;({raiseAmountPeriodTimeFailReasonValue})
                </span>
              ) : null}
            </div>
            {raiseAmountThresholdSubsidyValue.map((item: PlayRuleInfo) => (
              <div key={item.periodTime} style={{ display: "flex" }}>
                <div style={labelStyle}> 门槛{item.conditionCny}元</div>
                <div style={valueStyle}>{item.shopSubsidyCny}元</div>
              </div>
            ))}
            <div style={{ display: "flex" }}>
              <div style={labelStyle}>每日库存</div>
              <div style={valueStyle}>
                {raiseAmountThresholdDailyStockValue}个
              </div>
            </div>
          </div>
        ) : null}
      </div>
    </Balloon>
  );
}
