import * as React from "react";
import { Field, Button, Message } from "@alifd/next";
import BasicInvestmentForm from "./basicInvestmentForm";
import ShopSupplementForm from "./shopSupplementForm";
import { BasicInvestmenntTS, ShopSupplementTS } from "../type";
import "./style.scss";

interface Props {
  value?: BasicInvestmenntTS | ShopSupplementTS;
  onChange?: (v: BasicInvestmenntTS | ShopSupplementTS) => void;
  onEvent?: (e: any) => void;
}

const BasicSubsidyContribution: React.FC<Props> = ({
  value,
  onChange,
  onEvent,
}) => {
  const field = Field.useField({
    values: value,
    onChange: () => {
      if (onChange) {
        onChange(field.getValues());
      }
    },
  });
  const onSubmit = async () => {
    const { errors: basicInvestmentErr } = await field.validatePromise();
    if (basicInvestmentErr) return false;
    if (onEvent) onEvent({ type: "next" });
  };

  return (
    <div className="zs-new-explosive-coupon-play-step">
      <BasicInvestmentForm field={field} />
      <div className="zs-new-explosive-coupon-play-step-footer">
        <Button
          data-test-id="scope-info-step-cancel-btn"
          onClick={() => onEvent && onEvent({ type: "cancel" })}
        >
          取消
        </Button>
        &nbsp;&nbsp;
        <Button
          data-test-id="scope-info-step-prev-btn"
          onClick={() => onEvent && onEvent({ type: "prev" })}
        >
          上一步
        </Button>
        &nbsp;&nbsp;
        <Button
          type="primary"
          data-test-id="scope-info-step-next-btn"
          onClick={onSubmit}
        >
          发布
        </Button>
      </div>
    </div>
  );
};
const PlayStepForExplosiveCoupon202409: React.FC<Props> = ({
  value,
  onChange,
  onEvent,
}) => {
  let field = Field.useField({
    values: value,
    onChange: (name: string, val: any) => {
      if (name === "allUserSupplementEnabled" && val === false) {
        field.remove([
          "allUserSupplementSubsidy",
          "allUserSupplementDailyStock",
        ]);
      }
      if (name === "newStoreUserSupplementEnabled" && val === false) {
        field.remove([
          "newStoreUserSupplementSubsidy",
          "newStoreUserSupplementDailyStock",
        ]);
      }
      if (name === "newBrandUserSupplementEnabled" && val === false) {
        field.remove([
          "newBrandUserSupplementSubsidy",
          "newBrandUserSupplementDailyStock",
        ]);
      }
      if (name === "newSupplierUserSupplementEnabled" && val === false) {
        field.remove([
          "newSupplierUserSupplementSubsidy",
          "newSupplierUserSupplementDailyStock",
        ]);
      }
      if (name === "timeSupplementEnabled" && val === false) {
        field.remove([
          "timeSupplement0To10Amount",
          "timeSupplement10To14Amount",
          "timeSupplement14To17Amount",
          "timeSupplement17To21Amount",
          "timeSupplement21ToEndAmount",
          "timeSupplementDailyStock",
        ]);
      }
      if (name === "thresholdSupplementEnabled" && val === false) {
        field.remove([
          "thresholdSupplementSubsidy1",
          "thresholdSupplementSubsidy2",
          "thresholdSupplementSubsidy3",
          "thresholdSupplementDailyStock",
        ]);
      }

      if (onChange) {
        onChange(field.getValues());
      }
    },
  });

  const onSubmit = async () => {
    const { errors: shopSupplementErr } = await field.validatePromise();
    if (shopSupplementErr) return false;
    let _value = value as ShopSupplementTS

    if (
      _value?.allUserSupplementEnabled ||
      _value?.newStoreUserSupplementEnabled ||
      _value?.newBrandUserSupplementEnabled ||
      _value?.newSupplierUserSupplementEnabled ||
      _value?.timeSupplementEnabled ||
      _value?.thresholdSupplementEnabled
    ) {
      if (onEvent) onEvent({ type: "next" });
    } else {
      Message.error('请至少选择一种追加出资')
    }
   
  };

  return (
    <div className="zs-new-explosive-coupon-play-step">
      <ShopSupplementForm field={field} />
      <div className="zs-new-explosive-coupon-play-step-footer">
        <Button
          data-test-id="scope-info-step-cancel-btn"
          onClick={() => onEvent && onEvent({ type: "cancel" })}
        >
          取消
        </Button>
        &nbsp;&nbsp;
        <Button
          data-test-id="scope-info-step-prev-btn"
          onClick={() => onEvent && onEvent({ type: "prev" })}
        >
          上一步
        </Button>
        &nbsp;&nbsp;
        <Button
          type="primary"
          data-test-id="scope-info-step-next-btn"
          onClick={onSubmit}
        >
          发布
        </Button>
      </div>
    </div>
  );
};

const PlayStepForExplosiveCoupon202409Wrap: React.FC<any> = (props) => {
  if (props?.subsidyInvestmentModel === "A") {
    return <BasicSubsidyContribution {...props} />;
  }
  return <PlayStepForExplosiveCoupon202409 {...props} />;
};

export default PlayStepForExplosiveCoupon202409Wrap;
