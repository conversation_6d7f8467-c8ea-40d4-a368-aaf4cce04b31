import { Form, Field, NumberPicker } from "@alifd/next";

interface Props {
  field: Field;
}
const formItemLayout = {
  labelCol: { span: 8, style: { color: "#333" } },
};

export default function BasicInvestmentForm({ field }: Props) {
  return (
    <Form
      field={field}
      labelAlign="left"
      {...formItemLayout}
      useLabelForErrorMessage
    >
      <Form.Item label="招商模式：">
        <div
          style={{
            lineHeight: "32px",
          }}
        >
          基础出资
        </div>
      </Form.Item>
      <Form.Item
        label="补贴金额："
        required
      >
        <NumberPicker
          style={{ width: 140 }}
          min={0.1}
          max={9999}
          innerAfter="元"
          precision={2}
          {...field.init("shopSubsidy", {
            rules: [
              {
                required: true,
                message: "填写补贴金额",
              },
            ],
          })}
          
        />
      </Form.Item>
      <Form.Item
        label="每日库存："
        required
      >
        <NumberPicker
          style={{ width: 140 }}
          min={1}
          max={9999}
          innerAfter="个"
          precision={0}
          {...field.init("dailyStock", {
            rules: [
              {
                required: true,
                message: "填写每日库存",
              },
            ],
          })}
          
        />
      </Form.Item>
    </Form>
  );
}
