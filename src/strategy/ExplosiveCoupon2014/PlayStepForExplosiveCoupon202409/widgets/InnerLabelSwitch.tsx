import { Switch } from "@alifd/next";

interface Props {
  label?: string
  value?: boolean
  onChange?: (value: boolean) => void
  name?: string
}

export default function InnerTitleSwitch({ label, value, onChange }: Props) {

  return (
    <div style={{
      display: 'flex', alignItems: 'center', justifyContent: 'space-between', padding: '16px 20px 0',
    }}>
      <div >{label}</div>
      <Switch checked={value} onChange={onChange} />
    </div>
  )
}