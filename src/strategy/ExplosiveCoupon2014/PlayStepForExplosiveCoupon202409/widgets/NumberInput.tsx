import { NumberPicker } from "@alifd/next";


interface Props {
  name?: string;
  disabled?: boolean;
  min?: number;
  max?: number;
  unit?: string;
  precision?: number
  value?: number;

  onChange?: (value: number | undefined) => void;
}

export  function NumberInput({
  disabled,
  min: pickerMin,
  max: pickerMax,
  unit,
  precision,
  value,
  onChange
}: Props) {

  return (
    <div>
      <NumberPicker
        style={{ width: 140 }}
        disabled={disabled}
        min={pickerMin}
        max={pickerMax}
        innerAfter={unit}
        precision={precision}
        value={value}
        onChange={(v: number) => {
          onChange?.( v)
        }}
      />
    </div>
  );
}

interface ThresholdProps {
  name?: string;
  disabled?: boolean;
  min?: number;
  max?: number;
  unit?: string;
  precision?: number
  value?: {
    threshold?: number;
    add?: number;
  };

  onChange?: (value: {
    threshold?: number;
    add?: number;
  }) => void;
}

export  function ThresholdNumberInput({
  disabled,
  min: pickerMin,
  max: pickerMax,
  unit,
  precision,
  value,
  onChange
}: ThresholdProps) {

  const handleChange = (key: 'threshold' | 'add', pickerValue: number) => {
    if (!onChange) return
    onChange({ ...value || {}, [key]: pickerValue, })
  }

  return (
    <div style={{lineHeight: "32px"}}>
      门槛 ≥ <NumberPicker
        style={{ width: 100 }}
        disabled={disabled}
        min={pickerMin}
        max={pickerMax}
        precision={precision}
        value={value?.threshold}
        onChange={(v) => {
          handleChange('threshold', v)
        }}
      /> 元，追补下限值 <NumberPicker
        style={{ width: 100 }}
        disabled={disabled}
        min={pickerMin}
        max={pickerMax}
        innerAfter={unit}
        precision={precision}
        value={value?.add}
        onChange={(v) => {
          handleChange('add', v)
        }}
      />
    </div>
  );
}


