import { Field, Form } from "@alifd/next";
import { NumberInput, InnerLabelSwitch } from "./widgets";
import { isValueSet } from "../../../common";
import { ThresholdNumberInput } from "./widgets/NumberInput";

interface Props {
  field: Field;
}

const formItemLayout = {
  labelCol: { span: 6, style: { color: "#333" } },
};
const formItemLayoutItem = {
  labelCol: { span: 10, style: { color: "#333" } },
};
const thresholdLayout = {
  labelCol: { span: 7, style: { color: "#333" } },
};

function validatethreshold(rule: any, value: any, callback: any) {
  if (isValueSet(value?.threshold) && !isValueSet(value?.add)) {
    callback("请填写追补下限值");
    return;
  }
  if (!isValueSet(value?.threshold) && isValueSet(value?.add)) {
    callback("请填写门槛");
    return;
  }
  callback();
}

function checkThresholds(supplementSubsidies: any) {
  const {
    thresholdSupplementSubsidy1,
    thresholdSupplementSubsidy2,
    thresholdSupplementSubsidy3,
  } = supplementSubsidies;

  const thresholds = [
    thresholdSupplementSubsidy1?.threshold,
    thresholdSupplementSubsidy2?.threshold,
    thresholdSupplementSubsidy3?.threshold,
  ];

  const validThresholds = thresholds.filter(Boolean);

  if (validThresholds.length > 1) {
    for (let i = 1; i < validThresholds.length; i++) {
      if (validThresholds[i]! <= validThresholds[i - 1]!) {
        return false; // 不是递增
      }
    }
  }
  return true;
}

export default function ShopSupplementForm({ field }: Props) {
  const validatortimeSupplement = (_rule: any, value: boolean) => {
    return new Promise((resolve, reject) => {
      if (value) {
        const {
          timeSupplement0To10Amount,
          timeSupplement10To14Amount,
          timeSupplement14To17Amount,
          timeSupplement17To21Amount,
          timeSupplement21ToEndAmount,
        }: any = field.getValues();
        if (
          isValueSet(timeSupplement0To10Amount) ||
          isValueSet(timeSupplement10To14Amount) ||
          isValueSet(timeSupplement14To17Amount) ||
          isValueSet(timeSupplement17To21Amount) ||
          isValueSet(timeSupplement21ToEndAmount)
        ) {
          resolve(true);
          return;
        }
        reject("请填写至少一项加码条件");
      }

      resolve(true);
    });
  };

  const validatethresholdSupplement = (_rule: any, value: any) => {
    return new Promise((resolve, reject) => {
      if (value) {
        const {
          thresholdSupplementSubsidy1,
          thresholdSupplementSubsidy2,
          thresholdSupplementSubsidy3,
        }: any = field.getValues();
        if (
          (isValueSet(thresholdSupplementSubsidy1?.threshold) &&
            isValueSet(thresholdSupplementSubsidy1?.add)) ||
          (isValueSet(thresholdSupplementSubsidy2?.threshold) &&
            isValueSet(thresholdSupplementSubsidy2?.add)) ||
          (isValueSet(thresholdSupplementSubsidy3?.threshold) &&
            isValueSet(thresholdSupplementSubsidy3?.add))
        ) {
          // 后一个门槛的值需要大于前一个
          if (
            !checkThresholds({
              thresholdSupplementSubsidy1,
              thresholdSupplementSubsidy2,
              thresholdSupplementSubsidy3,
            })
          ) {
            reject("后一个门槛的值需要大于前一个");
          } else {
            resolve(true);
          }
          return;
        } else {
          reject("请填写至少一项加码条件");
        }
      }

      resolve(true);
    });
  };

  return (
    <Form
      field={field}
      labelAlign="left"
      {...formItemLayout}
      useLabelForErrorMessage
    >
      <Form.Item label="招商模式：">
        <div
          style={{
            lineHeight: "32px",
          }}
        >
          商户追加出资
        </div>
      </Form.Item>
      <div className="shop-new-supplement-content-box">
        <div className="shop-new-supplement-content-box-label">通用加码:</div>
        <div className="shop-new-supplement-content-item">
          <Form.Item>
            <InnerLabelSwitch
              label="加码条件: 所有人群"
              name="allUserSupplementEnabled"
            />
          </Form.Item>
          {field.getValue("allUserSupplementEnabled") ? (
            <div className="userScopeSupplement">
              <Form.Item {...formItemLayoutItem} label="加码金额下限值">
                <NumberInput
                  min={0.1}
                  max={9999}
                  unit="元"
                  precision={2}
                  {...field.init("allUserSupplementSubsidy", {
                    rules: [
                      {
                        required: true,
                        message: "填写加码金额下限",
                      },
                    ],
                  })}
                />
              </Form.Item>
              <Form.Item {...formItemLayoutItem} label="每日库存下限值">
                <NumberInput
                  min={1}
                  max={9999}
                  unit="个"
                  {...field.init("allUserSupplementDailyStock", {
                    rules: [
                      {
                        required: true,
                        message: "填写每日库存下限",
                      },
                    ],
                  })}
                />
              </Form.Item>
            </div>
          ) : null}
        </div>
        <div className="shop-new-supplement-content-box-label">分人群加码:</div>
        <div className="shop-new-supplement-content-item">
          <Form.Item>
            <InnerLabelSwitch
              label="追补条件: 门店新客"
              name="newStoreUserSupplementEnabled"
            />
          </Form.Item>
          {field.getValue("newStoreUserSupplementEnabled") ? (
            <div className="userScopeSupplement">
              <Form.Item {...formItemLayoutItem} label="加码金额下限值">
                <NumberInput
                  min={0.1}
                  max={9999}
                  unit="元"
                  precision={2}
                  {...field.init("newStoreUserSupplementSubsidy", {
                    rules: [
                      {
                        required: true,
                        message: "填写加码金额下限",
                      },
                    ],
                  })}
                />
              </Form.Item>
              <Form.Item {...formItemLayoutItem} label="每日库存下限值">
                <NumberInput
                  min={1}
                  max={9999}
                  unit="个"
                  {...field.init("newStoreUserSupplementDailyStock", {
                    rules: [
                      {
                        required: true,
                        message: "填写每日库存下限",
                      },
                    ],
                  })}
                />
              </Form.Item>
            </div>
          ) : null}
        </div>
        <div className="shop-new-supplement-content-item">
          <Form.Item>
            <InnerLabelSwitch
              label="追补条件: 品牌新客"
              name="newBrandUserSupplementEnabled"
            />
          </Form.Item>
          {field.getValue("newBrandUserSupplementEnabled") ? (
            <div className="userScopeSupplement">
              <Form.Item {...formItemLayoutItem} label="加码金额下限值">
                <NumberInput
                  {...field.init("newBrandUserSupplementSubsidy", {
                    rules: [
                      {
                        required: true,
                        message: "填写加码金额下限",
                      },
                    ],
                  })}
                  min={0.1}
                  max={9999}
                  unit="元"
                  precision={2}
                />
              </Form.Item>
              <Form.Item {...formItemLayoutItem} label="每日库存下限值">
                <NumberInput
                  {...field.init("newBrandUserSupplementDailyStock", {
                    rules: [
                      {
                        required: true,
                        message: "填写每日库存下限",
                      },
                    ],
                  })}
                  min={1}
                  max={9999}
                  unit="个"
                />
              </Form.Item>
            </div>
          ) : null}
        </div>

        <div className="shop-new-supplement-content-item">
          <Form.Item>
            <InnerLabelSwitch
              label="追补条件: 供应商新客"
              name="newSupplierUserSupplementEnabled"
            />
          </Form.Item>
          {field.getValue("newSupplierUserSupplementEnabled") ? (
            <div className="userScopeSupplement">
              <Form.Item {...formItemLayoutItem} label="加码金额下限值">
                <NumberInput
                  {...field.init("newSupplierUserSupplementSubsidy", {
                    rules: [
                      {
                        required: true,
                        message: "填写加码金额下限",
                      },
                    ],
                  })}
                  min={0.1}
                  max={9999}
                  unit="元"
                  precision={2}
                />
              </Form.Item>
              <Form.Item {...formItemLayoutItem} label="每日库存下限值">
                <NumberInput
                  {...field.init("newSupplierUserSupplementDailyStock", {
                    rules: [
                      {
                        required: true,
                        message: "填写每日库存下限",
                      },
                    ],
                  })}
                  min={1}
                  max={9999}
                  unit="个"
                />
              </Form.Item>
            </div>
          ) : null}
        </div>
        <div className="shop-new-supplement-content-box-label">分时段加码:</div>
        <div className="shop-new-supplement-content-item">
          <Form.Item validator={validatortimeSupplement}>
            <InnerLabelSwitch
              label="追补条件: 特殊时段"
              name="timeSupplementEnabled"
            />
          </Form.Item>
          {field.getValue("timeSupplementEnabled") ? (
            <div className="timeSupplement">
              <Form.Item {...formItemLayoutItem} label="00:00-10:00追加金额">
                <NumberInput
                  {...field.init("timeSupplement0To10Amount")}
                  min={0.1}
                  max={9999}
                  unit="元"
                  precision={2}
                />
              </Form.Item>
              <Form.Item {...formItemLayoutItem} label="10:00-14:00追加金额">
                <NumberInput
                  {...field.init("timeSupplement10To14Amount")}
                  min={0.1}
                  max={9999}
                  unit="元"
                  precision={2}
                />
              </Form.Item>
              <Form.Item {...formItemLayoutItem} label="14:00-17:00追加金额">
                <NumberInput
                  {...field.init("timeSupplement14To17Amount")}
                  min={0.1}
                  max={9999}
                  unit="元"
                  precision={2}
                />
              </Form.Item>
              <Form.Item {...formItemLayoutItem} label="17:00-21:00追加金额">
                <NumberInput
                  {...field.init("timeSupplement17To21Amount")}
                  min={0.1}
                  max={9999}
                  unit="元"
                  precision={2}
                />
              </Form.Item>
              <Form.Item {...formItemLayoutItem} label="21:00-23:59追加金额">
                <NumberInput
                  {...field.init("timeSupplement21ToEndAmount")}
                  min={0.1}
                  max={9999}
                  unit="元"
                  precision={2}
                />
              </Form.Item>
              <Form.Item {...formItemLayoutItem} label="每日库存下限值">
                <NumberInput
                  {...field.init("timeSupplementDailyStock", {
                    rules: [
                      {
                        required: true,
                        message: "填写每日库存下限",
                      },
                    ],
                  })}
                  min={1}
                  max={9999}
                  unit="个"
                />
              </Form.Item>
            </div>
          ) : null}
        </div>
        <div className="shop-new-supplement-content-box-label">
          分门槛加码:{" "}
        </div>
        <div className="shop-new-supplement-content-item">
          <Form.Item validator={validatethresholdSupplement}>
            <InnerLabelSwitch
              label="加码条件: 特殊门槛"
              name="thresholdSupplementEnabled"
            />
          </Form.Item>
          {field.getValue("thresholdSupplementEnabled") ? (
            <div className="thresholdSupplement">
              <Form.Item>
                <ThresholdNumberInput
                  min={0.1}
                  max={9999}
                  unit="元"
                  precision={2}
                  {...field.init("thresholdSupplementSubsidy1", {
                    rules: [{ validator: validatethreshold }],
                  })}
                />
              </Form.Item>
              <Form.Item>
                <ThresholdNumberInput
                  min={0.1}
                  max={9999}
                  unit="元"
                  precision={2}
                  {...field.init("thresholdSupplementSubsidy2", {
                    rules: [{ validator: validatethreshold }],
                  })}
                />
              </Form.Item>
              <Form.Item>
                <ThresholdNumberInput
                  min={0.1}
                  max={9999}
                  unit="元"
                  precision={2}
                  {...field.init("thresholdSupplementSubsidy3", {
                    rules: [{ validator: validatethreshold }],
                  })}
                />
              </Form.Item>
              <Form.Item {...thresholdLayout} label="每日库存下限值">
                <NumberInput
                  min={1}
                  max={9999}
                  unit="个"
                  {...field.init("thresholdSupplementDailyStock", {
                    rules: [
                      {
                        required: true,
                        message: "填写每日库存下限",
                      },
                    ],
                  })}
                />
              </Form.Item>
            </div>
          ) : null}
        </div>
      </div>
    </Form>
  );
}
