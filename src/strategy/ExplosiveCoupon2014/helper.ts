import { isValueSet } from '../../common';
import { ActivityTypeEnum } from '../../constants'
import {
  ShopSupplementTS, BasicInvestmenntTS, ViewJsonType, ActivityRuleType, CouponPeriodRuleType, PeriodTimeType,
  AmountScenceAType, AmountScenceYUserScopeType, AmountScenceYTimeType,
  AmountScenceA1Type,
  AmountScenceThresholdType,
  CouponRule,
} from './type'


const formatNumberToString = (value?: number) => {
  if (isValueSet(value)) return '' + value;
  return undefined
}

export const transformPlayStepToViewJson = (value: BasicInvestmenntTS | ShopSupplementTS): ViewJsonType => {
  if ('shopSubsidy' in value) {
    return {
      activityType: ActivityTypeEnum.EXPLOSIVE_ORDER_COUPON,
      activityRule: [
        {
          amountScene: "A_WITHOUT_THRESHOLD",
          activityLimitRule: {
            dayCountLimit: value.dailyStock
          },
          couponRule: {
            shopSubsidyCny: formatNumberToString(value.shopSubsidy)
          }
        }
      ]
    }
  }

  const activityRule: ActivityRuleType[] = []

  if (value?.allUserSupplementEnabled) {
    // 所有人群
    activityRule.push({
      amountScene: "Y_WITH_BASIC",
      activityLimitRule: {
        dayCountLimit: value.allUserSupplementDailyStock,
      },
      couponRule: {
        shopSubsidyMinCny: formatNumberToString(value.allUserSupplementSubsidy),

      }
    })
  }

  if (value?.newStoreUserSupplementEnabled) {
    activityRule.push({
      amountScene: "Y_WITH_USER_SCOPE",
      activityLimitRule: {
        dayCountLimit: value.newStoreUserSupplementDailyStock,
      },
      activityMarketingInfo: {
        userScope: 4, // 门店新客
      },
      couponRule: {
        shopSubsidyMinCny: formatNumberToString(value.newStoreUserSupplementSubsidy),

      }
    })
  }
  if (value?.newBrandUserSupplementEnabled) {
    activityRule.push({
      amountScene: "Y_WITH_USER_SCOPE",
      activityLimitRule: {
        dayCountLimit: value.newBrandUserSupplementDailyStock,
      },
      activityMarketingInfo: {
        userScope: 11, // 品牌新客
      },
      couponRule: {
        shopSubsidyMinCny: formatNumberToString(value.newBrandUserSupplementSubsidy),

      }
    })
  }
  if(value?.newSupplierUserSupplementEnabled) {
    activityRule.push({
      amountScene: "Y_WITH_USER_SCOPE",
      activityLimitRule: {
        dayCountLimit: value.newSupplierUserSupplementDailyStock,
      },
      activityMarketingInfo: {
        userScope: 28, // 供应商新客
      },
      couponRule: {
        shopSubsidyMinCny: formatNumberToString(value.newSupplierUserSupplementSubsidy),
      }
    })
  }

  if (value?.timeSupplementEnabled) {
    activityRule.push({
      amountScene: "Y_WITH_TIME",
      activityLimitRule: {
        dayCountLimit: value.timeSupplementDailyStock
      },
      couponPeriodAndRule: [
        {
          periodTime: '00:00:00_10:00:00',
          shopSubsidyMinCny: formatNumberToString(value.timeSupplement0To10Amount)
        },
        {
          periodTime: '10:00:00_14:00:00',
          shopSubsidyMinCny: formatNumberToString(value.timeSupplement10To14Amount)
        },
        {
          periodTime: '14:00:00_17:00:00',
          shopSubsidyMinCny: formatNumberToString(value.timeSupplement14To17Amount)
        },
        {
          periodTime: '17:00:00_21:00:00',
          shopSubsidyMinCny: formatNumberToString(value.timeSupplement17To21Amount)
        },
        {
          periodTime: '21:00:00_23:59:59',
          shopSubsidyMinCny: formatNumberToString(value.timeSupplement21ToEndAmount)
        },
      ].filter((item) => isValueSet(item.shopSubsidyMinCny)) as CouponPeriodRuleType[]
    })
  }

  if (value?.thresholdSupplementEnabled) {
    activityRule.push({
      amountScene: "Y_WITH_STEP",
      activityLimitRule: {
        dayCountLimit: value.thresholdSupplementDailyStock
      },
      couponRule: [
        {
          conditionMinCny: formatNumberToString(value.thresholdSupplementSubsidy1?.threshold),
          shopSubsidyMinCny: formatNumberToString(value.thresholdSupplementSubsidy1?.add)
        },
        {
          conditionMinCny: formatNumberToString(value.thresholdSupplementSubsidy2?.threshold),
          shopSubsidyMinCny: formatNumberToString(value.thresholdSupplementSubsidy2?.add)
        },
        {
          conditionMinCny: formatNumberToString(value.thresholdSupplementSubsidy3?.threshold),
          shopSubsidyMinCny: formatNumberToString(value.thresholdSupplementSubsidy3?.add)
        },
      ].filter((item) => isValueSet(item.shopSubsidyMinCny))
    })
  }


  return {
    activityType: ActivityTypeEnum.EXPLOSIVE_ORDER_COUPON,
    activityRule
  }

}

const formatStringToNumber = (value?: string) => {
  if (isValueSet(value)) {
    return Number(value)
  }
  return undefined
}

const getTimeSupplementInfoByPeriodTime = (value: PeriodTimeType, data: CouponPeriodRuleType[]) => {
  const periodTimeInfo = data?.find((item) => item.periodTime === value);
  return periodTimeInfo ? formatStringToNumber(periodTimeInfo?.shopSubsidyMinCny)
    : undefined
}


export const transformViewJsonToPlayStep = (value: ViewJsonType): BasicInvestmenntTS | ShopSupplementTS => {
  const basicInvestmentInfo = value?.activityRule?.find((item) => item.amountScene === 'A_WITHOUT_THRESHOLD') as AmountScenceAType
  const commonInvestmentInfo = value?.activityRule?.find((item) => item.amountScene === 'Y_WITH_BASIC' || item.amountScene === 'Y_WITH_BASE_RULE') as AmountScenceA1Type
  const userScope4SupplementInfo = value?.activityRule?.find((item) => item.amountScene === 'Y_WITH_USER_SCOPE' && item.activityMarketingInfo.userScope === 4) as AmountScenceYUserScopeType
  const userScope9SupplementInfo = value?.activityRule?.find((item) => item.amountScene === 'Y_WITH_USER_SCOPE' && item.activityMarketingInfo.userScope === 11) as AmountScenceYUserScopeType
  const userScope28SupplementInfo = value?.activityRule?.find((item) => item.amountScene === 'Y_WITH_USER_SCOPE' && item.activityMarketingInfo.userScope === 28) as AmountScenceYUserScopeType
  const timeSupplementInfo = value?.activityRule?.find((item) => item.amountScene === 'Y_WITH_TIME') as AmountScenceYTimeType
  const thresholdSupplementInfo = value?.activityRule?.find((item) => item.amountScene === 'Y_WITH_STEP') as AmountScenceThresholdType
  if (isValueSet(basicInvestmentInfo)) {
    return {
      shopSubsidy: formatStringToNumber((basicInvestmentInfo?.couponRule as CouponRule[])[0]?.shopSubsidyCny)!,
      dailyStock: basicInvestmentInfo?.activityLimitRule?.dayCountLimit!
    }
  } else {
    let palyStepValue = {
      allUserSupplementEnabled: isValueSet(commonInvestmentInfo),
      allUserSupplementSubsidy: formatStringToNumber((commonInvestmentInfo?.couponRule as CouponRule[])?.[0]?.shopSubsidyMinCny)!,
      allUserSupplementDailyStock: commonInvestmentInfo?.activityLimitRule?.dayCountLimit!,
      newStoreUserSupplementEnabled: isValueSet(userScope4SupplementInfo),
      newStoreUserSupplementSubsidy: formatStringToNumber((userScope4SupplementInfo?.couponRule as CouponRule[])?.[0]?.shopSubsidyMinCny)!,
      newStoreUserSupplementDailyStock: userScope4SupplementInfo?.activityLimitRule?.dayCountLimit!,
      newBrandUserSupplementEnabled: isValueSet(userScope9SupplementInfo),
      newBrandUserSupplementSubsidy: formatStringToNumber((userScope9SupplementInfo?.couponRule as CouponRule[])?.[0]?.shopSubsidyMinCny)!,
      newBrandUserSupplementDailyStock: userScope9SupplementInfo?.activityLimitRule?.dayCountLimit!,
      newSupplierUserSupplementEnabled: isValueSet(userScope28SupplementInfo),
      newSupplierUserSupplementSubsidy: formatStringToNumber((userScope28SupplementInfo?.couponRule as CouponRule[])?.[0]?.shopSubsidyMinCny)!,
      newSupplierUserSupplementDailyStock: userScope28SupplementInfo?.activityLimitRule?.dayCountLimit!,

      timeSupplementEnabled: isValueSet(timeSupplementInfo),
      timeSupplement0To10Amount: getTimeSupplementInfoByPeriodTime('00:00:00_10:00:00', timeSupplementInfo?.couponPeriodAndRule),
      timeSupplement10To14Amount: getTimeSupplementInfoByPeriodTime('10:00:00_14:00:00', timeSupplementInfo?.couponPeriodAndRule),
      timeSupplement14To17Amount: getTimeSupplementInfoByPeriodTime('14:00:00_17:00:00', timeSupplementInfo?.couponPeriodAndRule),
      timeSupplement17To21Amount: getTimeSupplementInfoByPeriodTime('17:00:00_21:00:00', timeSupplementInfo?.couponPeriodAndRule),
      timeSupplement21ToEndAmount: getTimeSupplementInfoByPeriodTime('21:00:00_23:59:59', timeSupplementInfo?.couponPeriodAndRule),
      timeSupplementDailyStock: timeSupplementInfo?.activityLimitRule?.dayCountLimit!,

      thresholdSupplementEnabled: isValueSet(thresholdSupplementInfo),
      thresholdSupplementSubsidy1: {
        threshold: formatStringToNumber(thresholdSupplementInfo?.couponRule?.[0]?.conditionMinCny),
        add: formatStringToNumber(thresholdSupplementInfo?.couponRule?.[0]?.shopSubsidyMinCny)
      },
      thresholdSupplementSubsidy2: {
        threshold: formatStringToNumber(thresholdSupplementInfo?.couponRule?.[1]?.conditionMinCny),
        add: formatStringToNumber(thresholdSupplementInfo?.couponRule?.[1]?.shopSubsidyMinCny)
      },
      thresholdSupplementSubsidy3: {
        threshold: formatStringToNumber(thresholdSupplementInfo?.couponRule?.[2]?.conditionMinCny),
        add: formatStringToNumber(thresholdSupplementInfo?.couponRule?.[2]?.shopSubsidyMinCny)
      },
      thresholdSupplementDailyStock: thresholdSupplementInfo?.activityLimitRule?.dayCountLimit!
    }
    return palyStepValue
  }
  

  
}