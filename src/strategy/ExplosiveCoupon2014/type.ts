

export interface BasicInvestmenntTS {
  shopSubsidy: number;
  dailyStock: number;
}

interface ThresholdTS {
  threshold?: number;
  add?: number;
}

export interface ShopSupplementTS {
  allUserSupplementEnabled: boolean;
  allUserSupplementSubsidy: number; // 所有人群
  allUserSupplementDailyStock: number;
  newStoreUserSupplementEnabled: boolean;
  newStoreUserSupplementSubsidy: number; // 门店新客
  newStoreUserSupplementDailyStock: number;
  newBrandUserSupplementEnabled: boolean;
  newBrandUserSupplementSubsidy: number;// 品牌新客
  newBrandUserSupplementDailyStock: number;
  newSupplierUserSupplementEnabled: boolean;
  newSupplierUserSupplementSubsidy: number;// 供应商新客
  newSupplierUserSupplementDailyStock: number;

  timeSupplementEnabled: boolean; // 特殊时段
  timeSupplement0To10Amount?: number;
  timeSupplement10To14Amount?: number;
  timeSupplement14To17Amount?: number;
  timeSupplement17To21Amount?: number;
  timeSupplement21ToEndAmount?: number;
  timeSupplementDailyStock: number;

  thresholdSupplementEnabled: boolean; // 特殊门槛
  thresholdSupplementSubsidy1?: ThresholdTS;
  thresholdSupplementSubsidy2?: ThresholdTS;
  thresholdSupplementSubsidy3?: ThresholdTS;
  thresholdSupplementDailyStock: number;
}

// viewJsonType api传递
type ActivityLimitRule = {
  dayCountLimit?: number;
};

export type CouponRule = {
  shopSubsidyMinCny?: string;
  shopSubsidyCny?: string;
};

// 基础出资
export type AmountScenceAType = {
  amountScene: "A_WITHOUT_THRESHOLD";
  activityLimitRule: ActivityLimitRule;
  couponRule: CouponRule | CouponRule[];
};

// 通用出资
export type AmountScenceA1Type = {
  amountScene: "Y_WITH_BASIC" | "Y_WITH_BASE_RULE";
  activityLimitRule: ActivityLimitRule;
  couponRule: CouponRule;
};
// 人群出资
export type AmountScenceYUserScopeType = {
  amountScene: "Y_WITH_USER_SCOPE";
  activityLimitRule: ActivityLimitRule;
  activityMarketingInfo: {
    userScope: 4 | 11 | 28; // 4 门店新客 11 品牌新客 28 供应商新客
  };
  couponRule: CouponRule;
};

// 门槛出资
export type AmountScenceThresholdType = {
  amountScene: "Y_WITH_STEP";
  activityLimitRule: ActivityLimitRule;
  couponRule: {
    conditionMinCny?: string;
    shopSubsidyMinCny?: string;
  }[]
};

export type PeriodTimeType = '00:00:00_10:00:00' | '10:00:00_14:00:00' | '14:00:00_17:00:00' |
  '17:00:00_21:00:00' | '21:00:00_23:59:59'
export type CouponPeriodRuleType = {
  periodTime: PeriodTimeType;
  shopSubsidyMinCny?: string;
};
// 特殊时段出资
export type AmountScenceYTimeType = {
  amountScene: "Y_WITH_TIME";
  activityLimitRule: ActivityLimitRule;
  couponPeriodAndRule: CouponPeriodRuleType[];
};

export type ActivityRuleType = AmountScenceAType | AmountScenceA1Type | AmountScenceYUserScopeType | AmountScenceYTimeType | AmountScenceThresholdType;

export interface ViewJsonType {
  activityType: 1000045;
  activityRule: ActivityRuleType[];
};
