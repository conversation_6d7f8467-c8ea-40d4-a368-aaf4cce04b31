/**
 * 爆单红包  1000045
 */

import { maskMS, parseJsonSafe } from "../../common";
import { ActivitySignType, ActivityTypeEnum } from "../../constants";
import { ActivityStrategy } from "../../models";
import { CableBase } from "../base";
import {
  checkAndUploadRichDescription,
  genericCreateActivity,
} from "../mixins";
import { auditServiceForCoupon } from "../service";
import loadingDialog from "../../components/LoadingDialog";
import {
  transformViewJsonToPlayStep,
  transformPlayStepToViewJson,
} from "./helper";
import { BasicInvestmenntTS, ShopSupplementTS } from "./type";
import { renderItemExtView } from "./renderItemExtView";
import { cable } from "./StepA2/cable";

const ExplosiveCoupon: ActivityStrategy = {
  activityType: ActivityTypeEnum.EXPLOSIVE_ORDER_COUPON,
  generation: "v2",
  cable: (options: {
    investmentSchema: number;
    investmentType: number;
    activitySignType?: ActivitySignType;
    subsidyInvestmentModel?: string;
  }) => {
    if (options?.subsidyInvestmentModel === "STEP_A") {
     return cable();
    } else {
      return {
        ...CableBase,
        workspace: "ExukovQrae6xWn2rt4NgG",
        model: "6sm2J2vODjQ5ZxkIfo8ohg",
        async checkNext(
          { step, dataStore: _dataStore },
          creationContext,
          history
        ) {
          let dataStore = _dataStore;
          if (!dataStore._session) {
            dataStore._session = {};
          }
          if (step.name === "baseinfo") {
            if (dataStore.data.baseinfo?.richDescription) {
              const { hide } = loadingDialog.show({
                message: "活动信息检查中...",
              });
              try {
                const error = await checkAndUploadRichDescription({
                  richDescription: dataStore.data.baseinfo?.richDescription,
                  session: dataStore._session,
                });
                if (error) {
                  return error;
                }
              } finally {
                hide();
              }
            }
          }
          if (step.name === "playdata") {
            const { baseinfo, scope } = dataStore.data;
            const viewJson = transformPlayStepToViewJson(
              dataStore?.data?.playdata
            );
            // 活动时间和报名时间可能是长期有效
            const activityTime = baseinfo.activityTime?.isLongTerm
              ? {
                  isLongTermActivity: true,
                }
              : {
                  beginTime: baseinfo.activityTime.start.toDate().getTime(),
                  endTime: maskMS(baseinfo.activityTime.end.toDate().getTime()),
                };

            const signTime = baseinfo.signupTime?.isLongTerm
              ? {
                  isLongTermSign: true,
                }
              : {
                  signUpStartTime: baseinfo.signupTime.start.toDate().getTime(),
                  signUpEndTime: maskMS(
                    baseinfo.signupTime.end.toDate().getTime()
                  ),
                };

            const payload: any = {
              // 活动类型信息
              templateId: creationContext.templateId,
              investmentType: creationContext.investmentType,
              investmentSchema: creationContext.investmentSchema,
              subsidyInvestmentModel: creationContext.subsidyInvestmentModel,
              activityType: ActivityTypeEnum.EXPLOSIVE_ORDER_COUPON,
              // 基本信息
              name: baseinfo.name,
              description: baseinfo.description,
              remark: baseinfo.remark,
              ...activityTime,
              allowCancel: baseinfo.allowCancel,

              signUpShopType: baseinfo.signUpShopType,
              ...signTime,
              subsidyType: creationContext?.subsidyType,
              manageOrgList: baseinfo?.manageOrgList,

              // 授权审核人
              auditMemberList:
                baseinfo.auditMemberList?.length > 0
                  ? baseinfo.auditMemberList
                  : null,

              // 招商范围信息
              shopPoolId: scope.pool.value,
              shopPoolName: scope.pool.label,

              // 玩法信息
              viewJson: JSON.stringify(viewJson),
            };
            if (dataStore._session?.ossURL) {
              const ossURL = dataStore._session?.ossURL;
              payload.description = baseinfo.richDescription.text;
              payload.attributes = {
                hasRichDescription: "on",
                richDescriptionURL: ossURL,
              };
            }

            return await genericCreateActivity(
              payload,
              history,
              creationContext
            );
          }
        },
        auditService: auditServiceForCoupon,
      };
    }
  },

  isHideSignUpInfo(params?: { subsidyInvestmentModel?: string }) {
    return params?.subsidyInvestmentModel === "A";
  },

  hasAgentSubsidy() {
    return false;
  },
  isOfflineEnabled: function (): boolean {
    // 下线权限由后端控制
    return true;
  },
  isDingtalkLinkEnabled: () => false,
  noAssociatedActScene: true,
  hasAuditMemberList: () => true,
  isSupportModifyStock: () => false,
  isDetailInviteStatsOnMainActivityTableVisible: true,
  renderPlayDataInfoForTableView: function (): JSX.Element {
    return <></>;
  },
  renderActivityRuleForTable(viewJson: string) {
    try {
      const viewDtoToCopy = parseJsonSafe(viewJson);
      const playdata: BasicInvestmenntTS | ShopSupplementTS =
        transformViewJsonToPlayStep(viewDtoToCopy);
      if ("shopSubsidy" in playdata) {
        return (
          <div>
            <span>优惠 {playdata.shopSubsidy} 元，</span>
            <span>库存 {playdata.dailyStock} 个</span>
          </div>
        );
      }
      const {
        allUserSupplementEnabled,
        allUserSupplementSubsidy,
        allUserSupplementDailyStock,
        newStoreUserSupplementEnabled,
        newStoreUserSupplementSubsidy,
        newStoreUserSupplementDailyStock,
        newBrandUserSupplementEnabled,
        newBrandUserSupplementSubsidy,
        newBrandUserSupplementDailyStock,
        newSupplierUserSupplementEnabled,
        newSupplierUserSupplementSubsidy,
        newSupplierUserSupplementDailyStock,
        timeSupplementEnabled,
        thresholdSupplementEnabled,
      } = playdata;
      return (
        <div>
          {allUserSupplementEnabled ? (
            <div>
              <span>通用加码下限 {allUserSupplementSubsidy} 元，</span>
              <span>库存下限 {allUserSupplementDailyStock} 个；</span>
            </div>
          ) : null}
          {newStoreUserSupplementEnabled ? (
            <div>
              <span>门店新客加码下限 {newStoreUserSupplementSubsidy} 元，</span>
              <span>库存下限 {newStoreUserSupplementDailyStock} 个；</span>
            </div>
          ) : null}
          {newBrandUserSupplementEnabled ? (
            <div>
              <span>品牌新客加码下限 {newBrandUserSupplementSubsidy} 元，</span>
              <span>库存下限 {newBrandUserSupplementDailyStock} 个；</span>
            </div>
          ) : null}
          {newSupplierUserSupplementEnabled ? (
            <div>
              <span>
                供应商新客加码下限 {newSupplierUserSupplementSubsidy} 元，
              </span>
              <span>库存下限 {newSupplierUserSupplementDailyStock} 个；</span>
            </div>
          ) : null}
          {timeSupplementEnabled ? (
            <div>分时段加码（请前往活动详情查看）</div>
          ) : null}
          {thresholdSupplementEnabled ? (
            <div>特殊门槛加码（请前往活动详情查看）</div>
          ) : null}
        </div>
      );
    } catch (e) {
      console.error(e);
      return "";
    }
  },

  renderGroupItemExtView: (record) => {
    return renderItemExtView(record);
  },
  renderOfflineGroupItemExtView: ({ record }) => {
    return renderItemExtView(record);
  },

  isSupportAgent: false,
  gotoLegacyDetail: function (): void {
    return undefined;
  },
  renderActivityRuleForTableLegacy: function () {
    return "";
  },
  auditMemberLimit: 50,
};

export default ExplosiveCoupon;
