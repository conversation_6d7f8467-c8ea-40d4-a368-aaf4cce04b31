import { useState, useEffect } from "react";
import * as api from "../../../api";
import { transformViewJsonToPlayStep } from "../helper";
import { ViewJsonType } from "../type";
import "./style.scss";

const PlayViewForExplosiveCoupon202409: React.FC<any> = ({ viewJson }) => {
  let ready = false;
  let viewJsonObj: ViewJsonType = {} as ViewJsonType;

  try {
    viewJsonObj = JSON.parse(viewJson);
   
    if (viewJsonObj == null) return null;
    ready = true;
  } catch (error) {
    console.error(error);
  }
  if (!ready) {
    return null;
  }
  const playdata = transformViewJsonToPlayStep(viewJsonObj);
  if ("shopSubsidy" in playdata) {
    return (
      <div className="zs-new-explosive-coupon-play-data-detail">
      <div className="play-data-card">
        <div className="play-data-title">基础出资</div>
        <div className="play-data-content-item">
          <div className="play-data-content-item-label">补贴金额：</div>
          <div>{playdata.shopSubsidy} 元</div>
        </div>
        <div className="play-data-content-item">
          <div className="play-data-content-item-label">每日库存：</div>
          <div>{playdata.dailyStock} 个 </div>
        </div>
      </div>
      </div>

    );
  } else {
    const {
      allUserSupplementEnabled,
      allUserSupplementSubsidy,
      allUserSupplementDailyStock,
      newStoreUserSupplementEnabled,
      newStoreUserSupplementSubsidy,
      newStoreUserSupplementDailyStock,
      newBrandUserSupplementEnabled,
      newBrandUserSupplementSubsidy,
      newBrandUserSupplementDailyStock,
      newSupplierUserSupplementEnabled,
      newSupplierUserSupplementSubsidy,
      newSupplierUserSupplementDailyStock,
  
      timeSupplementEnabled,
      timeSupplement0To10Amount,
      timeSupplement10To14Amount,
      timeSupplement14To17Amount,
      timeSupplement17To21Amount,
      timeSupplement21ToEndAmount,
      timeSupplementDailyStock,
      thresholdSupplementEnabled,
      thresholdSupplementSubsidy1,
      thresholdSupplementSubsidy2,
      thresholdSupplementSubsidy3,
      thresholdSupplementDailyStock,
  
    } = playdata;

    return (
      <div className="zs-new-explosive-coupon-play-data-detail">
        <div className="play-data-card">
          <div className="play-data-title">商家追加出资</div>
          {allUserSupplementEnabled ? (
            <div className="play-data-content">
              <div className="play-data-content-title">加码条件：通用加码</div>
              <div className="play-data-content-item">
                <div className="play-data-content-item-label">加码金额下限值：</div>
                <div>{allUserSupplementSubsidy} 元 </div>
              </div>
              <div className="play-data-content-item">
                <div className="play-data-content-item-label">每日库存下限值：</div>
                <div>{allUserSupplementDailyStock} 个</div>
              </div>
            </div>
          ) : null}
          {newStoreUserSupplementEnabled ? (
            <div className="play-data-content">
              <div className="play-data-content-title">加码条件：门店新客</div>
              <div className="play-data-content-item">
                <div className="play-data-content-item-label">加码金额下限值：</div>
                <div>{newStoreUserSupplementSubsidy} 元 </div>
              </div>
              <div className="play-data-content-item">
                <div className="play-data-content-item-label">每日库存下限值：</div>
                <div>{newStoreUserSupplementDailyStock} 个</div>
              </div>
            </div>
          ) : null}
          {newBrandUserSupplementEnabled ? (
            <div className="play-data-content">
              <div className="play-data-content-title">加码条件：品牌新客</div>
              <div className="play-data-content-item">
                <div className="play-data-content-item-label">加码金额下限值：</div>
                <div>{newBrandUserSupplementSubsidy} 元 </div>
              </div>
              <div className="play-data-content-item">
                <div className="play-data-content-item-label">每日库存下限值：</div>
                <div>{newBrandUserSupplementDailyStock} 个</div>
              </div>
            </div>
          ) : null}
          {newSupplierUserSupplementEnabled ? (
            <div className="play-data-content">
              <div className="play-data-content-title">加码条件：供应商新客</div>
              <div className="play-data-content-item">
                <div className="play-data-content-item-label">加码金额下限值：</div>
                <div>{newSupplierUserSupplementSubsidy} 元 </div>
              </div>
              <div className="play-data-content-item">
                <div className="play-data-content-item-label">每日库存下限值：</div>
                <div>{newSupplierUserSupplementDailyStock} 个</div>
              </div>
            </div>
          ) : null}
          {timeSupplementEnabled ? (
            <div className="play-data-content">
              <div className="play-data-content-title">追补条件：特殊时段</div>
              {timeSupplement0To10Amount ? (
                <div className="play-data-content-item">
                  <div className="play-data-content-item-label">
                    00:00-10:00加码金额下限值：
                  </div>
                  <div>{timeSupplement0To10Amount} 元</div>
                </div>
              ) : null}
              {timeSupplement10To14Amount ? (
                <div className="play-data-content-item">
                  <div className="play-data-content-item-label">
                    10:00-14:00加码金额下限值：
                  </div>
                  <div>{timeSupplement10To14Amount} 元</div>
                </div>
              ) : null}
              {timeSupplement14To17Amount ? (
                <div className="play-data-content-item">
                  <div className="play-data-content-item-label">
                    14:00-17:00加码金额下限值：
                  </div>
                  <div>{timeSupplement14To17Amount} 元</div>
                </div>
              ) : null}
              {timeSupplement17To21Amount ? (
                <div className="play-data-content-item">
                  <div className="play-data-content-item-label">
                    17:00-21:00加码金额下限值：
                  </div>
                  <div>{timeSupplement17To21Amount} 元</div>
                </div>
              ) : null}
              {timeSupplement21ToEndAmount ? (
                <div className="play-data-content-item">
                  <div className="play-data-content-item-label">
                    21:00-23:59加码金额下限值：
                  </div>
                  <div>{timeSupplement21ToEndAmount} 元</div>
                </div>
              ) : null}
  
              <div className="play-data-content-item">
                <div className="play-data-content-item-label">每日库存下限值：</div>
                <div>{timeSupplementDailyStock} 个</div>
              </div>
            </div>
          ) : null}
          {thresholdSupplementEnabled ? (
            <div className="play-data-content">
              <div className="play-data-content-title">追补条件：特殊门槛</div>
              {thresholdSupplementSubsidy1?.add  ? (
                <div className="play-data-content-item">
                  <div className="play-data-content-item-label">门槛 {thresholdSupplementSubsidy1?.threshold} 元，加码金额下限值：</div>
                  <div>{thresholdSupplementSubsidy1?.add} 元</div>
                </div>
              ) : null}
              {thresholdSupplementSubsidy2?.add  ? (
                <div className="play-data-content-item">
                  <div className="play-data-content-item-label">门槛 {thresholdSupplementSubsidy2?.threshold} 元，加码金额下限值：</div>
                  <div>{thresholdSupplementSubsidy2?.add} 元</div>
                </div>
              ) : null}
              {thresholdSupplementSubsidy3?.add ? (
                <div className="play-data-content-item">
                  <div className="play-data-content-item-label">门槛 {thresholdSupplementSubsidy3?.threshold} 元，加码金额下限值：</div>
                  <div>{thresholdSupplementSubsidy3?.add} 元</div>
                </div>
              ) : null}
              <div className="play-data-content-item">
                <div className="play-data-content-item-label">每日库存下限值：</div>
                <div>{thresholdSupplementDailyStock} 元</div>
              </div>
            </div>
          ) : null}
        </div>
      </div>
    );

  }
};

const PlaydataViewForSubExplosiveCouponWrap: React.FC<any> = ({
  activityInfo,
}) => {
  const [detail, setDetail] = useState<any>();

  useEffect(() => {
    api.subActivity.getDetail(activityInfo.activityId).then((res) => {
      setDetail(res.data);
    });
  }, [activityInfo.activityId]);
  return (
    <div className="zs-act-info">
      <div className="zs-act-area">
        <div className="zs-act-area-title" style={{ marginBottom: 30 }}>
          玩法设置
        </div>
        <div className="zs-act-section">
          <PlayViewForExplosiveCoupon202409 viewJson={detail?.viewJson} />
        </div>
      </div>
    </div>
  );
};

export default PlaydataViewForSubExplosiveCouponWrap;
