import {
  transformFormData2ViewJson,
  transformViewJson2FormData,
} from "./helper";
import { v4 as uuid4 } from "uuid";

// 使用jest.spyOn来模拟uuid.v4函数
jest.mock("uuid", () => ({
  v4: jest.fn(),
}));

describe("ItemExchange", () => {
  describe("helper", () => {
    describe("transformFormData2ViewJson", () => {
      // - 招商类型:营销IP招商-招商模版:买一赠一
      test("招商类型:日常招商", () => {
        const viewJson = transformFormData2ViewJson({
          baseinfo: {
            activityTime: {
              weeks: [0, 1, 2, 3, 4, 5, 6],
              batchRange: {
                type: "all",
                list: [{ start: "00:00", end: "23:59" }],
              },
            },
          },
          playdata: {
            userScope: 0, // 限制人群
            deliveryType: 1,
            activityService: [
              {
                name: "优先02",
                priority: 2,
                image:
                  "https://img.alicdn.com/imgextra/i4/175656/O1CN011reV6wG8LTcO6SN_!!175656-2-eleretailmkt.png",
                commodityListFilePath:
                  "https://bwm-private-b.oss-cn-hangzhou.aliyuncs.com/mkt/329684-316dfa89-c556-43b2-a0ef-de4c42ab9f67.xlsx",
                rule: {
                  subsidy: {
                    platformSubsidyMax: 0,
                  },
                  discount: 0.02,
                },
              },
              {
                name: "优先03",
                priority: 3,
                image:
                  "https://img.alicdn.com/imgextra/i4/175656/O1CN011reV6wG8LTeVhMS_!!175656-2-eleretailmkt.png",
                commodityListFilePath:
                  "https://bwm-private-b.oss-cn-hangzhou.aliyuncs.com/mkt/329684-c0f6f83f-b139-4d66-b226-52d2f860dba0.xlsx",
                rule: {
                  subsidy: {
                    platformSubsidyMax: 0,
                  },
                  discount: 0.03,
                },
              },
            ],
            userTotalCountLimit: 6,
            budget: { budgetId: "foobar", budgetName: "foobar" },
          },
        });
        expect(viewJson).toStrictEqual({
          activityType: 1000042,
          activityLimitRule: {
            userTotalCountLimit: 6,
          },
          activityMarketingInfo: {
            budgetId: "foobar",
            budgetName: "foobar",
            deliveryType: 1,
            userScope: 0,
          },
          activityService: [
            {
              name: "优先02",
              priority: 2,
              image:
                "https://img.alicdn.com/imgextra/i4/175656/O1CN011reV6wG8LTcO6SN_!!175656-2-eleretailmkt.png",
              commodityListFilePath: undefined,
              rule: {
                subsidy: {
                  platformSubsidyMax: 0,
                },
                discount: 0.02,
                discountType: 3,
              },
            },
            {
              name: "优先03",
              priority: 3,
              image:
                "https://img.alicdn.com/imgextra/i4/175656/O1CN011reV6wG8LTeVhMS_!!175656-2-eleretailmkt.png",
              commodityListFilePath: undefined,
              rule: {
                subsidy: {
                  platformSubsidyMax: 0,
                },
                discount: 0.03,
                discountType: 3,
              },
            },
          ],
          weekday: "0,1,2,3,4,5,6",
          period: [
            {
              openTime: "00:00:00",
              closeTime: "23:59:59",
            },
          ],
        });
      });
    });

    describe("transformViewJson2FormData", () => {
      const mockUuid = "123e4567-e89b-12d3-a456-************";
      beforeEach(() => {
        // 在每个测试用例运行之前，重置模拟的UUID值
        (uuid4 as jest.Mock).mockReturnValue(mockUuid);
      });
      test("招商类型:日常招商", () => {
        const viewJson = JSON.stringify({
          activityLimitRule: { userTotalCountLimit: 22 },
          activityMarketingInfo: {
            budgetId: "400000000003202329",
            budgetName: "2022招商活动测试预算",
            deliveryChannel: 0,
            deliveryType: 2,
            outBizContext: {},
            userScope: 4,
          },
          activityService: [
            {
              activityId: 6338842003,
              commodityListFilePath:
                "https://bwm-private-b.oss-cn-hangzhou.aliyuncs.com/mkt/222146-fd1b718f-af41-410e-9646-8d2b94212270.xlsx",
              image:
                "https://img.alicdn.com/imgextra/i1/175656/O1CN011reV6rvuBtaG0vl_!!175656-2-eleretailmkt.png",
              key: mockUuid,
              limitRule: { userTotalCountLimit: 22 },
              mktActivityId: 6000001945236001,
              mktServiceTemplateId: 23001,
              name: "优先01",
              priority: 1,
              rule: {
                discount: "0.01",
                discountType: 3,
                subsidy: { platformSubsidyMax: "0.02" },
              },
              serviceTemplateId: 130001,
            },
            {
              activityId: 6338842003,
              commodityListFilePath:
                "https://bwm-private-b.oss-cn-hangzhou.aliyuncs.com/mkt/222146-4688b052-8d61-4c06-a7e5-c136ff2f0ec5.xlsx",
              image:
                "https://img.alicdn.com/imgextra/i4/175656/O1CN011reV6wG8LTcO6SN_!!175656-2-eleretailmkt.png",
              key: mockUuid,
              limitRule: { userTotalCountLimit: 22 },
              mktActivityId: 6000001945236001,
              mktServiceTemplateId: 23002,
              name: "优先02",
              priority: 2,
              rule: {
                discount: "0.02",
                discountType: 3,
                subsidy: { platformSubsidyMax: "0.03" },
              },
              serviceTemplateId: 130002,
            },
            {
              activityId: 6338842003,
              commodityListFilePath:
                "https://bwm-private-b.oss-cn-hangzhou.aliyuncs.com/mkt/222146-91278643-2a6f-4182-a2a0-e5bb7830956c.xlsx",
              image:
                "https://img.alicdn.com/imgextra/i4/175656/O1CN011reV6wG8LTeVhMS_!!175656-2-eleretailmkt.png",
              key: mockUuid,
              limitRule: { userTotalCountLimit: 22 },
              mktActivityId: 6000001945236001,
              mktServiceTemplateId: 23003,
              name: "优先03",
              priority: 3,
              rule: {
                discount: "0.03",
                discountType: 3,
                subsidy: { platformSubsidyMax: "0.04" },
              },
              serviceTemplateId: 130003,
            },
          ],
          activityType: 1000042,
          attributes: {
            hasRichDescription: "on",
            businessForm: "[2025000200040001,2025000300010002]",
            businessLine: "[20250002,20250003]",
            richDescriptionURL:
              "https://bwm-private-b.oss-cn-hangzhou.aliyuncs.com/mkt/cb0aade1f8286fb47a6c54f87bb95dfe?Expires=2033269926&OSSAccessKeyId=LTAIhP07XE91RzLJ&Signature=vnbwFCxhvlmAj4u1dg4fcpvYdC8%3D",
          },
          isSupportMerchantModifyActPrice: false,
          periodDTOList: [
            {
              closeTime: "03:04:59",
              openTime: "00:01:00",
              weekday: "0,1,2,3,4,5,6",
            },
            {
              closeTime: "16:01:59",
              openTime: "05:01:00",
              weekday: "0,1,2,3,4,5,6",
            },
            {
              closeTime: "23:59:59",
              openTime: "21:39:00",
              weekday: "0,1,2,3,4,5,6",
            },
          ],
          weekday: "0,1,2,3,4,5,6",
        });
        const formData = transformViewJson2FormData({
          viewJson,
        });
        expect(formData).toStrictEqual({
          userScope: 4,
          userTotalCountLimit: 22,
          deliveryType: 2,
          budget: undefined,
          activityService: [
            {
              name: "优先01",
              priority: 1,
              image:
                "https://img.alicdn.com/imgextra/i1/175656/O1CN011reV6rvuBtaG0vl_!!175656-2-eleretailmkt.png",
              key: mockUuid,
              rule: {
                discount: "0.01",
                subsidy: undefined,
              },
              commodityList: undefined,
            },
            {
              name: "优先02",
              priority: 2,
              image:
                "https://img.alicdn.com/imgextra/i4/175656/O1CN011reV6wG8LTcO6SN_!!175656-2-eleretailmkt.png",
              key: mockUuid,
              rule: {
                discount: "0.02",
                subsidy: undefined,
              },
              commodityList: undefined,
            },
            {
              name: "优先03",
              priority: 3,
              image:
                "https://img.alicdn.com/imgextra/i4/175656/O1CN011reV6wG8LTeVhMS_!!175656-2-eleretailmkt.png",
              key: mockUuid,
              rule: {
                discount: "0.03",
                subsidy: undefined,
              },
              commodityList: undefined,
            },
          ],
        });
      });
      test("招商类型:日常招商 --异常情况", () => {
        const viewJson = undefined;
        const formData = transformViewJson2FormData({
          viewJson,
        });
        expect(formData).toStrictEqual(null);
      });
    });
  });
});
