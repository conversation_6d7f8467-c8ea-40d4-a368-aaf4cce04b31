import { Message } from "@alifd/next";

import * as api from "../../api";
import loadingDialog from "../../components/LoadingDialog";
import {
  ActivityTypeEnum,
  InvestmentSchemaEnum,
  InvestmentTypeEnum,
} from "../../constants";
import { ActivityStrategy } from "../../models";
import {
  CableBase,
  resolveCreateActivityCommonData,
  resolveTimeRangeBatchFromMarketplayValue,
} from "../base";
import {
  resolveCommodityAuditProps,
  checkValidateCopyActivityResult,
  checkAndUploadRichDescription,
  resolveRichDescription,
  checkNextCommoditySignupRuleForAlpha,
  checkNextTimeForAlpha
} from "../mixins";
import {
  transformFormData2ViewJson,
  transformViewJson2FormData,
} from "./helper";
import { ItemExchangePlaydata, ItemExchangePlaydataForDetail } from "./types";
import { numberType } from "./PlayStepForItemExchange/DiscountRule";
import moment from "moment";

const hgAuditCondition = (data: {
  hgPassCondition: {
    categoryCorrect: any;
    nameKeywordConditionEnabled: any;
    nameKeywordCondition: any;
    tpcInfoConditionEnabled: any;
    tpcInfoConditionList: any;
  };
  hgRefuseCondition: {
    nameKeywordConditionEnabled: any;
    nameKeywordCondition: any;
  };
}) => {
  const condition: any = {};
  if (data.hgPassCondition) {
    const {
      categoryCorrect,
      nameKeywordConditionEnabled,
      nameKeywordCondition,
      tpcInfoConditionEnabled,
      tpcInfoConditionList,
    } = data.hgPassCondition;
    condition.hgPassCondition = {
      commodityCategoryCorrect: !!categoryCorrect,
      commodityNameKeywordCorrect: !!nameKeywordConditionEnabled,
      commodityNameKeywordCondition: nameKeywordConditionEnabled
        ? nameKeywordCondition
        : null,
      commodityTpcCorrect: !!tpcInfoConditionEnabled,
      commodityAuditTpcInfoCondition: tpcInfoConditionEnabled
        ? { condition: tpcInfoConditionList, matchType: 1 }
        : null,
    };
  } else {
    // 未设置任何通过条件
    condition.hgPassCondition = {
      commodityCatetgoryCorrect: false,
      commodityNameKeywordCorrect: false,
      commodityNameKeywordCondition: null,
      commodityTpcCorrect: false,
      commodityAuditTpcInfoCondition: null,
    };
  }
  if (data.hgRefuseCondition) {
    const { nameKeywordConditionEnabled, nameKeywordCondition } =
      data.hgRefuseCondition;
    condition.hgRefuseCondition = {
      commodityNameKeywordRefuse: !!nameKeywordConditionEnabled,
      commodityNameKeywordCondition: nameKeywordConditionEnabled
        ? nameKeywordCondition
        : null,
    };
  } else {
    // 未设置拒绝条件，按要求补充默认值
    condition.hgRefuseCondition = {
      commodityNameKeywordRefuse: false,
      commodityNameKeywordCondition: null,
    };
  }
  return condition;
};

const ItemExchange: ActivityStrategy = {
  activityType: ActivityTypeEnum.ITEM_RXCHANGE,
  cable: {
    ...CableBase,
    workspace: "ExukovQrae6xWn2rt4NgG",
    model: "acBpWuG06Pf7J69E6ZW1vW",
    async checkNext(checkNextContext, creationContext, history) {
      const { step, dataStore } = checkNextContext;
      if (!dataStore._session) {
        dataStore._session = {};
      }
      if (step.name === "baseinfo") {
        if (dataStore.data.baseinfo?.richDescription) {
          const { hide } = loadingDialog.show({
            message: "活动信息检查中...",
          });
          try {
            const error = await checkAndUploadRichDescription({
              richDescription: dataStore.data.baseinfo?.richDescription,
              session: dataStore._session,
            });
            if (error) {
              return error;
            }
          } finally {
            hide();
          }
        }
      }
      if (step.name === "baseinfo") {
        const timeError = await checkNextTimeForAlpha(
          { step, dataStore },
          creationContext
        );
        if (timeError) {
          return timeError;
        }
      }
      if (step.name === "scope") {
        const commoditySignupRuleError =
          await checkNextCommoditySignupRuleForAlpha(
            checkNextContext,
            creationContext
          );
        if (commoditySignupRuleError) {
          return commoditySignupRuleError;
        }
      }
      if (step.name === "playdata") {
        const { baseinfo, scope, playdata } = dataStore.data;
        const commonData = resolveCreateActivityCommonData(
          ActivityTypeEnum.ITEM_RXCHANGE,
          checkNextContext,
          creationContext
        );
        const viewJson = transformFormData2ViewJson({
          baseinfo,
          playdata,
        });
        delete commonData.dataMap;

        const commodityAuditProps: any = resolveCommodityAuditProps(scope);
        const hgCommodtyAuditProps = hgAuditCondition(scope);

        const payload: any = {
          ...commonData,

          // 自动审核规则
          commodityAuditCondition: {
            ...commodityAuditProps.commodityAuditCondition,
            ...hgCommodtyAuditProps,
          },
          commodityAuditType: commodityAuditProps.commodityAuditType,

          // 降套补 商品清退规则
          commodityRemoveCondition: {
            removeCondition: scope.removeCondition,
            hgRemoveCondition: scope.exchangeRemoveCondition,
          },

          auditMemberList:
            baseinfo.auditMemberList?.length > 0
              ? baseinfo.auditMemberList
              : null,

          // 报名规则
          reviewRuleId: scope.rule?.ruleId,
          reviewRuleName: scope.rule?.name,

          viewJson: JSON.stringify(viewJson),
        };

        const { hide, updateMessage } = loadingDialog.show({
          message: creationContext.copy ? "活动信息校验中..." : "活动创建中...",
        });

        if (dataStore._session?.ossURL) {
          const ossURL = dataStore._session?.ossURL;
          payload.description = baseinfo.richDescription.text;
          payload.attributes = {
            ...payload.attributes,
            hasRichDescription: "on",
            richDescriptionURL: ossURL,
          };
        }

        try {
          if (creationContext.copy) {
            const res = await api.subActivity.validateCopyActivityData({
              copyActivityId: creationContext.activity?.activityId,
              ...payload,
            });
            const copyError = checkValidateCopyActivityResult(res);
            if (copyError) {
              return copyError;
            }
            updateMessage("活动创建中...");
          }
          const res = await api.subActivity.create(payload);
          if (res.success) {
            Message.success("创建成功");
            if (creationContext.activitySceneId) {
                  history.push(
                    "/sub-activity/" +
                      res.data +
                      "?activitySceneId=" +
                      creationContext.activitySceneId +
                      "&activityPlanId=" +
                      creationContext.activityPlanId
                  );
                } else {
                  history.push("/sub-activity/" + res.data);
                }
          } else {
            return { message: res.errorMessage };
          }
        } catch (e: any) {
          return { message: e?.message || "系统异常" };
        } finally {
          hide();
        }
      }
    },
  },

  hasAgentSubsidy() {
    return false;
  },
  isSupportAgent: false,
  isOfflineEnabled: function (state: number): boolean {
    // 活动状态: 1:未开始 2:活动中 3:活动暂停 4:活动取消 5:活动结束 8: 预算熔断
    return [1, 2, 3, 8].includes(state);
  },

  isDingtalkLinkEnabled: () => true,
  hasAuditMemberList: () => true,
  isSupportModifyStock: () => false,
  isItemAuditStatsVisible: true,
  isDetailInviteStatsOnMainActivityTableVisible: true,
  renderPlayDataInfoForTableView: function (): JSX.Element {
    return <></>;
  },
  gotoLegacyDetail: function (): void {},
  renderActivityRuleForTable(viewJson: any) {
    try {
      const data: ItemExchangePlaydata = JSON.parse(viewJson);
      const activityService = data.activityService;
      return (
        <>
          {activityService?.map((r: any, i: number) => (
            <div key={i} style={{ marginBottom: 10 }}>
              <div>
                服务类型（{numberType[i]}） 服务名称：{r.name}， 活动价：
                {r.rule.discount || 0}元，平台补贴上限：
                {r.rule.subsidy?.platformSubsidyMax || 0}元
              </div>
            </div>
          ))}
        </>
      );
    } catch (e) {
      console.error(e);
      return <></>;
    }
  },
  renderActivityRuleForTableLegacy: function () {
    return <></>;
  },
  isCopyEnabled({ investmentSchema, investmentType }) {
    // 目前只有日常招商-日常招商
    return (
      investmentSchema === InvestmentSchemaEnum.RICHANG &&
      investmentType === InvestmentTypeEnum.RICHANG
    );
  },
  async resolveCopyData(creationContext) {
    let viewJson: ItemExchangePlaydataForDetail;
    try {
      viewJson =
        creationContext.activity &&
        JSON.parse(creationContext.activity?.viewJson);
      const { periodDTOList, weekday } = viewJson || {};
      const timeRangeBatch = resolveTimeRangeBatchFromMarketplayValue(
        JSON.stringify(
          periodDTOList.map((item) => {
            return { start: item.openTime, end: item.closeTime };
          })
        )
      );

      // 获取富文本描述信息
      const richDescription = await resolveRichDescription(creationContext);

      const fields = transformViewJson2FormData({
        viewJson: creationContext.activity?.viewJson,
      });

      return {
        baseinfo: {
          name: creationContext.activity?.name,
          richDescription,
          remark: creationContext.activity?.remark,
          activityTime: {
            start: moment(creationContext.activity?.beginTime),
            end: moment(creationContext.activity?.endTime),
            weeks: weekday
              .split(",")
              .filter(Boolean)
              .map((d: string) => +d),
            batchRange: timeRangeBatch,
          },
          auditMemberList: creationContext.activity?.auditMemberList || [],
          signupTime: {
            start: moment(creationContext.activity?.signUpStartTime),
            end: moment(creationContext.activity?.signUpEndTime),
          },
          allowCancel: creationContext.activity?.allowCancel,
          signUpShopType: creationContext.activity?.signUpShopType,
        },
        scope: {
          // 复制活动，门店池和报名规则均清空
          commodityAuditType: 1,
        },
        playdata: fields,
      };
    } catch (e) {
      console.error(e);
      return {};
    }
  },
};

export default ItemExchange;
