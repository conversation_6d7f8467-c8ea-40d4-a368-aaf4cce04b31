import "./style.scss";

import React, { useEffect, useMemo, useState } from "react";

import * as api from "../../../api";
import { Subsidy, deliveryTypeStr } from "../../../constants";
import { ItemExchangePlaydataForDetail } from "../types";
import { numberType } from "../PlayStepForItemExchange/DiscountRule";
import { Button } from "@alifd/next";
import AddUpcModal from "./AddUpcModal";
import { checkPermission } from "../service";
import { BudgetPickerDetail } from "@alife/carbon-biz-kunlun-zs/lib/components/BudgetPicker/BudgetPicker";

const Field: React.FC<any> = ({ label, children }) => {
  return (
    <div className="zs-act-field">
      <label>{label}</label>
      <div>{children}</div>
    </div>
  );
};

// 生效人群对应关系
export const UserScopeEnumStr = {
  0: "全部用户",
  4: "门店新客",
};

enum STATUS_ENUM {
  CANCELED = 4,
  FINISHED = 5,
}
const MODIFY_ACTIVITY = 5;
interface Props {
  viewJson: string;
  env: "daily" | "pre" | "prod";
  onEvent?: (event: any) => void;
  activityInfo: {
    investmentActivityDetailDTO: {
      activityId: number;
      subsidyType: Subsidy;
      businessLineList: {
        businessLineName: string;
      }[];
      status: number;
    };
  };
}

const PlayViewForItemExchange: React.FC<Props> = ({
  viewJson,
  activityInfo,
  env,
  onEvent,
}) => {
  const [addUpcModal, setAddUpcModal] = useState<{
    visible: boolean;
    serviceTemplateId?: number;
  }>({
    visible: false,
  });
  let ready = false;
  let viewJsonObj: ItemExchangePlaydataForDetail =
    {} as ItemExchangePlaydataForDetail;
  const detail = activityInfo?.investmentActivityDetailDTO;

  const addVisible = useMemo(() => {
    return (
      detail?.status !== STATUS_ENUM.CANCELED &&
      detail?.status !== STATUS_ENUM.FINISHED
    );
  }, [detail]);

  if (!viewJson) {
    return null;
  }

  try {
    viewJsonObj = JSON.parse(viewJson);
    if (viewJsonObj === null) return null;
    ready = true;
  } catch (error) {
    console.error(error);
  }

  if (!ready) {
    return null;
  }

  return (
    <div className="zs-play-view-item-exchange">
      {addUpcModal.visible && <AddUpcModal
        env={env}
        activityInfo={detail}
        visible={addUpcModal.visible}
        serviceTemplateId={addUpcModal?.serviceTemplateId}
        onCancel={() => {
          setAddUpcModal({ visible: false });
        }}
      />}
      <div className="zs-play-view-item-exchange-header">基础规则</div>
      <Field label="限制人群：">
        {UserScopeEnumStr[viewJsonObj.activityMarketingInfo.userScope]}
      </Field>
      <Field label="生效渠道：">
        {deliveryTypeStr[viewJsonObj.activityMarketingInfo.deliveryType]}
      </Field>
      <div className="zs-play-view-item-exchange-header">优惠规则</div>
      <div className="zs-play-view-item-exchange-rule">
        {(viewJsonObj.activityService || []).map((item, index) => {
          return (
            <div className="zs-play-view-item-exchange-rule-item" key={index}>
              <div className="zs-play-view-item-exchange-rule-title">
                服务类型（{numberType[index]}）
              </div>
              <Field label="服务名称：">{item.name}</Field>
              <Field label="服务生效优先级：">{item.priority}</Field>
              <Field label="服务头图：">
                <img src={item.image} alt="" width={100} height={100} />
              </Field>
              <Field label="优惠价格：">
                活动价 {item.rule?.discount} 元，平台补贴{" "}
                {item.rule?.subsidy?.platformSubsidyMax} 元
              </Field>
              <Field label="关联搭配品：">
                <a href={item.commodityListFilePath} target="_blank">
                  下载关联搭购品详情{">"}
                  {">"}
                </a>
                {addVisible && (
                  <Button
                    type="primary"
                    style={{ marginLeft: 20 }}
                    onClick={async () => {
                      const res = await checkPermission(
                        detail.activityId,
                        MODIFY_ACTIVITY
                      );
                      if (res?.accessible) {
                        setAddUpcModal({
                          visible: true,
                          serviceTemplateId: item.serviceTemplateId,
                        });
                      } else {
                        onEvent &&
                          onEvent({
                            type: "permission-error",
                            armsType: "shop_audit_err",
                            c1: 4,
                            c2: detail.activityId,
                          });
                      }
                    }}
                  >
                    新增加购品
                  </Button>
                )}
              </Field>
            </div>
          );
        })}
      </div>

      <div className="zs-play-view-item-exchange-header">限购规则</div>
      <Field label="服务最大限购：">
        {viewJsonObj.activityLimitRule?.userTotalCountLimit}
      </Field>
      <div className="zs-play-view-item-exchange-header">预算信息</div>

      <Field label="预算信息：">
        {viewJsonObj?.activityMarketingInfo.budgetId ? (
          <BudgetPickerDetail
            value={viewJsonObj?.activityMarketingInfo as any}
            env={{
              view: 'detail',
              env: env,
              detailContext: {
                activityId:activityInfo.investmentActivityDetailDTO.activityId,
              }
            }}
          />
        ) : (
          "-"
        )}
      </Field>
    </div>
  );
};

const PlayViewForItemExchangeWrap: React.FC<any> = (props) => {
  const { activityInfo, env, onEvent } = props;
  const [detail, setDetail] = useState<any>();

  useEffect(() => {
    api.subActivity.getDetail(activityInfo.activityId).then((res) => {
      setDetail(res.data);
    });
  }, [activityInfo.activityId]);
  return (
    <div className="zs-act-info">
      <div className="zs-act-area">
        <div className="zs-act-area-title" style={{ marginBottom: 30 }}>
          玩法设置
        </div>
        <div className="zs-act-section">
          <PlayViewForItemExchange
            viewJson={detail?.viewJson}
            activityInfo={detail}
            env={env}
            onEvent={onEvent}
          />
        </div>
      </div>
    </div>
  );
};

export default PlayViewForItemExchangeWrap;
