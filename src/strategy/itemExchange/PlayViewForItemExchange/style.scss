.zs-play-view-item-exchange {
  &-header {
    font-size: 16px;
    color: #999;
    margin-bottom: 30px;
    margin-top: 40px;
  }

  .zs-act-field {
    display: flex;
    > label {
      text-align: right;
      width: 20.833%;
      padding-right: 20px;
      padding-bottom: 25px;
      flex-shrink: 0;
    }
  }

  .zs-play-view-item-exchange-rule {
    &-title {
        margin-bottom: 10px;
        display: flex;
        justify-content: space-between;
        color: rgb(255,85,37);
    }
    &-item {
        line-height: 30px;
        color: rgba(0, 0, 0, 0.42);
        padding: 15px;
        
        &-label {
            text-align: right;
        }
        &-value {
            color: #000;
        }
        &-row {
            margin-bottom: 15px;
        }
    }
  }
}
