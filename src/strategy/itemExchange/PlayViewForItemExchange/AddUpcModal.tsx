import { Dialog, Message } from "@alifd/next";
import { CommodityRulesWithUpLoad } from "@alife/carbon-biz-kunlun-zs";
import React, { useState } from "react";
import { uploadInviteUpc } from "../service";
interface Props {
  visible: boolean;
  onCancel: () => void;
  activityInfo: any;
  env: "daily" | "pre" | "prod";
  serviceTemplateId?: number;
}

const AddUpcModal: React.FC<Props> = ({
  visible,
  onCancel,
  activityInfo,
  env,
  serviceTemplateId,
}) => {
  const [uploadFileCheck, setUploadFileCheck] = useState<any>(null);

  return (
    <Dialog
      visible={visible}
      title="新增加购品"
      onClose={() => {
        setUploadFileCheck(null)
        onCancel();
      }}
      onCancel={() => {
        setUploadFileCheck(null)
        onCancel();
      }}
      onOk={async () => {
        try {
          if (!uploadFileCheck?.uploadPath) {
            Message.error("请上传文件，如已上传请稍后");
            return;
          }
          if (uploadFileCheck?.fileUploadStatus === 2) {
            Message.error("正在检查文件，请稍后");
            return;
          }
          if (uploadFileCheck?.fileUploadStatus === 3) {
            Message.error("文件上传失败，请重新上传文件");
            return;
          }
          const res = await uploadInviteUpc({
            activityId: activityInfo.activityId,
            uploadFilePath: uploadFileCheck.uploadPath,
            uploadFileOperatorType: "add",
            uploadTemplateVersion: 1, 
            uploadTotalUpc: uploadFileCheck.checkExcel.uploadTotalUpc,
            serviceTemplateId,
          });
          if (res.success) {
            Message.success("操作成功");
            onCancel();
            setUploadFileCheck(null)
          } else {
            Message.error(res?.errorMessage || "操作失败，请稍后重试");
          }
        } catch (e: any) {
          console.error(e);
          Message.error(e?.message || "操作失败，请稍后重试");
        }
      }}
    >
      <div style={{ minWidth: 450, marginTop: "10px" }}>
        <CommodityRulesWithUpLoad
          extraCheckParams={{
            uploadTemplateVersion: "1",
            activityId: activityInfo.activityId,
            serviceTemplateId: serviceTemplateId
          }}
          limitFileSize={1024 * 1024 * 2}
          uploadFileOperatorType={"add"}
          env={env}
          activityType={activityInfo.activityType}
          templateUrl={'https://bwm-private-b.oss-cn-hangzhou.aliyuncs.com/mkt/service_template_bundle_commodity_1?Expires=4872895074&OSSAccessKeyId=LTAIhP07XE91RzLJ&Signature=GlxFaCIuObDCImEK4Vgu%2BOO6CFc%3D&response-content-disposition=attachment%3Bfilename%3D%E6%9C%8D%E5%8A%A1%E6%A8%A1%E6%9D%BF-%E6%90%AD%E8%B4%AD%E5%93%81.xlsx'}
          investmentSchema={activityInfo.investmentSchema}
          onChange={(v) => setUploadFileCheck(v)}
          value={uploadFileCheck}
        />
      </div>
    </Dialog>
  );
};
export default AddUpcModal;
