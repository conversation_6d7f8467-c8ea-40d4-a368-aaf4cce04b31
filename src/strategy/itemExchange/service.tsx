import { aslcClient } from "../../api";


  export async function uploadInviteUpc(param: {
    activityId: number | string;
    uploadFilePath: string;
    uploadFileOperatorType: string;
    uploadTemplateVersion: number;
    uploadTotalUpc: number;
    serviceTemplateId?: number;
  }) {
    const res = await aslcClient.fetch({
      apiKey:
        'ele-newretail-investment.NewretailInvestmentActivityInviteYundingService.uploadInviteUpc',
      params: [param],
    });
    return res;
  }

  export async function checkPermission(activityId: string | number,  action?: number): Promise<{ accessible: boolean; redirect: string; }> {
    const res = await aslcClient.fetch({
      apiKey: "ele-newretail-investment.NewretailInvestmentActivityYundingService.checkActivityOperateAuthorization",
      params: [activityId, action]
    })
    return { accessible: res.data, redirect: '' }
  }