interface ItemExchangePlaydataBase {
  activityType: number;
  activityLimitRule: {
    userTotalCountLimit?: number; //每人最多限购
  };
  activityMarketingInfo: {
    budgetId?: string;
    budgetName?: string;
    deliveryType: 1 | 2 | 3; //生效渠道
    userScope: 0 | 4; //限制人群
  };
  activityService: {
    serviceTemplateId?: number
    name?: string; // 服务名称
    priority?: number; // 优先级
    image?: string; // 头图
    commodityListFilePath?: string; // 商品excel
    rule?: {
        subsidy?: {
        platformSubsidyMax?: number; //平台补贴最大值
      };
      discount?: number; // 活动价
      discountType: 3;
    };
  }[];
}

// 创建场景下的玩法规则数据结构
export interface ItemExchangePlaydata extends ItemExchangePlaydataBase {
  weekday: string;
  // 创建活动场景下，通过 period + weekday 来传递生效周期和生效时段
  period: {
    openTime: string;
    closeTime: string;
  }[];
}

// 详情场景下的玩法规则数据结构
export interface ItemExchangePlaydataForDetail
  extends ItemExchangePlaydataBase {
  weekday: string;
  // 详情场景下，通过 periodDTOList + weekday 来表示生效周期和生效时段
  periodDTOList: {
    openTime: string;
    closeTime: string;
  }[];
}
