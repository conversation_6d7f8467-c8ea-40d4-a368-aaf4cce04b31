import * as React from "react";
import { Form, Radio, NumberPicker, Field, Button, Message } from "@alifd/next";
import { v4 as uuid4 } from "uuid";
import "./style.scss";
import { useValueAsDefaultValueOnFirstRender } from "@alife/carbon-biz-kunlun-zs/lib/helper";
import { BudgetPickerEdit } from "@alife/carbon-biz-kunlun-zs/lib/components/BudgetPicker/BudgetPicker";
import DiscountRule, { ActivityService, numberType } from "./DiscountRule";

const formItemLayout = {
  labelCol: { span: 7 },
  wrapperCol: { span: 17 },
};

interface Budget {
  budgetId: string;
  budgetName: string;
}
export interface Value {
  userScope: 0 | 4; // 限制人群
  deliveryType: 1 | 2 | 3; // 生效渠道
  activityService: ActivityService[];
  userTotalCountLimit?: number;
  budget?: Budget; // 预算
}

interface BudgetParams {
  budgetType?: "ACTIVITY" | "COUPON";
  bizDate?: number;
  templateId?: string;
  subsidyType?: "b" | "p" | "c" | "d"; // 补贴类型
}
interface Props {
  value?: Value;
  onChange: (v: Value | undefined) => void;
  onEvent?: (event: any) => void;
  env: "daily" | "pre" | "prod";
  activityType: number;
  investmentSchema: number;
  templateUrl?: string; // 下载模版
  budgetParams?: BudgetParams; // 预算相关参数
}

function wrapCallback(callback: any) {
  return callback
    ? callback
    : (msg: string) => {
        if (msg) {
          throw new Error(msg);
        }
      };
}

function isEmpty(v: any) {
  return v === "" || v === null || v === undefined;
}

const ItemExchangePlayStep: React.FC<Props> = ({
  value,
  onChange,
  onEvent,
  env,
  activityType,
  investmentSchema,
  templateUrl,
  budgetParams,
}) => {
  const defaultValue = useValueAsDefaultValueOnFirstRender(value);
  const field = Field.useField({
    values: defaultValue || {
      userScope: 0,
      deliveryType: 1,
      activityService: [
        {
          key: uuid4(),
          name: undefined,
          priority: undefined,
          image: undefined,
          commodityListFilePath: undefined,
          rule: {
            subsidy: {
              platformSubsidyMax: undefined,
            },
            discount: undefined,
          },
        },
      ],
    },
    onChange(name, values) {
      if (name === "activityService") {
        const hasSubsidy = (values || []).some(
          (s: { rule: { subsidy: { platformSubsidyMax: any } } }) =>
            s?.rule?.subsidy?.platformSubsidyMax
        );

        if (!hasSubsidy) {
          field.setValue("budget", undefined);
        }
      }
      onChange(field.getValues());
    },
  });

  const hasSubsidy = React.useMemo(() => {
    return (value?.activityService || []).some(
      (s) => s?.rule?.subsidy?.platformSubsidyMax
    );
  }, [field.getValue("activityService")]);

  return (
    <div className="zs-item-exchange-play">
      <Form field={field} {...formItemLayout}>
        <div className="zs-item-exchange-play-title">基础信息</div>
        <Form.Item label="限制人群:" required={true}>
          <Radio.Group {...field.init("userScope")}>
            <Radio value={0}>全部顾客</Radio>
            <Radio value={4}>门店新客</Radio>
          </Radio.Group>
        </Form.Item>
        <Form.Item label="生效渠道:" required={true}>
          <Radio.Group {...field.init("deliveryType")}>
            <Radio value={1}>仅外卖生效</Radio>
            <Radio value={2}>仅到店自提生效</Radio>
            <Radio value={3}>全部渠道生效</Radio>
          </Radio.Group>
        </Form.Item>
        <div className="zs-item-exchange-play-title">优惠规则</div>
        <Form.Item label="" required={true}>
          {/* @ts-ignore */}
          <DiscountRule
            activityType={activityType}
            investmentSchema={investmentSchema}
            templateUrl={templateUrl}
            env={env}
            {...field.init("activityService", {
              rules: [
                { required: true, message: "请至少填写一个服务信息" },
                {
                  validator: (r: any, v: any, callback) => {
                    callback = wrapCallback(callback);
                    if (v.length === 0) {
                      callback("请至少填写一个服务信息");
                      return;
                    } else {
                      for (let index = 0; index < v.length; index++) {
                        const service = v[index];
                        if (isEmpty(service.name)) {
                          callback(`请填写服务（${numberType[index]}）名称`);
                          return;
                        }
                        if (isEmpty(service.priority)) {
                          callback(`请填写服务（${numberType[index]}）优先级`);
                          return;
                        }
                        if (isEmpty(service.image)) {
                          callback(`请上传服务（${numberType[index]}）头图`);
                          return;
                        }
                        if (isEmpty(service.commodityList)) {
                          callback(
                            `请上传服务（${numberType[index]}）关联搭配品`
                          );
                          return;
                        }
                        if (!isEmpty(service.commodityList)) {
                          if (service?.commodityList?.fileUploadStatus === 2) {
                            callback(
                              `服务（${numberType[index]}）关联搭配品文件正在解析中，请等待...`
                            );
                            return;
                          }
                          if (service.commodityList?.fileUploadStatus === 3) {
                            callback(
                              `服务（${numberType[index]}）请重新关联搭配品上传文件`
                            );
                            return;
                          }
                          const valid =
                            !isEmpty(service.commodityList.checkExcel) &&
                            !isEmpty(service.commodityList.uploadPath);
                          if (!valid) {
                            callback(
                              `请上传服务（${numberType[index]}）关联搭配品`
                            );
                            return;
                          }
                        }

                        if (isEmpty(service.rule)) {
                          callback(
                            `请填写服务（${numberType[index]}）优惠信息`
                          );
                          return;
                        }
                        if (isEmpty(service.rule?.discount)) {
                          callback(
                            `请填写服务（${numberType[index]}）活动价格`
                          );
                          return;
                        }
                        if (isEmpty(service.rule.subsidy?.platformSubsidyMax)) {
                          callback(
                            `请填写服务（${numberType[index]}）平台补贴`
                          );
                          return;
                        }
                      }
                    }
                    callback();
                  },
                },
              ],
            })}
          />
        </Form.Item>
        <div className="zs-item-exchange-play-title">限购规则</div>
        <Form.Item label="服务最大限购:" required={true}>
          <NumberPicker
            step={1}
            precision={0}
            min={1}
            max={100}
            className="zs-item-exchange-play-item"
            {...field.init("userTotalCountLimit", {
              rules: [{ required: true, message: "请输入服务最大限购" }],
            })}
          />
          &nbsp;件
        </Form.Item>

        <div className="zs-item-exchange-play-title">追加预算</div>

        <Form.Item label="预算:" required={hasSubsidy}>
          <BudgetPickerEdit
            style={{ width: 300 }}
            {...field.init("budget", {
              rules: [
                {
                  required: hasSubsidy,
                  message: "请选择预算",
                },
                {
                  validator: (r: any, v: any, callback) => {
                    callback = wrapCallback(callback);
                    if (!isEmpty(v?.budget?.budgetId) && hasSubsidy) {
                      callback("请选择预算");
                    } else {
                      callback();
                    }
                  },
                },
              ],
            })}
            disabled={!hasSubsidy}
            value={value?.budget ? value.budget : undefined}
            env={env}
            bizDate={budgetParams?.bizDate}
            budgetType={budgetParams?.budgetType}
            templateId={budgetParams?.templateId}
            subsidyType={budgetParams?.subsidyType}
          />
        </Form.Item>
        <Form.Item label=" ">
          <div style={{ padding: "30px 0px 20px" }}>
            <Button
              data-test-id="scope-info-step-cancel-btn"
              onClick={() => onEvent && onEvent({ type: "cancel" })}
            >
              取消
            </Button>
            &nbsp;&nbsp;
            <Button
              data-test-id="scope-info-step-prev-btn"
              onClick={() => onEvent && onEvent({ type: "prev" })}
            >
              上一步
            </Button>
            &nbsp;&nbsp;
            <Button
              type="primary"
              data-test-id="scope-info-step-next-btn"
              onClick={async () => {
                const r = await field.validatePromise();
                console.log("validate result", r);
                if (r.errors && Object.keys(r.errors).length > 0) {
                  const key = Object.keys(r.errors)[0];
                  // @ts-ignore
                  const msg = r.errors[key].errors[0];
                  Message.error(msg);
                } else {
                  onEvent && onEvent({ type: "next" });
                }
              }}
            >
              发布
            </Button>
          </div>
        </Form.Item>
      </Form>
    </div>
  );
};

export default ItemExchangePlayStep;
