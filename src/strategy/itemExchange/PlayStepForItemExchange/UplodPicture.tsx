import * as React from "react";
import { Upload, <PERSON><PERSON>, <PERSON><PERSON>, Message } from "@alifd/next";
import * as api from "../../../api";
import "./style.scss";

interface Props {
  value?: string;
  onChange: (value?: string) => void;
}

const UploadPicture: React.FC<Props> = ({ value, onChange }) => {
    const [loading, setLoading] = React.useState(false);
  const handleUploadFile = (f: any) => {
    if (!f || !f.originFileObj) {
      return false;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      const size = {
        width: 750,
        height: 750,
      };
      if (e.total > 1024 * 1024) {
        Message.error("图片尺寸不能超过1M");
        return;
      }
      const src = reader.result as string;
      const img = new Image();
      img.onload = () => {
        if (img.width !== size.width || img.height !== size.height) {
          Message.error('图片尺寸必须为750*750');
          return;
        }
        setLoading(true);

        api
          .uploadPictureForComponentForm(src)
          .then((res: any) => {
            if (!res.data) {
              Message.error("接口异常，请稍后重试");
              return;
            }
            const { result, url, failReason } = res.data || {};
            if (!result) {
              Message.error("接口异常，请稍后重试");
              return;
            }
            if (result == "1") {
              Message.error(failReason || "图片涉及违规，请重新上传");
              return;
            }

            onChange(url);
          })
          .catch(() => {
            Message.error("接口异常，请稍后重试");
          })
          .finally(() => {
            setLoading(false);
          });
      };
      img.src = src;
    };
    reader.readAsDataURL(f.originFileObj);
    return false;
  }

  return (
    <div className="zs-upload-picture">
      {value ? (
        <div
          className="zs-upload-picture-preview"
          onClick={() => {
            if (value) {
              Dialog.show({
                footer: false,
                title: "图片预览",
                content: (
                  <div style={{ textAlign: "center" }}>
                    <img src={value} alt="" />
                  </div>
                ),
              });
            }
          }}
        >
          <img src={value} alt="" width={100} height={100} />
        </div>
      ) : null}
      <div className="zs-upload-picture-ops">
        <div style={{ display: "flex" }}>
          <Upload
            autoUpload={false}
            afterSelect={handleUploadFile}
            accept="image/png, image/jpg"
          >
            <Button style={{ marginRight: 8 }} type="primary" loading={loading}>
              {value ? "重新上传" : "上传图片"}
            </Button>
          </Upload>
          {value && <Button onClick={() => onChange(undefined)}>清空</Button>}
        </div>
        <div className="zs-upload-picture-placeholder"></div>
      </div>
    </div>
  );
};

export default UploadPicture;
