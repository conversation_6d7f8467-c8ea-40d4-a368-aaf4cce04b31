.zs-item-exchange-play {
    width: 1000px;
    margin: 0 auto;
    overflow: auto;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.84);
    &-title {
      font-size: 16px;
      color: #999;
      margin-bottom: 30px;
      margin-top: 40px;
    }
    &-item {
      width: 200px !important;
    }
    &-short{
      width: 80px !important;
    }
    &-text {
      line-height: 32px;
    }
  }

  .zs-item-exchange-discount-rule {
    width: 100%;
    margin-left: 100px;
    &-item {
        line-height: 30px;
        color: rgba(0, 0, 0, 0.42);
        border: 1px solid #d1cfcf;
        margin-bottom: 15px;
        // background-color: rgb(243,243,247);
        padding: 15px;
        &-title {
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            color: rgb(255,85,37);
        }
        &-label {
            text-align: right;
        }
        &-value {
            color: #000;
        }
        &-row {
            margin-bottom: 15px;
        }
    }
    &-add {
        color: rgb(235,123,48);
        margin-top: 20px;
        width: 100%;
        cursor: pointer;
        display: flex;
        justify-content: center;
        &-circle {
            border-radius: 50%;
            border: 1px solid rgb(235,123,48);
            height: 15px;
            width: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 5px;
        }
        &-text {
          color: rgba(0, 0, 0, 0.5);
        }
      }
  }

  .zs-upload-picture {
    display: flex;
    &-preview {
      margin-right: 20px;
      cursor: pointer;
    }
    &-ops {
      width: 200px;
    }
    &-placeholder {
      margin-top: 8px;
      font-size: 12px;
      line-height: 16px;
      color: #999;
    }
  }
  