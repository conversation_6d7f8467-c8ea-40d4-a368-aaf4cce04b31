import * as React from "react";
import {
  Input,
  Grid,
  NumberPicker,
  Select,
  Icon,
  Message,
} from "@alifd/next";
import { v4 as uuid4 } from 'uuid';
import { CommodityRulesWithUpLoad } from "@alife/carbon-biz-kunlun-zs";
import { CommodityRules } from "@alife/carbon-biz-kunlun-zs/lib/internal/CommodityRulesWithUpLoad";
import UploadPicture from "./UplodPicture";

const { Row, Col } = Grid;
const Option = Select.Option;

export interface ActivityService {
  key?: string;
  name?: string; // 服务名称
  priority?: number; // 优先级
  image?: string; // 头图
  commodityListFilePath?: string; // 商品excel
  commodityList?: CommodityRules;
  rule?: {
    subsidy?: {
      platformSubsidyMax?: number; //平台补贴最大值
    };
    discount?: number; // 活动价
    discountType?: number;
  };
}

interface Props {
  value?: ActivityService[];
  onChange: (value: ActivityService[]) => void;
  env: "daily" | "pre" | "prod";
  activityType: number;
  investmentSchema: number;
  templateUrl?: string; // 下载模版
}

export const numberType = ["一", "二", "三"];


const DiscountRule: React.FC<Props> = ({
  value,
  onChange,
  env,
  activityType,
  investmentSchema,
  templateUrl,
}) => {
  const defaultData = {
    name: undefined,
    priority: undefined,
    image: undefined,
    commodityListFilePath: undefined,
    rule: {
        subsidy: {
        platformSubsidyMax: undefined,
      },
      discount: undefined,
    },
    key: uuid4()
  };

  const selectOption = React.useMemo(() => {
    return (value || []).map((item: ActivityService) => {
      if (item.priority) {
        return item.priority;
      }
    });
  }, [value]);



  return (
    <div className="zs-item-exchange-discount-rule">
      {(value || []).map((item: ActivityService, index) => {
        const optionList = [1, 2, 3].filter(
          (_item) => !selectOption.includes(_item)
        );
        return (
          <div className="zs-item-exchange-discount-rule-item" key={item?.key}>
            <div className="zs-item-exchange-discount-rule-item-title">
              服务类型（{numberType[index]}）
              {(value || []).length > 1 && (
                <Icon
                  type="error"
                  onClick={() => {
                    if(item?.commodityList?.fileUploadStatus === 2) {
                        Message.error('商品excel正在上传中，请稍后');
                        return;
                    }
                    const newValue = value
                      ?.map((p) => {
                        if (p.key === item?.key) {
                          return null;
                        } else {
                          return p;
                        }
                      })
                      .filter(Boolean);
                    // @ts-ignore
                    onChange(newValue);
                  }}
                />
              )}
            </div>
            <Row className="zs-item-exchange-discount-rule-item-row">
              <Col
                span="6"
                className="zs-item-exchange-discount-rule-item-label"
              >
                服务名称：
              </Col>
              <Col span="6">
                <Input
                  placeholder="请输入服务名称"
                  maxLength={4}
                  value={item?.name}
                  onChange={(v) => {
                    const data = (value || []).map(
                      (i: ActivityService) => {
                        if (i.key === item?.key) {
                          return {
                            ...i,
                            name: v,
                          };
                        }
                        return i;
                      }
                    );
                    onChange(data);
                  }}
                />
              </Col>
              <Col
                span="6"
                className="zs-item-exchange-discount-rule-item-label"
              >
                服务生效优先级：
              </Col>
              <Col span="6">
                <Select
                  value={item?.priority}
                  onChange={(v) => {
                    const data = (value || []).map(
                      (i: ActivityService) => {
                        if (i.key === item?.key) {
                          return {
                            ...i,
                            priority: v,
                          };
                        }
                        return i;
                      }
                    );
                    onChange(data);
                  }}
                >
                  {optionList.map((_item) => (
                    <Option value={_item}>{_item}</Option>
                  ))}
                </Select>
              </Col>
            </Row>
            <Row className="zs-item-exchange-discount-rule-item-row">
              <Col
                span="6"
                className="zs-item-exchange-discount-rule-item-label"
              >
                服务头图：
              </Col>
              <Col span="6">
                <UploadPicture
                  value={item?.image}
                  onChange={(v) => {
                    const data = (value || []).map(
                      (i: ActivityService) => {
                        if (i.key === item?.key) {
                          return {
                            ...i,
                            image: v,
                          };
                        }
                        return i;
                      }
                    );
                    console.log("---", data, value)
                    onChange(data);
                  }}
                />
              </Col>
            </Row>
            <Row className="zs-item-exchange-discount-rule-item-row">
              <Col
                span="6"
                className="zs-item-exchange-discount-rule-item-label"
              >
                优惠价格：
              </Col>
              <Col
                span="18"
                className="zs-item-exchange-discount-rule-item-value"
              >
                活动价&nbsp;
                <NumberPicker
                  min={0}
                  max={9999}
                  step={1}
                  precision={2}
                  value={item?.rule?.discount}
                  onChange={(v) => {
                    const data = (value || []).map(
                      (i: ActivityService) => {
                        if (i.key === item?.key) {
                          return {
                            ...i,
                            rule: {
                              ...i.rule,
                              discount: v,
                            },
                          };
                        }
                        return i;
                      }
                    );
                    onChange(data);
                  }}
                />
                &nbsp;元，平台补贴上限&nbsp;
                <NumberPicker
                  min={0}
                  max={9999}
                  step={1}
                  precision={2}
                  value={item?.rule?.subsidy?.platformSubsidyMax}
                  onChange={(v) => {
                    const data = (value || []).map(
                      (i: ActivityService) => {
                        if (i.key === item?.key) {
                          return {
                            ...i,
                            rule: {
                              ...i.rule,
                              subsidy: {
                                platformSubsidyMax: v,
                              },
                            },
                          };
                        }
                        return i;
                      }
                    );
                    onChange(data);
                  }}
                />
                &nbsp;元
              </Col>
            </Row>
            <Row className="zs-item-exchange-discount-rule-item-row">
              <Col
                span="6"
                className="zs-item-exchange-discount-rule-item-label"
              >
                关联搭配品：
              </Col>
              <Col
                span="18"
                className="zs-item-exchange-discount-rule-item-value"
              >
                <CommodityRulesWithUpLoad
                  uploadFileOperatorType="add"
                  env={env}
                  limitFileSize={1024 * 1024 * 2}
                  activityType={activityType}
                  templateUrl={templateUrl}
                  investmentSchema={investmentSchema}
                  onChange={(v) => {
                    const data = (value || []).map(
                      (i: ActivityService) => {
                        if (i.key === item?.key) {
                          return {
                            ...i,
                            commodityList: v,
                          };
                        }
                        return i;
                      }
                    );
                    onChange(data);
                  }}
                  value={item?.commodityList}
                />
              </Col>
            </Row>
          </div>
        );
      })}
      {(value || [])?.length < 3 && (
        <div
          className="zs-item-exchange-discount-rule-add"
          onClick={() => {
            onChange([
              ...(value || []),
              {
                ...defaultData,
              },
            ]);
          }}
        >
          <span className="zs-item-exchange-discount-rule-add-circle">
            <Icon type="add" size="xs" />
          </span>
          增加服务类型（{(value || [])?.length || 1}/3）
        </div>
      )}
    </div>
  );
};

export default DiscountRule;
