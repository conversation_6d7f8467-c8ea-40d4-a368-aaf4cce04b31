
import { v4 as uuid4 } from "uuid";
import { ActivityTypeEnum } from "../../constants";
import { Value } from "./PlayStepForItemExchange";
import { ItemExchangePlaydata, ItemExchangePlaydataForDetail } from "./types";


interface Budget {
  budgetId: string;
  budgetName: string;
}

interface ItemExchangeBaseInfo {
  activityTime: {
    weeks: number[];
    batchRange: {
      type: "all" | "custom";
      list: { start: string; end: string }[];
    };
  };
}

export function transformFormData2ViewJson(options: {
  baseinfo: ItemExchangeBaseInfo;
  playdata: Value;
}): ItemExchangePlaydata {
  const budget: Budget | undefined = options.playdata.budget;
  const { playdata } = options;
  return {
    activityType: ActivityTypeEnum.ITEM_RXCHANGE,
    activityLimitRule: {
      userTotalCountLimit: playdata.userTotalCountLimit,
    },
    activityMarketingInfo: {
      budgetId: budget?.budgetId,
      budgetName: budget?.budgetName,
      deliveryType: playdata.deliveryType,
      userScope: playdata.userScope,
    },
    activityService: playdata.activityService.map((item) => {
      return {
        name: item.name,
        priority: item.priority,
        image: item.image,
        commodityListFilePath: item?.commodityList?.uploadPath,
        rule: {
          subsidy: item?.rule?.subsidy,
          discount: item?.rule?.discount,
          discountType: 3,
        },
      };
    }),
    weekday: options.baseinfo.activityTime.weeks.join(","),
    period: options.baseinfo.activityTime.batchRange.list.map((item) => {
      return {
        openTime: item.start.slice(0, "HH:mm".length) + ":00",
        closeTime: item.end.slice(0, "HH:mm".length) + ":59",
      };
    }),
  };
}

export function transformViewJson2FormData(options: {
  viewJson?: string;
}): Value | null {
  try {
    const playdata: ItemExchangePlaydataForDetail = JSON.parse(
      options?.viewJson || "{}"
    );
    if (!playdata?.activityMarketingInfo) {
      return null;
    }

    const ret: Value = {
      userScope: playdata.activityMarketingInfo.userScope,
      userTotalCountLimit: playdata.activityLimitRule.userTotalCountLimit,
      deliveryType: playdata.activityMarketingInfo.deliveryType,
      activityService: (playdata.activityService || []).map((item) => {
        return {
          key: uuid4(),
          name: item.name,
          priority: item.priority,
          image: item.image,
          commodityList: undefined,
          rule: {
            subsidy: undefined,
            discount: item.rule?.discount,
          },
        };
      }),
      budget: undefined,
    };

    return ret;
  } catch (e) {
    return null;
  }
}
