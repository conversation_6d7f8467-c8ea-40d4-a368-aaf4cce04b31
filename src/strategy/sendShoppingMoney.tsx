import BigNumber from "bignumber.js";
import { Dialog, Message, NumberPicker } from "@alifd/next";
import * as api from "./../api";
import { maskMS } from "./../common";
import { ActivityTypeEnum } from "./../constants";
import { ActivityStrategy, InvestmentActivityDetailDTO } from "./../models";
import { CableBase } from "./base";
import {
  genericCreateActivity,
  gotoLegacyActivityDetailByDefault,
  isOfflineEnabledByDefault,
} from "./mixins";
import { auditServiceForCoupon } from "./service";


const UserScopeEnumStr = {
  11: "商家品牌新客",
  21: "大商超新客",
};
export interface Channel {
  class?: string;
  externalDeliveryChannelName: string; // 渠道名称
  externalDeliveryChannelCode: number; // 渠道code
  externalDeliveryChannelType: number; // 渠道类型，独享、非独享
}

export interface RangeInputProps {
  start: number;
  end: number;
}
export interface Count {
  first: number;
  second: number;
}
export interface Budget {
  budgetId?: string;
  budgetName?: string;
}
interface PreSendShoppingMoneyViewJsonTs {
  activityExternalDeliveryChannelList: Channel[];
  availableTime: {
    // 使用时间
    availableDays: number;
    availableHours: number;
  };
  deliveryType: 1 | 2 | 3;
  inStoreDeliveryType: number;
  outStoreDeliveryType: number;
  shopSubsidys: {
    isShopSubsidysZero: boolean; // 商家承担金额是否是0
    start: number;
    end: number;
  }; // 商家承担金额
  sellerDayCount: RangeInputProps; // 连锁每日库存
  displayPlatformSubsidys: RangeInputProps; // 商家展示平台补贴
  platformSubsidy: number; // 平台实际补贴
  countLimit: Count; // 发放数量
  userCountLimit: Count; // 领取限制
  budget?: Budget;
  platformStrategySwitch: boolean; // 平台策略是否关闭
}

interface SendShoppingMoneyViewJson {
  activityMarketingInfo: {
    activityExternalDeliveryChannelList: {
      externalDeliveryChannelCode: number;
      externalDeliveryChannelName: string;
      externalDeliveryChannelType: number;
    }[];

    availableDays?: number; // 使用时间：自领取日期天数
    availableHours?: number; // 使用时间：自领取日期小时
    deliveryType: 1 | 2 | 3; //  核销渠道
    inStoreDeliveryType: number; // 店内投放方式
    outStoreDeliveryType: number; // 店外投放方式
  };
  merchantStrategyViewModelList: {
    sellerDayCountLimit?: number; // 商家承担金额最小值
    sellerDayCountLimitMax?: number; // 商家承担金额最大值
    shopSubsidy?: number; // 连锁每日库存
    shopSubsidyMax?: number;
    userScope: 11; // 生效人群
  }[];

  platformStrategyViewModelList?: {
    budgetId?: string;
    budgetName?: string;
    dayCountLimit?: number; // 发放数量：每天
    displayPlatformSubsidy?: number; // 商家展示平台补贴最小值
    displayPlatformSubsidyMax?: number; // 商家展示平台补贴最大值
    platformSubsidy?: number; // 平台实际补贴
    totalCountLimit?: number; // 发放数量：活动期间
    userDayCountLimit?: number; // 领取限制：每人每日
    userScope: 21;
    userTotalCountLimit?: number; // 领取限制：活动期间每人总共
  }[];
}

const yuanTurnFen = (menoy: number) => {
  return new BigNumber(menoy).multipliedBy(100).toNumber();
};

const SendShoppingMoney: ActivityStrategy = {
  activityType: ActivityTypeEnum.SEND_SHOPPING_MONEY,
  generation: "v2",
  cable: {
    ...CableBase,
    workspace: "ExukovQrae6xWn2rt4NgG",
    model: "6yhXCSWxflEaOeSNcP1YuW",
    async checkNext({ step, dataStore }, creationContext, history) {
      console.log("checkNext", step, dataStore);
      if (step.name === "playdata") {
        const { baseinfo, scope } = dataStore.data;
        const playdata: PreSendShoppingMoneyViewJsonTs =
          dataStore.data?.playdata;
        const isShopSubsidysZero = playdata?.shopSubsidys?.isShopSubsidysZero;
        const viewJson: SendShoppingMoneyViewJson = {
          activityMarketingInfo: {
            activityExternalDeliveryChannelList:
              playdata?.activityExternalDeliveryChannelList,
            availableDays: playdata.availableTime?.availableDays,
            availableHours: playdata.availableTime?.availableHours,
            deliveryType: playdata.deliveryType,
            inStoreDeliveryType: playdata.inStoreDeliveryType,
            outStoreDeliveryType: playdata.outStoreDeliveryType,
          },
          merchantStrategyViewModelList: [
            {
              sellerDayCountLimit: isShopSubsidysZero
                ? undefined
                : playdata.sellerDayCount.start,
              sellerDayCountLimitMax: isShopSubsidysZero
                ? undefined
                : playdata.sellerDayCount.end,
              shopSubsidy: isShopSubsidysZero
                ? 0
                : yuanTurnFen(playdata.shopSubsidys.start),
              shopSubsidyMax: isShopSubsidysZero
                ? 0
                : yuanTurnFen(playdata.shopSubsidys.end),
              userScope: 11,
            },
          ],
          platformStrategyViewModelList: playdata?.platformStrategySwitch
            ? [
                {
                  budgetId: playdata?.budget?.budgetId,
                  budgetName: playdata?.budget?.budgetName,
                  dayCountLimit: playdata.countLimit?.second,
                  displayPlatformSubsidy: yuanTurnFen(
                    playdata.displayPlatformSubsidys.start
                  ),
                  displayPlatformSubsidyMax: yuanTurnFen(
                    playdata.displayPlatformSubsidys.end
                  ),
                  platformSubsidy: yuanTurnFen(playdata.platformSubsidy),
                  totalCountLimit: playdata.countLimit?.first,
                  userDayCountLimit: playdata.userCountLimit?.second,
                  userScope: 21,
                  userTotalCountLimit: playdata.userCountLimit?.first,
                },
              ]
            : undefined,
        };

        const payload = {
          // 活动类型信息
          templateId: creationContext.templateId,
          investmentType: creationContext.investmentType,
          investmentSchema: creationContext.investmentSchema,
          activityType: ActivityTypeEnum.SEND_SHOPPING_MONEY,
          // 基本信息
          name: baseinfo.name,
          description: baseinfo.description,
          remark: baseinfo.remark,
          beginTime: baseinfo.activityTime.start.toDate().getTime(),
          endTime: maskMS(baseinfo.activityTime.end.toDate().getTime()),
          allowCancel: baseinfo.allowCancel,
          signUpShopType: baseinfo.signUpShopType,
          signUpStartTime: baseinfo.signupTime.start.toDate().getTime(),
          signUpEndTime: maskMS(baseinfo.signupTime.end.toDate().getTime()),
          activityPeriodRequestList: [
            {
              weekday: baseinfo.activityTime.weeks.join(","),
              openTime: baseinfo.activityTime.range.start + ":00",
              closeTime: baseinfo.activityTime.range.end + ":00",
            },
          ],
          subsidyType: creationContext?.subsidyType,
          manageOrgList: baseinfo?.manageOrgList,

          // 招商范围信息
          shopPoolId: scope.pool.value,
          shopPoolName: scope.pool.label,

          // 玩法信息
          viewJson: JSON.stringify(viewJson),
        };
        return await genericCreateActivity(payload, history, creationContext);
      }
    },
    auditService: auditServiceForCoupon,
  },
  hasAgentSubsidy() {
    return false;
  },
  isOfflineEnabled: isOfflineEnabledByDefault,
  isDingtalkLinkEnabled: () => false,
  hasAuditMemberList: () => false,
  isSupportModifyStock: () => false,
  isDetailInviteStatsOnMainActivityTableVisible: true,
  renderPlayDataInfoForTableView: function (): JSX.Element {
    return <></>;
  },
  renderActivityRuleForTable(viewJson: string) {
    try {
      const data: SendShoppingMoneyViewJson = JSON.parse(viewJson);
      return (
        <div>
          {(data?.merchantStrategyViewModelList || [])?.map((item) => {
            return (
              // @ts-ignore
              <div key={item.userScope}>
                {UserScopeEnumStr[item.userScope]}：商家承担
                {item?.shopSubsidy && item?.shopSubsidyMax
                  ? new BigNumber(item.shopSubsidy).dividedBy(100).toNumber() +
                    "～" +
                    new BigNumber(item.shopSubsidyMax).dividedBy(100).toNumber()
                  : 0}
                {(data?.platformStrategyViewModelList || []).length > 0 && ";"}
              </div>
            );
          })}
          {(data?.platformStrategyViewModelList || [])?.map((item) => {
            return (
              // @ts-ignore
              <div key={item.userScope}>
                {UserScopeEnumStr[item.userScope]}：平台实际补贴
                {item.platformSubsidy
                  ? new BigNumber(item.platformSubsidy)
                      .dividedBy(100)
                      .toNumber()
                  : 0}
              </div>
            );
          })}
        </div>
      );
    } catch (e) {
      console.error(e);
      return "";
    }
  },
  isSupportAgent: false,
  gotoLegacyDetail: function (
    act: InvestmentActivityDetailDTO,
    scene: "lookup" | "examine"
  ): void {
    gotoLegacyActivityDetailByDefault(act, scene);
  },
  renderActivityRuleForTableLegacy: function () {
    return "";
  },
  isModifyPlatformSubsidy: function(viewJson) {
    const viewJsonObj: SendShoppingMoneyViewJson = JSON.parse(
      viewJson
    );
    if((viewJsonObj?.platformStrategyViewModelList || []).length === 0) {
      return false
    }
    return true
  },
  handleModifyPlatformSubsidy: function (record, options) {
    try {
      if (!record.viewJson) {
        return null;
      }
      let platformSubsidy: number;
      let totalCountLimit: number;
      let dayCountLimit: number;
      const viewJsonObj: SendShoppingMoneyViewJson = JSON.parse(
        record.viewJson
      );
      Dialog.show({
        title: "修改",
        width: "300px",
        content: (viewJsonObj.platformStrategyViewModelList || []).map(
          (item) => {
            return (
              <div style={{ width: "300px" }}>
                <div>平台实际补贴：</div>
                <div>
                  原值 ¥
                  {new BigNumber(
                    item?.platformSubsidy ? item.platformSubsidy : 0
                  )
                    .dividedBy(100)
                    .toNumber()}
                  ，修改为&emsp;
                  <NumberPicker
                    style={{ width: 80 }}
                    min={1}
                    onChange={(v: any) => {
                      platformSubsidy = yuanTurnFen(v);
                    }}
                    max={200}
                  />
                </div>
                {item?.totalCountLimit && (
                  <div style={{ marginTop: "15px" }}>
                    <div>活动期间总共发放：</div>
                    <div>
                      原值
                      {item.totalCountLimit}
                      ，修改为&emsp;
                      <NumberPicker
                        style={{ width: 100 }}
                        min={item.totalCountLimit}
                        onChange={(v: any) => {
                          totalCountLimit = v;
                        }}
                        max={99999999}
                      />
                    </div>
                  </div>
                )}
                {item?.dayCountLimit && (
                  <div style={{ marginTop: "15px" }}>
                    <div>活动期间每日发放：</div>
                    <div>
                      原值
                      {item.dayCountLimit}
                      ，修改为&emsp;
                      <NumberPicker
                        style={{ width: 100 }}
                        min={item.dayCountLimit}
                        onChange={(v: any) => {
                          dayCountLimit = v;
                        }}
                        max={99999999}
                      />
                    </div>
                  </div>
                )}
                <div style={{ marginTop: "15px", color: "rgb(205,55,45)" }}>
                  注意！发放数量只能增加不能减少
                </div>
              </div>
            );
          }
        ),
        onOk: async () => {
          if (!totalCountLimit && !dayCountLimit && !platformSubsidy) {
            Message.error("请至少填写一项");
            return;
          }
          try {
            const res: any = await api.modifySinglePromotionActivityLimitRule({
              activityId: record.activityId,
              platformSubsidy,
              totalCountLimit,
              dayCountLimit,
            });
            if (res?.success) {
              options.onSuccess();
            } else {
              options.onFail(res?.errorMessage as string);
            }
          } catch (e: any) {
            options.onFail(e?.message as string);
          }
        },
      });
    } catch (error) {}
  },
};

export default SendShoppingMoney;
