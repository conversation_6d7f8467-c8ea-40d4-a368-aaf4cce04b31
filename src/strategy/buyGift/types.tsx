interface BuyGiftPlaydataBase {
  activityType: number;
  activityLimitRule: {
    userDayCountLimit?: number; //每人每天赠品最多赠送
    userTotalCountLimit?: number; //每人活动期间赠品最多赠送
  };
  activityMarketingInfo: {
    budgetId?: string;
    budgetName?: string;
    deliveryType: number; //生效渠道
    userScope: number; //限制人群
  };
  activityRule: {
    platformSubsidyMax: number; //平台补贴最大值
    discount: 1;
    discountType: 4; //买赠
    condition: 1;
    conditionType: 1; //满X件
  };
  period: {
    openTime: string;
    closeTime: string;
  }[];
  weekday: string;
}

// 创建场景下的玩法规则数据结构
export interface BuyGiftPlaydata extends BuyGiftPlaydataBase {
  weekday: string;
  // 创建活动场景下，通过 period + weekday 来传递生效周期和生效时段
  period: {
    openTime: string;
    closeTime: string;
  }[];
}

// 详情场景下的玩法规则数据结构
export interface BuyGiftPlaydataForDetail extends BuyGiftPlaydataBase {
  weekday: string;
  // 详情场景下，通过 periodDTOList + weekday 来表示生效周期和生效时段
  periodDTOList: {
    openTime: string;
    closeTime: string;
  }[];
}