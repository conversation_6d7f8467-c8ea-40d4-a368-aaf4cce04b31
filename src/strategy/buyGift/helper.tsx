import { parseJsonSafe } from "../../common";
import { ActivityTypeEnum } from "../../constants";
import { BuyGiftPlaydata } from "./types";

function isValueSet(v: any) {
  return v !== "" && v !== null && v !== undefined;
}

interface Budget {
  budgetId?: string;
  budgetName?: string;
}

interface giftLimit {
  userDayCountLimit?: { value?: number };
  userTotalCountLimit?: { value?: number };
}

interface BuyGiftBaseInfo {
  activityTime: {
    weeks: number[];
    batchRange: {
      type: "all" | "custom";
      list: { start: string; end: string }[];
    };
  };
}

interface BuyGiftFormData {
  creator: string;
  userScope: string;
  giftLimit: string;
  platformSubsidyMax?: string;
  deliveryType: string;
  budget?: string;
}

export function transformFormData2ViewJson(options: {
  baseinfo: BuyGiftBaseInfo;
  formData: BuyGiftFormData;
}): BuyGiftPlaydata {
  const budget: Budget = options.formData.budget
    ? parseJsonSafe(options.formData.budget)
    : null;
  const giftLimit: giftLimit = parseJsonSafe(options.formData.giftLimit);
  const platformSubsidyMax = parseJsonSafe(options.formData.platformSubsidyMax)

  return {
    activityType: ActivityTypeEnum.BUY_GIFT,
    activityLimitRule: {
      userDayCountLimit: isValueSet(giftLimit.userDayCountLimit?.value)
        ? +giftLimit.userDayCountLimit?.value!
        : undefined,
        userTotalCountLimit: isValueSet(giftLimit.userTotalCountLimit?.value)
        ? +giftLimit.userTotalCountLimit?.value!
        : undefined,
    },
    activityMarketingInfo: {
      budgetId: budget?.budgetId,
      budgetName: budget?.budgetName,
      deliveryType: +options.formData.deliveryType,
      userScope: +options.formData.userScope,
    },
    activityRule: {
      discountType: 4, // 前端写死成 4
      discount: 1,
      platformSubsidyMax: platformSubsidyMax.value.toString(),
      condition: 1,
      conditionType: 1
    },
    weekday: options.baseinfo.activityTime.weeks.join(","),
    period: options.baseinfo.activityTime.batchRange.list.map((item) => {
      return {
        openTime: item.start.slice(0, "HH:mm".length) + ":00",
        closeTime: item.end.slice(0, "HH:mm".length) + ":59",
      };
    }),
  };
}

export function transformViewJson2FormData(options: {
  viewJson: string;
  baseinfo: {
    createdUserId: string;
    createdUserName: string;
  };
  isCopy?: boolean
}): BuyGiftFormData | null {
  try {
    const playdata: BuyGiftPlaydata = JSON.parse(options.viewJson);
    const creator = {
      id: options.baseinfo.createdUserId,
      name: options.baseinfo.createdUserName,
    };
    let giftLimit: giftLimit = {};
    if (isValueSet(playdata.activityLimitRule.userDayCountLimit)) {
      giftLimit.userDayCountLimit = {
        value: playdata.activityLimitRule.userDayCountLimit,
      };
    }
    if (isValueSet(playdata.activityLimitRule.userTotalCountLimit)) {
      giftLimit.userTotalCountLimit = {
        value: playdata.activityLimitRule.userTotalCountLimit,
      };
    }
    let budget: Budget | null = null;
    if (
      isValueSet(playdata.activityMarketingInfo.budgetId) &&
      isValueSet(playdata.activityMarketingInfo.budgetName)
    ) {
      budget = {
        budgetId: playdata.activityMarketingInfo.budgetId,
        budgetName: playdata.activityMarketingInfo.budgetName,
      };
    }
    let platformSubsidyMax = {
      value: playdata.activityRule.platformSubsidyMax
    }
    const ret: BuyGiftFormData = {
      creator: JSON.stringify(creator),
      userScope: `${playdata.activityMarketingInfo.userScope}`,
      giftLimit: JSON.stringify(giftLimit),
      deliveryType: `${playdata.activityMarketingInfo.deliveryType}`,
      platformSubsidyMax: JSON.stringify(platformSubsidyMax),
      budget: budget ? JSON.stringify(budget) : undefined,
    };
    if(options?.isCopy) {
      delete ret.budget;
      ret.platformSubsidyMax = undefined
    }

    return ret;
  } catch (e) {
    return null;
  }
}
