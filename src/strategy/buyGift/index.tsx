import { Message } from "@alifd/next";
import BigNumber from "bignumber.js";

import * as api from "../../api";
import loadingDialog from "../../components/LoadingDialog";
import { ActivityTypeEnum } from "../../constants";
import { ActivityStrategy } from "../../models";
import {
  CableBase,
  resolveCreateActivityCommonData,
} from "../base";
import {
  resolveCommodityAuditProps,
  checkValidateCopyActivityResult,
  checkAndUploadRichDescription,
} from "../mixins";
import {
  transformFormData2ViewJson,
  transformViewJson2FormData,
} from "./helper";
import { BuyGiftPlaydata } from "./types";

const BuyGift: ActivityStrategy = {
  activityType: ActivityTypeEnum.BUY_GIFT,
  cable: {
    ...CableBase,
    workspace: "ExukovQrae6xWn2rt4NgG",
    model: "2HIaHECyueE69k31VFPn6w",
    async checkNext(checkNextContext, creationContext, history) {
      const { step, dataStore } = checkNextContext;
      if (!dataStore._session) {
        dataStore._session = {};
      }
      if (step.name === "baseinfo") {
        if (dataStore.data.baseinfo?.richDescription) {
          const { hide } = loadingDialog.show({
            message: "活动信息检查中...",
          });
          try {
            const error = await checkAndUploadRichDescription({
              richDescription: dataStore.data.baseinfo?.richDescription,
              session: dataStore._session,
            });
            if (error) {
              return error;
            }
          } finally {
            hide();
          }
        }
      }
      if (step.name === "stock") {
        const { baseinfo, stock, scope, playdata } = dataStore.data;
        // 买赠的活动库存是必填的
        if (!stock.range.start || !stock.range.end) {
          return { message: "请填写库存" };
        }
        if (stock.range.start >= stock.range.end) {
          return { message: "库存最小值必须小于最大值" };
        }
        const commonData = resolveCreateActivityCommonData(
          ActivityTypeEnum.BUY_GIFT,
          checkNextContext,
          creationContext
        );
        const viewJson = transformFormData2ViewJson({
          baseinfo,
          formData: playdata.snapshot.data,
        });
        delete commonData.dataMap;

        const commodityAuditProps: any = resolveCommodityAuditProps(scope);

        const payload: any = {
          ...commonData,

          // 自动审核规则
          ...commodityAuditProps,

          // 降套补 商品清退规则
          commodityRemoveCondition: {
            removeCondition: scope.removeCondition,
          },

          auditMemberList:
            baseinfo.auditMemberList?.length > 0
              ? baseinfo.auditMemberList
              : null,

          // 报名规则
          reviewRuleId: scope.rule?.ruleId,
          reviewRuleName: scope.rule?.name,

          // 库存设置
          stockType: stock.stockType,
          stockMin: stock.range.start,
          stockMax: stock.range.end,

          viewJson: JSON.stringify(viewJson),
        };

        const { hide, updateMessage } = loadingDialog.show({
          message: creationContext.copy ? "活动信息校验中..." : "活动创建中...",
        });

        if (dataStore._session?.ossURL) {
          const ossURL = dataStore._session?.ossURL;
          payload.description = baseinfo.richDescription.text;
          payload.attributes = {
            ...payload.attributes,
            hasRichDescription: "on",
            richDescriptionURL: ossURL,
          };
        }

        try {
          if (creationContext.copy) {
            const res = await api.subActivity.validateCopyActivityData({
              copyActivityId: creationContext.activity?.activityId,
              ...payload,
            });
            const copyError = checkValidateCopyActivityResult(res);
            if (copyError) {
              return copyError;
            }
            updateMessage("活动创建中...");
          }
          const res = await api.subActivity.create(payload);
          if (res.success) {
            Message.success("创建成功");
            if (creationContext.activitySceneId) {
                  history.push(
                    "/sub-activity/" +
                      res.data +
                      "?activitySceneId=" +
                      creationContext.activitySceneId +
                      "&activityPlanId=" +
                      creationContext.activityPlanId
                  );
                } else {
                  history.push("/sub-activity/" + res.data);
                }
          } else {
            return { message: res.errorMessage };
          }
        } catch (e: any) {
          return { message: e?.message || "系统异常" };
        } finally {
          hide();
        }
      }
    },
  },

  hasAgentSubsidy() {
    return false;
  },
  isSupportAgent: false,
  isOfflineEnabled: function (state: number): boolean {
    // 活动状态: 1:未开始 2:活动中 3:活动暂停 4:活动取消 5:活动结束 8: 预算熔断
    return [1, 2, 3, 8].includes(state);
  },

  isDingtalkLinkEnabled: () => true,
  hasAuditMemberList: () => true,
  isSupportModifyStock: () => false,
  isItemAuditStatsVisible: true,
  isDetailInviteStatsOnMainActivityTableVisible: true,
  renderPlayDataInfoForTableView: function (): JSX.Element {
    return <></>;
  },
  gotoLegacyDetail: function (): void {},
  renderActivityRuleForTable(viewJson: any) {
    try {
      const data: BuyGiftPlaydata = JSON.parse(viewJson);
      let result = [];
      result.push(`优惠规则: 买 1 件，赠 1 件`);
      result.push(`平台补贴上限: ${new BigNumber(data.activityRule.platformSubsidyMax).toNumber()}元`);
      return (
        <>
          {result.map((r: any, i: number) => (
            <div key={i}>{r}</div>
          ))}
        </>
      );
    } catch (e) {
      console.error(e);
      return <></>;
    }
  },
  renderActivityRuleForTableLegacy: function () {
    return <></>;
  },
  marketPlayMapper: async function (
    viewJson: string,
    baseInfo: any,
  ) {
    try {
      const fields =
        transformViewJson2FormData({
          baseinfo: baseInfo,
          viewJson,
        }) || {};
      const model = await api.subActivity.getComponentDataModel(
        "3oNNWxkJYzc6eWQyKz5Lp2",
        "2hSxdi5EcRea3L0Okd3ZE8"
      );
      const marketPlay = { instance: { fields }, model };
      return marketPlay;
    } catch (e) {
      return null;
    }
  },
};

export default BuyGift;
