import {
  transformFormData2ViewJson,
  transformViewJson2FormData,
} from "./helper";

describe("bugGift", () => {
  describe("helper", () => {
    describe("transformFormData2ViewJson", () => {
      // - 招商类型:营销IP招商-招商模版:买一赠一
      test("招商类型:营销IP招商-招商模版:买一赠一-无补贴和预算", () => {
        const viewJson = transformFormData2ViewJson({
          baseinfo: {
            activityTime: {
              weeks: [0, 1, 2, 3, 4, 5, 6],
              batchRange: {
                type: "all",
                list: [{ start: "00:00", end: "23:59" }],
              },
            },
          },
          formData: {
            budget: "{}",
            userScope: "0",
            giftLimit: '{"userDayCountLimit":{"value":1}}',
            creator: "{}",
            platformSubsidyMax: '{"value":0}',
            deliveryType: "1",
          },
        });
        expect(viewJson).toStrictEqual({
          activityLimitRule: {
            userDayCountLimit: 1,
            userTotalCountLimit: undefined,
          },
          activityMarketingInfo: {
            budgetId: undefined,
            budgetName: undefined,
            deliveryType: 1,
            userScope: 0,
          },
          activityRule: {
            condition: 1,
            conditionType: 1,
            discount: 1,
            discountType: 4,
            platformSubsidyMax: "0",
          },
          activityType: 1000036,
          period: [
            {
              closeTime: "23:59:59",
              openTime: "00:00:00",
            },
          ],
          weekday: "0,1,2,3,4,5,6",
        });
      });
      test("招商类型:营销IP招商-招商模版:买一赠一-多时段-含补贴和预算", () => {
        const viewJson = transformFormData2ViewJson({
          baseinfo: {
            activityTime: {
              weeks: [0, 1, 2, 3, 4, 5, 6],
              batchRange: {
                type: "custom",
                list: [
                  {
                    start: "00:00",
                    end: "03:00",
                  },
                  {
                    start: "18:00",
                    end: "20:00",
                  },
                  {
                    start: "22:00",
                    end: "23:59",
                  },
                ],
              },
            },
          },
          formData: {
            "budget": "{\"budgetId\":\"foobar\",\"budgetName\":\"foobar\"}",
            userScope: "0",
            giftLimit: '{"userDayCountLimit":{"value":1}}',
            creator: "{}",
            platformSubsidyMax: '{"value":0}',
            deliveryType: "1",
          },
        });
        expect(viewJson).toStrictEqual({
          activityType: 1000036,
          activityLimitRule: {
            userDayCountLimit: 1,
            userTotalCountLimit: undefined,
          },
          activityMarketingInfo: {
            budgetId: "foobar",
            budgetName: "foobar",
            deliveryType: 1,
            userScope: 0,
          },
          activityRule: {
            condition: 1,
            conditionType: 1,
            discount: 1,
            discountType: 4,
            platformSubsidyMax: "0",
          },
          period: [
            {
              openTime: "00:00:00",
              closeTime: "03:00:59",
            },
            {
              openTime: "18:00:00",
              closeTime: "20:00:59",
            },
            {
              openTime: "22:00:00",
              closeTime: "23:59:59",
            },
          ],
          weekday: "0,1,2,3,4,5,6",
        });
      });
      test("营销IP招商-买一赠一-活动期间每人每天限购-无补贴和预算", () => {
        const viewJson = transformFormData2ViewJson({
          baseinfo: {
            activityTime: {
              weeks: [0, 1, 2, 3, 4, 5, 6],
              batchRange: {
                type: "all",
                list: [{ start: "00:00", end: "23:59" }],
              },
            },
          },
          formData: {
            budget: "{}",
            userScope: "0",
            giftLimit: '{"userTotalCountLimit":{"value":22}}',
            creator: "{}",
            platformSubsidyMax: '{"value":0}',
            deliveryType: "1",
          },
        });
        expect(viewJson).toStrictEqual({
          activityLimitRule: {
            userDayCountLimit: undefined,
            userTotalCountLimit: 22,
          },
          activityMarketingInfo: {
            budgetId: undefined,
            budgetName: undefined,
            deliveryType: 1,
            userScope: 0,
          },
          activityRule: {
            condition: 1,
            conditionType: 1,
            discount: 1,
            discountType: 4,
            platformSubsidyMax: "0",
          },
          activityType: 1000036,
          period: [
            {
              closeTime: "23:59:59",
              openTime: "00:00:00",
            },
          ],
          weekday: "0,1,2,3,4,5,6",
        });
      });
    });

    describe("transformViewJson2FormData", () => {
      // - 招商类型:营销IP招商-招商模版:买一赠一
      test("招商类型:营销IP招商-招商模版:买一赠一-每人每天最多赠送-无补贴和预算", () => {
        const viewJson = JSON.stringify({
          activityLimitRule: {
            userDayCountLimit: 1,
            userTotalCountLimit: undefined,
          },
          activityMarketingInfo: {
            budgetId: undefined,
            budgetName: undefined,
            deliveryType: 1,
            userScope: 0,
          },
          activityRule: {
            condition: 1,
            conditionType: 1,
            discount: 1,
            discountType: 4,
            platformSubsidyMax: "0",
          },
          activityType: 1000036,
          period: [
            {
              closeTime: "23:59:59",
              openTime: "00:00:00",
            },
          ],
          weekday: "0,1,2,3,4,5,6",
        });
        const baseinfo = {
          createdUserId: "329684",
          createdUserName: "郭宇帆",
        };
        const formData = transformViewJson2FormData({
          viewJson,
          baseinfo,
          isCopy: true,
        });
        expect(formData).toStrictEqual({
          userScope: "0",
          giftLimit: '{"userDayCountLimit":{"value":1}}',
          creator: '{"id":"329684","name":"郭宇帆"}',
          platformSubsidyMax: undefined,
          deliveryType: "1",
        });
      });
      test("招商类型:营销IP招商-招商模版:买一赠一-活动期间每人每天限购-有补贴和预算", () => {
        const viewJson = JSON.stringify({
          activityLimitRule: {
            userDayCountLimit: undefined,
            userTotalCountLimit: 28,
          },
          activityMarketingInfo: {
            budgetId: "foobar",
            budgetName: "foobar",
            deliveryType: 1,
            userScope: 0,
          },
          activityRule: {
            condition: 1,
            conditionType: 1,
            discount: 1,
            discountType: 4,
            platformSubsidyMax: "1",
          },
          activityType: 1000036,
          period: [
            {
              closeTime: "23:59:59",
              openTime: "00:00:00",
            },
          ],
          weekday: "0,1,2,3,4,5,6",
        });
        const baseinfo = {
          createdUserId: "329684",
          createdUserName: "郭宇帆",
        };
        const formData = transformViewJson2FormData({ viewJson, baseinfo });
        expect(formData).toStrictEqual({
          "budget": "{\"budgetId\":\"foobar\",\"budgetName\":\"foobar\"}",
          userScope: "0",
          giftLimit: '{"userTotalCountLimit":{"value":28}}',
          creator: '{"id":"329684","name":"郭宇帆"}',
          platformSubsidyMax: '{"value":"1"}',
          deliveryType: "1",
        });
      });
    });
  });
});
