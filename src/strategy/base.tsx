import { v4 as uuid4 } from 'uuid';
import { resolveEnv, maskMS } from "../common"
import { ActivityTypeEnum } from "../constants"
import { CheckNextContext, CreationContext } from "../models"

// alpha 和 invite路由下，如果 main/v2/#/act/activity 页面会404
let hasMainV2 = true;
if (/alpha/.test(window.location.host) 
  || /invite/.test(window.location.host)) {
  hasMainV2 = false;
}

interface ManageOrgSelectDateTs {
  subList?: ManageOrgSelectDateTs[];
  code: string;
  name: string;
}
/**
 * 创建活动的通用信息
 */
export interface CreateActivityCommonData {
  templateId?: number;
  investmentType: number;
  investmentSchema: number;
  activityType: number;
  subsidyType: string;

  name: string;
  remark?: string;
  description: string;
  beginTime: number;
  endTime: number;
  allowCancel: number;
  signUpShopType: number;
  signUpStartTime?: number;
  signUpEndTime?: number;
  manageOrgList?: ManageOrgSelectDateTs[];

  shopPoolId?: number;
  shopPoolName?: string;
  auditRuleId?: number;

  dataMap?: {
    resource: any;
  }
}

export const CableBase = {
  onEvent(event: { type: string }) {
    const env = resolveEnv()
    if (event.type === 'cancel') {
      window.location.href = hasMainV2 ? '/main/v2/#/act/activity' : '/#/act/activity';
      return { finished: true }
    } else if (event.type === 'create-pool') {
      if (env === 'pre') {
        window.open('https://pre-invite.kunlun.alibaba-inc.com/#/shop-pool', '_blank')
      } else if (env === 'prod') {
        window.open('https://invite.kunlun.alibaba-inc.com/#/shop-pool', '_blank')
      }
      return { finished: true }
    } else if (event.type === 'create-rule') {
      if (env === 'pre') {
        window.open('https://pre-invite.kunlun.alibaba-inc.com/#/review-rule', '_blank')
      } else if (env === 'prod') {
        window.open('https://invite.kunlun.alibaba-inc.com/#/review-rule', '_blank')
      }
      return { finished: true }
    } else if (event.type === "create-commodity-pool") {
      if (env === 'pre') {
        window.open('https://pre-kunlun.alibaba-inc.com/selection#/pool/list', '_blank')
      } else if (env === 'prod') {
        window.open('https://selection.kunlun.alibaba-inc.com/?from=header-v2#/pool/list/create/1', '_blank')
      }
      return { finished: true };
    } else {
      return { finished: false }
    }
  }
}

export function resolveCreateActivityCommonData(
  activityType: ActivityTypeEnum,
  ctx: CheckNextContext,
  creationContext: CreationContext
): CreateActivityCommonData {
  const { dataStore } = ctx
  const { baseinfo, playdata, scope } = dataStore.data
  return {
    // 活动类型信息
    templateId: creationContext.templateId,
    investmentType: creationContext.investmentType,
    investmentSchema: creationContext.investmentSchema,
    activityType,
    subsidyType: creationContext.subsidyType,

    // 基本信息
    name: baseinfo.name,
    remark: baseinfo.remark,
    description: baseinfo.description,
    beginTime: baseinfo.activityTime.start.toDate().getTime(),
    endTime: maskMS(baseinfo.activityTime.end.toDate().getTime()),
    allowCancel: baseinfo.allowCancel,
    signUpShopType: baseinfo.signUpShopType,
    signUpStartTime: baseinfo.signupTime ? baseinfo.signupTime.start.toDate().getTime() : undefined,
    signUpEndTime: baseinfo?.signupTime ? maskMS(baseinfo?.signupTime.end.toDate().getTime()) : undefined,
    // 管理业态
    manageOrgList: baseinfo?.manageOrgList,


    // 招商范围信息
    shopPoolId: scope.pool && scope.pool.value,
    shopPoolName: scope.pool && scope.pool.label,

    // 玩法信息
    dataMap: {
      resource: {
        ...playdata.snapshot
      }
    }
  }
}

type TimeRangeFromMarketPlay = [{
  start: string;
  end: string;
}]

export function resolveSingleTimeRangeFromMarketplayValue(value: string) {
  try {
    const rangeList :TimeRangeFromMarketPlay = JSON.parse(value)
    const range = rangeList[0]
    if (range.start === '00:00:00' && range.end === '23:59:59') {
      return {
        type: 'all',
        start: '00:00',
        end: '23:59'
      }
    } else {
      return {
        type: 'custom',
        start: range.start.slice(0, 'HH:mm'.length),
        end: range.end.slice(0, 'HH:mm'.length)
      }
    }
  } catch(e) {
    console.error(e)
    return {
      type: 'all',
      start: '00:00',
      end: '23:59'
    }
  }
}

export function resolveTimeRangeBatchFromMarketplayValue(value: string) {
  try {
    const rangeList = JSON.parse(value);
    const range = rangeList[0];
    if (
      rangeList.length === 1 &&
      range.start === "00:00:00" &&
      range.end === "23:59:59"
    ) {
      return {
        type: "all",
        list: [
          {
            key: uuid4(),
            start: "00:00",
            end: "23:59",
          },
        ],
      };
    } else {
      return {
        type: "custom",
        list: rangeList.map((r: any) => {
          return {
            key: uuid4(),
            start: r.start.slice(0, "HH:mm".length),
            end: r.end.slice(0, "HH:mm".length),
          };
        }),
      };
    }
  } catch (e) {
    console.error(e);
    return {
      type: "custom",
      list: [],
    };
  }
}


export function resolveTimeRangeBatchFromPeriodDTOList(
  periodDTOList: { openTime: string; closeTime: string }[]
) {
  const rangeList = periodDTOList;
  const range = rangeList[0];
  if (
    rangeList.length === 1 &&
    range.openTime === "00:00:00" &&
    range.closeTime === "23:59:59"
  ) {
    return {
      type: "all",
      list: [
        {
          key: uuid4(),
          start: "00:00",
          end: "23:59",
        },
      ],
    };
  } else {
    return {
      type: "custom",
      list: rangeList.map((r) => {
        return {
          key: uuid4(),
          start: r.openTime.slice(0, "HH:mm".length),
          end: r.closeTime.slice(0, "HH:mm".length),
        };
      }),
    };
  }
}
