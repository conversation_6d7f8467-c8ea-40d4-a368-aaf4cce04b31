import './style.scss';

import React, { useEffect, useState } from 'react';
import { BigNumber } from 'bignumber.js';

import * as api from '../../../api';
import { BudgetPickerDetail } from '@alife/carbon-biz-kunlun-zs/lib/components/BudgetPicker/BudgetPicker';

const Field: React.FC<any> = ({ label, children }) => {
  return (
    <div className="zs-act-field">
      <label>{label}</label>
      <div>{children}</div>
    </div>
  );
};

// 生效人群对应关系
export const UserScopeEnumStr = {
  0: '全部用户',
};

// 核销场景
export const deliveryTypeStr = {
  1: '仅外卖可使用',
  2: '仅到店可使用',
  3: '外卖和自提都可使用',
};

// 玩法介绍类型对应关系
export const playRuleDescTypeStr = {
  1: '主投饿了么站内',
  2: '主投饿了么站外',
  3: '主投饿了么站内爆红包频道',
  4: '饿了么全能超市',
};

interface CouponRule {
  activityLimitRule: {
    dayCountLimit: number; // 每日库存最小值
    dayCountLimitMax: number; //  每日库存最大值
  };
  activityMarketingInfo: {
    activitySupplyPoolId: number; // 门店池 id
    userScope: 0 //  生效人群
  };
  couponRule: {
    shopSubsidy: number; //  商家出资
  };
}

interface BlastRedPacketViewJsonTs {
  activityRule: CouponRule[];
  activityType: number;
  attributes: {
    displayPlatformSubsidyMin: string;
    displayPlatformSubsidyMax: string;
    playRuleDescType: string;
  };
  budgetId: string;
  budgetName: string;
  deliveryChannel: 1 | 2; // 投放渠道
  deliveryType: 1 | 2 | 3; //  核销渠道
  userScopeContributionType: 1 | 2 | 3; // 分人群出资方式
}

const deliveryChannelStr = {
  1: '店内投放',
  2: '店外投放',
};

const BlastRedPlayDetail: React.FC<any> = ({ viewJson, activityInfo, env }) => {
  let ready = false;
  let viewJsonObj: BlastRedPacketViewJsonTs = {} as BlastRedPacketViewJsonTs;

  try {
    viewJsonObj = JSON.parse(viewJson);
    if (viewJsonObj === null) return null;
    ready = true;
  } catch (error) {
    console.error(error);
  }

  if (!ready) {
    return null;
  }

  return (
    <div className="zs-blast-red-play-data">
      <div className="zs-blast-red-play-data-header">
        商家展示平台补贴：
        {viewJsonObj?.attributes?.displayPlatformSubsidyMin} -{' '}
        {viewJsonObj?.attributes?.displayPlatformSubsidyMax}
      </div>

      {viewJsonObj.activityRule.map((item) => {
        return (
          <div className="list" key={item?.activityMarketingInfo?.userScope}>
            <div className="list-header">
              生效人群:{' '}
              {UserScopeEnumStr[item?.activityMarketingInfo?.userScope]}{' '}
              <span style={{ marginLeft: 80 }}>
                供给池ID：{item?.activityMarketingInfo?.activitySupplyPoolId}
              </span>
            </div>
            <div className="list-item">
              商家出资金额：
              {new BigNumber(item?.couponRule?.shopSubsidy || 0)
                .dividedBy(100)
                .toNumber()}
              元
            </div>
            <div className="list-item">
              单店每日库存：{item?.activityLimitRule?.dayCountLimit} -{' '}
              {item?.activityLimitRule?.dayCountLimitMax}
            </div>
          </div>
        );
      })}

      <div className="zs-blast-red-play-data-footer">
        <Field label="预算信息：">
          <div style={{ marginTop: -8 }}>
            <BudgetPickerDetail
              value={viewJsonObj}
              env={{
                view: 'detail',
                env: env,
                detailContext: {
                  activityId: activityInfo.activityId
                }
              }}
            />
          </div>
        </Field>

        <Field label="投放渠道：">
          {deliveryChannelStr[viewJsonObj.deliveryChannel]}
        </Field>

        <Field label="玩法介绍：">
          {
            playRuleDescTypeStr[
            +viewJsonObj?.attributes?.playRuleDescType as 1 | 2 | 3
            ]
          }
        </Field>

        <Field label="核销场景：">
          {deliveryTypeStr[viewJsonObj.deliveryType]}
        </Field>

        <Field label="叠斥规则：">
          本活动红包与商家权益（如：全店满减红包，门店新客红包等）可同时使用；
          与平台权益（如：吃货联盟，品质联盟，超会红包等）不可同时使用
        </Field>
      </div>
    </div>
  );
};

const BlastRedPlayDetailWrap: React.FC<any> = ({ activityInfo, env }) => {
  const [detail, setDetail] = useState<any>();

  useEffect(() => {
    api.subActivity.getDetail(activityInfo.activityId).then((res) => {
      setDetail(res.data);
    });
  }, [activityInfo.activityId]);
  return (
    <div className="zs-act-info">
      <div className="zs-act-area">
        <div className="zs-act-area-title" style={{ marginBottom: 30 }}>
          玩法设置
        </div>
        <div className="zs-act-section">
          <BlastRedPlayDetail
            viewJson={detail?.viewJson}
            activityInfo={detail?.investmentActivityDetailDTO}
            env={env}
          />
        </div>
      </div>
    </div>
  );
};

export default BlastRedPlayDetailWrap;
