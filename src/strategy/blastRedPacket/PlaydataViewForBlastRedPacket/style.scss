.zs-blast-red-play-data {
  &-header {
    margin-bottom: 20px;
  }
  .list {
    margin-bottom: 10px;
    &-header {
      border-bottom: 1px solid #ccc;
      padding: 10px 0 10px 40px;
    }
    &-item {
      margin: 10px 0 10px 100px;
    }
  }
  &-footer {
    margin-top: 40px;
  }

  .zs-act-field {
    display: flex;
    > label {
      text-align: right;
      width: 20.833%;
      padding-right: 20px;
      padding-bottom: 25px;
      font-weight: 600;
      flex-shrink: 0;
    }
  }
}
.zs-base-info-budget-prompt {
  display: inline-block;
  background-color: rgb(211,211,211);
  margin-left: 10px;
  border-radius: 5px;
  padding: 0 8px;
}