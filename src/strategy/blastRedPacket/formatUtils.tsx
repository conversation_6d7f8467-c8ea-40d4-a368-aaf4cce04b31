import BigNumber from "bignumber.js";
import { ReqBlastRedPacketViewJson, PlayData } from "./type";

export const formatPlayData2ViewJson = (playdata: PlayData) => {
  const contributionTypeList = Object.keys(playdata)
          ?.filter((v) => +v >= 0)
          .filter((v) => playdata[(v as `${number}`)]?.enabled)
          ?.map((v) => {
            return  playdata[(v as `${number}`)]
          });

  const viewJson: ReqBlastRedPacketViewJson = {
    activityRule: [
      {
        activityLimitRule: {
          dayCountLimit: contributionTypeList[0]?.dayCountLimit,
          dayCountLimitMax: contributionTypeList[0]?.dayCountLimitMax,
        },
        activityMarketingInfo: {
          userScope: 0,
        },
        couponRule: {
          shopSubsidy: new BigNumber(contributionTypeList[0]?.shopSubsidy)
            .multipliedBy(100)
            .toNumber(),
        },
      },
    ],
    activityType: 1000035,
    attributes: {
      // 活动属性字段, 用于存储活动维度的扩展属性，常用于透传字段，避免频繁新增字段
      displayPlatformSubsidyMin: playdata?.displayPlatformSubsidyMin,
      displayPlatformSubsidyMax: playdata?.displayPlatformSubsidyMax,
      playRuleDescType: playdata?.playRuleDescType,
    },
    budgetId: playdata?.budgetId,
    budgetName: playdata?.budgetName,
    deliveryChannel: playdata.deliveryChannel,
    deliveryType: playdata.deliveryType,
  };
  return viewJson;
};
