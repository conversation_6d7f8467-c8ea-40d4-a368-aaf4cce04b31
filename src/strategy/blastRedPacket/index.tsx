/**
 * 爆红包活动
 */
import { Message } from '@alifd/next';
import BigNumber from 'bignumber.js';
import { <PERSON>ton, Dialog } from '@alifd/next';

import * as api from '../../api';
import { maskMS } from '../../common';
import { ActivityTypeEnum } from '../../constants';
import { ActivityStrategy, InvestmentActivityDetailDTO } from '../../models';
import { CableBase } from '../base';
import {
  checkNextTimeForAlpha,
  genericCreateActivity,
  gotoLegacyActivityDetailByDefault,
} from '../mixins';
import { auditServiceForCoupon } from '../service';
import { formatPlayData2ViewJson } from './formatUtils';
import { viewJsonCouponRule } from './type';


const ActivityCancelled = 4; // 活动已取消
const ActivityFinished = 5; // 活动已结束
const ActivityBudgetExhausted = 8; // 活动熔断

export const UserScopeEnumStr = {
  0: '全部用户',
};

const styles = {
  wid: {
    marginTop: '4px',
    fontSize: '14px',
    color: '#999',
  },
  modal: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    width: '448px',
    marginBottom: '40px',
  },
  header: {
    display: 'flex',
    backgroundColor: '#f5f6f7',
  },
  row: {
    display: 'flex',
    alignItems: 'center',
    height: '40px',
  },
  child: {
    lineHeight: '40px',
    borderRight: '1px solid #f5f6f7',
    borderBottom: '1px solid #f5f6f7',
    borderLeft: '1px solid #f5f6f7',
    width: '150px',
    height: '40px',
    textAlign: 'center',
  },
};




const onShowDialog = async (
  record: any,
  activityId: number,
  shopStatus: string,
  activityStatus: number
) => {
  let isEndStatus = false; // 结束态

  // 活动状态为
  if (
    shopStatus === 'offline' ||
    activityStatus === ActivityCancelled ||
    activityStatus === ActivityFinished ||
    activityStatus === ActivityBudgetExhausted
  ) {
    isEndStatus = true;
  }

  const res =
    (await api.queryActivityParticipantStock({
      activityId,
      storeId: record.storeId,
      sellerId: record.aliSellerId,
    })) || {};
  const data = res?.data;

  if (!data) {
    return Message.error(res.errorMessage);
  }

  Dialog.show({
    title: (
      <div>
        <div>{record.storeName}</div>
        <div style={styles.wid}>ID：{record.wid}</div>
      </div>
    ),
    content: (
      // @ts-ignore
      <div style={styles.modal}>
        <div style={styles.header}>
          {/* @ts-ignore */}
          <div style={styles.child}>生效人群</div>
          {/* @ts-ignore */}
          <div style={styles.child}>单店每日库存</div>
          {/* @ts-ignore */}
          <div style={styles.child}>当日剩余库存</div>
        </div>
        {data?.activitySignUpRuleDTOList &&
          data.activitySignUpRuleDTOList?.map((item: any) => {
            return (
              <div style={styles.row} key={item?.userScope}>
                {/* @ts-ignore */}
                <div style={styles.child}>
                  {/* @ts-ignore */}
                  {UserScopeEnumStr[item.userScope]}
                </div>
                {/* @ts-ignore */}
                <div style={styles.child}>
                  <span>{item?.dayCountStock || '-'}</span>
                </div>
                {/* @ts-ignore */}
                <div style={styles.child}>
                  {isEndStatus ? '-' : item?.dayCountRemainingStock}
                </div>
              </div>
            );
          })}
      </div>
    ),
    footer: false,
  });
};

const BlastRedPacket: ActivityStrategy = {
  activityType: ActivityTypeEnum.BAO_HONG_BAO,
  generation: 'v2',
  cable: {
    ...CableBase,
    workspace: 'ExukovQrae6xWn2rt4NgG',
    model: 'ahlV3Dp7w4G8KLFdaq2oqe',
    async checkNext({ step, dataStore }, creationContext, history) {
      console.log('checkNext', step, dataStore);
      if(step.name === "baseinfo") {
        const timeError = await checkNextTimeForAlpha({ step, dataStore }, creationContext);
        if(timeError) {
          return timeError;
        }
      }
      if (step.name === 'playdata') {
        const { baseinfo, scope } = dataStore.data;
        let playdata: any = dataStore.data.playdata;

       
        if (!playdata?.budgetId) {
          return { message: '请先选择预算' };
        }

        const viewJson = formatPlayData2ViewJson(playdata)

        const payload = {
          // 活动类型信息
          templateId: creationContext.templateId,
          investmentType: creationContext.investmentType,
          investmentSchema: creationContext.investmentSchema,
          activityType: ActivityTypeEnum.BAO_HONG_BAO,
          // 基本信息
          name: baseinfo.name,
          description: baseinfo.description,
          remark: baseinfo.remark,
          beginTime: baseinfo.activityTime.start.toDate().getTime(),
          endTime: maskMS(baseinfo.activityTime.end.toDate().getTime()),
          allowCancel: baseinfo.allowCancel,
          signUpShopType: baseinfo.signUpShopType,
          signUpStartTime: baseinfo.signupTime.start.toDate().getTime(),
          signUpEndTime: maskMS(baseinfo.signupTime.end.toDate().getTime()),
          activityPeriodRequestList: [
            {
              weekday: baseinfo.activityTime.weeks.join(','),
              openTime: baseinfo.activityTime.range.start + ':00',
              closeTime: baseinfo.activityTime.range.end + ':00',
            },
          ],
          subsidyType: creationContext?.subsidyType,
          manageOrgList: baseinfo?.manageOrgList,

          // 授权审核人
          auditMemberList:
            baseinfo.auditMemberList?.length > 0
              ? baseinfo.auditMemberList
              : null,

          // 招商范围信息
          shopPoolId: scope.pool.value,
          shopPoolName: scope.pool.label,

          // 玩法信息
          viewJson: JSON.stringify(viewJson),
        };

        return await genericCreateActivity(payload, history, creationContext);
      }
    },
    auditService: auditServiceForCoupon,
  },
  hasAgentSubsidy() {
    return false;
  },
  isOfflineEnabled: function (): boolean {
    // 业态大额红包和其他活动不太一样，下线权限由后端控制
    return true;
  },
  isDingtalkLinkEnabled: () => false,
  hasAuditMemberList: () => true,
  isSupportModifyStock: () => false,
  isDetailInviteStatsOnMainActivityTableVisible: true,
  renderPlayDataInfoForTableView: function (): JSX.Element {
    return <></>;
  },
  renderActivityRuleForTable(viewJson: string) {
    try {
      const rule: viewJsonCouponRule[] =
        JSON.parse(viewJson)?.activityRule || undefined;

      if (rule.length === 0) {
        return <></>;
      }

      return (
        <div>
          {rule.map((item) => {
            return (
              <div
                key={item?.activityMarketingInfo?.userScope}
              >{`活动生效人群：${
                UserScopeEnumStr[item?.activityMarketingInfo?.userScope]
              }；商家出资金额 ${new BigNumber(item?.couponRule?.shopSubsidy)
                .dividedBy(100)
                .toNumber()} 元；单门店每日可消耗数量 ${
                item?.activityLimitRule?.dayCountLimit
              } - ${item?.activityLimitRule?.dayCountLimitMax}`}</div>
            );
          })}
        </div>
      );
    } catch (e) {
      console.error(e);
      return '';
    }
  },

  renderGroupItemExtView: (record, activityId, shopStatus, activityStatus) => {
    return (
      <Button
        text
        type="secondary"
        size="medium"
        style={{ display: 'flax', flex: 2 }}
        onClick={() =>
          onShowDialog(record, activityId, shopStatus, activityStatus)
        }
      >
        查看库存
      </Button>
    );
  },
  isSupportAgent: false,
  gotoLegacyDetail: function (
    act: InvestmentActivityDetailDTO,
    scene: 'lookup' | 'examine'
  ): void {
    gotoLegacyActivityDetailByDefault(act, scene);
  },
  renderActivityRuleForTableLegacy: function () {
    return '';
  },
};

export default BlastRedPacket;
