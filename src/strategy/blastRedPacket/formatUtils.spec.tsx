import { formatPlayData2ViewJson } from "./formatUtils";

describe("blastRedPacket", () => {
  describe("utils", () => {
    describe("formatPlayData2ViewJson", () => {
      test("日常模版", () => {
        const viewJson = formatPlayData2ViewJson({
          "0": {
            enabled: true,
            shopSubsidy: 1,
            dayCountLimit: 10,
            dayCountLimitMax: 55,
            userScope: 0,
          },
          userScopeContributionType: 5,
          deliveryChannel: 2,
          deliveryType: 1,
          playRuleDescType: 1,
          displayPlatformSubsidyMin: 2,
          displayPlatformSubsidyMax: 30,
          budgetId: "400000000004734089",
          budgetName: "招商红包预算单品叠加",
        });
        expect(viewJson).toStrictEqual({
          activityRule: [
            {
              activityLimitRule: { dayCountLimit: 10, dayCountLimitMax: 55 },
              activityMarketingInfo: { userScope: 0 },
              couponRule: { shopSubsidy: 100 },
            },
          ],
          activityType: 1000035,
          attributes: {
            displayPlatformSubsidyMin: 2,
            displayPlatformSubsidyMax: 30,
            playRuleDescType: 1,
          },
          budgetId: "400000000004734089",
          budgetName: "招商红包预算单品叠加",
          deliveryChannel: 2,
          deliveryType: 1,
        });
      });
    });
  });
});
