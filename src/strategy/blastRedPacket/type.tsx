// 组件中取出来的 value
export interface CouponRule {
  enabled: boolean;
  dayCountLimit: number; // 每日库存最小值
  dayCountLimitMax: number; //  每日库存最大值
  shopSubsidy: number; //  商家出资
  userScope: 0; //  生效人群
}
// 接口返回的 viewJson 中的 activityRule
export interface viewJsonCouponRule {
  activityLimitRule: {
    dayCountLimit: number; // 每日库存最小值
    dayCountLimitMax: number; //  每日库存最大值
  };
  activityMarketingInfo: {
    activitySupplyPoolId?: number; // 门店池 id, 接口返回的时候有
    userScope: 0; //  生效人群
  };
  couponRule: {
    shopSubsidy: number; //  商家出资
  };
}

export interface ReqBlastRedPacketViewJson {
  deliveryChannel: number;
  deliveryType: number;
  attributes: {
    playRuleDescType: number;
    displayPlatformSubsidyMin: number;
    displayPlatformSubsidyMax: number;
  };
  budgetId: string;
  activityType: 1000035;
  budgetName: string;
  activityRule: viewJsonCouponRule[];
}
export interface PlayData {
  userScopeContributionType: number;
  deliveryChannel: number;
  deliveryType: number;
  playRuleDescType: number;
  displayPlatformSubsidyMin: number;
  displayPlatformSubsidyMax: number;
  budgetId: string;
  budgetName: string;
  [useScope: `${number}`]: CouponRule;
}