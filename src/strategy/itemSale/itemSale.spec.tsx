import axios from "axios";
import AxiosMockAdapter from "axios-mock-adapter";

import strategy from ".";

describe("itemSale", () => {
  describe("resolveCopyData", () => {
    let axiosMock: AxiosMockAdapter;

    beforeEach(() => {
      axiosMock = new AxiosMockAdapter(axios);
      axiosMock.onGet(/\/mkt\/.*/).reply(200, {
        version: "v1",
        content: [
          {
            type: "paragraph",
            children: [
              {
                text: "yc-test",
              },
            ],
          },
        ],
        text: "yc-test",
      });
    });

    afterEach(() => {
      axiosMock.reset();
    });

    function normalizeCopyData(copyData: any) {
      copyData.baseinfo.activityTime.batchRange.list.forEach((item: any) => {
        delete item.key;
      });
      copyData.baseinfo.activityTime.start =
        copyData.baseinfo.activityTime.start.toISOString();
      copyData.baseinfo.activityTime.end =
        copyData.baseinfo.activityTime.end.toISOString();
      copyData.baseinfo.signupTime.start =
        copyData.baseinfo.signupTime.start.toISOString();
      copyData.baseinfo.signupTime.end =
        copyData.baseinfo.signupTime.end.toISOString();
    }

    test("「基于组件的活动复制」招商类型:日常招商-招商模版:日常招商", async () => {
      const creationContext = {
        copy: true,
        createActiivtyStartTime: 1689673094344,
        createActivitySessionId: "2305d439-8918-4d96-9e0a-98eeaf8daea2",
        isSingleActivity: true,
        activityType: 1000005,
        investmentType: 2,
        investmentSchema: 0,
        fineGrainedActivityTimeDisabled: true,
        activity: {
          activityId: 6323196001,
          createdUserId: "213137",
          createdUserName: "杨辰",
          name: "yc-test",
          remark: "yc-test",
          description: "yc-test",
          beginTime: "2023-07-04T16:00:00.000Z",
          endTime: "2023-07-26T15:59:59.000Z",
          signUpStartTime: "2023-07-04T16:00:00.000Z",
          signUpEndTime: "2023-07-26T15:59:59.000Z",
          allowCancel: 1 as 1 | 2,
          signUpShopType: 0,
          shopPoolId: 258086,
          shopPoolName: "0519测试门店池",
          reviewRuleId: 10012004,
          reviewRuleName: "yc-水果-1份",
          marketPlay: {
            instance: {
              fields: {
                creator: '{"id":"213137","prefix":"yc213137","name":"杨辰"}',
                dateRange:
                  '{"start":"2023-07-05 00:00:00","end":"2023-07-26 23:59:59"}',
                userScope: "0",
                saleRule: '{"saleFixed":{"price":100,"elemeSubsidy":0}}',
                weekday: "0,1,2,3,4,5,6",
                name: "yc-test",
                itemDiscountType: "1",
                userLimit: '{"dayLimit":{"value":"1"}}',
                effectiveChannel: "1",
                launchChannel: "0",
                timeRange: '[{"start":"00:00:00","end":"23:59:59"}]',
                budget: "{}",
              },
            },
          },
          stockType: 1,
          auditMemberList: [],
          viewJson:
            '{"activityLimitRule":{"userDayCountLimit":1},"activityMarketingInfo":{"budgetId":"null","deliveryChannel":0,"deliveryType":1,"userScope":0},"activityRule":{"discount":100,"discountType":3,"platformSubsidyMax":0},"activityType":1000005,"attributes":{"hasRichDescription":"on","version":"3.0","richDescriptionURL":"https://bwm-private-b.oss-cn-hangzhou.aliyuncs.com/mkt/189a1000078fba274375b4d8d165d3c1?Expires=2004171443&OSSAccessKeyId=LTAIhP07XE91RzLJ&Signature=fIS60xBGnV%2B4cmnJh62CXWzNiic%3D"},"period":{"closeTime":"23:59:59","openTime":"00:00:00","weekday":"0,1,2,3,4,5,6"},"periodDTOList":[{"closeTime":"23:59:59","openTime":"00:00:00","weekday":"0,1,2,3,4,5,6"}],"weekday":"0,1,2,3,4,5,6"}',
        },
      };
      const copyData = await strategy.resolveCopyData!(creationContext);
      normalizeCopyData(copyData);
      expect(copyData).toStrictEqual({
        baseinfo: {
          name: "yc-test",
          richDescription: {
            version: "v1",
            content: [
              {
                type: "paragraph",
                children: [
                  {
                    text: "yc-test",
                  },
                ],
              },
            ],
            text: "yc-test",
          },
          remark: "yc-test",
          activityTime: {
            start: "2023-07-04T16:00:00.000Z",
            end: "2023-07-26T15:59:59.000Z",
            weeks: [0, 1, 2, 3, 4, 5, 6],
            batchRange: {
              type: "all",
              list: [
                {
                  start: "00:00",
                  end: "23:59",
                },
              ],
            },
          },
          auditMemberList: [],
          cycleTime: undefined,
          signupTime: {
            start: "2023-07-04T16:00:00.000Z",
            end: "2023-07-26T15:59:59.000Z",
          },
          allowCancel: 1,
          signUpShopType: 0,
        },
        scope: {
          commodityAuditType: 1,
        },
        playdata: {
          fields: {
            budget: undefined,
            creator: '{"id":"213137","prefix":"yc213137","name":"杨辰"}',
            dateRange:
              '{"start":"2023-07-05 00:00:00","end":"2023-07-26 23:59:59"}',
            userScope: "0",
            saleRule: '{"saleFixed":{"price":100,"elemeSubsidy":0}}',
            weekday: "0,1,2,3,4,5,6",
            name: "yc-test",
            itemDiscountType: "1",
            userLimit: '{"dayLimit":{"value":"1"}}',
            effectiveChannel: "1",
            launchChannel: "0",
            timeRange: '[{"start":"00:00:00","end":"23:59:59"}]',
          },
        },
        stock: {
          stockType: 1,
          range: {},
        },
      });
    });

    test("「非组件链路的活动复制」招商类型:日常招商-招商模版:日常招商", async () => {
      const creationContext = {
        copy: true,
        createActiivtyStartTime: 1689676636321,
        createActivitySessionId: "a288fb80-9df1-4834-b920-966c1c73f643",
        isSingleActivity: true,
        activityType: 1000005,
        investmentType: 2,
        investmentSchema: 0,
        fineGrainedActivityTimeDisabled: true,
        activity: {
          activityId: 6323790006,
          createdUserId: "213137",
          createdUserName: "杨辰",
          name: "yc-特价",
          remark: "yc-特价",
          description: "yc-特价",
          beginTime: "2023-07-17T16:00:00.000Z",
          endTime: "2023-07-31T15:59:59.000Z",
          signUpStartTime: "2023-07-17T16:00:00.000Z",
          signUpEndTime: "2023-07-31T15:59:59.000Z",
          allowCancel: 1 as 1 | 2,
          signUpShopType: 0,
          shopPoolId: 258086,
          shopPoolName: "0519测试门店池",
          reviewRuleId: 2646001,
          reviewRuleName: "yc-empty",
          marketPlay: null,
          signUpMinCycleTime: null,
          signUpMaxCycleTime: null,
          stockType: 1,
          auditMemberList: [],
          viewJson:
            '{"activityLimitRule":{"userDayCountLimit":1},"activityMarketingInfo":{"budgetId":"null","deliveryChannel":0,"deliveryType":1,"userScope":0},"activityRule":{"discount":10000,"discountType":3,"platformSubsidyMax":0},"activityType":1000005,"attributes":{"hasRichDescription":"on","version":"3.0","richDescriptionURL":"https://bwm-private-b.oss-cn-hangzhou.aliyuncs.com/mkt/d75dcc65af01954efe7cd574137c609b?Expires=2005295113&OSSAccessKeyId=LTAIhP07XE91RzLJ&Signature=dOM2YDCz%2BE4OpRWBqR6vfl7Xjwg%3D"},"period":{"closeTime":"23:59:59","openTime":"00:00:00","weekday":"0,1,2,3,4,5,6"},"periodDTOList":[{"closeTime":"23:59:59","openTime":"00:00:00","weekday":"0,1,2,3,4,5,6"}],"weekday":"0,1,2,3,4,5,6"}',
        },
      };
      const copyData = await strategy.resolveCopyData!(creationContext);
      normalizeCopyData(copyData);
      expect(copyData).toStrictEqual({
        baseinfo: {
          name: "yc-特价",
          richDescription: {
            version: "v1",
            content: [
              {
                type: "paragraph",
                children: [
                  {
                    text: "yc-test",
                  },
                ],
              },
            ],
            text: "yc-test",
          },
          remark: "yc-特价",
          cycleTime: undefined,
          activityTime: {
            start: "2023-07-17T16:00:00.000Z",
            end: "2023-07-31T15:59:59.000Z",
            weeks: [0, 1, 2, 3, 4, 5, 6],
            batchRange: {
              type: "all",
              list: [
                {
                  start: "00:00",
                  end: "23:59",
                },
              ],
            },
          },
          auditMemberList: [],
          signupTime: {
            start: "2023-07-17T16:00:00.000Z",
            end: "2023-07-31T15:59:59.000Z",
          },
          allowCancel: 1,
          signUpShopType: 0,
        },
        scope: {
          commodityAuditType: 1,
        },
        playdata: {
          fields: {
            budget: undefined,
            name: "yc-特价",
            creator: '{"id":"213137","name":"杨辰"}',
            saleRule: '{"saleFixed":{"price":10000,"elemeSubsidy":0}}',
            userScope: "0",
            userLimit: '{"dayLimit":{"value":1}}',
            launchChannel: "0",
            effectiveChannel: "1",
            dateRange:
              '{"start":"2023-07-18 00:00:00","end":"2023-07-31 23:59:59"}',
            weekday: "0,1,2,3,4,5,6",
            timeRange: '[{"start":"00:00:00","end":"23:59:59"}]',
          },
        },
        stock: {
          stockType: 1,
          range: {},
        },
      });
    });
  });
});
