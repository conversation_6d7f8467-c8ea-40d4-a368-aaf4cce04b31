import debugFn from "debug";
import {
  Button,
  Field,
  Form,
  Message,
  NumberPicker,
  Radio,
  Select,
} from "@alifd/next";
import { FieldValues } from "@alifd/next/types/field";
import {
  BudgetPickerEdit,
  BudgetPickerEditProps,
} from "@alife/carbon-biz-kunlun-zs/lib/components/BudgetPicker/BudgetPicker";
import { useRef } from "react";

import {
  DeliveryChannelEnum,
  DeliveryChannelOptions,
  DeliveryChannelSelect,
} from "components/formControls/DeliveryChannelSelect";
import {
  DeliveryTypeEnum,
  DeliveryTypeOptions,
  DeliveryTypeSelect,
} from "components/formControls/DeliveryTypeSelect";
import {
  UserScopeEnum,
  UserScopeSelect,
} from "components/formControls/UserScopeSelect";
import { DiscountWithSubsidy } from "components/formControls/DiscountWithSubsidy";

const debug = debugFn("zs:strategy:itemSale:Baohaojia:PlaydataStep");

function OrderCountLimit({ value, onChange }: any) {
  return (
    <div style={{ display: "flex", alignItems: "center" }}>
      单品每单限购&nbsp;
      <NumberPicker
        value={value}
        onChange={onChange}
        min={1}
        max={999}
        precision={0}
        step={1}
        placeholder="请输入"
        style={{ width: 120 }}
      />
      &nbsp;件
    </div>
  );
}

export interface FormValue {
  userScope?: UserScopeEnum;
  orderCountLimit?: number;
  deliveryChannel?: DeliveryChannelEnum;
  deliveryType?: DeliveryTypeEnum;
  budgetInfo?: BudgetPickerEditProps["value"];
  deliveryFeePlatformSubsidyMax?: number;
  deliveryFeeDiscount?: boolean;
}

interface Props {
  env: "prod" | "pre" | "daily";
  value?: FormValue;
  onChange?: (v?: FormValue) => void;
  onEvent?: (event: any) => void;
  budgetRequired?: boolean;
  budgetPickerProps: Omit<BudgetPickerEditProps, "env">;
}

export default function PlaydataStep({
  env,
  value,
  onChange,
  onEvent,
  budgetRequired = false,
  budgetPickerProps,
}: Props) {
  const finalBudgetPickerProps = { ...budgetPickerProps, env };
  debug("finalBudgetPickerProps", budgetPickerProps);

  const defaultValueRef = useRef<FormValue | undefined>(
    value || {
      userScope: UserScopeEnum.ALL,
      orderCountLimit: 1,
      deliveryChannel: DeliveryChannelEnum.GENERAL,
      deliveryType: DeliveryTypeEnum.WAIMAI,
      deliveryFeeDiscount: true,
    }
  );
  const field = Field.useField({
    values: defaultValueRef.current as FieldValues,
    onChange: () => {
      onChange?.(field.getValues());
    },
  });
  return (
    <div style={{ width: 720, margin: "0 auto" }}>
      <Form wrapperCol={{ span: 19 }} labelCol={{ span: 5 }} field={field}>
        <Form.Item label="活动类型">
          <Select value={1} disabled style={{ width: 200 }}>
            <Select.Option value={1}>商品特价</Select.Option>
          </Select>
        </Form.Item>
        <Form.Item
          label="限制人群"
          name="userScope"
          required
          requiredMessage="请填写限制人群"
        >
          <UserScopeSelect options={[{ label: "全部顾客", value: 0 }]} />
        </Form.Item>
        <Form.Item
          label="用户购买限定"
          name="orderCountLimit"
          required
          requiredMessage="请填写用户购买限定"
        >
          <OrderCountLimit />
        </Form.Item>
        <Form.Item
          label="投放渠道"
          name="deliveryChannel"
          required
          requiredMessage="请选择投放渠道"
        >
          <DeliveryChannelSelect
            options={DeliveryChannelOptions.filter(
              (op) => op.value === DeliveryChannelEnum.GENERAL
            )}
          />
        </Form.Item>
        <Form.Item
          label="生效渠道"
          name="deliveryType"
          required
          requiredMessage="请选择生效渠道"
        >
          <DeliveryTypeSelect
            options={DeliveryTypeOptions.map((op) => {
              const disabled = op.value !== DeliveryTypeEnum.WAIMAI;
              return { ...op, disabled };
            })}
          />
        </Form.Item>

        <Form.Item
          label="是否0起送免运费"
          name="deliveryFeeDiscount"
          required
          requiredMessage="请选择是否0起送免运费"
        >
          <Radio value={1} disabled style={{ width: 200 }}>
            是
          </Radio>
        </Form.Item>

        <Form.Item
          label="运费补贴上限"
          name="deliveryFeePlatformSubsidyMax"
          required
          requiredMessage="请填写运费补贴上限"
        >
          <DiscountWithSubsidy
            precision={0}
            numberMin={3}
            numberMax={50}
            prompt="优惠金额扣除消费者运费减免后，用做于商家运费补贴， 且平台配商家的运费补贴最高不超过商家物流增值服务费（Y）， 自配送商家运费补贴最高不超过X元"
          />
        </Form.Item>

        <Form.Item
          label="预算"
          name="budgetInfo"
          required={budgetRequired || field.getValue("deliveryFeePlatformSubsidyMax")}
          requiredMessage="请选择预算"
          validatorTrigger={["onBlur"]}
          validator={(_: any, v: BudgetPickerEditProps["value"], callback) => {
            if (budgetRequired || field.getValue("deliveryFeePlatformSubsidyMax")) {
              if (!v?.budgetId) {
                callback("请选择预算");
                return;
              }
            } else {
              if (v?.budgetId) {
                callback("当前活动不需要配置预算，请清空");
                return;
              }
            }
            callback();
          }}
        >
          <BudgetPickerEdit {...finalBudgetPickerProps} hasClear />
        </Form.Item>

        <Form.Item label=" " style={{ padding: "30px 0px 20px" }}>
          <div style={{ display: "flex" }}>
            <Button
              onClick={() => onEvent?.({ type: "cancel" })}
              style={{ marginRight: 8 }}
            >
              取消
            </Button>
            <Button
              onClick={() => onEvent?.({ type: "prev" })}
              style={{ marginRight: 8 }}
            >
              上一步
            </Button>
            <Button
              type="primary"
              onClick={async () => {
                const result = await field.validatePromise();
                if (result.errors) {
                  console.log("result.errors", result.errors);
                  const message = Object.values(result.errors)?.[0]
                    ?.errors?.[0] as string;
                  Message.error(message);
                  return;
                }
                onEvent?.({ type: "next" });
              }}
            >
              创建活动
            </Button>
          </div>
        </Form.Item>
      </Form>
    </div>
  );
}
