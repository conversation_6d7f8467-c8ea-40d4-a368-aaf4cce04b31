import { Form } from "@alifd/next";

import { ActivitySignType, ActivityTypeEnum } from "../../../constants";
import { CableInst, CheckNextContext, CreationContext } from "models";
import loadingDialog from "components/LoadingDialog";

import { CableBase, resolveCreateActivityCommonData } from "strategy/base";
import {
  checkAndUploadRichDescription,
  checkMarketRules,
  checkMktRulesForMultiBusinessFormAndConfirmIfNeed,
  checkNextTimeForAlpha,
  genericCreateActivity,
  resolveCommodityAuditProps,
} from "strategy/mixins";

import {
  QualityProductPlanTime,
  QualityProductPlanRule,
} from "./QualityProductPlan";
import { transformPlaydata2ViewJson } from "./translator";
import { maskMS } from "common";

export function cable(option: {
  investmentSchema: number;
  investmentType: number;
  activitySignType?: ActivitySignType;
  extra?: CreationContext["extra"];
}): CableInst {
  const ret: CableInst = {
    ...CableBase,
    workspace: "ExukovQrae6xWn2rt4NgG",
    model: "7XnE6TFcVx5a9diIyF1skb",
    async checkNext(
      checkNextContext: CheckNextContext,
      creationContext: CreationContext,
      history: any
    ) {
      const { step, dataStore } = checkNextContext;
      if (!dataStore._session) {
        dataStore._session = {};
      }
      if (step.name === "baseinfo") {
        if (dataStore.data.baseinfo?.richDescription) {
          const { hide } = loadingDialog.show({
            message: "活动信息检查中...",
          });
          try {
            const error = await checkAndUploadRichDescription({
              richDescription: dataStore.data.baseinfo?.richDescription,
              session: dataStore._session,
            });
            if (error) {
              return error;
            }
          } finally {
            hide();
          }
        }
      }
      if(step.name === "baseinfo") {
        const { baseinfo } = dataStore.data;
        const beginTime = baseinfo.activityTime.start.toDate().getTime()
        const endTime = maskMS(baseinfo.activityTime.end.toDate().getTime())
        // 当beginTime或者endTime变化的时候，就清空 scope.pool 的值
        if (beginTime !== dataStore._session.beginTime || endTime !== dataStore._session.endTime) {
          dataStore._session.beginTime = beginTime;
          dataStore._session.endTime = endTime;
          // 清空 scope.pool 的值
          dataStore.update('scope', (prev: any) => {
            return {
              ...prev,
              version: prev?.version
              ? prev.version + 1
              : Date.now(),
              pool: undefined
            }
          });
        }
      }
      if (step.name === "baseinfo") {
        const timeError = await checkNextTimeForAlpha(
          checkNextContext,
          creationContext
        );
        if (timeError) {
          return timeError;
        }
      }
      if (step.name === "baseinfo") {
        // 重置 auditUpgradeForMultiMktRule，避免重新回到第一步重新选择业务线和业态的情况
        dataStore._session.auditUpgradeForMultiMktRule = undefined;
        // 校验是否跨业态，并且返回弹窗点击结果
        const { conflicted, userConfirm } =
          await checkMktRulesForMultiBusinessFormAndConfirmIfNeed(
            creationContext,
            dataStore.data.baseinfo
          );
        if (!conflicted) {
          // 没有错误信息，不需要拦截进入下一步
          return;
        }
        if (userConfirm) {
          // 有错误，并且用户二次确认了，确认需提交审批
          dataStore._session.auditUpgradeForMultiMktRule = true;
          // 不需要拦截进入下一步
          return;
        } else {
          // 有错误，单用户未二次确认，页面停留在当前步骤
          return { silent: true };
        }
      }   
      if (step.name === "playdata") {
        const { baseinfo, scope, playdata, product } = dataStore.data;
        const commonData: any = resolveCreateActivityCommonData(
          ActivityTypeEnum.SKU_TE_BASE,
          checkNextContext,
          creationContext
        );
        delete commonData.dataMap;
        const commodityAuditProps: any = resolveCommodityAuditProps(scope);
        const viewJson = transformPlaydata2ViewJson({
          baseinfo,
          playdata,
          creationContext,
        });

        const payload: any = {
          ...commonData,
          // 自动审核规则
          ...commodityAuditProps,
          // 降套补 商品清退规则
          commodityRemoveCondition: {
            removeCondition: scope.removeCondition,
          },
          // 审核人列表
          auditMemberList:
            baseinfo.auditMemberList?.length > 0
              ? baseinfo.auditMemberList
              : null,
          // 玩法设置
          viewJson: JSON.stringify(viewJson),
          // 商品规则
          uploadPath: product?.file?.url,
          // 优品投放计划 id
          qualityProductPlanId:
            creationContext.extra?.qualityProductPlan?.planId,
        };

        if (
          baseinfo.modifyRules &&
          baseinfo.modifyRules.indexOf("merchant_modify_activity_price") !== -1
        ) {
          payload.activityModifyRules = [
            {
              activityModifyRuleType: "merchant_modify_activity_price",
              isSupport: true,
            },
          ];
        }

        if (dataStore._session?.ossURL) {
          const ossURL = dataStore._session?.ossURL;
          payload.description = baseinfo.richDescription.text;
          payload.attributes = {
            ...payload.attributes,
            hasRichDescription: "on",
            richDescriptionURL: ossURL,
          };
        }
        const {
          interrupt,
          auditUpgradeForCheckMktRuleUnPass,
          auditUpgradeForMultiMktRule,
        } = await checkMarketRules(
          dataStore,
          creationContext,
          JSON.stringify(viewJson)
        );
        if (interrupt) {
          return { silent: true };
        }

        payload.auditUpgradeForCheckMktRuleUnPass = auditUpgradeForCheckMktRuleUnPass;
        payload.auditUpgradeForMultiMktRule = auditUpgradeForMultiMktRule;    

        return await genericCreateActivity(payload, history, creationContext);
      }
    },

    stepOptions: {
      baseinfo: {
        slotBeforeActivityTime: (
          <Form.Item label="投放生效时间">
            <p>
              <QualityProductPlanTime plan={option.extra?.qualityProductPlan} />
            </p>
          </Form.Item>
        ),
      },
      scope: {
        slotBeforeRule: (
          <>
            <div className="zs-scope-info-step-title">报名规则</div>
            <QualityProductPlanRule plan={option.extra?.qualityProductPlan} />
          </>
        ),
      },
    },
  };

  return ret;
}
