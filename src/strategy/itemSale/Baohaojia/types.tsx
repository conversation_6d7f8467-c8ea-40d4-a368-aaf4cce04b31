import { DeliveryChannelEnum } from "components/formControls/DeliveryChannelSelect";
import { DeliveryTypeEnum } from "components/formControls/DeliveryTypeSelect";
import { UserScopeEnum } from "components/formControls/UserScopeSelect";

export interface BaohaojiaViewJson {
  activityRule:{
    platformSubsidyRateUpperLimit?: number;
    deliveryFeePlatformSubsidyMax?: number;
    supportReduceDeliveryFee?: boolean;
  }
  activityType: 1000005;
  activityLimitRule: {
    detailOrderCountLimit: string;
  };
  activityMarketingInfo: {
    deliveryChannel: DeliveryChannelEnum;
    deliveryType: DeliveryTypeEnum;
    userScope: UserScopeEnum;
    budgetId: string;
    budgetName: string;
  };
  period: { openTime: string; closeTime: string }[];
  weekday: string;
}
