import axios from "axios";
import { v4 as uuid4 } from "uuid";
import { <PERSON>ton, Dialog, Icon, Message, Upload } from "@alifd/next";
import { ObjectFile } from "@alifd/next/types/upload/types";

import { alscClient } from "api";

export async function getUploadURL(key: string) {
  const res = await alscClient.fetch({
    apiKey:
      "ele-newretail-investment.NewretailInvestmentActivityYundingService.getUploadURL",
    params: [key],
  });
  return res.data;
}

async function checkExcel(uploadFilePath: string, checkExcelParams?: any) {
  const res = await alscClient.fetch({
    apiKey:
      "ele-newretail-investment.NewretailInvestmentActivityYundingService.checkExcel",
    params: { uploadFilePath, ...checkExcelParams },
  });
  return res;
}

async function uploadFile(file: any) {
  const key = uuid4() + ".xlsx";

  let ossURL: string;
  // @ts-ignore
  ossURL = await getUploadURL(key);

  await axios({
    method: "PUT",
    url: ossURL,
    headers: {
      "Content-Type": "application/octet-stream",
    },
    data: new Blob([file], { type: "application/octet-stream" }),
  });
  return ossURL.split("?")[0];
}

interface CheckExcelResult {
  totalRow: number;
  successRow: number;
  errorRow: number;
  repeatRow: number;
  extInfoMap: {
    invitedUpcHasPlatformSubsidy: string;
    checkResultFilePath: string;
  };
}

interface Value {
  hasPlatformSubsidy?: boolean;
  _checkResult?: CheckExcelResult;
  file?: ObjectFile;
}

interface Props {
  skipCheckExcel?: boolean;
  templateURL: string;
  checkExcelParams?: any;
  qualityProductPlanId?: number;
  value?: Value;
  onChange?: (value?: Value) => void;
  onEvent?: (event: any) => void;
}

export default function QualityProductConfigStep({
  skipCheckExcel = false,
  value,
  qualityProductPlanId,
  onChange,
  checkExcelParams,
  templateURL,
  onEvent,
}: Props) {
  function handleExport() {
    alscClient
      .fetch({
        apiKey:
          "ele-newretail-investment.QualityProductYundingService.exportQualityProductGroup",
        params: [qualityProductPlanId],
      })
      .then((res) => {
        if (res?.data) {
          window.open(res?.data, "_blank");
        }
      });
  }

  return (
    <div style={{ width: 640, margin: "0 auto" }}>
      <div style={{ marginBottom: 8 }}>
        <Button text type="primary" onClick={() => handleExport()}>
          下载商品组数据
        </Button>
      </div>
      <div style={{ marginBottom: 8 }}>
        请先
        <a
          href={templateURL}
          target="_blank"
          style={{ color: "#ff7000", cursor: "pointer" }}
        >
          下载模板
        </a>
        文件，按要求填写后上传
      </div>

      <Upload.Dragger
        accept=".xlsx"
        disabled={value?.file?.state === "uploading"}
        limit={1}
        listType="text"
        onError={(file: any) => {
          if (file.size > 1024 * 1024) {
            Message.error("文件需小于1M");
            return;
          }
          Message.error("文件上传失败，请重新尝试上传文件");
        }}
        onChange={(v) => {
          if (v?.length) {
            const f = v[0] as any;
            onChange?.({
              hasPlatformSubsidy: f?.response?.hasPlatformSubsidy,
              _checkResult: f?.response?.checkResult,
              file: f,
            });
          } else {
            onChange?.(undefined);
          }
        }}
        beforeUpload={(file: any) => {
          if (file.size > 1024 * 1024) {
            return false;
          }
        }}
        // @ts-ignore
        request={async (option: {
          onError: any;
          onSuccess: any;
          file: ObjectFile;
        }) => {
          let aborted = false;
          uploadFile(option.file)
            .then(async (url) => {
              if (aborted) {
                return;
              }
              let hasPlatformSubsidy = false;
              let checkResult: Value["_checkResult"] | null = null;
              if (!skipCheckExcel) {
                const res = await checkExcel(url, checkExcelParams);
                if (!res.success) {
                  Message.error(res.resultMessage);
                  option.onError(new Error(res.resultMessage));
                  return;
                }
                if (!res?.data?.successRow) {
                  Dialog.error({
                    title: "错误提示",
                    content: "商品配置无效，请重新上传",
                    okProps: { children: "导出失败信息" },
                    footerActions: ["cancel", "ok"],
                    onOk: () => {
                      window.open(
                        res?.data?.extInfoMap?.checkResultFilePath,
                        "_blank"
                      );
                    },
                  });
                  option.onError(new Error("商品配置无效，请重新上传"));
                  return;
                } else if (res?.data?.errorRow > 0) {
                  checkResult = res?.data;
                }
                hasPlatformSubsidy =
                  res?.data?.extInfoMap?.invitedUpcHasPlatformSubsidy ===
                  "true";
              }

              option.onSuccess({
                success: true,
                url,
                checkResult,
                name: option.file.name,
                hasPlatformSubsidy,
              });
            })
            .catch((e) => {
              if (aborted) {
                return;
              }
              option.onError(e);
              Message.error(e?.message || "服务器错误，请稍后重试");
            });

          return {
            abort() {
              aborted = true;
            },
          };
        }}
        // @ts-ignore
        value={value?.file ? [value.file] : []}
      >
        <div className="next-upload-drag">
          <p className="next-upload-drag-icon">
            <Icon type="upload" />
          </p>
          <p className="next-upload-drag-text">
            点击或者拖动文件到虚线框内上传
          </p>
          <p className="next-upload-drag-hint">
            支持 .xlsx类型的文件，需小于1MB
          </p>
        </div>
      </Upload.Dragger>

      {value?._checkResult ? (
        <div style={{ marginTop: 12, display: "flex", alignItems: "center" }}>
          {value?._checkResult?.successRow || "-"} 条数据成功，
          {value?._checkResult?.errorRow || "-"} 条数据失败
          {value?._checkResult?.extInfoMap?.checkResultFilePath ? (
            <Button
              type="primary"
              style={{ marginLeft: 8 }}
              text
              onClick={() => {
                window.open(
                  value?._checkResult?.extInfoMap?.checkResultFilePath,
                  "_blank"
                );
              }}
            >
              导出明细
            </Button>
          ) : null}
        </div>
      ) : null}

      <div style={{ textAlign: "center", padding: "30px 0 20px" }}>
        <Button onClick={() => onEvent?.({ type: "prev" })}>上一步</Button>
        <Button
          type="primary"
          style={{ marginLeft: 8 }}
          onClick={() => {
            if (!value?.file?.url || value?.file?.state !== "done") {
              if (value?.file?.state === "error") {
                Message.error("请重新上传文件");
              } else {
                Message.error("请上传文件");
              }
              return;
            }
            if(value?._checkResult?.errorRow) {
              Message.error(`有${value?._checkResult?.errorRow}条数据失败，请重新上传`);
              return;
            }

            onEvent?.({ type: "next" });
          }}
        >
          下一步
        </Button>
      </div>
    </div>
  );
}
