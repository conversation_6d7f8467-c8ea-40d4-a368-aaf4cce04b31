import { CreationContext } from "models";

import { FormValue as BaohaojiaPlaydata } from "./PlaydataStep";
import { Baohaojia<PERSON>iew<PERSON>son } from "./types";

export function transformPlaydata2ViewJson(options: {
  playdata: BaohaojiaPlaydata;
  baseinfo: {
    platformSubsidyRateUpperLimit: number;
    activityTime: {
      weeks: number[];
      batchRange: {
        type: "all" | "custom";
        list: { start: string; end: string }[];
      };
    };
  };
  creationContext: CreationContext;
}): BaohaojiaViewJson {
  return {
    activityType: 1000005,
    activityLimitRule: {
      detailOrderCountLimit: `${options.playdata.orderCountLimit}`,
    },
    activityRule: {
      platformSubsidyRateUpperLimit: options.baseinfo?.platformSubsidyRateUpperLimit,
      deliveryFeePlatformSubsidyMax: options.playdata.deliveryFeePlatformSubsidyMax,
      supportReduceDeliveryFee: options.playdata.deliveryFeeDiscount,
    },
    activityMarketingInfo: {
      deliveryChannel: options.playdata.deliveryChannel!,
      deliveryType: options.playdata.deliveryType!,
      userScope: options.playdata.userScope!,
      budgetId: options.playdata.budgetInfo?.budgetId!,
      budgetName: options.playdata.budgetInfo?.budgetName!,
    },
    weekday: options.baseinfo.activityTime.weeks.join(","),
    period: options.baseinfo.activityTime.batchRange.list.map((item) => {
      return {
        openTime: item.start.slice(0, "HH:mm".length) + ":00",
        closeTime: item.end.slice(0, "HH:mm".length) + ":59",
      };
    }),
  };
}

export function transformViewJson2Playdata(
  viewJson: BaohaojiaViewJson
): BaohaojiaPlaydata {
  return {
    orderCountLimit: +viewJson.activityLimitRule.detailOrderCountLimit,
    deliveryChannel: viewJson.activityMarketingInfo.deliveryChannel,
    deliveryType: viewJson.activityMarketingInfo.deliveryType,
    userScope: viewJson.activityMarketingInfo.userScope,
    budgetInfo: viewJson.activityMarketingInfo?.budgetId
      ? {
          budgetId: viewJson.activityMarketingInfo.budgetId,
          budgetName: viewJson.activityMarketingInfo.budgetName,
        }
      : undefined,
    deliveryFeePlatformSubsidyMax: viewJson.activityRule?.deliveryFeePlatformSubsidyMax,
    deliveryFeeDiscount: viewJson.activityRule?.supportReduceDeliveryFee,
  };
}
