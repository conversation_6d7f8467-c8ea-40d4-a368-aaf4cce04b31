import moment from "moment";
import { Form } from "@alifd/next";

import { alscClient } from "api";

function isEmpty(v: any) {
  return v === "" || v === undefined || v === null;
}

interface Plan {
  planId: number;
  startTime: number;
  endTime: number;
  productRule: {
    t30LiabilityPI: number; // 商责无效率
    deliveyFeeThreshold: number; //  运费门槛
    baseFreight: number; // 基础运费
    isExpressShop: true; // 是否快递店
    minT30ShopSaleDays: number; // 店铺30d动销天数
    minT30ValidOrderCnt: number; // 商品近30d的累计销量数据
  };
}

export interface Service {
  getPlan(params: { planId: number }): Promise<{ data?: Plan }>;
}

export class DefaultService implements Service {
  async getPlan(params: { planId: number }): Promise<{ data?: Plan }> {
    const res = await alscClient.fetch({
      apiKey:
        "ele-newretail-investment.QualityProductYundingService.queryQualityProductPlan",
      params: [params.planId, ""],
    });
    const result = (res?.data || []).find(
      (item: any) => item.planId === params.planId
    );
    return { data: result };
  }
}

interface Props {
  plan?: Plan;
}

export function QualityProductPlanTime({ plan }: Props) {
  if (!plan) {
    return <></>;
  }

  return (
    <>
      {moment(plan?.startTime).format("YYYY-MM-DD HH:mm:ss")}
      <span style={{ marginLeft: 4, marginRight: 4 }}>~</span>
      {moment(plan?.endTime).format("YYYY-MM-DD HH:mm:ss")}
    </>
  );
}

export function QualityProductPlanRule({ plan }: Props) {
  if (!plan) {
    return <></>;
  }

  const layout = {
    labelCol: { span: 5 },
    wrapperCol: { span: 19 },
  };

  // 注意!!!：这里不能直接套一个 Form，因为这个组件最终是用在 ScopeItemStep 中的，ScopeItemStep 本身就是一个 form 组件
  // 这里如果直接套一个 Form 组件会出现 Form 嵌套的情况，可能会出现一些无法预期的故障
  return (
    <>
      <Form.Item label="起送门槛" {...layout}>
        <p>
          {isEmpty(plan?.productRule.deliveyFeeThreshold)
            ? "-"
            : "≤" + plan?.productRule.deliveyFeeThreshold + "元"}
        </p>
      </Form.Item>
      <Form.Item label="基础运费" {...layout}>
        <p>
          {isEmpty(plan?.productRule.baseFreight)
            ? "-"
            : "≤" + plan?.productRule.baseFreight + "元"}
        </p>
      </Form.Item>
      <Form.Item label="是否允许快递店报名" {...layout}>
        {isEmpty(plan?.productRule.isExpressShop) ? (
          <p>-</p>
        ) : (
          <p>{plan?.productRule.isExpressShop ? "是" : "否"}</p>
        )}
      </Form.Item>
      <Form.Item label="店铺近30天商责无效率" {...layout}>
        {isEmpty(plan?.productRule.t30LiabilityPI) ? (
          <p>-</p>
        ) : (
          <p>≤{plan?.productRule.t30LiabilityPI}%</p>
        )}
      </Form.Item>
      <Form.Item label="店铺近30天动销天数" {...layout}>
        {isEmpty(plan?.productRule.minT30ShopSaleDays) ? (
          <p>-</p>
        ) : (
          <p>≥{plan?.productRule.minT30ShopSaleDays}天</p>
        )}
      </Form.Item>
      <Form.Item label="商品近30天累计订单量" {...layout}>
        {isEmpty(plan?.productRule.minT30ValidOrderCnt) ? (
          <p>-</p>
        ) : (
          <p>≥{plan?.productRule.minT30ValidOrderCnt}单</p>
        )}
      </Form.Item>
    </>
  );
}
