import { But<PERSON>, <PERSON>alog, Message } from "@alifd/next";
import * as React from "react";
import { useCallback, useEffect, useMemo, useState } from "react";

import { DefaultChangeUpcService } from "./service";
import { CommodityRulesWithUpLoad } from "@alife/carbon-biz-kunlun-zs";
import { resolveEnv } from "common";

interface Props {
  activityType: number;
  investmentSchema: number;
  service?: any;
  activityId: number | string;
  qualityProductPlanId: number;
}

interface OperatorType {
  type: "add" | "modify" | "delete";
  url: string;
  promptText?: string;
}

const typeStr = {
  add: "添加",
  modify: "修改",
  delete: "删除",
};

const MODIFY_ACTIVITY = 5;

const FILE_UPLOAD_STATUS = {
  UPLOADING: 2,
  FAILED: 3
} as const;

const CommodityRule: React.FC<Props> = ({
  investmentSchema,
  activityType,
  service,
  activityId,
  qualityProductPlanId
}) => {
  const [uploadFileCheck, setUploadFileCheck] = useState<any>();
  const [hasModifyPermission, setHasModifyPermission] = useState(false);
  const [isBidden, setIsBidden] = useState(false);
  const [changeUPCModalVisible, setChangeUPCModalVisible] = useState(false);
  const [uploadFileOperator, setUploadFileOperator] = useState<OperatorType>({
    type: "add",
    promptText: "",
    url: "https://bwm-private-b.oss-cn-hangzhou.aliyuncs.com/mkt/M_FROM_N_ONE_PERCENT_ADD.xlsx?Expires=4844211623&OSSAccessKeyId=LTAIhP07XE91RzLJ&Signature=oUYIsTcYoOLx3V%2B4uSu%2FsYAToRw%3D&response-content-disposition=attachment%3Bfilename%3DN%E9%80%89M%E5%91%A8%E6%9C%AB%E4%B8%80%E5%88%86%E8%B4%AD%E4%B8%8A%E4%BC%A0%E9%82%80%E8%AF%B7UPC.xlsx",
  });
  const finalService = useMemo(() => {
    if (!service) {
      return new DefaultChangeUpcService();
    } else {
      return service;
    }
  }, [service]);
  useEffect(() => {
    finalService
      .checkPermission(activityId, MODIFY_ACTIVITY)
      .then((res: any) => {
        setHasModifyPermission(res?.accessible);
      });
    finalService.checkForbidden().then((res: boolean) => {
      setIsBidden(res);
    });
  }, [activityId]);
  const editable = useMemo(() => {
    if (!hasModifyPermission) {
      return false;
    }
    if (isBidden) {
      return false;
    }
    return true;
  }, [isBidden, hasModifyPermission]);

  const initialize = useCallback(() => {
    setChangeUPCModalVisible(false);
    setUploadFileCheck(null);
  }, []);

  return (
    <div>
      <Dialog
        visible={changeUPCModalVisible}
        title={`${typeStr[uploadFileOperator.type]}商品信息`}
        onClose={() => initialize()}
        onCancel={() => initialize()}
        onOk={async () => {
          try {
            if (!uploadFileCheck?.uploadPath) {
              Message.error("请上传文件");
              return;
            }
            if (uploadFileCheck?.fileUploadStatus === FILE_UPLOAD_STATUS.UPLOADING) {
              Message.error("正在检查文件，请稍后");
              return;
            }
            if (uploadFileCheck?.fileUploadStatus === FILE_UPLOAD_STATUS.FAILED) {
              Message.error("文件上传失败，请重新上传文件");
              return;
            }
            if (uploadFileCheck?.checkExcel?.errorRow) {
              Message.error(`有${uploadFileCheck?.checkExcel?.errorRow}条数据失败，请重新上传`);
              return;
            }
            const res = await finalService.uploadInviteUpc({
              activityId,
              uploadFilePath: uploadFileCheck.uploadPath,
              uploadFileOperatorType: uploadFileOperator.type,
              uploadTotalUpc: uploadFileCheck.checkExcel.uploadTotalUpc,
            });
            if (res.success) {
              Message.success("操作成功");
              initialize();
            } else {
              Message.error(res?.errorMessage || "操作失败，请稍后重试");
            }
          } catch (e: any) {
            console.error(e);
            Message.error(e?.message || "操作失败，请稍后重试");
          }
        }}
      >
        <div style={{ minWidth: 450, marginTop: "10px" }}>
          <div>{uploadFileOperator.promptText}</div>
          <CommodityRulesWithUpLoad
            extraCheckParams={{
              qualityProductPlanId
            }}
            uploadFileOperatorType={uploadFileOperator.type}
            env={resolveEnv()}
            activityType={activityType}
            templateUrl={uploadFileOperator.url}
            investmentSchema={investmentSchema}
            onChange={(v) => setUploadFileCheck(v)}
            value={uploadFileCheck}
          />
        </div>
      </Dialog>

      {editable && (
        <div style={{ marginTop: "5px" }}>
          <Button
            style={{ marginRight: "10px" }}
            onClick={() => {
              setChangeUPCModalVisible(true);
              setUploadFileOperator({
                type: "add",
                url: 'https://bwm-private-b.oss-cn-hangzhou.aliyuncs.com/mkt/%E7%88%86%E5%A5%BD%E4%BB%B7%E5%AF%BC%E5%85%A5%E5%95%86%E5%93%81%E7%BB%84%E6%A8%A1%E6%9D%BF.xlsx?Expires=4901052576&OSSAccessKeyId=LTAIhP07XE91RzLJ&Signature=yJy80G5%2Bn%2BNKWX6yDb3CwzHHJ2A%3D&response-content-disposition=attachment%3Bfilename%3D%E7%88%86%E5%A5%BD%E4%BB%B7%E5%AF%BC%E5%85%A5%E5%95%86%E5%93%81%E7%BB%84%E6%A8%A1%E6%9D%BF.xlsx',
              });
            }}
          >
            追加商品组
          </Button>
          <Button
            onClick={() => {
              setChangeUPCModalVisible(true);
              setUploadFileOperator({
                type: "delete",
                url: 'https://bwm-private-b.oss-cn-hangzhou.aliyuncs.com/mkt/%E7%88%86%E5%A5%BD%E4%BB%B7%E5%88%A0%E9%99%A4%E5%95%86%E5%93%81%E7%BB%84%E6%A8%A1%E6%9D%BF.xlsx?Expires=4901052784&OSSAccessKeyId=LTAIhP07XE91RzLJ&Signature=QCTsxuKfI2CSpGBxk3Ja6TGvMpg%3D&response-content-disposition=attachment%3Bfilename%3D%E7%88%86%E5%A5%BD%E4%BB%B7%E5%88%A0%E9%99%A4%E5%95%86%E5%93%81%E7%BB%84%E6%A8%A1%E6%9D%BF.xlsx',
              });
            }}
          >
            删除商品组
          </Button>
        </div>
      )}
    </div>
  );
};

export default CommodityRule;
