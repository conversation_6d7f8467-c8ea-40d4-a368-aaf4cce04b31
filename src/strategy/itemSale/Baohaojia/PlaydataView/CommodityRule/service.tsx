import { alscClient } from "api";

export class DefaultChangeUpcService {
  async checkForbidden() {
    const res = await alscClient.fetch({
      apiKey:
        "ele-newretail-investment.NewretailInvestmentForbiddenWriteYundingService.isKunlunForbiddenWrite",
    });
    return res?.data;
  }

  async uploadInviteUpc(param: {
    activityId: number | string;
    uploadFilePath: string;
    uploadFileOperatorType: string;
  }) {
    const res = await alscClient.fetch({
      apiKey:
        param?.uploadFileOperatorType === "add"
          ? "ele-newretail-investment.NewretailInvestmentActivityInviteYundingService.importCommodityGroup"
          : "ele-newretail-investment.NewretailInvestmentActivityInviteYundingService.deleteCommodityGroup",
      params: [{
        activityId: param.activityId,
        uploadFilePath: param.uploadFilePath,
      }],
    });
    return res;
  }

  async exportInviteUpc(activityId: number | string) {
    const res = await alscClient.fetch({
      apiKey:
        "ele-newretail-investment.NewretailInvestmentActivityInviteYundingService.exportInviteUpc",
      params: [activityId],
    });
    return res;
  }

  async checkPermission(
    activityId: string | number,
    action?: number
  ): Promise<{ accessible: boolean; redirect: string }> {
    const res = await alscClient.fetch({
      apiKey:
        "ele-newretail-investment.NewretailInvestmentActivityYundingService.checkActivityOperateAuthorization",
      params: [activityId, action],
    });
    return { accessible: res.data, redirect: "" };
  }
}
