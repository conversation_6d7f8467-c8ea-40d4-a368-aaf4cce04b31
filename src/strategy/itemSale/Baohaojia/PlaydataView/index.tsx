import { Button, Message } from "@alifd/next";
import { useEffect, useMemo, useState } from "react";

import { BudgetPickerDetail } from "@alife/carbon-biz-kunlun-zs/lib/components/BudgetPicker/BudgetPicker";

import * as api from "api";
import { transformViewJson2Playdata } from "../translator";
import { BaohaojiaViewJson } from "../types";

import { DeliveryChannelView } from "components/formControls/DeliveryChannelSelect";
import { DeliveryTypeView } from "components/formControls/DeliveryTypeSelect";
import { UserScopeView } from "components/formControls/UserScopeSelect";
import CommodityRule from "./CommodityRule";

const Field: React.FC<any> = ({ label, children, alignCenter = true }) => {
  return (
    <div
      className="zs-act-field"
      style={{ alignItems: alignCenter ? "center" : "flex-start" }}
    >
      <label>{label}</label>
      <div>{children}</div>
    </div>
  );
};

interface Props {
  env: "daily" | "pre" | "prod";
  activityInfo: {
    activityId: number;
  };
}

export default function PlaydataView({ env, activityInfo }: Props) {
  const [detail, setDetail] = useState<any>();
  useEffect(() => {
    api.subActivity.getDetail(activityInfo.activityId).then((res) => {
      setDetail(res.data);
    });
  }, [activityInfo.activityId]);

  const playdata = useMemo(() => {
    try {
      const viewJsonObj = JSON.parse(detail?.viewJson) as BaohaojiaViewJson;
      return transformViewJson2Playdata(viewJsonObj);
    } catch (e) {
      console.error(e);
      return null;
    }
  }, [detail?.viewJson]);

  function handleExport() {
    api.alscClient
      .fetch({
        apiKey:
          "ele-newretail-investment.NewretailInvestmentActivityInviteYundingService.exportInviteElement",
        params: [activityInfo.activityId],
      })
      .then((res) => {
        if (!res?.success) {
          Message.error(res.errorMessage);
          return;
        }
        if (res?.data) {
          window.open(res.data, "_blank");
        }
      });
  }

  if (!playdata) {
    return null;
  }

  return (
    <div className="zs-act-info">
      <div className="zs-act-area">
        <div className="zs-act-area-title" style={{ marginBottom: 30 }}>
          玩法设置
        </div>
        <div className="zs-act-section">
          <Field label="限制人群:" name="userScope">
            <UserScopeView value={playdata.userScope!} />
          </Field>
          <Field label="用户购买限定:">
            每单限购{playdata.orderCountLimit}件
          </Field>
          <Field label="投放渠道:">
            <DeliveryChannelView value={playdata.deliveryChannel} />
          </Field>
          <Field label="生效渠道:">
            <DeliveryTypeView value={playdata.deliveryType} />
          </Field>
          <Field label="是否0起送免运费:">
            {playdata.deliveryFeeDiscount ? "是" : "否"}
          </Field>
          <Field label="运费补贴上限:">
            {playdata.deliveryFeePlatformSubsidyMax
              ? `${playdata.deliveryFeePlatformSubsidyMax}元`
              : "-"}
          </Field>
          <Field label="预算:">
            {playdata.budgetInfo ? (
              <BudgetPickerDetail
                env={{
                  env,
                  view: "detail",
                  detailContext: {
                    activityId: activityInfo.activityId,
                  },
                }}
                value={playdata.budgetInfo}
              />
            ) : (
              "-"
            )}
          </Field>
          <Field label="商品设置:" alignCenter={false}>
            <Button type="primary" text onClick={() => handleExport()}>
              下载规则
            </Button>
            {/* 爆好价新活动才会展示增加删除商品组 */}
            {detail?.investmentActivityDetailDTO?.activitySignUpcType ===
              "qualityProduct" && (
              <CommodityRule
                investmentSchema={
                  detail.investmentActivityDetailDTO.investmentSchema
                }
                activityType={detail.investmentActivityDetailDTO.activityType}
                activityId={activityInfo.activityId}
                qualityProductPlanId={
                  detail.investmentActivityDetailDTO.qualityProductPlanId
                }
              />
            )}
          </Field>
        </div>
      </div>
    </div>
  );
}
