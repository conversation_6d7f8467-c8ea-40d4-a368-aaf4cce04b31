import { Message } from "@alifd/next";
import moment from "moment";

import * as api from "../../api";
import { isValueSet, parseJsonSafe } from "../../common";
import loadingDialog from "../../components/LoadingDialog";
import {
  ActivitySignType,
  ActivityTypeEnum,
  InvestmentSchemaEnum,
  InvestmentTypeEnum,
} from "../../constants";
import {
  ActivityStrategy,
  CreationContext,
  InvestmentActivityDetailDTO,
} from "../../models";
import {
  CableBase,
  resolveCreateActivityCommonData,
  resolveTimeRangeBatchFromMarketplayValue,
  resolveTimeRangeBatchFromPeriodDTOList,
} from "../base";
import {
  gotoLegacyActivityDetailByDefault,
  resolveCommodityAuditProps,
  checkValidateCopyActivityResult,
  checkAndUploadRichDescription,
  resolveRichDescription,
  checkNextTimeForAlpha,
  checkNextCommoditySignupRuleForAlpha,
  checkMktRulesForMultiBusinessFormAndConfirmIfNeed,
  checkMarketRules,
} from "../mixins";
import { merchantMemberAndMultiCommodityCable } from "./merchantMemberAndMultiCommodityCable";
import { cable as baohaojiaCable } from "./Baohaojia/cable";
import {
  transformFormData2ViewJson,
  transformViewJson2FormDataForMultiCommodityLikeActivity,
  transformViewJson2FormData,
} from "./helper";
import { COMMODITY_REMOVE_CONDITION_ENUM, ItemSalePlaydata, ItemSalePlaydataForDetail } from "./types";
import { DefaultService as QualityProductPlanService } from "./Baohaojia/QualityProductPlan";
import { CommodityAuditTypeEnum } from "@alife/carbon-biz-kunlun-zs/lib/constants";

const debug = require("debug")("zs:strategy:ItemSale");

interface ItemSalePlaydataMerchantMember {
  activityLimitRule: {
    userDayCountLimit?: number;
    userTotalCountLimit?: number;
  };
  activityMarketingInfo: {
    budgetId?: string;
    budgetName?: string;
    deliveryChannel: number;
    deliveryType: number;
    discountPriceRangeType: number;
    invitedUpcHasPlatformSubsidy: boolean;
    cityDistinguishType: number;
    userScope: number;
  };
  activityType: number;
  commodityListFilePath: string;
  period: {
    closeTime: string;
    openTime: string;
    weekday: string;
  };
}

const ItemSale: ActivityStrategy = {
  activityType: ActivityTypeEnum.SKU_TE_BASE,
  async generateExtraInfoForCreationContext(
    creationContext: CreationContext,
    query: Record<string, string | undefined>
  ) {
    if (
      creationContext.investmentSchema ===
      InvestmentSchemaEnum.SUPER_COMMODITY_GOOD_PRICE
    ) {
      const service = new QualityProductPlanService();
      const res = await service.getPlan({
        planId: +query.qualityProductPlanId!,
      });
      return Object.freeze({
        qualityProductPlan: res.data,
      });
    } else {
      return {};
    }
  },
  cable: (options: {
    investmentSchema: number;
    investmentType: number;
    activitySignType?: ActivitySignType;
    extra?: CreationContext["extra"];
  }) => {
    const { investmentSchema, investmentType } = options;
    if (
      investmentSchema === InvestmentSchemaEnum.MERCHANTS_MEMBER ||
      investmentSchema === InvestmentSchemaEnum.MUTI_COMMDITY ||
      investmentSchema === InvestmentSchemaEnum.WEEKEND_SPLIT_PURCHASE
    ) {
      // 多商品，商家会员和周末一分购
      return merchantMemberAndMultiCommodityCable;
    } else if (
      investmentSchema === InvestmentSchemaEnum.SUPER_COMMODITY_GOOD_PRICE
    ) {
      // 爆好价
      return baohaojiaCable(options);
    } else {
      return {
        ...CableBase,
        workspace: "ExukovQrae6xWn2rt4NgG",
        model: "CedgjunI3F9Nsg4WhCaC2",
        async checkNext(checkNextContext, creationContext, history) {
          const { step, dataStore } = checkNextContext;
          if (!dataStore._session) {
            dataStore._session = {};
          }
          if (step.name === "baseinfo") {
            if (dataStore.data.baseinfo?.richDescription) {
              const { hide } = loadingDialog.show({
                message: "活动信息检查中...",
              });
              try {
                const error = await checkAndUploadRichDescription({
                  richDescription: dataStore.data.baseinfo?.richDescription,
                  session: dataStore._session,
                });
                if (error) {
                  return error;
                }
              } finally {
                hide();
              }
            }
          }
          if (step.name === "baseinfo") {
            // 重置 auditUpgradeForMultiMktRule，避免重新回到第一步重新选择业务线和业态的情况
            dataStore._session.auditUpgradeForMultiMktRule = undefined;
            // 校验是否跨业态，并且返回弹窗点击结果
            const { conflicted, userConfirm } =
              await checkMktRulesForMultiBusinessFormAndConfirmIfNeed(
                creationContext,
                dataStore.data.baseinfo
              );
            if (!conflicted) {
              // 没有错误信息，不需要拦截进入下一步
              return;
            }
            if (userConfirm) {
              // 有错误，并且用户二次确认了，确认需提交审批
              dataStore._session.auditUpgradeForMultiMktRule = true;
              // 不需要拦截进入下一步
              return;
            } else {
              // 有错误，单用户未二次确认，页面停留在当前步骤
              return { silent: true };
            }
          }
          if (step.name === "baseinfo") {
            const timeError = await checkNextTimeForAlpha(
              checkNextContext,
              creationContext
            );
            if (timeError) {
              return timeError;
            }
          }
          if (step.name === "scope") {
            const commoditySignupRuleError =
              await checkNextCommoditySignupRuleForAlpha(
                checkNextContext,
                creationContext
              );
            if (commoditySignupRuleError) {
              return commoditySignupRuleError;
            }
          }

          if (step.name === "stock") {
            const { baseinfo, stock, scope } = dataStore.data;
            const commonData = resolveCreateActivityCommonData(
              ActivityTypeEnum.SKU_TE_BASE,
              checkNextContext,
              creationContext
            );
            const viewJson = transformFormData2ViewJson({
              baseinfo,
              formData: commonData.dataMap?.resource?.data,
              investmentSchema: creationContext.investmentSchema,
            });
            delete commonData.dataMap;

            const commodityAuditProps: any = resolveCommodityAuditProps(scope);

            let creativeMaterialRules = undefined;
            if (scope.creatives?.enabled && scope.creatives?.creatives.length) {
              creativeMaterialRules = scope.creatives.creatives;
            }

            const payload: any = {
              ...commonData,

              // 自动审核规则
              ...commodityAuditProps,

              // 降套补 商品清退规则
              commodityRemoveCondition: {
                removeCondition: scope.removeCondition,
              },

              auditMemberList:
                baseinfo.auditMemberList?.length > 0
                  ? baseinfo.auditMemberList
                  : null,

              // 报名规则
              reviewRuleId: scope.rule?.ruleId,
              reviewRuleName: scope.rule?.name,

              // 库存设置
              stockType: stock.stockType,
              stockMin: stock.range.start,
              stockMax: stock.range.end,
              requestId: stock.stockType === 2 ? stock.requestId : undefined,

              creativeMaterialRules,

              viewJson: JSON.stringify(viewJson),
            };

            const isModifyPriceEnabled =
              investmentType === InvestmentTypeEnum.RICHANG &&
              (investmentSchema === InvestmentSchemaEnum.RICHANG ||
                investmentSchema === InvestmentSchemaEnum.BAIYI_BUTIE);
            if (isModifyPriceEnabled) {
              if (
                baseinfo.modifyRules &&
                baseinfo.modifyRules.indexOf(
                  "merchant_modify_activity_price"
                ) !== -1
              ) {
                payload.activityModifyRules = [
                  {
                    activityModifyRuleType: "merchant_modify_activity_price",
                    isSupport: true,
                  },
                ];
              }
            }

            if (creationContext.investmentType === InvestmentTypeEnum.LANMU) {
              // 栏目招商
              payload.signUpMinCycleTime = baseinfo.cycleTime.min;
              payload.signUpMaxCycleTime = baseinfo.cycleTime.max;
            }

            const { hide, updateMessage } = loadingDialog.show({
              message: creationContext.copy
                ? "活动信息校验中..."
                : "活动创建中...",
            });

            if (dataStore._session?.ossURL) {
              const ossURL = dataStore._session?.ossURL;
              payload.description = baseinfo.richDescription.text;
              payload.attributes = {
                ...payload.attributes,
                hasRichDescription: "on",
                richDescriptionURL: ossURL,
              };
            }

            try {
              const {
                interrupt,
                auditUpgradeForCheckMktRuleUnPass,
                auditUpgradeForMultiMktRule,
              } = await checkMarketRules(
                dataStore,
                creationContext,
                JSON.stringify(viewJson)
              );
              if (interrupt) {
                return { silent: true };
              }

              payload.auditUpgradeForCheckMktRuleUnPass =
                auditUpgradeForCheckMktRuleUnPass;
              payload.auditUpgradeForMultiMktRule = auditUpgradeForMultiMktRule;  
              
              if (creationContext.copy) {
                const res = await api.subActivity.validateCopyActivityData({
                  copyActivityId: creationContext.activity?.activityId,
                  ...payload,
                });
                const copyError = checkValidateCopyActivityResult(res);
                if (copyError) {
                  return copyError;
                }
                updateMessage("活动创建中...");
              }
              const res = await api.subActivity.create(payload);
              if (res.success) {
                Message.success("创建成功");
                if (creationContext.activitySceneId) {
                  history.push(
                    "/sub-activity/" +
                      res.data +
                      "?activitySceneId=" +
                      creationContext.activitySceneId +
                      "&activityPlanId=" +
                      creationContext.activityPlanId
                  );
                } else {
                  history.push("/sub-activity/" + res.data);
                }
              } else {
                return { message: res.errorMessage };
              }
            } catch (e: any) {
              return { message: e?.message || "系统异常" };
            } finally {
              hide();
            }
          }
        },
      };
    }
  },

  hasAgentSubsidy() {
    return false;
  },
  isSupportAgent: false,
  isOfflineEnabled: function (state: number): boolean {
    // 活动状态: 1:未开始 2:活动中 3:活动暂停 4:活动取消 5:活动结束 8: 预算熔断
    return [1, 2, 3, 8].includes(state);
  },
  isCopyEnabled({ investmentSchema, investmentType }) {
    return (
      [
        InvestmentSchemaEnum.RICHANG,
        InvestmentSchemaEnum.BAIYI_BUTIE,
        InvestmentSchemaEnum.SHILING_HAOHUO,
      ].includes(investmentSchema) &&
      [InvestmentTypeEnum.RICHANG, InvestmentTypeEnum.LANMU].includes(
        investmentType
      )
    );
  },
    async resolvePartialCreateData(createContext) {
      if (
        createContext.investmentSchema === InvestmentSchemaEnum.SUPER_COMMODITY_GOOD_PRICE
      ) {
        return {
          scope: {
              commodityAuditType: CommodityAuditTypeEnum.AUTO,
              removeCondition: [
                COMMODITY_REMOVE_CONDITION_ENUM.COMMODITY_GUIDE_PRICE,
                COMMODITY_REMOVE_CONDITION_ENUM.SHOP_RULE,
                COMMODITY_REMOVE_CONDITION_ENUM.SPU_CSPU_CPV,
              ],
          },
        };
      }
    },
  async resolveCopyData(creationContext) {
    debug("resolveCopyData creationContext:", creationContext);
    let weekday: string;
    let timeRangeBatch: any;

    // 获取富文本描述信息
    const richDescription = await resolveRichDescription(creationContext);

    let playdata;
    if (creationContext.activity?.marketPlay?.instance) {
      weekday =
        creationContext.activity?.marketPlay.instance.fields.weekday || "";
      timeRangeBatch = resolveTimeRangeBatchFromMarketplayValue(
        creationContext.activity?.marketPlay.instance.fields.timeRange
      );
      // 基于组件创建的活动，依然通过组件的方式设置复制的信息
      // 虽然是通过组件的方式设置复制的信息，但是创建是统一使用非组件的链路
      playdata = {
        ...creationContext.activity?.marketPlay.instance,
        fields: {
          ...creationContext.activity?.marketPlay.instance.fields,
          budget: undefined, // 复制活动要清空预算
        },
      };
    } else {
      const viewDtoToCopy: ItemSalePlaydataForDetail = parseJsonSafe(
        creationContext.activity!.viewJson
      );
      weekday = viewDtoToCopy.weekday;
      timeRangeBatch = resolveTimeRangeBatchFromPeriodDTOList(
        viewDtoToCopy.periodDTOList
      );
      const formData = transformViewJson2FormData({
        viewJson: creationContext.activity!.viewJson,
        baseinfo: {
          name: creationContext.activity!.name,
          beginTime: creationContext.activity!.beginTime,
          endTime: creationContext.activity!.endTime,
          createdUserId: creationContext.activity!.createdUserId,
          createdUserName: creationContext.activity!.createdUserName,
        },
        investmentSchema: creationContext.investmentSchema,
        isCopy: true,
      });
      playdata = {
        fields: {
          ...formData,
          budget: undefined,
        },
      };
    }

    const ret = {
      baseinfo: {
        name: creationContext.activity?.name,
        richDescription,
        remark: creationContext.activity?.remark,
        activityTime: {
          start: moment(creationContext.activity?.beginTime),
          end: moment(creationContext.activity?.endTime),
          weeks: weekday
            .split(",")
            .filter(Boolean)
            .map((d: string) => +d),
          batchRange: timeRangeBatch,
        },
        auditMemberList: creationContext.activity?.auditMemberList || [],
        signupTime: {
          start: moment(creationContext.activity?.signUpStartTime),
          end: moment(creationContext.activity?.signUpEndTime),
        },
        cycleTime:
          creationContext.investmentType === InvestmentTypeEnum.LANMU
            ? {
                min: creationContext.activity?.signUpMinCycleTime,
                max: creationContext.activity?.signUpMaxCycleTime,
              }
            : undefined,
        allowCancel: creationContext.activity?.allowCancel,
        signUpShopType: creationContext.activity?.signUpShopType,
      },
      scope: {
        // 复制活动，门店池和报名规则等其他配置均清空
        commodityAuditType: 1,
      },
      playdata,
      stock: {
        stockType: creationContext.activity?.stockType,
        range: {},
      },
    };
    debug("resolveCopyData result:", ret);
    return ret;
  },
  isDingtalkLinkEnabled: () => true,
  hasAuditMemberList: () => true,
  isSupportModifyStock: () => false,
  isItemAuditStatsVisible: true,
  isDetailInviteStatsOnMainActivityTableVisible: true,
  renderPlayDataInfoForTableView: function (): JSX.Element {
    return <></>;
  },
  gotoLegacyDetail: function (
    act: InvestmentActivityDetailDTO,
    scene: "lookup" | "examine"
  ): void {
    gotoLegacyActivityDetailByDefault(act, scene);
  },
  renderActivityRuleForTable(
    viewJson: any,
    option?: { investmentSchema?: number }
  ) {
    if (
      option &&
      [
        InvestmentSchemaEnum.MERCHANTS_MEMBER,
        InvestmentSchemaEnum.MUTI_COMMDITY,
        InvestmentSchemaEnum.WEEKEND_SPLIT_PURCHASE,
      ].includes(option?.investmentSchema!)
    ) {
      try {
        const data: ItemSalePlaydataMerchantMember = JSON.parse(viewJson);
        let result = [];
        if (data.activityMarketingInfo?.discountPriceRangeType === 2) {
          // 价格区间
          let text =
            data.activityMarketingInfo?.cityDistinguishType === 1
              ? "活动价区间，活动价区分城市"
              : "活动价区间，活动价不区分城市";
          result.push(text);
        } else {
          let texts =
            data.activityMarketingInfo?.cityDistinguishType === 1
              ? "活动价固定，活动价区分城市"
              : "活动价固定，活动价不区分城市";
          result.push(texts);
        }
        return (
          <>
            {result.map((r: any, i: number) => (
              <div key={i}>{r}</div>
            ))}
          </>
        );
      } catch (e) {
        console.error(e);
        return <></>;
      }
    }

    if (
      InvestmentSchemaEnum.SUPER_COMMODITY_GOOD_PRICE ===
      option?.investmentSchema
    ) {
      return <>指定商品固定活动价</>;
    }

    try {
      const data: ItemSalePlaydata = JSON.parse(viewJson);
      let result = [];
      if (
        isValueSet(data.activityRule.discountMin) &&
        isValueSet(data.activityRule.discountMax)
      ) {
        // 区间招商
        result.push(
          `活动价格区间: ${data.activityRule!.discountMin! / 100} ~ ${
            data.activityRule!.discountMax! / 100
          }元`
        );
        result.push(
          `限制最低折扣: ${
            data.activityRule!.minDiscountPercentForItemSalePrice! / 100
          }`
        );
        result.push(
          `平台补贴上限: ${data.activityRule.platformSubsidyMax / 100}元`
        );
      } else if (isValueSet(data.activityRule.discount)) {
        // 固定招商
        result.push(`价格固定: 锁${data.activityRule!.discount! / 100}元`);
        result.push(
          `平台补贴上限: ${data.activityRule.platformSubsidyMax / 100}元`
        );
      }
      return (
        <>
          {result.map((r: any, i: number) => (
            <div key={i}>{r}</div>
          ))}
        </>
      );
    } catch (e) {
      console.error(e);
      return <></>;
    }
  },
  renderActivityRuleForTableLegacy: function () {
    return <></>;
  },
  marketPlayMapper: async function (
    viewJson: string,
    baseInfo: any,
    option: { investmentSchema: any; marketPlay: any }
  ) {
    if (
      ![
        InvestmentSchemaEnum.MERCHANTS_MEMBER,
        InvestmentSchemaEnum.MUTI_COMMDITY,
        InvestmentSchemaEnum.WEEKEND_SPLIT_PURCHASE,
      ].includes(option.investmentSchema)
    ) {
      if (option.marketPlay) {
        // 活动信息中包含 marketPlay 说明是基于组件链路创建的老活动，直接返回即可
        return option.marketPlay;
      }
      const fields =
        transformViewJson2FormData({
          baseinfo: baseInfo,
          viewJson,
          investmentSchema: option.investmentSchema,
        }) || {};
      const model = await api.subActivity.getComponentDataModel(
        "6v4M8woywiK9aecPk9yWLh",
        "oaBHOB3aoP82IEBUq50yM"
      );
      const marketPlay = { instance: { fields }, model };
      return marketPlay;
    } else {
      const fields =
        transformViewJson2FormDataForMultiCommodityLikeActivity(
          viewJson,
          baseInfo
        ) || {};
      const model = await api.subActivity.getComponentDataModel(
        "ExukovQrae6xWn2rt4NgG",
        "7As3D07hbif6eMmy6Dx2jX"
      );
      const marketPlay = { instance: { fields }, model };
      return marketPlay;
    }
  },
};

export default ItemSale;
