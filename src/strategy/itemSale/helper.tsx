import BigNumber from "bignumber.js";
import { parseJsonSafe } from "../../common";
import { ActivityTypeEnum, InvestmentSchemaEnum } from "../../constants";
import { ItemSalePlaydata, ItemSalePlaydataForDetail } from "./types";
import moment from "moment";

function isValueSet(v: any) {
  return v !== "" && v !== null && v !== undefined;
}

interface SaleRule {
  saleFixed?: { price: number; elemeSubsidy: number };
  saleRange?: {
    minPrice: number;
    maxPrice: number;
    minDiscount: number;
    elemeSubsidy: number;
  };
}

interface Budget {
  budgetId?: string;
  budgetName?: string;
}

interface LimitValue {
  dayLimit?: { value?: number };
  totalLimit?: { value?: number };
}

interface CommodityRules {
  budgetId?: string;
  budgetName?: string;
  checkExcel: {
    extInfoMap: {
      invitedUpcHasPlatformSubsidy: string;
    };
  };
  discountPriceRangeType: number;
  distinguishCity: number;
}

interface ItemSaleBaseInfo {
  platformSubsidyRateUpperLimit?: number;
  activityTime: {
    weeks: number[];
    batchRange: {
      type: "all" | "custom";
      list: { start: string; end: string }[];
    };
  };
}

interface ItemSaleFormData {
  name?: string; // 详情展示才需要，创建流程不需要
  creator: string;
  saleRule: string;
  userScope: string;
  userLimit: string;
  launchChannel: string;
  effectiveChannel: string;
  budget?: string;
  platformSubsidyRateUpperLimit?: number;
  [prop: string]: unknown;
}

export function transformFormData2ViewJson(options: {
  baseinfo: ItemSaleBaseInfo;
  formData: ItemSaleFormData;
  investmentSchema: InvestmentSchemaEnum;
}): ItemSalePlaydata {
  const saleRule: SaleRule = parseJsonSafe(options.formData.saleRule);
  const budget: Budget = options.formData.budget
    ? parseJsonSafe(options.formData.budget)
    : null;
  const userLimit: LimitValue = parseJsonSafe(options.formData.userLimit);
  const isRangeActivity = !isValueSet(saleRule.saleFixed?.price);

  return {
    activityType: ActivityTypeEnum.SKU_TE_BASE,
    activityLimitRule: {
      userTotalCountLimit: isValueSet(userLimit.totalLimit?.value)
        ? +userLimit.totalLimit?.value!
        : undefined,
      userDayCountLimit: isValueSet(userLimit.dayLimit?.value)
        ? +userLimit.dayLimit?.value!
        : undefined,
    },
    activityMarketingInfo: {
      budgetId: budget?.budgetId,
      budgetName: budget?.budgetName,
      deliveryChannel: +options.formData.launchChannel,
      deliveryType: +options.formData.effectiveChannel,
      userScope: +options.formData.userScope,
      trafficChannel: options?.investmentSchema === InvestmentSchemaEnum.FINAL_DEAL_PRICE
        ? options.formData?.trafficChannel as number : undefined,
      deliveryBreakStartFee: options?.investmentSchema === InvestmentSchemaEnum.FINAL_DEAL_PRICE
        ? 1 : undefined,
    },
    activityRule: {
      platformSubsidyRateUpperLimit: options.baseinfo?.platformSubsidyRateUpperLimit,
      discountType: 3, // 前端写死成 3
      discount: !isRangeActivity ? saleRule.saleFixed?.price : undefined,
      discountMin: isRangeActivity ? saleRule.saleRange?.minPrice : undefined,
      discountMax: isRangeActivity ? saleRule.saleRange?.maxPrice : undefined,
      minDiscountPercentForItemSalePrice: isRangeActivity
        ? new BigNumber(saleRule.saleRange?.minDiscount!)
            .multipliedBy(100)
            .toNumber()
        : undefined,
      platformSubsidyMax: isRangeActivity
        ? saleRule.saleRange?.elemeSubsidy!
        : saleRule.saleFixed?.elemeSubsidy!,
    },
    weekday: options.baseinfo.activityTime.weeks.join(","),
    period: options.baseinfo.activityTime.batchRange.list.map((item) => {
      return {
        openTime: item.start.slice(0, "HH:mm".length) + ":00",
        closeTime: item.end.slice(0, "HH:mm".length) + ":59",
      };
    }),
  };
}

export function transformViewJson2FormData(options: {
  viewJson: string;
  baseinfo: {
    // beginTime 和 endTime 在 options.isCoopy 为 true 的时候需要，默认不需要
    beginTime?: string; // 数据示例： "2023-07-17T16:00:00.000Z"
    endTime?: string; // 数据示例： "2023-07-17T16:00:00.000Z"

    name: string;
    createdUserId: string;
    createdUserName: string;
    platformSubsidyRateUpperLimit?: number;
  };
  investmentSchema: InvestmentSchemaEnum;
  isCopy?: boolean; // 是否是活动复制，默认为 false
}): ItemSaleFormData | null {
  try {
    const playdata: ItemSalePlaydataForDetail = JSON.parse(options.viewJson);
    const creator = {
      id: options.baseinfo.createdUserId,
      name: options.baseinfo.createdUserName,
    };
    let saleRule: SaleRule;
    if (isValueSet(playdata.activityRule.discount)) {
      saleRule = {
        saleFixed: {
          price: playdata.activityRule.discount!,
          elemeSubsidy: playdata.activityRule.platformSubsidyMax,
        },
      };
    } else {
      saleRule = {
        saleRange: {
          minPrice: playdata.activityRule.discountMin!,
          maxPrice: playdata.activityRule.discountMax!,
          minDiscount: new BigNumber(
            playdata.activityRule.minDiscountPercentForItemSalePrice!
          )
            .dividedBy(100)
            .toNumber(),
          elemeSubsidy: playdata.activityRule.platformSubsidyMax,
        },
      };
    }
    let userLimit: LimitValue = {};
    if (isValueSet(playdata.activityLimitRule.userDayCountLimit)) {
      userLimit.dayLimit = {
        value: playdata.activityLimitRule.userDayCountLimit,
      };
    }
    if (isValueSet(playdata.activityLimitRule.userTotalCountLimit)) {
      userLimit.totalLimit = {
        value: playdata.activityLimitRule.userTotalCountLimit,
      };
    }
    let budget: Budget | null = null;
    if (
      isValueSet(playdata.activityMarketingInfo.budgetId) &&
      isValueSet(playdata.activityMarketingInfo.budgetName)
    ) {
      budget = {
        budgetId: playdata.activityMarketingInfo.budgetId,
        budgetName: playdata.activityMarketingInfo.budgetName,
      };
    }
    const ret: ItemSaleFormData = {
      platformSubsidyRateUpperLimit: playdata.activityRule.platformSubsidyRateUpperLimit,
      name: options.baseinfo.name,
      creator: JSON.stringify(creator),
      saleRule: JSON.stringify(saleRule),
      userScope: `${playdata.activityMarketingInfo.userScope}`,
      userLimit: JSON.stringify(userLimit),
      launchChannel: `${playdata.activityMarketingInfo.deliveryChannel}`,
      effectiveChannel: `${playdata.activityMarketingInfo.deliveryType}`,
      budget: budget ? JSON.stringify(budget) : undefined,
      trafficChannel: `${playdata?.activityMarketingInfo?.trafficChannel}`,
    };

    if (options.isCopy) {
      // 复制场景
      // 由于目前特价活动去组件化，目前只修改了创建链路，创建流程基本信息和玩法表单之间的数据同步逻辑还没哟精简掉
      // 所以，保险期间，复制场景下，要最大程度地，与基于组件链路产生的活动的复制数据保持一致
      ret.dateRange = JSON.stringify({
        start: moment(options.baseinfo.beginTime).format("YYYY-MM-DD HH:mm:ss"),
        end: moment(options.baseinfo.endTime).format("YYYY-MM-DD HH:mm:ss"),
      });
      ret.weekday = playdata.weekday;
      ret.timeRange = JSON.stringify(
        playdata.periodDTOList.map((item) => {
          return { start: item.openTime, end: item.closeTime };
        })
      );
    }

    return ret;
  } catch (e) {
    return null;
  }
}

// 多商品、商家会员、周末一分购模版通用转换逻辑函数，这三个模版玩法字段类似
export function transformFormData2ViewJsonForMultiCommodityLikeActivity({
  baseinfo,
  commodityRules,
  playdata,
  investmentSchema,
}: {
  baseinfo: ItemSaleBaseInfo;
  commodityRules: CommodityRules;
  playdata: any;
  investmentSchema: InvestmentSchemaEnum;
}) {
  return {
    activityRule: {
      platformSubsidyRateUpperLimit: baseinfo?.platformSubsidyRateUpperLimit,
    },
    activityLimitRule: {
      userDayCountLimit: playdata.data.userLimit?.dayLimit?.value,
      userTotalCountLimit: playdata.data.userLimit?.totalLimit?.value,
    },
    activityMarketingInfo: {
      budgetId: commodityRules.budgetId,
      budgetName: commodityRules.budgetName,
      deliveryType: playdata.data.deliveryType,
      deliveryChannel: playdata.data.launchChannel || 0, // 预订团新增字段，默认传 0： 通用渠道，
      userScope: playdata.data.userScope,
      invitedUpcHasPlatformSubsidy:
        commodityRules.checkExcel.extInfoMap.invitedUpcHasPlatformSubsidy,
      discountPriceRangeType: commodityRules.discountPriceRangeType,
      cityDistinguishType: commodityRules.distinguishCity,
    },
    activityType: ActivityTypeEnum.SKU_TE_BASE,
    period:
      investmentSchema !== InvestmentSchemaEnum.WEEKEND_SPLIT_PURCHASE
        ? (baseinfo?.activityTime.batchRange.list || []).map((r: any) => {
            return {
              openTime: r.start + ":00",
              closeTime: r.end + ":59",
              weekday: (baseinfo.activityTime.weeks || []).join(","),
            };
          })
        : (playdata.data.weeklyTimeRangeSetting || []).map((time: any) => {
            return {
              closeTime: time.timeRange.closeTime + ":59",
              openTime: time.timeRange.openTime + ":00",
              weekday: time.day,
            };
          }),
  };
}

// 多商品、商家会员、周末一分购模版通用转换逻辑函数，这三个模版玩法字段类似
export function transformViewJson2FormDataForMultiCommodityLikeActivity(
  viewJson: string,
  baseInfo: {
    createdUserId: string;
    createdUserName: string;
  },
) {
  const viewDto = JSON.parse(viewJson) || {};
  const { activityLimitRule = {}, activityMarketingInfo = {} } = viewDto || {};
  let userLimit = {};
  let userScope = ""; // 用户类型
  let launchChannel = ""; // 投放渠道
  let deliveryType = ""; // 生效渠道
  let budget = {}; //  预算
  let creator = {}; // 创建人名称、创建人id

  if (
    isValueSet(baseInfo.createdUserId) &&
    isValueSet(baseInfo.createdUserName)
  ) {
    creator = {
      name: `${baseInfo.createdUserName}`,
      id: baseInfo.createdUserId,
    };
  }
  if (isValueSet(activityLimitRule?.userDayCountLimit)) {
    userLimit = {
      dayLimit: {
        value: activityLimitRule.userDayCountLimit + "",
      },
    };
  }
  if (isValueSet(activityLimitRule?.userTotalCountLimit)) {
    userLimit = {
      ...userLimit,
      totalLimit: {
        value: activityLimitRule.userTotalCountLimit + "",
      },
    };
  }
  if (
    isValueSet(activityLimitRule?.userTotalCountLimit) &&
    isValueSet(activityLimitRule?.userDayCountLimit)
  ) {
    userLimit = {
      dayLimit: {
        value: activityLimitRule.userDayCountLimit + "",
      },
      totalLimit: {
        value: activityLimitRule.userTotalCountLimit + "",
      },
    };
  }
  if (isValueSet(activityMarketingInfo?.deliveryChannel)) {
    launchChannel = activityMarketingInfo.deliveryChannel + "";
  }
  if (isValueSet(activityMarketingInfo?.deliveryType)) {
    deliveryType = activityMarketingInfo.deliveryType + "";
  }

  if (isValueSet(activityMarketingInfo?.userScope)) {
    userScope = activityMarketingInfo.userScope + "";
  }
  if (isValueSet(activityMarketingInfo?.budgetId)) {
    budget = {
      budgetName: activityMarketingInfo?.budgetName,
      budgetId: activityMarketingInfo.budgetId + "",
    };
  }

  return {
    creator: JSON.stringify(creator),
    userLimit: JSON.stringify(userLimit),
    userScope,
    launchChannel: launchChannel,
    deliveryType: deliveryType,
    budget: JSON.stringify(budget),
  };
}
