import { CableBase, resolveCreateActivityCommonData } from "../base";
import { Message } from "@alifd/next";
import * as api from "../../api";
import { checkAndUploadRichDescription, checkMarketRules, checkMktRulesForMultiBusinessFormAndConfirmIfNeed, checkNextCommoditySignupRuleForAlpha, checkNextTimeForAlpha, resolveCommodityAuditProps } from "../mixins";
import { ActivityTypeEnum,InvestmentSchemaEnum } from "../../constants";
import loadingDialog from "../../components/LoadingDialog";
import { CheckNextContext, CreationContext } from "../../models";
import { transformFormData2ViewJsonForMultiCommodityLikeActivity } from "./helper";

export const merchantMemberAndMultiCommodityCable = {
  ...CableBase,
  workspace: "ExukovQrae6xWn2rt4NgG",
  model: "56P1VL6ftVE5z8o74ejfab",
  async checkNext(
    checkNextContext: CheckNextContext,
    creationContext: CreationContext,
    history: any
  ) {
    const { step, dataStore } = checkNextContext;
    if (!dataStore._session) {
      dataStore._session = {};
    }
    if (step.name === "baseinfo") {
      if (dataStore.data.baseinfo?.richDescription) {
        const { hide } = loadingDialog.show({
          message: "活动信息检查中...",
        });
        try {
          const error = await checkAndUploadRichDescription({
            richDescription: dataStore.data.baseinfo?.richDescription,
            session: dataStore._session,
          });
          if (error) {
            return error;
          }
        } finally {
          hide();
        }
      }
    }
    if(step.name === "baseinfo") {
      const timeError = await checkNextTimeForAlpha(checkNextContext, creationContext);
      if(timeError) {
        return timeError;
      }
    }
    if (step.name === "baseinfo") {
      // 重置 auditUpgradeForMultiMktRule，避免重新回到第一步重新选择业务线和业态的情况
      dataStore._session.auditUpgradeForMultiMktRule = undefined;
      // 校验是否跨业态，并且返回弹窗点击结果
      const { conflicted, userConfirm } =
        await checkMktRulesForMultiBusinessFormAndConfirmIfNeed(
          creationContext,
          dataStore.data.baseinfo
        );
      if (!conflicted) {
        // 没有错误信息，不需要拦截进入下一步
        return;
      }
      if (userConfirm) {
        // 有错误，并且用户二次确认了，确认需提交审批
        dataStore._session.auditUpgradeForMultiMktRule = true;
        // 不需要拦截进入下一步
        return;
      } else {
        // 有错误，单用户未二次确认，页面停留在当前步骤
        return { silent: true };
      }
    }
    if(step.name === "scope") {
      const commoditySignupRuleError = await checkNextCommoditySignupRuleForAlpha(checkNextContext, creationContext);
      if(commoditySignupRuleError) {
        return commoditySignupRuleError;
      }
    }
    if (step.name === "commodityRules") {
      const { baseinfo, scope, playdata, commodityRules } = dataStore.data;
      const commonData: any = resolveCreateActivityCommonData(
        ActivityTypeEnum.SKU_TE_BASE,
        checkNextContext,
        creationContext
      );
      delete commonData.dataMap
      const commodityAuditProps: any = resolveCommodityAuditProps(scope);
      const viewJson = transformFormData2ViewJsonForMultiCommodityLikeActivity({
        baseinfo,
        playdata,
        commodityRules,
        investmentSchema: creationContext.investmentSchema,
      })
      const payload: any = {
        ...commonData,

        // 招商范围信息 过滤条件
        merchantMemberCardId: scope.filterCondition?.merchantMemberCardId
          ? scope.filterCondition.merchantMemberCardId
          : null,
        merchantMemberCardName: scope.filterCondition?.merchantMemberCardName
          ? scope.filterCondition.merchantMemberCardName
          : null,

        // 自动审核规则
        ...commodityAuditProps,

        // 降套补 商品清退规则
        commodityRemoveCondition: {
          removeCondition: scope.removeCondition,
        },

        auditMemberList:
          baseinfo.auditMemberList?.length > 0
            ? baseinfo.auditMemberList
            : null,

        // 报名规则
        reviewRuleId: scope.rule?.ruleId,
        reviewRuleName: scope.rule?.name,

        // 玩法设置
        viewJson: JSON.stringify(viewJson),

        // 商品规则
        uploadPath: commodityRules.uploadPath,
      };
      // 周末一分购归一码 uploadTemplateVersion版本1 
      // 传递 uploadTotalUpc为excl解析返回，后端用来处理特殊逻辑
      if(creationContext.investmentSchema === InvestmentSchemaEnum.WEEKEND_SPLIT_PURCHASE){
        payload.uploadTemplateVersion ='1'
        payload.uploadTotalUpc = commodityRules?.checkExcel?.uploadTotalUpc
      }

      if (
        baseinfo.modifyRules &&
        baseinfo.modifyRules.indexOf(
          "merchant_modify_activity_price"
        ) !== -1
      ) {
        payload.activityModifyRules = [
          {
            activityModifyRuleType: "merchant_modify_activity_price",
            isSupport: true,
          },
        ];
      }

      const { hide } = loadingDialog.show({
        message: creationContext.copy ? "活动信息校验中..." : "活动创建中...",
      });

      if (dataStore._session?.ossURL) {
        const ossURL = dataStore._session?.ossURL;
        payload.description = baseinfo.richDescription.text;
        payload.attributes = {
          ...payload.attributes,
          hasRichDescription: "on",
          richDescriptionURL: ossURL,
        };
      }

      try {
        const {
          interrupt,
          auditUpgradeForCheckMktRuleUnPass,
          auditUpgradeForMultiMktRule,
        } = await checkMarketRules(
          dataStore,
          creationContext,
          JSON.stringify(viewJson)
        );
        if (interrupt) {
          return { silent: true };
        }

        payload.auditUpgradeForCheckMktRuleUnPass =
          auditUpgradeForCheckMktRuleUnPass;
        payload.auditUpgradeForMultiMktRule = auditUpgradeForMultiMktRule;  
        const res = await api.subActivity.create(payload);
        if (res.success) {
          Message.success("创建成功");
          if (creationContext.activitySceneId) {
                  history.push(
                    "/sub-activity/" +
                      res.data +
                      "?activitySceneId=" +
                      creationContext.activitySceneId +
                      "&activityPlanId=" +
                      creationContext.activityPlanId
                  );
                } else {
                  history.push("/sub-activity/" + res.data);
                }
        } else {
          return { message: res.errorMessage };
        }
      } catch (e: any) {
        return { message: e?.message || "系统异常" };
      } finally {
        hide();
      }
    }
  },
};
