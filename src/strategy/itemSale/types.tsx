interface ItemSalePlaydataBase {
  activityType: number;
  activityLimitRule: {
    userTotalCountLimit?: number;
    userDayCountLimit?: number;
  };
  activityMarketingInfo: {
    budgetId?: string;
    budgetName?: string;
    deliveryChannel: number;
    deliveryType: number;
    userScope: number;
    trafficChannel?: number; // 一口价模版才会存在
    deliveryBreakStartFee?: number; // 一口价模版才会存在
  };
  activityRule: {
    discountType: 3;
    discount?: number;
    discountMin?: number;
    discountMax?: number;
    minDiscountPercentForItemSalePrice?: number;
    platformSubsidyMax: number;
    platformSubsidyRateUpperLimit?: number;

  };
}

// 创建场景下的玩法规则数据结构
export interface ItemSalePlaydata extends ItemSalePlaydataBase {
  weekday: string;
  // 创建活动场景下，通过 period + weekday 来传递生效周期和生效时段
  period: {
    openTime: string;
    closeTime: string;
  }[];
}

// 详情场景下的玩法规则数据结构
export interface ItemSalePlaydataForDetail extends ItemSalePlaydataBase {
  weekday: string;
  // 详情场景下，通过 periodDTOList + weekday 来表示生效周期和生效时段
  periodDTOList: {
    openTime: string;
    closeTime: string;
  }[];
}

export enum COMMODITY_REMOVE_CONDITION_ENUM {
  COMMODITY_CATEGORY = 1,   // 商品类目
  COMMODITY_NAME = 2,   // 商品名称
  COMMODITY_UPC = 3,    // 商品条形码
  COMMODITY_ORIGINAL_PRICE = 4,   // 商品原价 报名规则中配置的原价范围规则
  COMMODITY_PURCHASE_NUM = 5, // 商品起购份数 
  COMMODITY_GUIDE_PRICE = 6,  // 商品原价价格力 中控的原价范围规则
  SHOP_RULE = 7, // 店铺规则
  SPU_CSPU_CPV = 8, // spu/cspu+cpv
}
