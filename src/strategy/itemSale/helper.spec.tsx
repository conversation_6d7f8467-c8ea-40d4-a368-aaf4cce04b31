import { InvestmentSchemaEnum } from "../../constants";
import {
  transformFormData2ViewJson,
  transformViewJson2FormData,
  transformFormData2ViewJsonForMultiCommodityLikeActivity,
  transformViewJson2FormDataForMultiCommodityLikeActivity,
} from "./helper";

describe("itemSale", () => {
  describe("helper", () => {
    describe("transformFormData2ViewJson", () => {
      // 注意，商品特价活动，以下几种细分活动场景，玩法表单这部分的逻辑是完全一样的，所以只需要验证日常招商即可
      // - 招商类型:日常招商-招商模版:日常招商
      // - 招商类型:日常招商-招商模版:百亿补贴
      // - 招商类型:日常招商-招商模版:全能拼团
      // - 招商类型:栏目招商-招商模版:时令好货
      test("招商类型:日常招商-招商模版:日常招商-固定招商-无补贴和预算-投放渠道:预订团", () => {
        const viewJson = transformFormData2ViewJson({
          baseinfo: {
            activityTime: {
              weeks: [0, 1, 2, 3, 4, 5, 6],
              batchRange: {
                type: "all",
                list: [{ start: "00:00", end: "23:59" }],
              },
            },
          },
          formData: {
            preheatTime: null,
            saleRule: '{"saleFixed":{"price":10000,"elemeSubsidy":0}}',
            budget: "{}",
            weekday: "0,1,2,3,4,5,6",
            userScope: "0",
            name: "yc-特价-日常招商",
            creator: '{"id":"213137","prefix":"yc213137","name":"杨辰"}',
            itemDiscountType: "1",
            dateRange:
              '{"start":"2023-07-17 00:00:00","end":"2023-07-31 23:59:59"}',
            launchChannel: "9",
            timeRange: '[{"start":"00:00:00","end":"23:59:59"}]',
            userLimit: '{"dayLimit":{"value":"1"},"totalLimit":{"value":2}}',
            _workspace_uuid_: "6v4M8woywiK9aecPk9yWLh",
            effectiveChannel: "1",
          },
          investmentSchema: InvestmentSchemaEnum.RICHANG,
        });
        expect(viewJson).toStrictEqual({
          activityLimitRule: {
            userDayCountLimit: 1,
            userTotalCountLimit: 2,
          },
          activityMarketingInfo: {
            budgetId: undefined,
            budgetName: undefined,
            deliveryChannel: 9,
            deliveryType: 1,
            userScope: 0,
          },
          activityRule: {
            discount: 10000,
            discountMax: undefined,
            discountMin: undefined,
            discountType: 3,
            minDiscountPercentForItemSalePrice: undefined,
            platformSubsidyMax: 0,
          },
          activityType: 1000005,
          period: [
            {
              closeTime: "23:59:59",
              openTime: "00:00:00",
            },
          ],
          weekday: "0,1,2,3,4,5,6",
        });
      });
      test("招商类型:日常招商-招商模版:日常招商-固定招商-多生效时段-无补贴和预算-投放渠道:预订团-医药人群", () => {
        const viewJson = transformFormData2ViewJson({
          baseinfo: {
            activityTime: {
              weeks: [0, 1, 2, 3, 4, 5, 6],
              batchRange: {
                type: "custom",
                list: [
                  {
                    start: "00:00",
                    end: "03:00",
                  },
                  {
                    start: "18:00",
                    end: "20:00",
                  },
                  {
                    start: "22:00",
                    end: "23:59",
                  },
                ],
              },
            },
          },
          formData: {
            preheatTime: null,
            saleRule: '{"saleFixed":{"price":10000,"elemeSubsidy":0}}',
            budget: "{}",
            weekday: "0,1,2,3,4,5,6",
            userScope: "9",
            name: "yc-特价-日常招商-固定招商",
            creator: '{"id":"213137","prefix":"yc213137","name":"杨辰"}',
            itemDiscountType: "1",
            dateRange:
              '{"start":"2023-07-18 00:00:00","end":"2023-07-31 23:59:59"}',
            launchChannel: "9",
            timeRange:
              '[{"start":"00:00:00","end":"03:00:59"},{"start":"18:00:00","end":"20:00:59"},{"start":"22:00:00","end":"23:59:59"}]',
            userLimit: '{"dayLimit":{"value":"1"}}',
            _workspace_uuid_: "6v4M8woywiK9aecPk9yWLh",
            effectiveChannel: "1",
          },
          investmentSchema: InvestmentSchemaEnum.RICHANG,
        });
        expect(viewJson).toStrictEqual({
          activityType: 1000005,
          activityLimitRule: {
            userDayCountLimit: 1,
            userTotalCountLimit: undefined,
          },
          activityMarketingInfo: {
            budgetId: undefined,
            budgetName: undefined,
            deliveryChannel: 9,
            deliveryType: 1,
            userScope: 9,
          },
          activityRule: {
            discountType: 3,
            discountMax: undefined,
            discountMin: undefined,
            discount: 10000,
            minDiscountPercentForItemSalePrice: undefined,
            platformSubsidyMax: 0,
          },
          weekday: "0,1,2,3,4,5,6",
          period: [
            {
              openTime: "00:00:00",
              closeTime: "03:00:59",
            },
            {
              openTime: "18:00:00",
              closeTime: "20:00:59",
            },
            {
              openTime: "22:00:00",
              closeTime: "23:59:59",
            },
          ],
        });
      });
      test("日常招商-固定招商-含补贴和预算-投放渠道:预订团", () => {
        const viewJson = transformFormData2ViewJson({
          baseinfo: {
            activityTime: {
              weeks: [0, 1, 2, 3, 4, 5, 6],
              batchRange: {
                type: "all",
                list: [{ start: "00:00", end: "23:59" }],
              },
            },
          },
          formData: {
            preheatTime: null,
            saleRule: '{"saleFixed":{"price":10000,"elemeSubsidy":200}}',
            budget: '{"budgetId":"foobar","budgetName":"foobar"}',
            weekday: "0,1,2,3,4,5,6",
            userScope: "0",
            name: "yc-特价-日常招商",
            creator: '{"id":"213137","prefix":"yc213137","name":"杨辰"}',
            itemDiscountType: "1",
            dateRange:
              '{"start":"2023-07-17 00:00:00","end":"2023-07-31 23:59:59"}',
            launchChannel: "9",
            timeRange: '[{"start":"00:00:00","end":"23:59:59"}]',
            userLimit: '{"dayLimit":{"value":"1"},"totalLimit":{"value":2}}',
            _workspace_uuid_: "6v4M8woywiK9aecPk9yWLh",
            effectiveChannel: "1",
          },
          investmentSchema: InvestmentSchemaEnum.RICHANG,
        });
        expect(viewJson).toStrictEqual({
          activityLimitRule: {
            userDayCountLimit: 1,
            userTotalCountLimit: 2,
          },
          activityMarketingInfo: {
            budgetId: "foobar",
            budgetName: "foobar",
            deliveryChannel: 9,
            deliveryType: 1,
            userScope: 0,
          },
          activityRule: {
            discount: 10000,
            discountMax: undefined,
            discountMin: undefined,
            discountType: 3,
            minDiscountPercentForItemSalePrice: undefined,
            platformSubsidyMax: 200,
          },
          activityType: 1000005,
          period: [
            {
              closeTime: "23:59:59",
              openTime: "00:00:00",
            },
          ],
          weekday: "0,1,2,3,4,5,6",
        });
      });

      test("日常招商-区间招商-无补贴和预算-限制人群:医药新客-投放渠道:预订团", () => {
        const viewJson = transformFormData2ViewJson({
          baseinfo: {
            activityTime: {
              weeks: [0, 1, 2, 3, 4, 5, 6],
              batchRange: {
                type: "all",
                list: [{ start: "00:00", end: "23:59" }],
              },
            },
          },
          formData: {
            preheatTime: null,
            saleRule:
              '{"saleRange":{"minPrice":20000,"maxPrice":30000,"minDiscount":5.5,"elemeSubsidy":0}}',
            budget: "{}",
            weekday: "0,1,2,3,4,5,6",
            userScope: "9",
            name: "yc-特价活动",
            creator: '{"id":"213137","prefix":"yc213137","name":"杨辰"}',
            itemDiscountType: "1",
            dateRange:
              '{"start":"2023-07-17 00:00:00","end":"2023-07-31 23:59:59"}',
            launchChannel: "9",
            timeRange: '[{"start":"00:00:00","end":"23:59:59"}]',
            userLimit: '{"totalLimit":{"value":1}}',
            _workspace_uuid_: "6v4M8woywiK9aecPk9yWLh",
            effectiveChannel: "1",
          },
          investmentSchema: InvestmentSchemaEnum.RICHANG,
        });
        expect(viewJson).toStrictEqual({
          activityLimitRule: {
            userDayCountLimit: undefined,
            userTotalCountLimit: 1,
          },
          activityMarketingInfo: {
            budgetId: undefined,
            budgetName: undefined,
            deliveryChannel: 9,
            deliveryType: 1,
            userScope: 9,
          },
          activityRule: {
            discount: undefined,
            discountMax: 30000,
            discountMin: 20000,
            discountType: 3,
            minDiscountPercentForItemSalePrice: 550,
            platformSubsidyMax: 0,
          },
          activityType: 1000005,
          period: [
            {
              closeTime: "23:59:59",
              openTime: "00:00:00",
            },
          ],
          weekday: "0,1,2,3,4,5,6",
        });
      });
    });

    describe("transformViewJson2FormData", () => {
      // 注意，商品特价活动，以下几种细分活动场景，玩法表单这部分的逻辑是完全一样的，所以只需要验证日常招商即可
      // - 招商类型:日常招商-招商模版:日常招商
      // - 招商类型:日常招商-招商模版:百亿补贴
      // - 招商类型:日常招商-招商模版:全能拼团
      // - 招商类型:栏目招商-招商模版:时令好货
      test("招商类型:日常招商-招商模版:日常招商-固定招商-无补贴和预算-投放渠道:预订团", () => {
        const viewJson = JSON.stringify({
          activityLimitRule: {
            userDayCountLimit: 1,
            userTotalCountLimit: 2,
          },
          activityMarketingInfo: {
            budgetId: "null", // 后端会有这种异常数据
            deliveryChannel: 9,
            deliveryType: 1,
            userScope: 0,
          },
          activityRule: {
            discount: 10000,
            discountType: 3,
            platformSubsidyMax: 0,
          },
          activityType: 1000005,
          periodDTOList: [
            {
              weekday: "0,1,2,3,4,5,6", // 实际数据里，后端会在 periodDTOList 中加入 weekday 字段
              closeTime: "23:59:59",
              openTime: "00:00:00",
            },
          ],
          weekday: "0,1,2,3,4,5,6",
        });
        const formData = transformViewJson2FormData({
          baseinfo: {
            name: "玩法名称",
            createdUserId: "213137",
            createdUserName: "杨辰",
          },
          viewJson,
          investmentSchema: InvestmentSchemaEnum.RICHANG,
        });
        expect(formData).toStrictEqual({
          name: "玩法名称",
          saleRule: '{"saleFixed":{"price":10000,"elemeSubsidy":0}}',
          budget: undefined,
          userScope: "0",
          creator: '{"id":"213137","name":"杨辰"}',
          launchChannel: "9",
          effectiveChannel: "1",
          userLimit: '{"dayLimit":{"value":1},"totalLimit":{"value":2}}',
        });
      });
      test("招商类型:日常招商-招商模版:日常招商-固定招商-无补贴和预算-投放渠道:预订团-无总限购", () => {
        const viewJson = JSON.stringify({
          activityLimitRule: {
            userDayCountLimit: 1,
          },
          activityMarketingInfo: {
            budgetId: "null", // 后端会有这种异常数据
            deliveryChannel: 9,
            deliveryType: 1,
            userScope: 0,
          },
          activityRule: {
            discount: 10000,
            discountType: 3,
            platformSubsidyMax: 0,
          },
          activityType: 1000005,
          periodDTOList: [
            {
              weekday: "0,1,2,3,4,5,6", // 实际数据里，后端会在 periodDTOList 中加入 weekday 字段
              closeTime: "23:59:59",
              openTime: "00:00:00",
            },
          ],
          weekday: "0,1,2,3,4,5,6",
        });
        const formData = transformViewJson2FormData({
          baseinfo: {
            name: "玩法名称",
            createdUserId: "213137",
            createdUserName: "杨辰",
          },
          viewJson,
          investmentSchema: InvestmentSchemaEnum.RICHANG,
        });
        expect(formData).toStrictEqual({
          name: "玩法名称",
          saleRule: '{"saleFixed":{"price":10000,"elemeSubsidy":0}}',
          budget: undefined,
          userScope: "0",
          creator: '{"id":"213137","name":"杨辰"}',
          launchChannel: "9",
          effectiveChannel: "1",
          userLimit: '{"dayLimit":{"value":1}}',
        });
      });
      test("招商类型:日常招商-招商模版:日常招商-固定招商-无补贴和预算-投放渠道:预订团-无每日限购", () => {
        const viewJson = JSON.stringify({
          activityLimitRule: {
            userTotalCountLimit: 2,
          },
          activityMarketingInfo: {
            budgetId: "null", // 后端会有这种异常数据
            deliveryChannel: 9,
            deliveryType: 1,
            userScope: 0,
          },
          activityRule: {
            discount: 10000,
            discountType: 3,
            platformSubsidyMax: 0,
          },
          activityType: 1000005,
          periodDTOList: [
            {
              weekday: "0,1,2,3,4,5,6", // 实际数据里，后端会在 periodDTOList 中加入 weekday 字段
              closeTime: "00:00:59",
              openTime: "00:00:00",
            },
          ],
          weekday: "0,1,2,3,4,5,6",
        });
        const formData = transformViewJson2FormData({
          baseinfo: {
            name: "玩法名称",
            createdUserId: "213137",
            createdUserName: "杨辰",
          },
          viewJson,
          investmentSchema: InvestmentSchemaEnum.RICHANG,
        });
        expect(formData).toStrictEqual({
          name: "玩法名称",
          saleRule: '{"saleFixed":{"price":10000,"elemeSubsidy":0}}',
          budget: undefined,
          userScope: "0",
          creator: '{"id":"213137","name":"杨辰"}',
          launchChannel: "9",
          effectiveChannel: "1",
          userLimit: '{"totalLimit":{"value":2}}',
        });
      });
      test("招商类型:日常招商-招商模版:日常招商-区间招商-含补贴和预算-投放渠道:预订团", () => {
        const viewJson = JSON.stringify({
          activityLimitRule: {
            userDayCountLimit: 1,
            userTotalCountLimit: 2,
          },
          activityMarketingInfo: {
            budgetId: "foobar",
            budgetName: "foobar",
            deliveryChannel: 9,
            deliveryType: 1,
            userScope: 0,
          },
          activityRule: {
            discountMin: 20000,
            discountMax: 30000,
            discountType: 3,
            minDiscountPercentForItemSalePrice: 550,
            platformSubsidyMax: 200,
          },
          activityType: 1000005,
          periodDTOList: [
            {
              weekday: "0,1,2,3,4,5,6", // 实际数据里，后端会在 periodDTOList 中加入 weekday 字段
              closeTime: "00:00:59",
              openTime: "00:00:00",
            },
          ],
          weekday: "0,1,2,3,4,5,6",
        });
        const formData = transformViewJson2FormData({
          baseinfo: {
            name: "玩法名称",
            createdUserId: "213137",
            createdUserName: "杨辰",
          },
          viewJson,
          investmentSchema: InvestmentSchemaEnum.RICHANG,
        });
        expect(formData).toStrictEqual({
          name: "玩法名称",
          saleRule:
            '{"saleRange":{"minPrice":20000,"maxPrice":30000,"minDiscount":5.5,"elemeSubsidy":200}}',
          budget: '{"budgetId":"foobar","budgetName":"foobar"}',
          userScope: "0",
          creator: '{"id":"213137","name":"杨辰"}',
          launchChannel: "9",
          effectiveChannel: "1",
          userLimit: '{"dayLimit":{"value":1},"totalLimit":{"value":2}}',
        });
      });
    });

    describe("transformFormData2ViewJsonForMultiCommodityLikeActivity", () => {
      // 注意，商品特价活动，以下几种模版的转换逻辑是走这个函数
      // - 招商类型:日常招商-招商模版:多商品
      // - 招商类型:日常招商-招商模版:商家会员
      // - 招商类型:日常招商-招商模版:周末一分购
      test("招商类型:日常招商-招商模版:多商品-固定招商-区分城市-无补贴和预算", () => {
        const viewJson = transformFormData2ViewJsonForMultiCommodityLikeActivity({
          commodityRules: {
            discountPriceRangeType: 1,
            distinguishCity: 1,
            checkExcel: {
              extInfoMap: {
                invitedUpcHasPlatformSubsidy: "false",
              },
            },
          },

          baseinfo: {
            activityTime: {
              weeks: [0, 1, 2, 3, 4, 5, 6],
              batchRange: {
                type: "all",
                list: [{ start: "00:00", end: "23:59" }],
              },
            },
          },
          playdata: {
            data: {
              _workspace_uuid_: "ExukovQrae6xWn2rt4NgG",
              creator: {
                id: "329684",
                prefix: "yufanguo.gyf",
                name: "郭宇帆",
              },
              userScope: "0",
              userLimit: {
                dayLimit: {
                  value: 22,
                },
              },
              deliveryType: "1",
            },
            type: "insert",
            snapshot: {
              data: {
                _workspace_uuid_: "ExukovQrae6xWn2rt4NgG",
                creator:
                  '{"id":"329684","prefix":"yufanguo.gyf","name":"郭宇帆"}',
                userScope: "0",
                userLimit: '{"dayLimit":{"value":22}}',
                deliveryType: "1",
              },
              type: "insert",
            },
          },
          investmentSchema: InvestmentSchemaEnum.MUTI_COMMDITY,
        });
        expect(viewJson).toStrictEqual({
          activityLimitRule: {
            userDayCountLimit: 22,
            userTotalCountLimit: undefined,
          },
          activityMarketingInfo: {
            budgetId: undefined,
            budgetName: undefined,
            deliveryType: "1",
            deliveryChannel: 0,
            userScope: "0",
            invitedUpcHasPlatformSubsidy: "false",
            discountPriceRangeType: 1,
            cityDistinguishType: 1,
          },
          activityType: 1000005,
          period: [
            {
              openTime: "00:00:00",
              closeTime: "23:59:59",
              weekday: "0,1,2,3,4,5,6",
            },
          ],
        });
      });
      test("招商类型:日常招商-招商模版:商家会员-区间招商-区分城市-无补贴和预算", () => {
        const viewJson = transformFormData2ViewJsonForMultiCommodityLikeActivity({
          commodityRules: {
            discountPriceRangeType: 2,
            distinguishCity: 2,
            checkExcel: {
              extInfoMap: {
                invitedUpcHasPlatformSubsidy: "false",
              },
            },
          },

          baseinfo: {
            activityTime: {
              weeks: [0, 1, 2, 3, 4, 5, 6],
              batchRange: {
                type: "all",
                list: [{ start: "00:00", end: "23:59" }],
              },
            },
          },
          playdata: {
            data: {
              _workspace_uuid_: "ExukovQrae6xWn2rt4NgG",
              creator: {
                id: "329684",
                prefix: "yufanguo.gyf",
                name: "郭宇帆",
              },
              userScope: "8",
              userLimit: {
                totalLimit: {
                  value: 33,
                },
              },
              deliveryType: "1",
            },
            type: "insert",
            snapshot: {
              data: {
                _workspace_uuid_: "ExukovQrae6xWn2rt4NgG",
                creator:
                  '{"id":"329684","prefix":"yufanguo.gyf","name":"郭宇帆"}',
                userScope: "8",
                userLimit: '{"totalLimit":{"value":33}}',
                deliveryType: "1",
              },
              type: "insert",
            },
          },
          investmentSchema: InvestmentSchemaEnum.MERCHANTS_MEMBER,
        });
        expect(viewJson).toStrictEqual({
          activityLimitRule: {
            userDayCountLimit: undefined,
            userTotalCountLimit: 33,
          },
          activityMarketingInfo: {
            budgetId: undefined,
            budgetName: undefined,
            deliveryType: "1",
            deliveryChannel: 0,
            userScope: "8",
            invitedUpcHasPlatformSubsidy: "false",
            discountPriceRangeType: 2,
            cityDistinguishType: 2,
          },
          activityType: 1000005,
          period: [
            {
              openTime: "00:00:00",
              closeTime: "23:59:59",
              weekday: "0,1,2,3,4,5,6",
            },
          ],
        });
      });
      test("招商类型:日常招商-招商模版:周末一分购-区间招商-不区分城市-无补贴和预算", () => {
        const viewJson = transformFormData2ViewJsonForMultiCommodityLikeActivity({
          commodityRules: {
            discountPriceRangeType: 1,
            distinguishCity: 2,
            checkExcel: {
              extInfoMap: {
                invitedUpcHasPlatformSubsidy: "false",
              },
            },
          },

          baseinfo: {
            activityTime: {
              weeks: [0, 1, 2, 3, 4, 5, 6],
              batchRange: {
                type: "all",
                list: [{ start: "00:00", end: "23:59" }],
              },
            },
          },
          playdata: {
            data: {
              _workspace_uuid_: "ExukovQrae6xWn2rt4NgG",
              creator: {
                id: "329684",
                prefix: "yufanguo.gyf",
                name: "郭宇帆",
              },
              userScope: "0",
              userLimit: {
                dayLimit: {
                  value: 44,
                },
                totalLimit: {
                  value: 33,
                },
              },
              deliveryType: "1",
              weeklyTimeRangeSetting: [
                {
                  day: 1,
                  timeRange: {
                    openTime: "00:00",
                    closeTime: "23:59",
                  },
                },
                {
                  day: 2,
                  timeRange: {
                    openTime: "00:00",
                    closeTime: "23:59",
                  },
                },
                {
                  day: 3,
                  timeRange: {
                    openTime: "00:00",
                    closeTime: "23:59",
                  },
                },
                {
                  day: 4,
                  timeRange: {
                    openTime: "00:00",
                    closeTime: "23:59",
                  },
                },
                {
                  day: 5,
                  timeRange: {
                    openTime: "00:00",
                    closeTime: "23:59",
                  },
                },
                {
                  day: 6,
                  timeRange: {
                    openTime: "03:00",
                    closeTime: "23:59",
                  },
                },
                {
                  day: 0,
                  timeRange: {
                    openTime: "00:00",
                    closeTime: "23:59",
                  },
                },
              ],
            },
            type: "insert",
            snapshot: {
              data: {
                _workspace_uuid_: "ExukovQrae6xWn2rt4NgG",
                creator:
                  '{"id":"329684","prefix":"yufanguo.gyf","name":"郭宇帆"}',
                userScope: "0",
                userLimit:
                  '{"dayLimit":{"value":44},"totalLimit":{"value":33}}',
                deliveryType: "1",
                weeklyTimeRangeSetting:
                  '[{"day":1,"timeRange":{"openTime":"00:00","closeTime":"23:59"}},{"day":2,"timeRange":{"openTime":"00:00","closeTime":"23:59"}},{"day":3,"timeRange":{"openTime":"00:00","closeTime":"23:59"}},{"day":4,"timeRange":{"openTime":"00:00","closeTime":"23:59"}},{"day":5,"timeRange":{"openTime":"00:00","closeTime":"23:59"}},{"day":6,"timeRange":{"openTime":"03:00","closeTime":"23:59"}},{"day":0,"timeRange":{"openTime":"00:00","closeTime":"23:59"}}]',
              },
              type: "insert",
            },
          },
          investmentSchema: InvestmentSchemaEnum.WEEKEND_SPLIT_PURCHASE,
        });
        expect(viewJson).toStrictEqual({
          activityLimitRule: {
            userDayCountLimit: 44,
            userTotalCountLimit: 33,
          },
          activityMarketingInfo: {
            budgetId: undefined,
            budgetName: undefined,
            deliveryType: "1",
            deliveryChannel: 0,
            userScope: "0",
            invitedUpcHasPlatformSubsidy: "false",
            discountPriceRangeType: 1,
            cityDistinguishType: 2,
          },
          activityType: 1000005,
          period: [
            {
              closeTime: "23:59:59",
              openTime: "00:00:00",
              weekday: 1,
            },
            {
              closeTime: "23:59:59",
              openTime: "00:00:00",
              weekday: 2,
            },
            {
              closeTime: "23:59:59",
              openTime: "00:00:00",
              weekday: 3,
            },
            {
              closeTime: "23:59:59",
              openTime: "00:00:00",
              weekday: 4,
            },
            {
              closeTime: "23:59:59",
              openTime: "00:00:00",
              weekday: 5,
            },
            {
              closeTime: "23:59:59",
              openTime: "03:00:00",
              weekday: 6,
            },
            {
              closeTime: "23:59:59",
              openTime: "00:00:00",
              weekday: 0,
            },
          ],
        });
      });
    });

    describe("transformViewJson2FormDataForMultiCommodityLikeActivity", () => {
      // 注意，商品特价活动，以下几种模版的转换逻辑是走这个函数,并且不区分模版，这里拿多商品测
      // - 招商类型:日常招商-招商模版:多商品
      // - 招商类型:日常招商-招商模版:商家会员
      // - 招商类型:日常招商-招商模版:周末一分购
      test("招商类型:日常招商-招商模版:多商品-区间招商-不区分城市-补贴和预算", () => {
        const viewJson = {
          activityLimitRule: {
            userDayCountLimit: 22,
            userTotalCountLimit: 33,
          },
          activityMarketingInfo: {
            budgetId: "test",
            budgetName: "test",
            deliveryType: "1",
            deliveryChannel: 0,
            userScope: "0",
            invitedUpcHasPlatformSubsidy: "false",
            discountPriceRangeType: 1,
            cityDistinguishType: 1,
          },
          activityType: 1000005,
          period: [
            {
              openTime: "00:00:00",
              closeTime: "23:59:59",
              weekday: "0,1,2,3,4,5,6",
            },
          ],
        };
        const baseInfo = {
          createdUserId: "329684",
          createdUserName: "郭宇帆",
        };
        const formData = transformViewJson2FormDataForMultiCommodityLikeActivity(
          JSON.stringify(viewJson),
          baseInfo
        );
        expect(formData).toEqual({
          budget: '{"budgetName":"test","budgetId":"test"}',
          creator: '{"name":"郭宇帆","id":"329684"}',
          userScope: "0",
          userLimit: '{"dayLimit":{"value":"22"},"totalLimit":{"value":"33"}}',
          deliveryType: "1",
          launchChannel: "0",
        });
      });
      test("招商类型:日常招商-招商模版:多商品-异常情况", () => {
        const viewJson = {
          activityLimitRule: {
            userDayCountLimit: undefined,
            userTotalCountLimit: undefined,
          },
          activityMarketingInfo: {
            budgetId: undefined,
            budgetName: undefined,
            deliveryType: undefined,
            deliveryChannel: undefined,
            userScope: undefined,
            invitedUpcHasPlatformSubsidy: undefined,
            discountPriceRangeType: undefined,
            cityDistinguishType: undefined,
          },
          activityType: 1000005,
          period: [
            {
              openTime: "00:00:00",
              closeTime: "23:59:59",
              weekday: "0,1,2,3,4,5,6",
            },
          ],
        };
        const baseInfo = {
          createdUserId: "329684",
          createdUserName: "郭宇帆",
        };
        const formData = transformViewJson2FormDataForMultiCommodityLikeActivity(
          JSON.stringify(viewJson),
          baseInfo
        );
        expect(formData).toEqual({
          budget: "{}",
          creator: '{"name":"郭宇帆","id":"329684"}',
          userScope: "",
          userLimit: "{}",
          deliveryType: "",
          launchChannel: "",
        });
      });
    });
  });
});
