import { Message } from "@alifd/next";

import * as api from "../api";
import loadingDialog from "../components/LoadingDialog";
import { ActivityTypeEnum, InvestmentTypeEnum } from "../constants";
import { ActivityStrategy, InvestmentActivityDetailDTO } from "../models";
import { CableBase, resolveCreateActivityCommonData } from "./base";
import {
  resolveCommodityAuditProps,
  gotoLegacyActivityDetailByDefault,
  checkValidateCopyActivityResult,
  checkNextTimeForAlpha,
} from "./mixins";

const debug = require("debug")("zs:strategy:CategoryReduce");

interface CategoryReducePlaydata {
  activityRule: Array<{
    condition: number;
    discount: number;
    platformSubsidy: number;
    shopSubsidy: number;
  }>;
  period: {
    closeTime: string;
    openTime: string;
    weekday: string;
  };
}

/**
 * 品类满减活动
 */
const CategoryReduce: ActivityStrategy = {
  activityType: ActivityTypeEnum.MKT_NR_ACT_TYPE_J_JIAN,
  cable: {
    ...CableBase,
    workspace: "ExukovQrae6xWn2rt4NgG",
    model: "80F5smjTe7I5NAcIJ3qHx7",
    async checkNext(checkNextContext, creationContext, history) {
      const { step, dataStore } = checkNextContext;
      if (step.name === "baseinfo") {
        const timeError = await checkNextTimeForAlpha(
          checkNextContext,
          creationContext
        );
        if (timeError) {
          return timeError;
        }
      }
      if (step.name === "playdata") {
        const { scope, baseinfo } = dataStore.data;
        const commonData = resolveCreateActivityCommonData(
          ActivityTypeEnum.MKT_NR_ACT_TYPE_J_JIAN,
          checkNextContext,
          creationContext
        );

        const commodityAuditProps: any = resolveCommodityAuditProps(scope)
        const payload: any = {
          ...commonData,

          auditMemberList:
            baseinfo.auditMemberList?.length > 0
              ? baseinfo.auditMemberList
              : null,
          // 自动审核规则
          ...commodityAuditProps,
          // 降套补 商品清退规则
          commodityRemoveCondition: { removeCondition: scope.removeCondition },
          // 报名规则
          reviewRuleId: scope.rule?.ruleId,
          reviewRuleName: scope.rule?.name,
        };

        if (creationContext.investmentType === InvestmentTypeEnum.LANMU) {
          // 栏目招商
          payload.signUpMinCycleTime = baseinfo.cycleTime.min;
          payload.signUpMaxCycleTime = baseinfo.cycleTime.max;
        }
        payload.dataMap.resource.componentModelUuid = "63S1qPtE0Pp71rpw6w3LoL";

        const { hide, updateMessage } = loadingDialog.show({
          message: creationContext.copy ? '活动信息校验中...' : '活动创建中...'
        })
        try {
          if (creationContext.copy) {
            const res = await api.subActivity.validateCopyActivityData({
              copyActivityId: creationContext.activity?.activityId,
              ...payload
            })
            const copyError = checkValidateCopyActivityResult(res)
            if (copyError) {
              return copyError
            }
            updateMessage('活动创建中...')
          }
          debug("create subActivity", payload);
          const res = await api.subActivity.create({
            copyActivityId: creationContext.activity?.activityId,
            ...payload
          });
          if (res.success) {
            Message.success("创建成功");
            if (creationContext.activitySceneId) {
                  history.push(
                    "/sub-activity/" +
                      res.data +
                      "?activitySceneId=" +
                      creationContext.activitySceneId +
                      "&activityPlanId=" +
                      creationContext.activityPlanId
                  );
                } else {
                  history.push("/sub-activity/" + res.data);
                }
          } else {
            return { message: res.errorMessage };
          }
        } catch(e: any) {
          return { message: e?.message || '系统异常' }
        } finally {
          hide()
        }
      }
    },
  },
  hasAgentSubsidy() {
    return false;
  },
  isSupportAgent: false,
  isOfflineEnabled: function (state: number): boolean {
    // 活动状态: 1:未开始 2:活动中 3:活动暂停 4:活动取消 5:活动结束 8: 预算熔断
    return [1, 2, 3, 8].includes(state);
  },
  isItemAuditStatsVisible: true,
  isDingtalkLinkEnabled: () => false,
  hasAuditMemberList: () => true,
  isSupportModifyStock: () => false,
  isDetailInviteStatsOnMainActivityTableVisible: true,
  renderPlayDataInfoForTableView: function (): JSX.Element {
    return <></>;
  },
  gotoLegacyDetail: function (
    act: InvestmentActivityDetailDTO,
    scene: "lookup" | "examine"
  ): void {
    gotoLegacyActivityDetailByDefault(act, scene);
  },
  renderActivityRuleForTable(viewJson: any) {
    try {
      const data: CategoryReducePlaydata = JSON.parse(viewJson);
      return (
        <>
          {data.activityRule.map((r: any, i: number) => {
            return (
              <div key={i}>
                满 {r.condition / 100}元，减{r.discount / 100}元，平台补贴
                {r.platformSubsidy / 100}元，商家补贴{r.shopSubsidy / 100}元
              </div>
            );
          })}
        </>
      );
    } catch (e) {
      console.error(e);
      return <></>;
    }
  },
  renderActivityRuleForTableLegacy: function (marketPlay: {
    instance: { fields: any };
  }) {
    const fields = marketPlay?.instance?.fields;
    try {
      const rule = JSON.parse(fields.rule);
      const ret = rule.map((r: any, i: number) => {
        return (
          <div key={i}>
            满{r.condition / 100}元，减{r.discount / 100}元，平台补贴
            {r.elemeSubsidy / 100}元，商家补贴{r.shopSubsidy / 100}元
          </div>
        );
      });
      return ret;
    } catch (e) {
      return "";
    }
  },
};

export default CategoryReduce;
