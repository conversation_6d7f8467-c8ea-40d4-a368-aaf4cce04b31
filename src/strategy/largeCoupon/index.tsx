import { Message } from '@alifd/next';
import BigNumber from 'bignumber.js';
import { <PERSON><PERSON>, Dialog } from '@alifd/next';

import * as api from '../../api';
import { maskMS } from '../../common';
import { ActivityTypeEnum } from '../../constants';
import { ActivityStrategy, InvestmentActivityDetailDTO } from '../../models';
import { CableBase } from '../base';
import {
  checkNextTimeForAlpha,
  genericCreateActivity,
  gotoLegacyActivityDetailByDefault,
} from '../mixins';
import { auditServiceForCoupon } from '../service';
import { formatPlayData2ViewJson } from './formatUtils';

const ActivityCancelled = 4; // 活动已取消
const ActivityFinished = 5; // 活动已结束
const ActivityBudgetExhausted = 8; // 活动熔断

export const UserScopeEnumStr = {
  0: '全部用户',
  10: '购物金首冲用户',
  11: '商家新客用户',
  12: '商家一单用户',
  13: '商家二单用户',
  14: '商家流失用户',
  15: '商家活跃用户',
  16: '频道新客用户',
  17: '频道一单用户',
  18: '频道二单用户',
  19: '频道流失用户',
  20: '频道活跃用户',
};

const styles = {
  wid: {
    marginTop: '4px',
    fontSize: '14px',
    color: '#999',
  },
  modal: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    width: '448px',
    marginBottom: '40px',
  },
  header: {
    display: 'flex',
    backgroundColor: '#f5f6f7',
  },
  row: {
    display: 'flex',
    alignItems: 'center',
    height: '40px',
  },
  child: {
    lineHeight: '40px',
    borderRight: '1px solid #f5f6f7',
    borderBottom: '1px solid #f5f6f7',
    borderLeft: '1px solid #f5f6f7',
    width: '150px',
    height: '40px',
    textAlign: 'center',
  },
};

interface LargeCouponViewJsonTs {
  activityType: number;
  couponRuleViewDTOList: {
    activitySupplyPoolId: number; // 门店池 id
    dayCountLimit: number; // 每日库存最小值
    dayCountLimitMax: number; //  每日库存最大值
    shopSubsidy: number; //  商家出资
    userScope: 10 | 11 | 12 | 13 | 14 | 15 | 16 | 17 | 18 | 19 | 20; //  生效人群
  }[];
  deliveryChannel: 1 | 2; // 投放渠道
  deliveryType: 1 | 2 | 3; //  核销渠道
  userScopeContributionType: 1 | 2 | 3; // 分人群出资方式
}



const onShowDialog = async (
  record: any,
  activityId: number,
  shopStatus: string,
  activityStatus: number
) => {
  let isEndStatus = false; // 结束态

  // 活动状态为
  if (
    shopStatus === 'offline' ||
    activityStatus === ActivityCancelled ||
    activityStatus === ActivityFinished ||
    activityStatus === ActivityBudgetExhausted
  ) {
    isEndStatus = true;
  }

  const res =
    (await api.queryActivityParticipantStock({
      activityId,
      storeId: record.storeId,
      sellerId: record.aliSellerId,
    })) || {};
  const data = res?.data;

  if (!data) {
    return Message.error(res.errorMessage);
  }

  Dialog.show({
    title: (
      <div>
        <div>{record.storeName}</div>
        <div style={styles.wid}>ID：{record.wid}</div>
      </div>
    ),
    content: (
      // @ts-ignore
      <div style={styles.modal}>
        <div style={styles.header}>
          {/* @ts-ignore */}
          <div style={styles.child}>生效人群</div>
          {/* @ts-ignore */}
          <div style={styles.child}>单店每日库存</div>
          {/* @ts-ignore */}
          <div style={styles.child}>当日剩余库存</div>
        </div>
        {data?.activitySignUpRuleDTOList &&
          data?.activitySignUpRuleDTOList.map((item: any) => {
            return (
              <div style={styles.row} key={item?.userScope}>
                {/* @ts-ignore */}
                <div style={styles.child}>{UserScopeEnumStr[item.userScope]}</div>
                {/* @ts-ignore */}
                <div style={styles.child}><span>{item?.dayCountStock || '-'}</span></div>
                {/* @ts-ignore */}
                <div style={styles.child}>{isEndStatus ? '-' : item?.dayCountRemainingStock}</div>
              </div>
            );
          })}
      </div>
    ),
    footer: false,
  });
};

const LargeCoupon: ActivityStrategy = {
  activityType: ActivityTypeEnum.LARGE_COUPON,
  generation: 'v2',
  cable: {
    ...CableBase,
    workspace: 'ExukovQrae6xWn2rt4NgG',
    model: '5tv8fkKESZs720RWSfnF7k',
    async checkNext({ step, dataStore }, creationContext, history) {
      if(step.name === "baseinfo") {
        const timeError = await checkNextTimeForAlpha({ step, dataStore }, creationContext);
        if(timeError) {
          return timeError;
        }
      }
      if (step.name === 'playdata' || step.name === 'playdataV2') {
        const { baseinfo, scope } = dataStore.data;
        let playdata: any;
        if (step.name === 'playdataV2') {
          playdata = dataStore.data.playdataV2;
        } else {
          playdata = dataStore.data.playdata;
        }
        const viewJson = formatPlayData2ViewJson(playdata);
        const payload = {
          // 活动类型信息
          templateId: creationContext.templateId,
          investmentType: creationContext.investmentType,
          investmentSchema: creationContext.investmentSchema,
          activityType: ActivityTypeEnum.LARGE_COUPON,
          // 基本信息
          name: baseinfo.name,
          description: baseinfo.description,
          remark: baseinfo.remark,
          beginTime: baseinfo.activityTime.start.toDate().getTime(),
          endTime: maskMS(baseinfo.activityTime.end.toDate().getTime()),
          allowCancel: baseinfo.allowCancel,
          signUpShopType: baseinfo.signUpShopType,
          signUpStartTime: baseinfo.signupTime.start.toDate().getTime(),
          signUpEndTime: maskMS(baseinfo.signupTime.end.toDate().getTime()),
          subsidyType: creationContext?.subsidyType,
          manageOrgList: baseinfo?.manageOrgList,
          activityPeriodRequestList: [
            {
              weekday: baseinfo.activityTime.weeks.join(','),
              openTime: baseinfo.activityTime.range.start + ':00',
              closeTime: baseinfo.activityTime.range.end + ':00',
            },
          ],

          // 授权审核人
          auditMemberList:
            baseinfo.auditMemberList?.length > 0
              ? baseinfo.auditMemberList
              : null,

          // 招商范围信息
          shopPoolId: scope.pool.value,
          shopPoolName: scope.pool.label,

          // 玩法信息
          viewJson: JSON.stringify(viewJson),
        };
        return await genericCreateActivity(payload, history, creationContext);
      }
    },
    auditService: auditServiceForCoupon,
  },
  hasAgentSubsidy() {
    return false;
  },
  isOfflineEnabled: function (): boolean {
    // 业态大额红包和其他活动不太一样，下线权限由后端控制
    return true;
  },
  isDingtalkLinkEnabled: () => false,
  hasAuditMemberList: () => true,
  isSupportModifyStock: () => false,
  isDetailInviteStatsOnMainActivityTableVisible: true,
  renderPlayDataInfoForTableView: function (): JSX.Element {
    return <></>;
  },
  renderActivityRuleForTable(viewJson: string) {
    try {
      const data: LargeCouponViewJsonTs = JSON.parse(viewJson);
      return (
        <div>
          {data?.couponRuleViewDTOList?.map((item) => {
            return (
              // @ts-ignore
              <div key={item.userScope}>{`活动生效人群：${
                UserScopeEnumStr[item.userScope]
              }；商家出资金额 ${new BigNumber(item.shopSubsidy)
                .dividedBy(100)
                .toNumber()} 元；单门店每日可消耗数量 ${item.dayCountLimit} - ${
                item.dayCountLimitMax
              }`}</div>
            );
          })}
        </div>
      );
    } catch (e) {
      console.error(e);
      return '';
    }
  },
  renderGroupItemExtView: (record, activityId, shopStatus, activityStatus) => {
    return (
      <Button
        text
        type="secondary"
        size="medium"
        style={{ display: 'flax', flex: 2 }}
        onClick={() =>
          onShowDialog(record, activityId, shopStatus, activityStatus)
        }
      >
        查看库存
      </Button>
    );
  },
  isSupportAgent: false,
  gotoLegacyDetail: function (
    act: InvestmentActivityDetailDTO,
    scene: 'lookup' | 'examine'
  ): void {
    gotoLegacyActivityDetailByDefault(act, scene);
  },
  renderActivityRuleForTableLegacy: function () {
    return '';
  },
};

export default LargeCoupon;
