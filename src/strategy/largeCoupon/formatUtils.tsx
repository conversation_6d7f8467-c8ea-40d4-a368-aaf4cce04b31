import BigNumber from "bignumber.js";

interface ReqLargeCouponViewJson {
  instanceType: number; // 招商类型
  userScopeContributionType: 1 | 2 | 3; // 多人群分档位红包, 1：单人群， 2 多人群
  activityLimitRuleRequest: {
    dayCountLimit: number; // 单店每日库存最小
    dayCountLimitMax: number; // 单店每日库存最大
  };
  activityMarketingInfoRequest: {
    deliveryChannel: 1 | 2; // 投放渠道
    deliveryType: 1 | 2 | 3; //  核销渠道
    userScope: number | string; // 生效人群：商家品牌活跃
  };
  couponPeriodAndRuleRequest: {
    couponActRuleList: [
      {
        activitySubsidyRequest: {
          shopSubsidy: number; // 商家出资金额
        };
      }
    ];
  };
}

interface ValueTS {
  userScopeContributionType: 1 | 2;
  deliveryChannel: 1 | 2; // 投放渠道
  deliveryType: 1 | 2 | 3; //  核销渠道
  [userScope: number]: {
    enabled: boolean;
    shopSubsidy: number; // 商家出资金额
    dayCountLimit: number; // 最小库存
    dayCountLimitMax: number; // 最大库存
  };
  instanceType: number; // 招商类型
  budgetId?: string;
  budgetName?: string;
  displayPlatformSubsidyMin: number; // 商家展示平台补贴最小值
  displayPlatformSubsidyMax: number; // 商家展示平台补贴最大值
  playRuleDescType: 1 | 2 | 3;
}

export const formatPlayData2ViewJson = (playdata: ValueTS) => {
  const contributionTypeList = Object.keys(playdata)
    ?.filter((v) => +v >= 10)
    .filter((v) => playdata[(v as `${number}`)]?.enabled)
    ?.map((v) => {
      return {
        ...playdata[(v as `${number}`)],
        userScope: v,
      };
    });

  // 人群相关
  const activityRuleRequestList = contributionTypeList.map(
    (contributionType) => {
      const contribution: ReqLargeCouponViewJson = {} as ReqLargeCouponViewJson;
      contribution.activityLimitRuleRequest = {
        dayCountLimit: contributionType.dayCountLimit,
        dayCountLimitMax: contributionType.dayCountLimitMax,
      };
      contribution.activityMarketingInfoRequest = {
        deliveryChannel: playdata.deliveryChannel,
        deliveryType: playdata.deliveryType,
        userScope: contributionType.userScope,
      };
      contribution.couponPeriodAndRuleRequest = {
        couponActRuleList: [
          {
            activitySubsidyRequest: {
              shopSubsidy: new BigNumber(contributionType.shopSubsidy)
                .multipliedBy(100)
                .toNumber(),
            },
          },
        ],
      };
      contribution.instanceType = playdata.instanceType;
      contribution.userScopeContributionType =
        playdata.userScopeContributionType;
      return contribution;
    }
  );

  // 活动属性字段, 用于存储活动维度的扩展属性，常用于透传字段，避免频繁新增字段
  const attributes = {
    displayPlatformSubsidyMin: playdata?.displayPlatformSubsidyMin,
    displayPlatformSubsidyMax: playdata?.displayPlatformSubsidyMax,
    playRuleDescType: playdata?.playRuleDescType,
  };

  return { activityRuleRequestList, attributes };
};
