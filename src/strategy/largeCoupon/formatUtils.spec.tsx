import { formatPlayData2ViewJson } from "./formatUtils";

describe("blastRedPacket", () => {
  describe("utils", () => {
    describe("formatPlayData2ViewJson", () => {
      test("日常模版-仅商家新客红包", () => {
        const viewJson = formatPlayData2ViewJson({
          "11": {
            enabled: true,
            shopSubsidy: 1,
            dayCountLimit: 999,
            dayCountLimitMax: 999,
            userScope: "11",
          },
          userScopeContributionType: 1,
          deliveryChannel: 2,
          deliveryType: 1,
          instanceType: 1000026,
          playRuleDescType: 1,
          displayPlatformSubsidyMin: 1,
          displayPlatformSubsidyMax: 25,
        });
        expect(viewJson).toStrictEqual({
          activityRuleRequestList: [
            {
              activityLimitRuleRequest: {
                dayCountLimit: 999,
                dayCountLimitMax: 999,
              },
              activityMarketingInfoRequest: {
                deliveryChannel: 2,
                deliveryType: 1,
                userScope: "11",
              },
              couponPeriodAndRuleRequest: {
                couponActRuleList: [
                  {
                    activitySubsidyRequest: {
                      shopSubsidy: 100,
                    },
                  },
                ],
              },
              instanceType: 1000026,
              userScopeContributionType: 1,
            },
          ],
          attributes: {
            displayPlatformSubsidyMin: 1,
            displayPlatformSubsidyMax: 25,
            playRuleDescType: 1,
          },
        });
      });
      test("日常模版-多人群分档位红包-商家流失用户", () => {
        const viewJson = formatPlayData2ViewJson({
          "14": {
            enabled: true,
            shopSubsidy: 2,
            dayCountLimit: 33,
            dayCountLimitMax: 555,
            userScope: "14",
          },
          "15": {
            enabled: true,
            shopSubsidy: 1,
            dayCountLimit: 66,
            dayCountLimitMax: 999,
            userScope: "15",
          },
          userScopeContributionType: 2,
          deliveryChannel: 2,
          deliveryType: 1,
          instanceType: 1000026,
          displayPlatformSubsidyMin: 2,
          displayPlatformSubsidyMax: 30,
          playRuleDescType: 1,
        });
        expect(viewJson).toStrictEqual({
          activityRuleRequestList: [
            {
              activityLimitRuleRequest: {
                dayCountLimit: 33,
                dayCountLimitMax: 555,
              },
              activityMarketingInfoRequest: {
                deliveryChannel: 2,
                deliveryType: 1,
                userScope: "14",
              },
              couponPeriodAndRuleRequest: {
                couponActRuleList: [
                  {
                    activitySubsidyRequest: {
                      shopSubsidy: 200,
                    },
                  },
                ],
              },
              instanceType: 1000026,
              userScopeContributionType: 2,
            },
            {
              activityLimitRuleRequest: {
                dayCountLimit: 66,
                dayCountLimitMax: 999,
              },
              activityMarketingInfoRequest: {
                deliveryChannel: 2,
                deliveryType: 1,
                userScope: "15",
              },
              couponPeriodAndRuleRequest: {
                couponActRuleList: [
                  {
                    activitySubsidyRequest: {
                      shopSubsidy: 100,
                    },
                  },
                ],
              },
              instanceType: 1000026,
              userScopeContributionType: 2,
            },
          ],
          attributes: {
            displayPlatformSubsidyMin: 2,
            displayPlatformSubsidyMax: 30,
            playRuleDescType: 1,
          },
        });
      });
      test("超级吃货卡-商家活跃用户-商家一单用户", () => {
        const viewJson = formatPlayData2ViewJson({
          "12": {
            enabled: true,
            shopSubsidy: 55,
            dayCountLimit: 33,
            dayCountLimitMax: 667,
            userScope: "12",
          },
          "15": {
            enabled: true,
            shopSubsidy: 3,
            dayCountLimit: 67,
            dayCountLimitMax: 88,
            userScope: "15",
          },
          userScopeContributionType: 2,
          deliveryChannel: 2,
          deliveryType: 1,
          instanceType: 1000026,
          displayPlatformSubsidyMin: 2,
          displayPlatformSubsidyMax: 30,
        });
        expect(viewJson).toStrictEqual({
          activityRuleRequestList: [
            {
              activityLimitRuleRequest: {
                dayCountLimit: 33,
                dayCountLimitMax: 667,
              },
              activityMarketingInfoRequest: {
                deliveryChannel: 2,
                deliveryType: 1,
                userScope: "12",
              },
              couponPeriodAndRuleRequest: {
                couponActRuleList: [
                  {
                    activitySubsidyRequest: {
                      shopSubsidy: 5500,
                    },
                  },
                ],
              },
              instanceType: 1000026,
              userScopeContributionType: 2,
            },
            {
              activityLimitRuleRequest: {
                dayCountLimit: 67,
                dayCountLimitMax: 88,
              },
              activityMarketingInfoRequest: {
                deliveryChannel: 2,
                deliveryType: 1,
                userScope: "15",
              },
              couponPeriodAndRuleRequest: {
                couponActRuleList: [
                  {
                    activitySubsidyRequest: {
                      shopSubsidy: 300,
                    },
                  },
                ],
              },
              instanceType: 1000026,
              userScopeContributionType: 2,
            },
          ],
          attributes: {
            displayPlatformSubsidyMin: 2,
            displayPlatformSubsidyMax: 30,
            playRuleDescType: undefined
          },
        });
      });
      test("站外卡券售卖-商家活跃用户-商家二单用户", () => {
        const viewJson = formatPlayData2ViewJson({
          "13": {
            enabled: true,
            shopSubsidy: 44,
            dayCountLimit: 55,
            dayCountLimitMax: 77,
            userScope: "13",
          },
          "15": {
            enabled: true,
            shopSubsidy: 4,
            dayCountLimit: 33,
            dayCountLimitMax: 56,
            userScope: "15",
          },
          userScopeContributionType: 2,
          deliveryChannel: 201,
          deliveryType: 1,
          instanceType: 1000026,
          displayPlatformSubsidyMin: 3,
          displayPlatformSubsidyMax: 30,
          playRuleDescType: 11,
        });
        expect(viewJson).toStrictEqual({
          activityRuleRequestList: [
            {
              activityLimitRuleRequest: {
                dayCountLimit: 55,
                dayCountLimitMax: 77,
              },
              activityMarketingInfoRequest: {
                deliveryChannel: 201,
                deliveryType: 1,
                userScope: "13",
              },
              couponPeriodAndRuleRequest: {
                couponActRuleList: [
                  {
                    activitySubsidyRequest: {
                      shopSubsidy: 4400,
                    },
                  },
                ],
              },
              instanceType: 1000026,
              userScopeContributionType: 2,
            },
            {
              activityLimitRuleRequest: {
                dayCountLimit: 33,
                dayCountLimitMax: 56,
              },
              activityMarketingInfoRequest: {
                deliveryChannel: 201,
                deliveryType: 1,
                userScope: "15",
              },
              couponPeriodAndRuleRequest: {
                couponActRuleList: [
                  {
                    activitySubsidyRequest: {
                      shopSubsidy: 400,
                    },
                  },
                ],
              },
              instanceType: 1000026,
              userScopeContributionType: 2,
            },
          ],
          attributes: {
            displayPlatformSubsidyMin: 3,
            displayPlatformSubsidyMax: 30,
            playRuleDescType: 11,
          },
        });
      });
    });
  });
});
