import { Message } from "@alifd/next";

import * as api from "../../api";
import { isValueSet, maskMS } from "../../common";
import loadingDialog from "../../components/LoadingDialog";
import { ActivityTypeEnum, InvestmentSchemaEnum } from "../../constants";
import { ActivityStrategy } from "../../models";
import { CableBase } from "../base";
import { checkMarketRules, checkMktRulesForMultiBusinessFormAndConfirmIfNeed, checkNextCommoditySignupRuleForAlpha, checkNextTimeForAlpha, resolveCommodityAuditProps } from "../mixins";
import { weekendOnePurchaseCable } from "./WeekendOnePurchase";


interface NChooseMPlaydata {
  activityLimitRule: {
    userDayOrderLimit: number;
  };
  activityMarketingInfo: {
    commodityLimitPerStoreAndOrder: number;
    budgetId: number;
    budgetName: string;
    deliveryType: number;
    userScope: number;
    discountPriceRangeType: number;
  };
  activityType: number;
  period?: {
    openTime: string;
    closeTime: string;
    weekday: string;
  };
}

const NChooseM: ActivityStrategy = {
  activityType: ActivityTypeEnum.N_CHOOSE_M,
  generation: "v2",
  cable: ({ investmentSchema }) => {
    if (investmentSchema === InvestmentSchemaEnum.WEEKEND_SPLIT_PURCHASE) {
      return weekendOnePurchaseCable;
    } else {
      return {
        ...CableBase,
        workspace: "ExukovQrae6xWn2rt4NgG",
        model: "6rwApBQ1fs7syQXvTNieb",
        async checkNext(checkNextContext, creationContext, history) {
          const { step, dataStore } = checkNextContext;
          if(step.name === "baseinfo") {
            const timeError = await checkNextTimeForAlpha(checkNextContext, creationContext);
            if(timeError) {
              return timeError;
            }
          }
          if (step.name === "baseinfo") {
            // 重置 auditUpgradeForMultiMktRule，避免重新回到第一步重新选择业务线和业态的情况
            dataStore._session.auditUpgradeForMultiMktRule = undefined;
            // 校验是否跨业态，并且返回弹窗点击结果
            const { conflicted, userConfirm } =
              await checkMktRulesForMultiBusinessFormAndConfirmIfNeed(
                creationContext,
                dataStore.data.baseinfo
              );
            if (!conflicted) {
              // 没有错误信息，不需要拦截进入下一步
              return;
            }
            if (userConfirm) {
              // 有错误，并且用户二次确认了，确认需提交审批
              dataStore._session.auditUpgradeForMultiMktRule = true;
              // 不需要拦截进入下一步
              return;
            } else {
              // 有错误，单用户未二次确认，页面停留在当前步骤
              return { silent: true };
            }
          }
          if(step.name === "scope") {
            const commoditySignupRuleError = await checkNextCommoditySignupRuleForAlpha(checkNextContext, creationContext);
            if(commoditySignupRuleError) {
              return commoditySignupRuleError;
            }
          }
          if (step.name === "commodityRules") {
            const { baseinfo, scope, commodityRules, playdata } =
              dataStore.data;
            const commodityAuditProps: any = resolveCommodityAuditProps(scope);

            const viewJsonDto = {
              activityRule:{
                platformSubsidyRateUpperLimit: baseinfo?.platformSubsidyRateUpperLimit,
              },
              activityLimitRule: {
                userDayOrderLimit: playdata.data.userDayOrderLimit.value,
              },
              activityMarketingInfo: {
                commodityLimitPerStoreAndOrder:
                  playdata.data.commodityLimitPerStoreAndOrder.value,
                budgetId: commodityRules.budgetId,
                budgetName: commodityRules.budgetName,
                deliveryType: playdata.data.effectiveChannel,
                userScope: playdata.data.userScope,
                discountPriceRangeType: commodityRules.discountPriceRangeType,
              },
              activityType: ActivityTypeEnum.N_CHOOSE_M,
              period: {
                closeTime: baseinfo.activityTime.range.end + ":59",
                openTime: baseinfo.activityTime.range.start + ":00",
                weekday: (baseinfo.activityTime.weeks || []).join(","),
              },
            }
            const payload: any = {
              // 活动类型信息
              templateId: creationContext.templateId,
              investmentType: creationContext.investmentType,
              investmentSchema: creationContext.investmentSchema,
              activityType: ActivityTypeEnum.N_CHOOSE_M,

              // 基本信息
              name: baseinfo.name,
              remark: baseinfo.remark,
              description: baseinfo.description,
              beginTime: baseinfo.activityTime.start.toDate().getTime(),
              endTime: maskMS(baseinfo.activityTime.end.toDate().getTime()),
              allowCancel: baseinfo.allowCancel,
              signUpShopType: baseinfo.signUpShopType,
              signUpStartTime: baseinfo.signupTime.start.toDate().getTime(),
              signUpEndTime: maskMS(baseinfo.signupTime.end.toDate().getTime()),
              subsidyType: creationContext?.subsidyType,
              manageOrgList: baseinfo?.manageOrgList,

              // 招商范围信息
              shopPoolId: scope.pool.value,
              shopPoolName: scope.pool.label,

              // 自动审核规则
              ...commodityAuditProps,

              // 降套补 商品清退规则
              commodityRemoveCondition: {
                removeCondition: scope.removeCondition,
              },

              auditMemberList:
                baseinfo.auditMemberList?.length > 0
                  ? baseinfo.auditMemberList
                  : null,

              // 报名规则
              reviewRuleId: scope.rule?.ruleId,
              reviewRuleName: scope.rule?.name,

              // 玩法设置
              viewJson: JSON.stringify(viewJsonDto),

              // 商品规则
              uploadPath: commodityRules.uploadPath,
            };

            const { hide } = loadingDialog.show({
              message: "活动创建中...",
            });
            try {
              const {
                interrupt,
                auditUpgradeForCheckMktRuleUnPass,
                auditUpgradeForMultiMktRule,
              } = await checkMarketRules(
                dataStore,
                creationContext,
                JSON.stringify(viewJsonDto)
              );
              if (interrupt) {
                return { silent: true };
              }

              payload.auditUpgradeForCheckMktRuleUnPass =
                auditUpgradeForCheckMktRuleUnPass;
              payload.auditUpgradeForMultiMktRule = auditUpgradeForMultiMktRule;  
                      
              const res = await api.subActivity.create(payload);
              if (res.success) {
                Message.success("创建成功");
                if (creationContext.activitySceneId) {
                  history.push(
                    "/sub-activity/" +
                      res.data +
                      "?activitySceneId=" +
                      creationContext.activitySceneId +
                      "&activityPlanId=" +
                      creationContext.activityPlanId
                  );
                } else {
                  history.push("/sub-activity/" + res.data);
                }
              } else {
                return { message: res.errorMessage };
              }
            } catch (e: any) {
              return { message: e?.message || "系统异常" };
            } finally {
              hide();
            }
          }
        },
      };
    }
  },
  hasAgentSubsidy() {
    return false;
  },
  isSupportAgent: false,
  isOfflineEnabled: function (state: number): boolean {
    // 活动状态: 1:未开始 2:活动中 3:活动暂停 4:活动取消 5:活动结束 8: 预算熔断
    return [1, 2, 3, 8].includes(state);
  },
  isDingtalkLinkEnabled: () => true,
  hasAuditMemberList: () => true,
  isSupportModifyStock: () => false,
  isItemAuditStatsVisible: true,
  isDetailInviteStatsOnMainActivityTableVisible: true,
  renderPlayDataInfoForTableView: function (): JSX.Element {
    return <></>;
  },
  gotoLegacyDetail: function (): void {},
  renderActivityRuleForTable(viewJson: any) {
    try {
      const data: NChooseMPlaydata = JSON.parse(viewJson);
      let result =
        data.activityMarketingInfo.discountPriceRangeType === 1
          ? "价格固定"
          : "价格区间";
      return (
        <>
          <div>{result}</div>
        </>
      );
    } catch (e) {
      console.error(e);
      return <></>;
    }
  },
  renderActivityRuleForTableLegacy: function () {},
  async marketPlayMapper(viewJson: any, baseInfo: any) {
    try {
      const viewDto = JSON.parse(viewJson) || {};
      const { activityLimitRule = {}, activityMarketingInfo = {} } =
        viewDto || {};
      let creator = {}; // 创建人名称、创建人id
      let name = ""; // 玩法名称
      let userDayOrderLimit = {}; // 用户购买限定
      let userScope = ""; // 用户类型
      let commodityLimitPerStoreAndOrder = {}; // 商品数量限定（M值）
      let deliveryType = ""; // 生效渠道
      let discountPriceRangeType = ""; // 优惠形式
      let budget = {}; //  预算
      let model = {};
      
      if (isValueSet(activityLimitRule.userDayOrderLimit)) {
        userDayOrderLimit = {
          value: activityLimitRule.userDayOrderLimit + "",
        };
      }
      if (isValueSet(activityMarketingInfo.commodityLimitPerStoreAndOrder)) {
        commodityLimitPerStoreAndOrder = {
          value: activityMarketingInfo.commodityLimitPerStoreAndOrder + "",
        };
      }
      if (
        isValueSet(baseInfo.createdUserId) &&
        isValueSet(baseInfo.createdUserName)
      ) {
        creator = {
          name: `${baseInfo.createdUserName}`,
          id: baseInfo.createdUserId,
        };
      }
      if (isValueSet(baseInfo.name)) {
        name = baseInfo.name;
      }
      if (isValueSet(activityMarketingInfo.deliveryType)) {
        deliveryType = activityMarketingInfo.deliveryType + "";
      }
      if (isValueSet(activityMarketingInfo.discountPriceRangeType)) {
        discountPriceRangeType = activityMarketingInfo.discountPriceRangeType;
      }
      if (isValueSet(activityMarketingInfo.userScope)) {
        userScope = activityMarketingInfo.userScope + "";
      }
      if (
        isValueSet(activityMarketingInfo.budgetName) &&
        isValueSet(activityMarketingInfo.budgetId)
      ) {
        budget = {
          budgetName: activityMarketingInfo.budgetName,
          budgetId: activityMarketingInfo.budgetId + "",
        };
      }
      try {
        model = await api.subActivity.getComponentDataModel(
          "6v4M8woywiK9aecPk9yWLh",
          "7oC6O4JAPD57lpTB0h7uZN"
        );
      } catch (e) {
        console.error(e);
      }
      const marketPlay = {
        instance: {
          fields: {
            creator: JSON.stringify(creator),
            name: name,
            userDayOrderLimit: JSON.stringify(userDayOrderLimit),
            userScope,
            commodityLimitPerStoreAndOrder: JSON.stringify(
              commodityLimitPerStoreAndOrder
            ),
            effectiveChannel: deliveryType,
            discountPriceRangeType: JSON.stringify(discountPriceRangeType),
            budget: JSON.stringify(budget),
            platformSubsidyRateUpperLimit: viewDto?.activityRule?.platformSubsidyRateUpperLimit,
          },
        },
        model: model,
      };
      return marketPlay;
    } catch (e) {
      return null;
    }
  },
};

export default NChooseM;
