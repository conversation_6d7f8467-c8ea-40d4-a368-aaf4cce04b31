import { CableBase } from "../base";
import { Message } from "@alifd/next";
import * as api from "../../api";
import { checkMarketRules, checkMktRulesForMultiBusinessFormAndConfirmIfNeed, checkNextCommoditySignupRuleForAlpha, checkNextTimeForAlpha, resolveCommodityAuditProps } from "../mixins";
import { ActivityTypeEnum } from "../../constants";
import loadingDialog from "../../components/LoadingDialog";
import { CheckNextContext, CreationContext } from "../../models";
import { maskMS } from "../../common";

// interface WeekendOnePurchasePlaydata {
//   activityLimitRule: {
//     userDayOrderLimit: number;
//     userDayCountLimit: number; // 每人每天限单，一分购独有
//   };
//   activityRule: {
//     discount: 1; // 活动价，周末一分购独有
//     discountType: 3; // 周末一分购独有
//   };
//   activityMarketingInfo: {
//     commodityLimitPerStoreAndOrder: number;
//     budgetId: number;
//     budgetName: string;
//     deliveryType: number;
//     userScope: number;
//     discountPriceRangeType: number;
//   };
//   activityType: number;
//   commodityListFilePath?: string; // 周末一分购upc文件
//   periodList?: {
//     openTime: string;
//     closeTime: string;
//     weekday: string;
//   }[];
// }

export const weekendOnePurchaseCable = {
  ...CableBase,
  workspace: "ExukovQrae6xWn2rt4NgG",
  model: "8UdnF3C65lG6PneUe4Z8hm",
  async checkNext(
    checkNextContext: CheckNextContext,
    creationContext: CreationContext,
    history: any
  ) {
    const { step, dataStore } = checkNextContext;
    if(step.name === "baseinfo") {
      const timeError = await checkNextTimeForAlpha(checkNextContext, creationContext);
      if(timeError) {
        return timeError;
      }
    }
    if (step.name === "baseinfo") {
      // 重置 auditUpgradeForMultiMktRule，避免重新回到第一步重新选择业务线和业态的情况
      dataStore._session.auditUpgradeForMultiMktRule = undefined;
      // 校验是否跨业态，并且返回弹窗点击结果
      const { conflicted, userConfirm } =
        await checkMktRulesForMultiBusinessFormAndConfirmIfNeed(
          creationContext,
          dataStore.data.baseinfo
        );
      if (!conflicted) {
        // 没有错误信息，不需要拦截进入下一步
        return;
      }
      if (userConfirm) {
        // 有错误，并且用户二次确认了，确认需提交审批
        dataStore._session.auditUpgradeForMultiMktRule = true;
        // 不需要拦截进入下一步
        return;
      } else {
        // 有错误，单用户未二次确认，页面停留在当前步骤
        return { silent: true };
      }
    }
    if(step.name === "scope") {
      const commoditySignupRuleError = await checkNextCommoditySignupRuleForAlpha(checkNextContext, creationContext);
      if(commoditySignupRuleError) {
        return commoditySignupRuleError;
      }
    }
    if (step.name === "palyDataWithWeekend") {
      const { baseinfo, scope, palyDataWithWeekend } = dataStore.data;
      const commodityAuditProps: any = resolveCommodityAuditProps(scope);
      const hasPlatformSubsidy =
        palyDataWithWeekend?.commodityRules?.checkExcel?.extInfoMap
          .invitedUpcHasPlatformSubsidy === "true";
      const viewJsonDto = {
        activityLimitRule: {
          userDayOrderLimit: palyDataWithWeekend.userDayOrderLimit,
          userDayCountLimit: palyDataWithWeekend.userDayCountLimit,
        },
        activityRule: {
          discount: 1,
          discountType: 3,
          platformSubsidyRateUpperLimit: baseinfo.platformSubsidyRateUpperLimit,
        },
        activityMarketingInfo: {
          commodityLimitPerStoreAndOrder:
            palyDataWithWeekend.commodityLimitPerStoreAndOrder,
          budgetId: hasPlatformSubsidy
            ? palyDataWithWeekend?.budget?.budgetId
            : undefined,
          budgetName: hasPlatformSubsidy
            ? palyDataWithWeekend?.budget?.budgetName
            : undefined,
          deliveryType: palyDataWithWeekend.deliveryType,
          userScope: palyDataWithWeekend.userScope,
          discountPriceRangeType: palyDataWithWeekend.discountPriceRangeType,
        },
        activityType: ActivityTypeEnum.N_CHOOSE_M,
        commodityListFilePath: palyDataWithWeekend.commodityRules.uploadPath,
        periodList: palyDataWithWeekend.weekTimeRange.map((time: any) => {
          return {
            closeTime: time.timeRange.closeTime + ":59",
            openTime: time.timeRange.openTime + ":00",
            weekday: time.day,
          };
        }),
      }
      const payload = {
        // 活动类型信息
        templateId: creationContext.templateId,
        investmentType: creationContext.investmentType,
        investmentSchema: creationContext.investmentSchema,
        activityType: ActivityTypeEnum.N_CHOOSE_M,

        // 基本信息
        name: baseinfo.name,
        remark: baseinfo.remark,
        description: baseinfo.description,
        beginTime: baseinfo.activityTime.start.toDate().getTime(),
        endTime: maskMS(baseinfo.activityTime.end.toDate().getTime()),
        allowCancel: baseinfo.allowCancel,
        signUpShopType: baseinfo.signUpShopType,
        signUpStartTime: baseinfo.signupTime.start.toDate().getTime(),
        signUpEndTime: maskMS(baseinfo.signupTime.end.toDate().getTime()),
        subsidyType: creationContext?.subsidyType,
        manageOrgList: baseinfo?.manageOrgList,

        // 招商范围信息
        shopPoolId: scope.pool.value,
        shopPoolName: scope.pool.label,

        // 自动审核规则
        ...commodityAuditProps,

        auditMemberList:
          baseinfo.auditMemberList?.length > 0
            ? baseinfo.auditMemberList
            : null,

        // 报名规则
        reviewRuleId: scope.rule?.ruleId,
        reviewRuleName: scope.rule?.name,

        // 降套补 商品清退规则
        commodityRemoveCondition: {
          removeCondition: scope.removeCondition,
        },
        
        // 玩法设置
        viewJson: JSON.stringify(viewJsonDto),
        // 商品规则
        uploadPath: palyDataWithWeekend.commodityRules.uploadPath,
        uploadTemplateVersion: '1',// 区分商品规则版本，归一码版本为 1，固定值
        uploadTotalUpc: palyDataWithWeekend.commodityRules?.checkExcel?.uploadTotalUpc,// 上传归一码数量
      };

      const { hide } = loadingDialog.show({
        message: "活动创建中...",
      });
      try {
        const {
          interrupt,
          auditUpgradeForCheckMktRuleUnPass,
          auditUpgradeForMultiMktRule,
        } = await checkMarketRules(
          dataStore,
          creationContext,
          JSON.stringify(viewJsonDto)
        );
        if (interrupt) {
          return { silent: true };
        }

        payload.auditUpgradeForCheckMktRuleUnPass =
          auditUpgradeForCheckMktRuleUnPass;
        payload.auditUpgradeForMultiMktRule =
          auditUpgradeForMultiMktRule;  
        const res = await api.subActivity.create(payload);
        if (res.success) {
          Message.success("创建成功");
          if (creationContext.activitySceneId) {
                  history.push(
                    "/sub-activity/" +
                      res.data +
                      "?activitySceneId=" +
                      creationContext.activitySceneId +
                      "&activityPlanId=" +
                      creationContext.activityPlanId
                  );
                } else {
                  history.push("/sub-activity/" + res.data);
                }
        } else {
          return { message: res.errorMessage };
        }
      } catch (e: any) {
        return { message: e?.message || "系统异常" };
      } finally {
        hide();
      }
    }
  },
};
