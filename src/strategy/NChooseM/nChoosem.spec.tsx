// @ts-ignore
import { handleAPI, clearAPI } from "@ali/alsc-gateway-web-client";
import moment from "moment";

import {
  ActivityTypeEnum,
  InvestmentSchemaEnum,
  InvestmentTypeEnum,
} from "../../constants";
import { CableInst } from "../../models";

import strategy from ".";

const mockHistory = { push: () => {} };

describe("N选M", () => {
  describe("单活动", () => {
    let fakeActivityId = 10001;
    let createActivityRequests: any[] = [];

    beforeEach(() => {
      handleAPI(
        "ele-newretail-investment.NewretailInvestmentForbiddenWriteYundingService.isKunlunForbiddenWrite",
        async () => ({ data: false })
      );
      handleAPI(
        "ele-newretail-investment.NewretailInvestmentActivityYundingService.createInvestmentActivity",
        async (req: { params: any[] }) => {
          createActivityRequests.push(req);
          return {
            success: true,
            data: ++fakeActivityId,
          };
        }
      );
    });

    afterEach(() => {
      createActivityRequests = [];
      clearAPI();
    });

    test("日常模版-checkNext", async () => {
      const creationContext = {
        createActiivtyStartTime: 0,
        createActivitySessionId: "0",
        isSingleActivity: true,
        investmentSchema: InvestmentSchemaEnum.RICHANG,
        investmentType: InvestmentTypeEnum.RICHANG,
        activityType: ActivityTypeEnum.N_CHOOSE_M,
        fineGrainedActivityTimeDisabled: true,
        copy: false,
      };

      const { checkNext } = (
        strategy.cable as (option: {
          investmentSchema: number;
          investmentType: number;
        }) => CableInst
      )({
        investmentSchema: creationContext.investmentSchema,
        investmentType: creationContext.investmentType,
      });

      const dataStore = {
        data: {
          baseinfo: {
            actManageOrgLv1Id: "yygs1_hyyy_20250002",
            manageOrgLv1IdList: ["yygs1_hyyy_20250002", "yygs1_hyyy_20250003"],
            activityPlanId: undefined,
            mainCategoryIdList: undefined,
            allowCancel: 2,
            signUpShopType: 0,
            activityTime: {
              weeks: [0, 1, 2, 3, 4, 5, 6],
              range: {
                start: "00:00",
                end: "23:59",
              },
              start: moment("2023-07-12T16:00:00.000Z"),
              end: moment("2023-07-20T15:59:59.000Z"),
            },
            applyPlatform: 1,
            activityType: 2,
            richDescription: {
              version: "v1",
              content: [
                {
                  type: "paragraph",
                  children: [
                    {
                      text: "test",
                    },
                  ],
                },
              ],
              text: "test",
            },
            name: "test",
            remark: "test",
            signupTime: {
              start: moment("2023-07-12T16:00:00.000Z"),
              end: moment("2023-07-20T15:59:59.000Z"),
            },
          },
          commodityRules: {
            discountPriceRangeType: 1,
            range: {},
            fileUploadStatus: 1,
            uploadPath:
              "https://bwm-private-b.oss-cn-hangzhou.aliyuncs.com/mkt/329684-07090112-c73e-4360-bf43-3b41f1ea5106.xlsx",
            checkExcel: {
              checkMessage: null,
              checkResult: null,
              errorRow: 0,
              extInfoMap: {
                invitedUpcHasPlatformSubsidy: "true",
              },
              repeatRow: 0,
              successRow: 19,
              totalRow: 19,
            },
            url: {
              uid: "lktqebfx",
            },
            budgetId: "400000000004208875",
            budgetName: "零售预发测试活动预算",
          },
          playdata: {
            data: {
              _workspace_uuid_: "6v4M8woywiK9aecPk9yWLh",
              creator:
                '{"id":"329684","prefix":"yufanguo.gyf","name":"郭宇帆"}',
              itemDiscountType: "1",
              name: "测试",
              userScope: "0",
              commodityLimitPerStoreAndOrder: '{"value":1}',
              userDayOrderLimit: '{"value":4}',
              effectiveChannel: "1",
            },
          },
          scope: {
            pool: {
              value: 284062,
              label: "测试门店zzg",
              count: 5,
            },
            commodityAuditType: 1,
            removeCondition: [6],
          },
        },
      };

      const checkNextContext = {
        step: { name: "commodityRules", title: "" },
        dataStore,
      };

      await checkNext(checkNextContext, creationContext, mockHistory);

      expect(
        createActivityRequests[createActivityRequests.length - 1]
      ).toStrictEqual({
        apiKey:
          "ele-newretail-investment.NewretailInvestmentActivityYundingService.createInvestmentActivity",
        params: [
          {
            actManageOrgLv1Id: "yygs1_hyyy_20250002",
            manageOrgLv1IdList: ["yygs1_hyyy_20250002", "yygs1_hyyy_20250003"],
            activityPlanId: undefined,
            mainCategoryIdList: undefined,
            activitySceneId: undefined,
            activityType: 1000029,
            allowCancel: 2,
            auditMemberList: null,
            beginTime: 1689177600000,
            subsidyType: undefined,
            commodityAuditType: 1,
            copyActivityId: undefined,
            description: undefined,
            endTime: 1689868799000,
            idempotentKey: "undefined",
            investmentSchema: 0,
            investmentType: 2,
            name: "test",
            remark: "test",
            reviewRuleId: undefined,
            reviewRuleName: undefined,
            commodityRemoveCondition: {
              removeCondition: [6],
            },
            shopPoolId: 284062,
            shopPoolName: "测试门店zzg",
            signUpEndTime: 1689868799000,
            signUpShopType: 0,
            signUpStartTime: 1689177600000,
            templateId: undefined,
            uploadPath:
              "https://bwm-private-b.oss-cn-hangzhou.aliyuncs.com/mkt/329684-07090112-c73e-4360-bf43-3b41f1ea5106.xlsx",
            viewJson:
              '{"activityLimitRule":{},"activityMarketingInfo":{"budgetId":"400000000004208875","budgetName":"零售预发测试活动预算","deliveryType":"1","userScope":"0","discountPriceRangeType":1},"activityType":1000029,"period":{"closeTime":"23:59:59","openTime":"00:00:00","weekday":"0,1,2,3,4,5,6"}}',
          },
        ],
      });
    });

    test("周末一分购-checkNext: 有预算", async () => {
      const creationContext = {
        createActiivtyStartTime: 0,
        createActivitySessionId: "0",
        isSingleActivity: true,
        investmentSchema: InvestmentSchemaEnum.WEEKEND_SPLIT_PURCHASE,
        investmentType: InvestmentTypeEnum.RICHANG,
        activityType: ActivityTypeEnum.N_CHOOSE_M,
        fineGrainedActivityTimeDisabled: true,
        copy: false,
      };

      const { checkNext } = (
        strategy.cable as (option: {
          investmentSchema: number;
          investmentType: number;
        }) => CableInst
      )({
        investmentSchema: creationContext.investmentSchema,
        investmentType: creationContext.investmentType,
      });

      const dataStore = {
        data: {
          baseinfo: {
            actManageOrgLv1Id: "yygs1_hyyy_20250002",
            manageOrgLv1IdList: ["yygs1_hyyy_20250002", "yygs1_hyyy_20250003"],
            allowCancel: 2,
            signUpShopType: 0,
            activityTime: {
              weeks: [0, 1, 2, 3, 4, 5, 6],
              range: {
                start: "00:00",
                end: "23:59",
              },
              start: moment("2023-07-12T16:00:00.000Z"),
              end: moment("2023-07-20T15:59:59.000Z"),
            },
            applyPlatform: 1,
            activityType: 2,
            richDescription: {
              version: "v1",
              content: [
                {
                  type: "paragraph",
                  children: [
                    {
                      text: "test",
                    },
                  ],
                },
              ],
              text: "test",
            },
            name: "test",
            remark: "test",
            signupTime: {
              start: moment("2023-07-12T16:00:00.000Z"),
              end: moment("2023-07-20T15:59:59.000Z"),
            },
          },
          palyDataWithWeekend: {
            userScope: "0",
            deliveryType: "1",
            weekTimeRange: [
              {
                day: 1,
                timeRange: {
                  openTime: "00:00",
                  closeTime: "23:59",
                },
              },
              {
                day: 2,
                timeRange: {
                  openTime: "00:00",
                  closeTime: "23:59",
                },
              },
              {
                day: 3,
                timeRange: {
                  openTime: "00:00",
                  closeTime: "23:59",
                },
              },
              {
                day: 4,
                timeRange: {
                  openTime: "00:00",
                  closeTime: "23:59",
                },
              },
              {
                day: 5,
                timeRange: {
                  openTime: "00:00",
                  closeTime: "23:59",
                },
              },
              {
                day: 6,
                timeRange: {
                  openTime: "00:00",
                  closeTime: "23:59",
                },
              },
              {
                day: 0,
                timeRange: {
                  openTime: "00:00",
                  closeTime: "23:59",
                },
              },
            ],
            discountPriceRangeType: "1",
            userDayCountLimit: 1,
            userDayOrderLimit: 2,
            commodityLimitPerStoreAndOrder: 1,
            commodityRules: {
              uploadPath:
                "https://bwm-private-b.oss-cn-hangzhou.aliyuncs.com/mkt/329684-95a92bb2-45e6-498d-975d-a57d96a7ca9f.xlsx",
              fileUploadStatus: 1,
              checkExcel: {
                checkMessage: null,
                checkResult: null,
                errorRow: 0,
                extInfoMap: {
                  invitedUpcHasPlatformSubsidy: "true",
                },
                repeatRow: 0,
                successRow: 5,
                totalRow: 5,
              },
              url: {
                uid: "lkujos7k",
              },
            },
            budget: {
              budgetId: "400000000004208875",
              budgetName: "零售预发测试活动预算",
            },
          },
          scope: {
            pool: {
              value: 284062,
              label: "测试门店zzg",
              count: 5,
            },
            commodityAuditType: 1,
            removeCondition: [6],
          },
        },
      };

      const checkNextContext = {
        step: { name: "palyDataWithWeekend", title: "" },
        dataStore,
      };

      await checkNext(checkNextContext, creationContext, mockHistory);

      expect(
        createActivityRequests[createActivityRequests.length - 1]
      ).toStrictEqual({
        apiKey:
          "ele-newretail-investment.NewretailInvestmentActivityYundingService.createInvestmentActivity",
        params: [
          {
            actManageOrgLv1Id: "yygs1_hyyy_20250002",
            manageOrgLv1IdList: ["yygs1_hyyy_20250002", "yygs1_hyyy_20250003"],
            activitySceneId: undefined,
            activityPlanId: undefined,
            mainCategoryIdList: undefined,
            activityType: 1000029,
            allowCancel: 2,
            auditMemberList: null,
            beginTime: 1689177600000,
            subsidyType: undefined,
            commodityAuditType: 1,
            copyActivityId: undefined,
            description: undefined,
            endTime: 1689868799000,
            idempotentKey: "undefined",
            investmentSchema: 20,
            investmentType: 2,
            name: "test",
            remark: "test",
            reviewRuleId: undefined,
            reviewRuleName: undefined,
            shopPoolId: 284062,
            shopPoolName: "测试门店zzg",
            signUpEndTime: 1689868799000,
            signUpShopType: 0,
            signUpStartTime: 1689177600000,
            templateId: undefined,
            commodityRemoveCondition: {
              removeCondition: [6],
            },
            uploadPath:
              "https://bwm-private-b.oss-cn-hangzhou.aliyuncs.com/mkt/329684-95a92bb2-45e6-498d-975d-a57d96a7ca9f.xlsx",
            uploadTemplateVersion: "1",
            uploadTotalUpc: undefined,
            viewJson:
              '{"activityLimitRule":{"userDayOrderLimit":2,"userDayCountLimit":1},"activityRule":{"discount":1,"discountType":3},"activityMarketingInfo":{"commodityLimitPerStoreAndOrder":1,"budgetId":"400000000004208875","budgetName":"零售预发测试活动预算","deliveryType":"1","userScope":"0","discountPriceRangeType":"1"},"activityType":1000029,"commodityListFilePath":"https://bwm-private-b.oss-cn-hangzhou.aliyuncs.com/mkt/329684-95a92bb2-45e6-498d-975d-a57d96a7ca9f.xlsx","periodList":[{"closeTime":"23:59:59","openTime":"00:00:00","weekday":1},{"closeTime":"23:59:59","openTime":"00:00:00","weekday":2},{"closeTime":"23:59:59","openTime":"00:00:00","weekday":3},{"closeTime":"23:59:59","openTime":"00:00:00","weekday":4},{"closeTime":"23:59:59","openTime":"00:00:00","weekday":5},{"closeTime":"23:59:59","openTime":"00:00:00","weekday":6},{"closeTime":"23:59:59","openTime":"00:00:00","weekday":0}]}',
          },
        ],
      });
    });

    test("周末一分购-checkNext: 无预算", async () => {
      const creationContext = {
        createActiivtyStartTime: 0,
        createActivitySessionId: "0",
        isSingleActivity: true,
        investmentSchema: InvestmentSchemaEnum.WEEKEND_SPLIT_PURCHASE,
        investmentType: InvestmentTypeEnum.RICHANG,
        activityType: ActivityTypeEnum.N_CHOOSE_M,
        fineGrainedActivityTimeDisabled: true,
        copy: false,
      };

      const { checkNext } = (
        strategy.cable as (option: {
          investmentSchema: number;
          investmentType: number;
        }) => CableInst
      )({
        investmentSchema: creationContext.investmentSchema,
        investmentType: creationContext.investmentType,
      });

      const dataStore = {
        data: {
          baseinfo: {
            actManageOrgLv1Id: "yygs1_hyyy_20250002",
            manageOrgLv1IdList: ["yygs1_hyyy_20250002", "yygs1_hyyy_20250003"],
            allowCancel: 2,
            signUpShopType: 0,
            activityTime: {
              weeks: [0, 1, 2, 3, 4, 5, 6],
              range: {
                start: "00:00",
                end: "23:59",
              },
              start: moment("2023-07-12T16:00:00.000Z"),
              end: moment("2023-07-20T15:59:59.000Z"),
            },
            applyPlatform: 1,
            activityType: 2,
            richDescription: {
              version: "v1",
              content: [
                {
                  type: "paragraph",
                  children: [
                    {
                      text: "test",
                    },
                  ],
                },
              ],
              text: "test",
            },
            name: "test",
            remark: "test",
            signupTime: {
              start: moment("2023-07-12T16:00:00.000Z"),
              end: moment("2023-07-20T15:59:59.000Z"),
            },
          },
          palyDataWithWeekend: {
            userScope: "0",
            deliveryType: "1",
            weekTimeRange: [
              {
                day: 1,
                timeRange: {
                  openTime: "00:00",
                  closeTime: "23:59",
                },
              },
              {
                day: 2,
                timeRange: {
                  openTime: "00:00",
                  closeTime: "23:59",
                },
              },
              {
                day: 3,
                timeRange: {
                  openTime: "00:00",
                  closeTime: "23:59",
                },
              },
              {
                day: 4,
                timeRange: {
                  openTime: "00:00",
                  closeTime: "23:59",
                },
              },
              {
                day: 5,
                timeRange: {
                  openTime: "00:00",
                  closeTime: "23:59",
                },
              },
              {
                day: 6,
                timeRange: {
                  openTime: "00:00",
                  closeTime: "23:59",
                },
              },
              {
                day: 0,
                timeRange: {
                  openTime: "00:00",
                  closeTime: "23:59",
                },
              },
            ],
            discountPriceRangeType: "1",
            userDayCountLimit: 1,
            userDayOrderLimit: 2,
            commodityLimitPerStoreAndOrder: 1,
            commodityRules: {
              uploadPath:
                "https://bwm-private-b.oss-cn-hangzhou.aliyuncs.com/mkt/329684-95a92bb2-45e6-498d-975d-a57d96a7ca9f.xlsx",
              fileUploadStatus: 1,
              checkExcel: {
                checkMessage: null,
                checkResult: null,
                errorRow: 0,
                extInfoMap: {
                  invitedUpcHasPlatformSubsidy: "false",
                },
                repeatRow: 0,
                successRow: 5,
                totalRow: 5,
              },
              url: {
                uid: "lkujos7k",
              },
            },
            budget: {
              budgetId: undefined,
              budgetName: undefined,
            },
          },
          scope: {
            pool: {
              value: 284062,
              label: "测试门店zzg",
              count: 5,
            },
            commodityAuditType: 1,
            removeCondition: [6],
          },
        },
      };

      const checkNextContext = {
        step: { name: "palyDataWithWeekend", title: "" },
        dataStore,
      };

      await checkNext(checkNextContext, creationContext, mockHistory);

      expect(
        createActivityRequests[createActivityRequests.length - 1]
      ).toStrictEqual({
        apiKey:
          "ele-newretail-investment.NewretailInvestmentActivityYundingService.createInvestmentActivity",
        params: [
          {
            actManageOrgLv1Id: "yygs1_hyyy_20250002",
            manageOrgLv1IdList: ["yygs1_hyyy_20250002", "yygs1_hyyy_20250003"],
            activitySceneId: undefined,
            activityPlanId: undefined,
            mainCategoryIdList: undefined,
            activityType: 1000029,
            allowCancel: 2,
            auditMemberList: null,
            beginTime: 1689177600000,
            subsidyType: undefined,
            commodityAuditType: 1,
            copyActivityId: undefined,
            description: undefined,
            endTime: 1689868799000,
            idempotentKey: "undefined",
            investmentSchema: 20,
            investmentType: 2,
            name: "test",
            remark: "test",
            reviewRuleId: undefined,
            reviewRuleName: undefined,
            shopPoolId: 284062,
            shopPoolName: "测试门店zzg",
            signUpEndTime: 1689868799000,
            signUpShopType: 0,
            signUpStartTime: 1689177600000,
            templateId: undefined,
            commodityRemoveCondition: {
              removeCondition: [6],
            },
            uploadPath:
              "https://bwm-private-b.oss-cn-hangzhou.aliyuncs.com/mkt/329684-95a92bb2-45e6-498d-975d-a57d96a7ca9f.xlsx",
            uploadTemplateVersion: "1",
            uploadTotalUpc: undefined,
            viewJson:
              '{"activityLimitRule":{"userDayOrderLimit":2,"userDayCountLimit":1},"activityRule":{"discount":1,"discountType":3},"activityMarketingInfo":{"commodityLimitPerStoreAndOrder":1,"deliveryType":"1","userScope":"0","discountPriceRangeType":"1"},"activityType":1000029,"commodityListFilePath":"https://bwm-private-b.oss-cn-hangzhou.aliyuncs.com/mkt/329684-95a92bb2-45e6-498d-975d-a57d96a7ca9f.xlsx","periodList":[{"closeTime":"23:59:59","openTime":"00:00:00","weekday":1},{"closeTime":"23:59:59","openTime":"00:00:00","weekday":2},{"closeTime":"23:59:59","openTime":"00:00:00","weekday":3},{"closeTime":"23:59:59","openTime":"00:00:00","weekday":4},{"closeTime":"23:59:59","openTime":"00:00:00","weekday":5},{"closeTime":"23:59:59","openTime":"00:00:00","weekday":6},{"closeTime":"23:59:59","openTime":"00:00:00","weekday":0}]}',
          },
        ],
      });
    });

    test("untils: viewjson => fromData, 日常模版", async () => {
      const viewJson = JSON.stringify({
        activityLimitRule: { userDayOrderLimit: 2 },
        activityMarketingInfo: {
          commodityLimitPerStoreAndOrder: 3,
          deliveryType: 1,
          discountPriceRangeType: 1,
          invitedUpcHasPlatformSubsidy: false,
          userScope: 0,
          budgetId: "400000000004208875",
          budgetName: "零售预发测试活动预算",
        },
        activityType: 1000029,
        attributes: { version: "3.0" },
        commodityListFilePath:
          "https://bwm-private-b.oss-cn-hangzhou.aliyuncs.com/mkt/activityParseResult_2501484100310245843.xlsx?Expires=1698204777&OSSAccessKeyId=LTAIhP07XE91RzLJ&Signature=DNwMDUoZnmzYj0yvor%2FmbqcPkFQ%3D&response-content-disposition=attachment%3Bfilename%3D%E5%95%86%E5%93%81%E8%AF%A6%E6%83%85.xlsx",
        period: {
          closeTime: "23:59:59",
          openTime: "00:00:00",
          weekday: "0,1,2,3,4,5,6",
        },
      });
      const baseInfo = {
        createdUserName: "郭宇帆",
        createdUserId: "329684",
        name: "99",
      };
      const fromData: any = await strategy.marketPlayMapper!(
        viewJson,
        baseInfo,
        {
          investmentSchema: InvestmentSchemaEnum.RICHANG,
        }
      );
      expect(fromData?.instance?.fields).toStrictEqual({
        commodityLimitPerStoreAndOrder: '{"value":"3"}',
        creator: '{"name":"郭宇帆","id":"329684"}',
        discountPriceRangeType: "1",
        effectiveChannel: "1",
        userDayOrderLimit: '{"value":"2"}',
        userScope: "0",
        name: "99",
        budget:
          '{"budgetName":"零售预发测试活动预算","budgetId":"400000000004208875"}',
      });
    });
  });
});
