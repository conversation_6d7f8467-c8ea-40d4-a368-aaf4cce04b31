export interface StoreReducePlaydata {
  activityType: number;
  activityRule: {
    condition: number;
    discount: number;
    platformSubsidy: number;
    shopSubsidy: number;
    agentSubsidy?: number;
  }[];
  activityMarketingInfo: {
    budgetId?: string;
    budgetName?: string;
    deliveryType: number;
    userScope: number;
  };
  // 虽然是数组，但是只会有一个
  period: { openTime: string; closeTime: string }[];
  weekday: string;
}
