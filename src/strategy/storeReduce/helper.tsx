import moment from "moment";
import { parseJsonSafe } from "../../common";
import { ActivityTypeEnum, InvestmentSchemaEnum } from "../../constants";
import { StoreReducePlaydata } from "./types";

function isValueSet(v: any) {
  return v !== "" && v !== null && v !== undefined;
}

interface StoreReduceBaseInfo {
  activityTime: {
    weeks: number[];
    range: {
      type: "all" | "custom";
      start: string;
      end: string;
    };
  };
}

interface RuleItem {
  condition: number;
  discount: number;
  elemeSubsidy: number;
  shopSubsidy: number;
  agentSubsidy: number;
}

interface Budget {
  budgetId?: string;
  budgetName?: string;
}

interface StoreReduceFormData {
  creator: string;
  userScope: string;
  effectiveChannel: string;
  budget?: string;
  rule: string;
  dateRange?: string; // 只有详情展示的时候才需要这个字段
  [prop: string]: unknown;
}

export function transformFormData2ViewJson(options: {
  baseinfo: StoreReduceBaseInfo;
  formData: StoreReduceFormData;
  investmentSchema: InvestmentSchemaEnum;
}): StoreReducePlaydata {
  const rules: RuleItem[] = parseJsonSafe(options.formData.rule);
  const budget: Budget = options.formData.budget
    ? parseJsonSafe(options.formData.budget)
    : null;
  return {
    activityType: ActivityTypeEnum.WHOLE_STORE_FULL_REDUCTION,
    activityRule: rules.map((r) => {
      return {
        condition: r.condition,
        discount: r.discount,
        platformSubsidy: r.elemeSubsidy,
        shopSubsidy: r.shopSubsidy,
        agentSubsidy: r.agentSubsidy,
      };
    }),
    activityMarketingInfo: {
      budgetId: budget?.budgetId,
      budgetName: budget?.budgetName,
      deliveryType: +options.formData.effectiveChannel,
      userScope: +options.formData.userScope,
    },
    weekday: options.baseinfo.activityTime.weeks.join(","),
    period: [
      {
        openTime:
          options.baseinfo.activityTime.range.start.slice(0, "HH:mm".length) +
          ":00",
        closeTime:
          options.baseinfo.activityTime.range.end.slice(0, "HH:mm".length) +
          ":59",
      },
    ],
  };
}

export function transformViewJson2FormData(options: {
  viewJson: string;
  baseinfo: {
    name: string;
    createdUserId: string;
    createdUserName: string;
    beginTime: string; // 示例：2023-07-17T16:00:00.000Z
    endTime: string; // 示例：2023-07-17T16:00:00.000Z
    // 对于详情展示 signUpShopType 是必须提供的
    signUpShopType: 0 | 1 | 2; // 0: 直营+代理  1: 直营  2: 代理
  };
  investmentSchema: InvestmentSchemaEnum;
}): StoreReduceFormData | null {
  try {
    const playdata: StoreReducePlaydata = JSON.parse(options.viewJson);
    const creator = {
      id: options.baseinfo.createdUserId,
      name: options.baseinfo.createdUserName,
    };
    const rules = playdata.activityRule.map((r) => {
      return {
        condition: r.condition,
        discount: r.discount,
        elemeSubsidy: r.platformSubsidy,
        shopSubsidy: r.shopSubsidy,
        agentSubsidy: r.agentSubsidy,
      };
    });
    let budget: Budget | null = null;
    if (
      isValueSet(playdata.activityMarketingInfo.budgetId) &&
      isValueSet(playdata.activityMarketingInfo.budgetName)
    ) {
      budget = {
        budgetId: playdata.activityMarketingInfo.budgetId,
        budgetName: playdata.activityMarketingInfo.budgetName,
      };
    }
    return {
      _signupShopType: `${options.baseinfo.signUpShopType}`,
      name: options.baseinfo.name,
      // 全店满减比较特殊，老的玩法表单是展示活动时间的，这里需要额外做一下活动时间的转换，做一下兼容
      dateRange: JSON.stringify({
        start: moment(options.baseinfo.beginTime).format("YYYY-MM-DD HH:mm:ss"),
        end: moment(options.baseinfo.endTime).format("YYYY-MM-DD HH:mm:ss"),
      }),
      creator: JSON.stringify(creator),
      rule: JSON.stringify(rules),
      userScope: `${playdata.activityMarketingInfo.userScope}`,
      effectiveChannel: `${playdata.activityMarketingInfo.deliveryType}`,
      budget: budget ? JSON.stringify(budget) : undefined,
    };
  } catch (e) {
    return null;
  }
}
