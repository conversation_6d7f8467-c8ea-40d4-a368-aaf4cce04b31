import * as api from "../../api";
import { ActivityTypeEnum } from "../../constants";
import { isValueSet, maskMS } from "../../common";
import { ActivityStrategy, InvestmentActivityDetailDTO } from "../../models"
import { CableBase } from "../base";
import { auditService } from "../service"
import { checkNextTimeForAlpha, genericCreateActivity, gotoLegacyActivityDetailByDefault } from "../mixins";
import { transformFormData2ViewJson, transformViewJson2FormData } from "./helper";
import { StoreReducePlaydata } from "./types";

const StoreReduce: ActivityStrategy = {
  activityType: ActivityTypeEnum.WHOLE_STORE_FULL_REDUCTION,
  cable: {
    ...CableBase,
    workspace: 'ExukovQrae6xWn2rt4NgG',
    model: '3AuehEPZPOB9LHxMEuqpk1',
    async checkNext({ step, dataStore }, creationContext, history) {
      if (step.name === "baseinfo") {
        const timeError = await checkNextTimeForAlpha(
          { step, dataStore },
          creationContext
        );
        if (timeError) {
          return timeError;
        }
      }
      if (step.name === 'playdata') {
        const { baseinfo, playdata, scope } = dataStore.data;
        const viewJson = transformFormData2ViewJson({
          baseinfo,
          formData: playdata?.snapshot?.data,
          investmentSchema: creationContext.investmentSchema
        })
        const payload = {
          // 活动类型信息
          templateId: creationContext.templateId,
          investmentType: creationContext.investmentType,
          investmentSchema: creationContext.investmentSchema,
          activityType: ActivityTypeEnum.WHOLE_STORE_FULL_REDUCTION,
          subsidyType: creationContext?.subsidyType,
          // 基本信息
          name: baseinfo.name,
          description: baseinfo.description,
          remark: baseinfo.remark,
          beginTime: baseinfo.activityTime.start.toDate().getTime(),
          endTime: maskMS(baseinfo.activityTime.end.toDate().getTime()),
          allowCancel: baseinfo.allowCancel,
          signUpShopType: baseinfo.signUpShopType,
          signUpStartTime: baseinfo.signupTime.start.toDate().getTime(),
          signUpEndTime: maskMS(baseinfo.signupTime.end.toDate().getTime()),
          manageOrgList: baseinfo?.manageOrgList,
          // 招商范围信息
          shopPoolId: scope.pool.value,
          shopPoolName: scope.pool.label,
          // 玩法信息
          viewJson: JSON.stringify(viewJson),
          // 不限制库存
          stockType: 0,
        };
        return await genericCreateActivity(payload, history, creationContext);
      }
    },
    auditService
  },
  hasAgentSubsidy(playdata: StoreReducePlaydata) {
    let flag = false;
    try {
      playdata.activityRule.forEach(r => {
        if (isValueSet(r.agentSubsidy) && +r.agentSubsidy! > 0) {
          flag = true
        }
      });
    } catch (e) {
      console.error(e);
    }

    return flag;
  },
  isSupportAgent: true,
  isOfflineEnabled: function (state: number): boolean {
    // 活动状态: 1:未开始 2:活动中 3:活动暂停 4:活动取消 5:活动结束 8: 预算熔断
    return [1, 2, 3, 8].includes(state);
  },
  isDingtalkLinkEnabled: () => false,
  hasAuditMemberList: () => false,
  isSupportModifyStock: () => false,
  isDetailInviteStatsOnMainActivityTableVisible: true,
  renderActivityRuleForTable(viewJson: string) {
    try {
      const data: StoreReducePlaydata = JSON.parse(viewJson);
      return (
        <>
          {data.activityRule.map((r, index) => {
            return (
              <div key={index}>
                满{r.condition / 100}元，减{r.discount / 100}元，平台补贴{r.platformSubsidy / 100}元，商家补贴{r.shopSubsidy / 100}元
                {isValueSet(r.agentSubsidy) && +r.agentSubsidy! > 0 ?
                  <>，代理商补贴{r.agentSubsidy! / 100}元</> :
                  null}
              </div>
            );
          })}
        </>
      );
    } catch (e) {
      console.error(e);
      return '';
    }
  },
  renderPlayDataInfoForTableView: function (): JSX.Element {
    return <></>;
  },
  gotoLegacyDetail: function (act: InvestmentActivityDetailDTO, scene: "lookup" | "examine"): void {
    gotoLegacyActivityDetailByDefault(act, scene);
  },
  renderActivityRuleForTableLegacy: function () {
    return <></>
  },
  marketPlayMapper: async function (
    viewJson: string,
    baseInfo: any,
    option: { investmentSchema: any; marketPlay: any }
  ) {
    if (option.marketPlay) {
      // 活动信息中包含 marketPlay 说明是基于组件链路创建的老活动，直接返回即可
      return option.marketPlay;
    }

    const fields =
      transformViewJson2FormData({
        baseinfo: baseInfo,
        viewJson,
        investmentSchema: option.investmentSchema,
      }) || {};
    const model = await api.subActivity.getComponentDataModel(
      "6v4M8woywiK9aecPk9yWLh",
      "5eKXPRRkkfY5CU7XJPB3eJ"
    );
    const marketPlay = { instance: { fields }, model };
    return marketPlay;
  },
}

export default StoreReduce
