import { InvestmentSchemaEnum } from "../../constants";
import {
  transformFormData2ViewJson,
  transformViewJson2FormData,
} from "./helper";

describe("storeReduce", () => {
  describe("helper", () => {
    describe("transformFormData2ViewJson", () => {
      test("日常招商-多阶梯-包含预算-自定义生效时段", () => {
        const viewJson = transformFormData2ViewJson({
          baseinfo: {
            activityTime: {
              weeks: [1, 2, 3, 4, 5, 6],
              range: {
                type: "custom",
                start: "00:00",
                end: "22:59",
              },
            },
          },
          formData: {
            budget:
              '{"budgetId":"400000000003202329","budgetName":"2022招商活动测试预算"}',
            _signupShopType: "0",
            weekday: "1,2,3,4,5,6",
            userScope: "0",
            name: "yc-全店满减",
            creator: '{"id":"213137","prefix":"yc213137","name":"杨辰"}',
            dateRange:
              '{"start":"2023-07-18 00:00:00","end":"2023-07-31 23:59:59"}',
            timeRange: '[{"start":"00:00:00","end":"22:59:59"}]',
            type: "14",
            _workspace_uuid_: "6v4M8woywiK9aecPk9yWLh",
            effectiveChannel: "1",
            rule: '[{"condition":10000,"discount":100,"shopSubsidy":0,"elemeSubsidy":100},{"condition":20000,"discount":200,"shopSubsidy":100,"elemeSubsidy":100}]',
          },
          investmentSchema: InvestmentSchemaEnum.RICHANG,
        });
        expect(viewJson).toStrictEqual({
          activityMarketingInfo: {
            budgetId: "400000000003202329",
            budgetName: "2022招商活动测试预算",
            deliveryType: 1,
            userScope: 0,
          },
          activityRule: [
            {
              agentSubsidy: undefined,
              condition: 10000,
              discount: 100,
              platformSubsidy: 100,
              shopSubsidy: 0,
            },
            {
              agentSubsidy: undefined,
              condition: 20000,
              discount: 200,
              platformSubsidy: 100,
              shopSubsidy: 100,
            },
          ],
          activityType: 1000014,
          period: [
            {
              closeTime: "22:59:59",
              openTime: "00:00:00",
            },
          ],
          weekday: "1,2,3,4,5,6",
        });
      });

      test("日常招商-多阶梯-含平台补贴+代理商补贴-全天", () => {
        const viewJson = transformFormData2ViewJson({
          baseinfo: {
            activityTime: {
              weeks: [0, 1, 2, 3, 4, 5, 6],
              range: {
                type: "all",
                start: "00:00",
                end: "23:59",
              },
            },
          },
          formData: {
            budget:
              '{"budgetId":"400000000003202329","budgetName":"2022招商活动测试预算"}',
            _signupShopType: "2",
            weekday: "0,1,2,3,4,5,6",
            userScope: "0",
            name: "yc-全店满减",
            creator: '{"id":"213137","prefix":"yc213137","name":"杨辰"}',
            dateRange:
              '{"start":"2023-07-18 00:00:00","end":"2023-07-31 23:59:59"}',
            timeRange: '[{"start":"00:00:00","end":"23:59:59"}]',
            type: "14",
            _workspace_uuid_: "6v4M8woywiK9aecPk9yWLh",
            effectiveChannel: "1",
            rule: '[{"condition":1000,"discount":300,"shopSubsidy":100,"elemeSubsidy":100,"agentSubsidy":100},{"condition":2000,"discount":400,"shopSubsidy":200,"elemeSubsidy":100,"agentSubsidy":100},{"condition":3000,"discount":500,"shopSubsidy":300,"elemeSubsidy":100,"agentSubsidy":100}]',
          },
          investmentSchema: InvestmentSchemaEnum.RICHANG,
        });
        expect(viewJson).toStrictEqual({
          activityMarketingInfo: {
            budgetId: "400000000003202329",
            budgetName: "2022招商活动测试预算",
            deliveryType: 1,
            userScope: 0,
          },
          activityRule: [
            {
              condition: 1000,
              discount: 300,
              platformSubsidy: 100,
              shopSubsidy: 100,
              agentSubsidy: 100,
            },
            {
              condition: 2000,
              discount: 400,
              platformSubsidy: 100,
              shopSubsidy: 200,
              agentSubsidy: 100,
            },
            {
              condition: 3000,
              discount: 500,
              platformSubsidy: 100,
              shopSubsidy: 300,
              agentSubsidy: 100,
            },
          ],
          activityType: 1000014,
          period: [
            {
              closeTime: "23:59:59",
              openTime: "00:00:00",
            },
          ],
          weekday: "0,1,2,3,4,5,6",
        });
      });
    });
    describe("transformViewJson2FormData", () => {
      test("日常招商-多阶梯-含平台补贴+代理商补贴-全天", () => {
        const viewJson = JSON.stringify({
          activityMarketingInfo: {
            budgetId: "400000000003202329",
            budgetName: "2022招商活动测试预算",
            deliveryType: 1,
            userScope: 0,
          },
          activityRule: [
            {
              agentSubsidy: 100,
              condition: 1000,
              discount: 300,
              platformSubsidy: 100,
              shopSubsidy: 100,
            },
            {
              agentSubsidy: 100,
              condition: 2000,
              discount: 400,
              platformSubsidy: 100,
              shopSubsidy: 200,
            },
            {
              agentSubsidy: 100,
              condition: 3000,
              discount: 500,
              platformSubsidy: 100,
              shopSubsidy: 300,
            },
          ],
          activityType: 1000014,
          attributes: {},
          period: {
            closeTime: "23:59:59",
            openTime: "00:00:00",
            weekday: "0,1,2,3,4,5,6",
          },
        });
        const formData = transformViewJson2FormData({
          viewJson,
          baseinfo: {
            signUpShopType: 2,
            name: "全店满减",
            beginTime: "2023-07-17T16:00:00.000Z",
            endTime: "2023-07-31T15:59:59.000Z",
            createdUserId: "213137",
            createdUserName: "杨辰",
          },
          investmentSchema: InvestmentSchemaEnum.RICHANG,
        });
        expect(formData).toStrictEqual({
          _signupShopType: "2",
          budget:
            '{"budgetId":"400000000003202329","budgetName":"2022招商活动测试预算"}',
          userScope: "0",
          name: "全店满减",
          creator: '{"id":"213137","name":"杨辰"}',
          dateRange:
            '{"start":"2023-07-18 00:00:00","end":"2023-07-31 23:59:59"}',
          effectiveChannel: "1",
          rule: '[{"condition":1000,"discount":300,"elemeSubsidy":100,"shopSubsidy":100,"agentSubsidy":100},{"condition":2000,"discount":400,"elemeSubsidy":100,"shopSubsidy":200,"agentSubsidy":100},{"condition":3000,"discount":500,"elemeSubsidy":100,"shopSubsidy":300,"agentSubsidy":100}]',
        });
      });

      test("日常招商-单阶梯-无补贴-全天整周", () => {
        const viewJson = JSON.stringify({
          activityMarketingInfo: {
            deliveryType: 1,
            userScope: 0,
          },
          activityRule: [
            {
              agentSubsidy: 0,
              condition: 10000,
              discount: 100,
              platformSubsidy: 0,
              shopSubsidy: 100,
            },
          ],
          activityType: 1000014,
          attributes: {},
          period: {
            closeTime: "23:59:59",
            openTime: "00:00:00",
            weekday: "0,1,2,3,4,5,6",
          },
        });
        const formData = transformViewJson2FormData({
          viewJson,
          baseinfo: {
            signUpShopType: 0,
            name: "全店满减",
            beginTime: "2023-07-17T16:00:00.000Z",
            endTime: "2023-07-31T15:59:59.000Z",
            createdUserId: "213137",
            createdUserName: "杨辰",
          },
          investmentSchema: InvestmentSchemaEnum.RICHANG,
        });
        expect(formData).toStrictEqual({
          creator: '{"id":"213137","name":"杨辰"}',
          dateRange:
            '{"start":"2023-07-18 00:00:00","end":"2023-07-31 23:59:59"}',
          userScope: "0",
          _signupShopType: "0",
          name: "全店满减",
          rule: '[{"condition":10000,"discount":100,"elemeSubsidy":0,"shopSubsidy":100,"agentSubsidy":0}]',
          effectiveChannel: "1",
          budget: undefined
        });
      });
    });
  });
});
