import { ActivityStrategy, InvestmentActivityDetailDTO } from "../models"
import { CableBase } from "./base"
import { gotoLegacyActivityDetailByDefault, isOfflineEnabledByDefault } from "./mixins"

/**
 * 跨店搭售，只用于展示老活动，不支持创建新活动
 * TODO: 完成灰度以后，去掉改 strategy
 */
const CrossStore: ActivityStrategy = {
  activityType: 1000015,
  cable: {
    ...CableBase,
    workspace: 'ExukovQrae6xWn2rt4NgG',
    model: 'CedgjunI3F9Nsg4WhCaC2',
    async checkNext() {
      throw new Error('跨店搭售不支持创建')
    }
  },
  hasAgentSubsidy() {
    return false
  },
  isSupportAgent: false,
  isOfflineEnabled: isOfflineEnabledByDefault,
  isDingtalkLinkEnabled: () => false,
  hasAuditMemberList: () => false,
  isSupportModifyStock: () => false,
  isDetailInviteStatsOnMainActivityTableVisible: true,
  renderPlayDataInfoForTableView: function (): JSX.Element {
    return <></>
  },
  renderActivityRuleForTable() {
    return ''
  },
  gotoLegacyDetail: function (act: InvestmentActivityDetailDTO, scene: "lookup" | "examine"): void {
    gotoLegacyActivityDetailByDefault(act, scene)
  },
  renderActivityRuleForTableLegacy: function (marketPlay: { instance: { fields: any } }) {
    const fields = marketPlay?.instance?.fields
    try {
      const { itemPriceArea, itemElemeSubsidy, itemDiscount } = fields;
      const result = [];
      if (itemPriceArea) {
        const value = JSON.parse(itemPriceArea)
        result.push(`活动价格区间:${value.start / 100}~${value.end / 100}`);
      }
      if (itemDiscount) {
        const value = JSON.parse(itemDiscount)
        result.push(`限制最低折扣:${value.value}`);
      }
      if (itemElemeSubsidy) {
        const value = JSON.parse(itemElemeSubsidy)
        result.push(`平台补贴最高${value.value / 100}元`);
      }
      result.push('免起送价限制');
      return result.filter(str => !!str).map((item: any, i: number) => {
        return (
          <div key={i}>{item}</div>
        )
      });
    } catch (e) {
      return ''
    }
  }
}


export default CrossStore

