import moment from 'moment';
import { ActivitySignType } from '../../constants';

import {
  formatMarketPlay2ViewJson,
  formatMarketPlay2ViewJson4InverseSelection,
  formatMarketPlay2ViewJson4ShareCoupon,
  formatViewJson2MarketPlay /* formatMarketPlay2ViewJson */,
  formatViewJson2MarketPlay4ShareCoupon,
} from './formatUtils';

describe('itemSale', () => {
  describe('全店满减券', () => {
    describe('formatMarketPlay2ViewJson', () => {
      // 正招
      test('全时段-允许取消-无业务线-固定门槛-无外投渠道-红包全天-无预算', () => {
        const formData = {
          outStoreDeliveryType: '2',
          externalDeliveryChannel: null,
          budget: '{}',
          _signupShopType: '0',
          userScope: '0',
          activityType: '0',
          inStoreDeliveryType: '1',
          exchangeTimeRangeList: '[{"start":"00:00:00","end":"23:59:59"}]',
          name: 'test-日常正招全部时段-无业务线',
          creator: '{"id":"WB899541","prefix":"wb-zyh899541","name":"张宇航"}',
          _playId: '保存后自动生成',
          dateRange:
            '{"start":"2023-10-08 00:00:00","end":"2023-10-22 23:59:59"}',
          couponStock: '{"dayLimit":{"minValue":1,"maxValue":100,"value":""}}',
          couponValid: '{"fromDraw":{"value":0}}',
          drawLimit: '{"dayLimit":{"minValue":1,"maxValue":2,"value":""}}',
          _workspace_uuid_: 'ExukovQrae6xWn2rt4NgG',
          effectiveChannel: '1',
          rule: '{"saleFixed":{"condition":10000,"discount":100,"elemeSubsidy":0}}',
        };

        const baseinfo = {
          activityType: 2,
          allowCancel: 1,
          applyPlatform: 1,
          description:
            'test-日常正招全部时段-无业务线test-日常正招全部时段-无业务线',
          name: 'test-日常正招全部时段-无业务线',
          signUpShopType: 0,
          activityTime: {
            weeks: [0, 1, 2, 3, 4, 5, 6],
            batchRange: {
              type: 'all',
              list: [
                {
                  start: '00:00',
                  end: '23:59',
                },
              ],
            },
            start: moment('2023-10-07T16:00:00.000Z'),
            end: moment('2023-10-22T15:59:59.000Z'),
          },
          remark: undefined,
          signupTime: {
            start: moment('2023-10-07T16:00:00.000Z'),
            end: moment('2023-10-22T15:59:59.000Z'),
          },
        };

        const viewJson = formatMarketPlay2ViewJson({
          // @ts-ignore
          formData,
          baseinfo,
        });

        expect(viewJson).toStrictEqual({
          activityRule: [
            {
              conditionType: 2,
              condition: 10000,
              discountType: 1,
              discount: 100,
              platformSubsidy: 0,

              agentSubsidy: undefined,
            },
          ],
          activityMarketingInfo: {
            userScope: 0,
            deliveryType: 1,
            availableDays: 0,
            activityExternalDeliveryChannelList: null,
            inStoreDeliveryType: '1',
            outStoreDeliveryType: '2',

            availableEndTime: undefined,
            availableStartTime: undefined,
            budgetId: undefined,
            budgetName: undefined,
          },
          activityLimitRule: {
            dayCountLimit: 1,
            dayCountLimitMax: 100,
            userDayCountLimit: 1,
            userDayCountLimitMax: 2,

            totalCountLimit: undefined,
            totalCountLimitMax: undefined,
            userTotalCountLimit: undefined,
            userTotalCountLimitMax: undefined,
          },
          periodDTOList: [{ openTime: '00:00:00', closeTime: '23:59:59' }],
          exchangePeriodDTOList: [
            { openTime: '00:00:00', closeTime: '23:59:59' },
          ],
          weekday: '0,1,2,3,4,5,6',
        });
      });

      test('全时段-允许取消-无业务线-固定门槛-无外投渠道-红包全天-无预算-异常场景：无券库存，无预算', () => {
        const formData = {
          outStoreDeliveryType: '2',
          externalDeliveryChannel: null,
          budget: null,
          _signupShopType: '0',
          userScope: '0',
          activityType: '0',
          inStoreDeliveryType: '1',
          exchangeTimeRangeList: '[{"start":"00:00:00","end":"23:59:59"}]',
          name: 'test-日常正招全部时段-无业务线',
          creator: '{"id":"WB899541","prefix":"wb-zyh899541","name":"张宇航"}',
          _playId: '保存后自动生成',
          dateRange:
            '{"start":"2023-10-08 00:00:00","end":"2023-10-22 23:59:59"}',
          couponStock: '',
          couponValid: '{"fromDraw":{"value":0}}',
          drawLimit: '{"dayLimit":{"minValue":1,"maxValue":2,"value":""}}',
          _workspace_uuid_: 'ExukovQrae6xWn2rt4NgG',
          effectiveChannel: '1',
          rule: '{"saleFixed":{"condition":10000,"discount":100,"elemeSubsidy":0}}',
        };

        const baseinfo = {
          activityType: 2,
          allowCancel: 1,
          applyPlatform: 1,
          description:
            'test-日常正招全部时段-无业务线test-日常正招全部时段-无业务线',
          name: 'test-日常正招全部时段-无业务线',
          signUpShopType: 0,
          activityTime: {
            weeks: [0, 1, 2, 3, 4, 5, 6],
            batchRange: {
              type: 'all',
              list: [
                {
                  start: '00:00',
                  end: '23:59',
                },
              ],
            },
            start: moment('2023-10-07T16:00:00.000Z'),
            end: moment('2023-10-22T15:59:59.000Z'),
          },
          remark: undefined,
          signupTime: {
            start: moment('2023-10-07T16:00:00.000Z'),
            end: moment('2023-10-22T15:59:59.000Z'),
          },
        };

        const viewJson = formatMarketPlay2ViewJson({
          // @ts-ignore
          formData,
          baseinfo,
        });

        expect(viewJson).toStrictEqual({
          activityRule: [
            {
              conditionType: 2,
              condition: 10000,
              discountType: 1,
              discount: 100,
              platformSubsidy: 0,

              agentSubsidy: undefined,
            },
          ],
          activityMarketingInfo: {
            userScope: 0,
            deliveryType: 1,
            availableDays: 0,
            activityExternalDeliveryChannelList: null,
            inStoreDeliveryType: '1',
            outStoreDeliveryType: '2',

            availableEndTime: undefined,
            availableStartTime: undefined,
            budgetId: undefined,
            budgetName: undefined,
          },
          activityLimitRule: {
            dayCountLimit: undefined,
            dayCountLimitMax: undefined,
            userDayCountLimit: 1,
            userDayCountLimitMax: 2,

            totalCountLimit: undefined,
            totalCountLimitMax: undefined,
            userTotalCountLimit: undefined,
            userTotalCountLimitMax: undefined,
          },
          periodDTOList: [{ openTime: '00:00:00', closeTime: '23:59:59' }],
          exchangePeriodDTOList: [
            { openTime: '00:00:00', closeTime: '23:59:59' },
          ],
          weekday: '0,1,2,3,4,5,6',
        });
      });

      test('多时段-不允许取消-有业务线-区间门槛-有外投渠道-红包多时段-无预算', () => {
        const formData = {
          outStoreDeliveryType: '1',
          externalDeliveryChannel:
            '[{"externalDeliveryChannelType":1,"externalDeliveryChannelCode":5,"externalDeliveryChannelName":"新零售导购红包雨"},{"externalDeliveryChannelType":1,"externalDeliveryChannelCode":7,"externalDeliveryChannelName":"新零售导购地推拉新"}]',
          budget: null,
          _signupShopType: '0',
          userScope: '0',
          activityType: '0',
          inStoreDeliveryType: '1',
          exchangeTimeRangeList: '[{"start":"00:00:00","end":"23:59:59"}]',
          name: 'test-日常正招全部时段-有业务线',
          creator: '{"id":"WB899541","prefix":"wb-zyh899541","name":"张宇航"}',
          _playId: '保存后自动生成',
          businessLineId: 1000004,
          dateRange:
            '{"start":"2023-10-08 00:00:00","end":"2023-10-22 23:59:59"}',
          couponStock:
            '{"totalLimit":{"minValue":10,"maxValue":100,"value":""}}',
          businessFormIds: [
            {
              businessFormName: '大型超市',
              businessFormId: 254,
              class:
                'me.ele.newretail.investment.service.merchant.dto.BusinessFormDTO',
            },
          ],
          couponValid:
            '{"validRange":{"start":"2023-10-15 00:00:00","end":"2023-10-22 23:59:59"}}',
          drawLimit: '{"totalLimit":{"minValue":1,"maxValue":2,"value":""}}',
          _workspace_uuid_: 'ExukovQrae6xWn2rt4NgG',
          configValidationRules: '{"loading":false}',
          effectiveChannel: '3',
          rule: '{"saleRange":{"minCondition":10000,"maxCondition":20000,"minDiscount":100,"maxDiscount":200,"maxElemeSubsidy":0}}',
        };
        const baseinfo = {
          allowCancel: 2,
          signUpShopType: 0,
          description:
            'test-日常正招全部时段-有业务线test-日常正招全部时段-有业务线',
          activityTime: {
            weeks: [0, 1, 2, 3, 4, 5, 6],
            batchRange: {
              type: 'custom',
              list: [
                { key: 3001, start: '00:00', end: '01:00' },
                { key: 3002, start: '01:01', end: '02:00' },
                { key: 3003, start: '02:01', end: '03:00' },
              ],
            },
            start: moment('2023-10-07T16:00:00.000Z'),
            end: moment('2023-10-22T15:59:59.000Z'),
          },
          applyPlatform: 1,
          activityType: 2,
          name: 'test-日常正招全部时段-有业务线',
          signupTime: {
            start: moment('2023-10-07T16:00:00.000Z'),
            end: moment('2023-10-22T15:59:59.000Z'),
          },
          businessLineId: {
            businessLineName: '超市便利',
            businessFormList: [
              {
                businessFormName: '大型超市',
                businessFormId: 254,
                class:
                  'me.ele.newretail.investment.service.merchant.dto.BusinessFormDTO',
              },
              {
                businessFormName: '水站',
                businessFormId: 255,
                class:
                  'me.ele.newretail.investment.service.merchant.dto.BusinessFormDTO',
              },
              {
                businessFormName: '奶站',
                businessFormId: 256,
                class:
                  'me.ele.newretail.investment.service.merchant.dto.BusinessFormDTO',
              },
              {
                businessFormName: '茶行',
                businessFormId: 258,
                class:
                  'me.ele.newretail.investment.service.merchant.dto.BusinessFormDTO',
              },
              {
                businessFormName: '便利店',
                businessFormId: 271,
                class:
                  'me.ele.newretail.investment.service.merchant.dto.BusinessFormDTO',
              },
              {
                businessFormName: '饮料冰品',
                businessFormId: 282,
                class:
                  'me.ele.newretail.investment.service.merchant.dto.BusinessFormDTO',
              },
              {
                businessFormName: '标超',
                businessFormId: 1603,
                class:
                  'me.ele.newretail.investment.service.merchant.dto.BusinessFormDTO',
              },
            ],
            class:
              'me.ele.newretail.investment.service.merchant.dto.BusinessLineDTO',
            businessLineId: 1000004,
          },
          businessFormIds: [
            {
              businessFormName: '大型超市',
              businessFormId: 254,
              class:
                'me.ele.newretail.investment.service.merchant.dto.BusinessFormDTO',
            },
          ],
        };

        const viewJson = formatMarketPlay2ViewJson({
          // @ts-ignore
          formData,
          baseinfo,
        });

        expect(viewJson).toStrictEqual({
          activityRule: [
            {
              conditionType: 2,
              conditionMin: 10000,
              conditionMax: 20000,
              discountType: 1,
              discountMin: 100,
              discountMax: 200,
              platformSubsidyMax: 0,

              agentSubsidyMax: undefined,
            },
          ],
          activityMarketingInfo: {
            userScope: 0,
            deliveryType: 3,
            availableStartTime: 1697299200000,
            availableEndTime: 1697990399000,
            activityExternalDeliveryChannelList: [
              {
                externalDeliveryChannelType: 1,
                externalDeliveryChannelCode: 5,
                externalDeliveryChannelName: '新零售导购红包雨',
              },
              {
                externalDeliveryChannelType: 1,
                externalDeliveryChannelCode: 7,
                externalDeliveryChannelName: '新零售导购地推拉新',
              },
            ],
            inStoreDeliveryType: '1',
            outStoreDeliveryType: '1',

            availableDays: undefined,
            budgetId: undefined,
            budgetName: undefined,
          },
          activityLimitRule: {
            totalCountLimit: 10,
            totalCountLimitMax: 100,
            userTotalCountLimit: 1,
            userTotalCountLimitMax: 2,

            dayCountLimit: undefined,
            dayCountLimitMax: undefined,
            userDayCountLimit: undefined,
            userDayCountLimitMax: undefined,
          },
          periodDTOList: [
            { openTime: '00:00:00', closeTime: '01:00:59' },
            { openTime: '01:01:00', closeTime: '02:00:59' },
            { openTime: '02:01:00', closeTime: '03:00:59' },
          ],
          exchangePeriodDTOList: [
            { openTime: '00:00:00', closeTime: '23:59:59' },
          ],
          weekday: '0,1,2,3,4,5,6',
        });
      });

      test('多时段-多时段-不允许取消-有业务线-区间门槛-有外投渠道-红包多时段-有预算', () => {
        const formData = {
          outStoreDeliveryType: '1',
          externalDeliveryChannel:
            '[{"externalDeliveryChannelType":1,"externalDeliveryChannelCode":5,"externalDeliveryChannelName":"新零售导购红包雨"},{"externalDeliveryChannelType":1,"externalDeliveryChannelCode":7,"externalDeliveryChannelName":"新零售导购地推拉新"}]',
          budget: '{"budgetId":"400000000005416533","budgetName":"LQ红包预算"}',
          _signupShopType: '0',
          userScope: '0',
          activityType: '0',
          inStoreDeliveryType: '1',
          exchangeTimeRangeList: '[{"start":"00:00:00","end":"23:59:59"}]',
          name: 'test-日常正招全部时段-无业务线',
          creator: '{"id":"WB899541","prefix":"wb-zyh899541","name":"张宇航"}',
          _playId: '保存后自动生成',
          businessLineId: 1000004,
          dateRange:
            '{"start":"2023-10-09 00:00:00","end":"2023-10-22 23:59:59"}',
          couponStock:
            '{"totalLimit":{"minValue":10,"maxValue":100,"value":""}}',
          businessFormIds: [
            {
              businessFormName: '大型超市',
              businessFormId: 254,
              class:
                'me.ele.newretail.investment.service.merchant.dto.BusinessFormDTO',
            },
          ],
          couponValid:
            '{"validRange":{"start":"2023-10-15 00:00:00","end":"2023-10-22 23:59:59"}}',
          drawLimit: '{"totalLimit":{"minValue":1,"maxValue":2,"value":""}}',
          _workspace_uuid_: 'ExukovQrae6xWn2rt4NgG',
          configValidationRules: '{"loading":false}',
          effectiveChannel: '3',
          rule: '{"saleRange":{"minCondition":10000,"maxCondition":20000,"minDiscount":100,"maxDiscount":200,"maxElemeSubsidy":100}}',
        };

        const baseinfo = {
          allowCancel: 2,
          signUpShopType: 0,
          description:
            'test-日常正招全部时段-有业务线test-日常正招全部时段-有业务线',
          activityTime: {
            weeks: [0, 1, 2, 3, 4, 5, 6],
            batchRange: {
              type: 'custom',
              list: [
                { key: 3001, start: '00:00', end: '01:00' },
                { key: 3002, start: '01:01', end: '02:00' },
                { key: 3003, start: '02:01', end: '03:00' },
              ],
            },
            start: moment('2023-10-07T16:00:00.000Z'),
            end: moment('2023-10-22T15:59:59.000Z'),
          },
          applyPlatform: 1,
          activityType: 2,
          name: 'test-日常正招全部时段-有业务线',
          signupTime: {
            start: moment('2023-10-07T16:00:00.000Z'),
            end: moment('2023-10-22T15:59:59.000Z'),
          },
          businessLineId: {
            businessLineName: '超市便利',
            businessFormList: [
              {
                businessFormName: '大型超市',
                businessFormId: 254,
                class:
                  'me.ele.newretail.investment.service.merchant.dto.BusinessFormDTO',
              },
              {
                businessFormName: '水站',
                businessFormId: 255,
                class:
                  'me.ele.newretail.investment.service.merchant.dto.BusinessFormDTO',
              },
              {
                businessFormName: '奶站',
                businessFormId: 256,
                class:
                  'me.ele.newretail.investment.service.merchant.dto.BusinessFormDTO',
              },
              {
                businessFormName: '茶行',
                businessFormId: 258,
                class:
                  'me.ele.newretail.investment.service.merchant.dto.BusinessFormDTO',
              },
              {
                businessFormName: '便利店',
                businessFormId: 271,
                class:
                  'me.ele.newretail.investment.service.merchant.dto.BusinessFormDTO',
              },
              {
                businessFormName: '饮料冰品',
                businessFormId: 282,
                class:
                  'me.ele.newretail.investment.service.merchant.dto.BusinessFormDTO',
              },
              {
                businessFormName: '标超',
                businessFormId: 1603,
                class:
                  'me.ele.newretail.investment.service.merchant.dto.BusinessFormDTO',
              },
            ],
            class:
              'me.ele.newretail.investment.service.merchant.dto.BusinessLineDTO',
            businessLineId: 1000004,
          },
          businessFormIds: [
            {
              businessFormName: '大型超市',
              businessFormId: 254,
              class:
                'me.ele.newretail.investment.service.merchant.dto.BusinessFormDTO',
            },
          ],
        };

        const viewJson = formatMarketPlay2ViewJson({
          // @ts-ignore
          formData,
          baseinfo,
        });

        expect(viewJson).toStrictEqual({
          activityRule: [
            {
              conditionType: 2,
              conditionMin: 10000,
              conditionMax: 20000,
              discountType: 1,
              discountMin: 100,
              discountMax: 200,
              platformSubsidyMax: 100,

              agentSubsidyMax: undefined,
            },
          ],
          activityMarketingInfo: {
            userScope: 0,
            deliveryType: 3,
            budgetId: '400000000005416533',
            budgetName: 'LQ红包预算',
            availableStartTime: 1697299200000,
            availableEndTime: 1697990399000,
            activityExternalDeliveryChannelList: [
              {
                externalDeliveryChannelType: 1,
                externalDeliveryChannelCode: 5,
                externalDeliveryChannelName: '新零售导购红包雨',
              },
              {
                externalDeliveryChannelType: 1,
                externalDeliveryChannelCode: 7,
                externalDeliveryChannelName: '新零售导购地推拉新',
              },
            ],
            inStoreDeliveryType: '1',
            outStoreDeliveryType: '1',

            availableDays: undefined,
          },
          activityLimitRule: {
            totalCountLimit: 10,
            totalCountLimitMax: 100,
            userTotalCountLimit: 1,
            userTotalCountLimitMax: 2,

            dayCountLimit: undefined,
            dayCountLimitMax: undefined,
            userDayCountLimit: undefined,
            userDayCountLimitMax: undefined,
          },
          periodDTOList: [
            { openTime: '00:00:00', closeTime: '01:00:59' },
            { openTime: '01:01:00', closeTime: '02:00:59' },
            { openTime: '02:01:00', closeTime: '03:00:59' },
          ],
          exchangePeriodDTOList: [
            { openTime: '00:00:00', closeTime: '23:59:59' },
          ],
          weekday: '0,1,2,3,4,5,6',
        });
      });

      test('多时段-不允许取消-有业务线-区间门槛-有外投渠道-红包多时段-有预算-有代理商补贴', () => {
        const formData = {
          outStoreDeliveryType: '1',
          externalDeliveryChannel:
            '[{"externalDeliveryChannelType":1,"externalDeliveryChannelCode":5,"externalDeliveryChannelName":"新零售导购红包雨"},{"externalDeliveryChannelType":1,"externalDeliveryChannelCode":7,"externalDeliveryChannelName":"新零售导购地推拉新"}]',
          budget: '{"budgetId":"400000000005416533","budgetName":"LQ红包预算"}',
          _signupShopType: '2',
          userScope: '0',
          activityType: '0',
          inStoreDeliveryType: '1',
          exchangeTimeRangeList: '[{"start":"00:00:00","end":"23:59:59"}]',
          name: 'test-日常正招全部时段-无业务线',
          creator: '{"id":"WB899541","prefix":"wb-zyh899541","name":"张宇航"}',
          _playId: '保存后自动生成',
          businessLineId: 1000004,
          dateRange:
            '{"start":"2023-10-09 00:00:00","end":"2023-10-22 23:59:59"}',
          couponStock:
            '{"totalLimit":{"minValue":10,"maxValue":100,"value":""}}',
          businessFormIds: [
            {
              businessFormName: '大型超市',
              businessFormId: 254,
              class:
                'me.ele.newretail.investment.service.merchant.dto.BusinessFormDTO',
            },
          ],
          couponValid:
            '{"validRange":{"start":"2023-10-15 00:00:00","end":"2023-10-22 23:59:59"}}',
          drawLimit: '{"totalLimit":{"minValue":1,"maxValue":2,"value":""}}',
          _workspace_uuid_: 'ExukovQrae6xWn2rt4NgG',
          configValidationRules: '{"loading":false}',
          effectiveChannel: '3',
          rule: '{"saleFixed":{"condition":10000,"discount":200,"elemeSubsidy":100,"agentSubsidy":100}}',
        };

        const baseinfo = {
          allowCancel: 2,
          signUpShopType: 2,
          description:
            'test-日常正招全部时段-有业务线test-日常正招全部时段-有业务线',
          activityTime: {
            weeks: [0, 1, 2, 3, 4, 5, 6],
            batchRange: {
              type: 'custom',
              list: [
                { key: 3001, start: '00:00', end: '01:00' },
                { key: 3002, start: '01:01', end: '02:00' },
                { key: 3003, start: '02:01', end: '03:00' },
              ],
            },
            start: moment('2023-10-07T16:00:00.000Z'),
            end: moment('2023-10-22T15:59:59.000Z'),
          },
          applyPlatform: 1,
          activityType: 2,
          name: 'test-日常正招全部时段-有业务线',
          signupTime: {
            start: moment('2023-10-07T16:00:00.000Z'),
            end: moment('2023-10-22T15:59:59.000Z'),
          },
          businessLineId: {
            businessLineName: '超市便利',
            businessFormList: [
              {
                businessFormName: '大型超市',
                businessFormId: 254,
                class:
                  'me.ele.newretail.investment.service.merchant.dto.BusinessFormDTO',
              },
              {
                businessFormName: '水站',
                businessFormId: 255,
                class:
                  'me.ele.newretail.investment.service.merchant.dto.BusinessFormDTO',
              },
              {
                businessFormName: '奶站',
                businessFormId: 256,
                class:
                  'me.ele.newretail.investment.service.merchant.dto.BusinessFormDTO',
              },
              {
                businessFormName: '茶行',
                businessFormId: 258,
                class:
                  'me.ele.newretail.investment.service.merchant.dto.BusinessFormDTO',
              },
              {
                businessFormName: '便利店',
                businessFormId: 271,
                class:
                  'me.ele.newretail.investment.service.merchant.dto.BusinessFormDTO',
              },
              {
                businessFormName: '饮料冰品',
                businessFormId: 282,
                class:
                  'me.ele.newretail.investment.service.merchant.dto.BusinessFormDTO',
              },
              {
                businessFormName: '标超',
                businessFormId: 1603,
                class:
                  'me.ele.newretail.investment.service.merchant.dto.BusinessFormDTO',
              },
            ],
            class:
              'me.ele.newretail.investment.service.merchant.dto.BusinessLineDTO',
            businessLineId: 1000004,
          },
          businessFormIds: [
            {
              businessFormName: '大型超市',
              businessFormId: 254,
              class:
                'me.ele.newretail.investment.service.merchant.dto.BusinessFormDTO',
            },
          ],
        };

        const viewJson = formatMarketPlay2ViewJson({
          // @ts-ignore
          formData,
          baseinfo,
        });

        expect(viewJson).toStrictEqual({
          activityRule: [
            {
              conditionType: 2,
              condition: 10000,
              discountType: 1,
              discount: 200,
              platformSubsidy: 100,
              agentSubsidy: 100,
            },
          ],
          activityMarketingInfo: {
            userScope: 0,
            deliveryType: 3,
            budgetId: '400000000005416533',
            budgetName: 'LQ红包预算',
            availableStartTime: 1697299200000,
            availableEndTime: 1697990399000,
            activityExternalDeliveryChannelList: [
              {
                externalDeliveryChannelType: 1,
                externalDeliveryChannelCode: 5,
                externalDeliveryChannelName: '新零售导购红包雨',
              },
              {
                externalDeliveryChannelType: 1,
                externalDeliveryChannelCode: 7,
                externalDeliveryChannelName: '新零售导购地推拉新',
              },
            ],
            inStoreDeliveryType: '1',
            outStoreDeliveryType: '1',

            availableDays: undefined,
          },
          activityLimitRule: {
            totalCountLimit: 10,
            totalCountLimitMax: 100,
            userTotalCountLimit: 1,
            userTotalCountLimitMax: 2,

            dayCountLimit: undefined,
            dayCountLimitMax: undefined,
            userDayCountLimit: undefined,
            userDayCountLimitMax: undefined,
          },
          periodDTOList: [
            { openTime: '00:00:00', closeTime: '01:00:59' },
            { openTime: '01:01:00', closeTime: '02:00:59' },
            { openTime: '02:01:00', closeTime: '03:00:59' },
          ],
          exchangePeriodDTOList: [
            { openTime: '00:00:00', closeTime: '23:59:59' },
          ],
          weekday: '0,1,2,3,4,5,6',
        });
      });
    });

    describe('formatMarketPlay2ViewJson4InverseSelection', () => {
      // 反圈
      test('多时段-非聊天购-券有效期区间-红包多时段-有预算', () => {
        const formData = {
          outStoreDeliveryType: '2',
          couponStockFixed: '{"totalLimit":{"value":100}}',
          externalDeliveryChannel: null,
          budget: '{"budgetId":"400000000005416533","budgetName":"LQ红包预算"}',
          _signupShopType: '0',
          userScope: '0',
          activityType: '0',
          inStoreDeliveryType: '1',
          exchangeTimeRangeList:
            '[{"start":"03:00:00","end":"04:00:59"},{"start":"04:01:00","end":"05:00:59"}]',
          name: 'test-反圈-全部时段',
          creator: '{"id":"WB899541","prefix":"wb-zyh899541","name":"张宇航"}',
          _playId: '保存后自动生成',
          dateRange:
            '{"start":"2023-10-09 00:00:00","end":"2023-10-22 23:59:59"}',
          bizUseChannel: '0',
          couponName: '{"value":"test-红包名称"}',
          couponValid:
            '{"validRange":{"start":"2023-10-09 00:00:00","end":"2023-10-23 23:59:59"}}',
          _workspace_uuid_: '3oNNWxkJYzc6eWQyKz5Lp2',
          effectiveChannel: '3',
          rule: '{"saleFixed":{"condition":10000,"discount":101,"elemeSubsidy":101}}',
          drawLimitFixed:
            '{"totalLimit":{"minValue":"","maxValue":"","value":1}}',
        };

        const baseinfo = {
          allowCancel: 2,
          signUpShopType: 0,
          description: 'test-反圈-全部时段test-反圈-全部时段',
          activityTime: {
            weeks: [0, 1, 2, 3, 4, 5, 6],
            batchRange: {
              type: 'custom',
              list: [
                {
                  key: 3001,
                  start: '00:00',
                  end: '01:00',
                },
                {
                  key: 3002,
                  start: '01:01',
                  end: '02:00',
                },
                {
                  key: 3003,
                  start: '02:01',
                  end: '03:00',
                },
              ],
            },
            start: moment('2023-10-08T16:00:00.000Z'),
            end: moment('2023-10-22T15:59:59.000Z'),
          },
          applyPlatform: 1,
          activityType: 2,
          name: 'test-反圈-全部时段',
        };

        const viewJson = formatMarketPlay2ViewJson4InverseSelection({
          // @ts-ignore
          formData,
          baseinfo,
        });

        expect(viewJson).toStrictEqual({
          activityRule: [
            {
              conditionType: 2,
              condition: 10000,
              discountType: 1,
              discount: 101,
              platformSubsidy: 101,

              agentSubsidy: undefined,
            },
          ],
          activityMarketingInfo: {
            userScope: 0,
            budgetId: '400000000005416533',
            deliveryType: 3,
            budgetName: 'LQ红包预算',
            availableStartTime: 1696780800000,
            availableEndTime: 1698076799000,
            activityExternalDeliveryChannelList: null,
            inStoreDeliveryType: '1',
            outStoreDeliveryType: '2',
            bizUseChannel: '0',
            couponName: 'test-红包名称',

            redirectUrl: undefined,
            useStoreType: undefined,
            availableDays: undefined,
          },
          activityLimitRule: {
            totalCountLimit: 100,
            totalCountLimitMax: 100,
            userTotalCountLimit: 1,
            userTotalCountLimitMax: 1,

            dayCountLimit: undefined,
            dayCountLimitMax: undefined,
            userDayCountLimit: undefined,
            userDayCountLimitMax: undefined,
          },
          periodDTOList: [
            { openTime: '00:00:00', closeTime: '01:00:59' },
            { openTime: '01:01:00', closeTime: '02:00:59' },
            { openTime: '02:01:00', closeTime: '03:00:59' },
          ],
          exchangePeriodDTOList: [
            { openTime: '03:00:00', closeTime: '04:00:59' },
            { openTime: '04:01:00', closeTime: '05:00:59' },
          ],
          weekday: '0,1,2,3,4,5,6',
        });
      });

      test('全时段-聊天购-券有效期天-红包全天-有预算', () => {
        const formData = {
          outStoreDeliveryType: '1',
          couponStockFixed:
            '{"dayLimit":{"minValue":"","maxValue":"","value":100}}',
          externalDeliveryChannel:
            '[{"externalDeliveryChannelType":2,"externalDeliveryChannelCode":82,"externalDeliveryChannelName":"聊天购"}]',
          budget: '{"budgetId":"400000000005416533","budgetName":"LQ红包预算"}',
          _signupShopType: '0',
          userScope: '0',
          activityType: '0',
          inStoreDeliveryType: '1',
          exchangeTimeRangeList: '[{"start":"00:00:00","end":"23:59:59"}]',
          name: 'test-反圈-全部时段',
          creator: '{"id":"WB899541","prefix":"wb-zyh899541","name":"张宇航"}',
          _playId: '保存后自动生成',
          dateRange:
            '{"start":"2023-10-09 00:00:00","end":"2023-10-22 23:59:59"}',
          bizUseChannel: '10',
          couponName: '{"value":"test-红包名称"}',
          redirectUrl: 'www.taobao.com',
          useStoreType: '1',
          couponValid: '{"fromDraw":{"value":0}}',
          _workspace_uuid_: '3oNNWxkJYzc6eWQyKz5Lp2',
          effectiveChannel: '3',
          rule: '{"saleFixed":{"condition":10000,"discount":101,"elemeSubsidy":101}}',
          drawLimitFixed:
            '{"dayLimit":{"minValue":"","maxValue":"","value":1}}',
        };

        const baseinfo = {
          allowCancel: 2,
          signUpShopType: 0,
          description: 'test-反圈-全部时段test-反圈-全部时段',
          activityTime: {
            weeks: [0, 1, 2, 3, 4, 5, 6],
            batchRange: {
              type: 'all',
              list: [{ start: '00:00', end: '23:59' }],
            },
            start: moment('2023-10-08T16:00:00.000Z'),
            end: moment('2023-10-22T15:59:59.000Z'),
          },
          applyPlatform: 1,
          activityType: 2,
          name: 'test-反圈-全部时段',
        };

        const viewJson = formatMarketPlay2ViewJson4InverseSelection({
          // @ts-ignore
          formData,
          baseinfo,
        });

        expect(viewJson).toStrictEqual({
          activityRule: [
            {
              conditionType: 2,
              condition: 10000,
              discountType: 1,
              discount: 101,
              platformSubsidy: 101,

              agentSubsidy: undefined,
            },
          ],
          activityMarketingInfo: {
            userScope: 0,
            budgetId: '400000000005416533',
            deliveryType: 3,
            budgetName: 'LQ红包预算',
            availableDays: 0,
            activityExternalDeliveryChannelList: [
              {
                externalDeliveryChannelType: 2,
                externalDeliveryChannelCode: 82,
                externalDeliveryChannelName: '聊天购',
              },
            ],
            inStoreDeliveryType: '1',
            outStoreDeliveryType: '1',
            bizUseChannel: '10',
            couponName: 'test-红包名称',
            redirectUrl: 'www.taobao.com',
            useStoreType: 1,

            availableStartTime: undefined,
            availableEndTime: undefined,
          },
          activityLimitRule: {
            dayCountLimit: 100,
            dayCountLimitMax: 100,
            userDayCountLimit: 1,
            userDayCountLimitMax: 1,

            totalCountLimit: undefined,
            totalCountLimitMax: undefined,
            userTotalCountLimit: undefined,
            userTotalCountLimitMax: undefined,
          },
          periodDTOList: [{ openTime: '00:00:00', closeTime: '23:59:59' }],
          exchangePeriodDTOList: [
            { openTime: '00:00:00', closeTime: '23:59:59' },
          ],
          weekday: '0,1,2,3,4,5,6',
        });
      });

      test('全时段-聊天购-券有效期天-红包全天-无预算-异常场景：无预算，无bizUseChannel，', () => {
        const formData = {
          outStoreDeliveryType: '1',
          couponStockFixed:
            '{"dayLimit":{"minValue":"","maxValue":"","value":100}}',
          externalDeliveryChannel:
            '[{"externalDeliveryChannelType":2,"externalDeliveryChannelCode":82,"externalDeliveryChannelName":"聊天购"}]',
          budget: null,
          _signupShopType: '0',
          userScope: '0',
          activityType: '0',
          inStoreDeliveryType: '1',
          exchangeTimeRangeList: '[{"start":"00:00:00","end":"23:59:59"}]',
          name: 'test-反圈-全部时段',
          creator: '{"id":"WB899541","prefix":"wb-zyh899541","name":"张宇航"}',
          _playId: '保存后自动生成',
          dateRange:
            '{"start":"2023-10-09 00:00:00","end":"2023-10-22 23:59:59"}',
          bizUseChannel: null,
          couponName: null,
          redirectUrl: 'www.taobao.com',
          useStoreType: '1',
          couponValid: '{"fromDraw":{"value":0}}',
          _workspace_uuid_: '3oNNWxkJYzc6eWQyKz5Lp2',
          effectiveChannel: '3',
          rule: '{"saleFixed":{"condition":10000,"discount":101,"elemeSubsidy":101}}',
          drawLimitFixed:
            '{"dayLimit":{"minValue":"","maxValue":"","value":1}}',
        };

        const baseinfo = {
          allowCancel: 2,
          signUpShopType: 0,
          description: 'test-反圈-全部时段test-反圈-全部时段',
          activityTime: {
            weeks: [0, 1, 2, 3, 4, 5, 6],
            batchRange: {
              type: 'all',
              list: [{ start: '00:00', end: '23:59' }],
            },
            start: moment('2023-10-08T16:00:00.000Z'),
            end: moment('2023-10-22T15:59:59.000Z'),
          },
          applyPlatform: 1,
          activityType: 2,
          name: 'test-反圈-全部时段',
        };

        const viewJson = formatMarketPlay2ViewJson4InverseSelection({
          // @ts-ignore
          formData,
          baseinfo,
        });

        expect(viewJson).toStrictEqual({
          activityRule: [
            {
              conditionType: 2,
              condition: 10000,
              discountType: 1,
              discount: 101,
              platformSubsidy: 101,

              agentSubsidy: undefined,
            },
          ],
          activityMarketingInfo: {
            userScope: 0,
            budgetId: undefined,
            budgetName: undefined,
            deliveryType: 3,
            availableDays: 0,
            activityExternalDeliveryChannelList: [
              {
                externalDeliveryChannelType: 2,
                externalDeliveryChannelCode: 82,
                externalDeliveryChannelName: '聊天购',
              },
            ],
            inStoreDeliveryType: '1',
            outStoreDeliveryType: '1',
            bizUseChannel: undefined,
            couponName: undefined,
            redirectUrl: 'www.taobao.com',
            useStoreType: 1,

            availableStartTime: undefined,
            availableEndTime: undefined,
          },
          activityLimitRule: {
            dayCountLimit: 100,
            dayCountLimitMax: 100,
            userDayCountLimit: 1,
            userDayCountLimitMax: 1,

            totalCountLimit: undefined,
            totalCountLimitMax: undefined,
            userTotalCountLimit: undefined,
            userTotalCountLimitMax: undefined,
          },
          periodDTOList: [{ openTime: '00:00:00', closeTime: '23:59:59' }],
          exchangePeriodDTOList: [
            { openTime: '00:00:00', closeTime: '23:59:59' },
          ],
          weekday: '0,1,2,3,4,5,6',
        });
      });
    });

    describe('formatViewJson2MarketPlay', () => {
      test('日常-正招-全时段-允许取消-无业务线-固定门槛-无外投渠道-红包全天-无预算', () => {
        const viewDto = {
          activityRule: [
            {
              conditionType: 2,
              condition: 10000,
              discountType: 1,
              discount: 100,
              platformSubsidy: 0,
            },
          ],
          activityMarketingInfo: {
            userScope: 0,
            deliveryType: 1,
            availableDays: 0,
            activityExternalDeliveryChannelList: null,
            inStoreDeliveryType: '1',
            outStoreDeliveryType: '2',
          },
          activityLimitRule: {
            dayCountLimit: 1,
            dayCountLimitMax: 100,
            userDayCountLimit: 1,
            userDayCountLimitMax: 2,
          },
          periodDTOList: [
            {
              openTime: '00:00:00',
              closeTime: '23:59:59',
            },
          ],
          exchangePeriodDTOList: [
            {
              openTime: '00:00:00',
              closeTime: '23:59:59',
            },
          ],
          weekday: '0,1,2,3,4,5,6',
        };

        const baseInfo = {
          createdUserId: '213137',
          createdUserName: '杨辰',
          beginTime: '2023-10-07T16:00:00.000Z',
          endTime: '2023-10-22T15:59:59.000Z',
          activitySignType: 'FORWARD_SIGN' as ActivitySignType,
          name: 'test-日常正招全部时段-无业务线',
          signUpShopType: 0,
        };
        const formData = formatViewJson2MarketPlay({
          // @ts-ignore
          viewDto,
          baseInfo,
        });

        expect(formData).toStrictEqual({
          outStoreDeliveryType: '2',
          budget: '{}',
          _signupShopType: '0',
          userScope: '0',
          inStoreDeliveryType: '1',
          exchangeTimeRangeList: '[{"start":"00:00","end":"23:59"}]',
          name: 'test-日常正招全部时段-无业务线',
          creator: '{"name":"杨辰","id":"213137"}',
          couponStock: '{"dayLimit":{"minValue":1,"maxValue":100}}',
          couponValid: '{"fromDraw":{"value":"0"}}',
          drawLimit: '{"dayLimit":{"minValue":1,"maxValue":2}}',
          effectiveChannel: '1',
          rule: '{"saleFixed":{"condition":10000,"discount":100,"elemeSubsidy":0,"agentSubsidy":0}}',

          couponStockFixed: '{"dayLimit":{"value":100}}',
          drawLimitFixed: '{"dayLimit":{"value":2}}',

          externalDeliveryChannel: undefined,
          launchChannel: undefined,
        });
      });

      test('日常-正招-全时段-允许取消-无业务线-固定门槛-无外投渠道-红包全天-无预算-异常场景：老活动', () => {
        const viewDto = {
          activityRule: [
            {
              conditionType: 2,
              condition: 10000,
              discountType: 1,
              discount: 100,
              platformSubsidy: 0,
            },
          ],
          activityMarketingInfo: {
            userScope: 0,
            deliveryType: 1,
            availableDays: 0,
            deliveryChannel: '1',
          },
          activityLimitRule: {
            dayCountLimit: 1,
            dayCountLimitMax: 100,
            userDayCountLimit: 1,
            userDayCountLimitMax: 2,
          },
          periodDTOList: [
            {
              openTime: '00:00:00',
              closeTime: '23:59:59',
            },
          ],
          exchangePeriodDTOList: null,
          weekday: '0,1,2,3,4,5,6',
        };

        const baseInfo = {
          createdUserId: '213137',
          createdUserName: '杨辰',
          beginTime: '2023-10-07T16:00:00.000Z',
          endTime: '2023-10-22T15:59:59.000Z',
          activitySignType: 'FORWARD_SIGN' as ActivitySignType,
          name: 'test-日常正招全部时段-无业务线',
          signUpShopType: 0,
        };
        const formData = formatViewJson2MarketPlay({
          // @ts-ignore
          viewDto,
          baseInfo,
        });

        expect(formData).toStrictEqual({
          budget: '{}',
          _signupShopType: '0',
          userScope: '0',
          exchangeTimeRangeList: '[{"start":"00:00","end":"23:59"}]',
          name: 'test-日常正招全部时段-无业务线',
          creator: '{"name":"杨辰","id":"213137"}',
          couponStock: '{"dayLimit":{"minValue":1,"maxValue":100}}',
          couponValid: '{"fromDraw":{"value":"0"}}',
          drawLimit: '{"dayLimit":{"minValue":1,"maxValue":2}}',
          effectiveChannel: '1',
          rule: '{"saleFixed":{"condition":10000,"discount":100,"elemeSubsidy":0,"agentSubsidy":0}}',
          launchChannel: '1',

          couponStockFixed: '{"dayLimit":{"value":100}}',
          drawLimitFixed: '{"dayLimit":{"value":2}}',

          externalDeliveryChannel: undefined,
          inStoreDeliveryType: undefined,
          outStoreDeliveryType: undefined,
        });
      });

      test('日常-正招-区间时段-允许取消-无业务线-固定门槛-包含外投渠道-红包区间-有预算', () => {
        const viewDto = {
          activityLimitRule: {
            totalCountLimit: 100,
            totalCountLimitMax: 200,
            userTotalCountLimit: 1,
            userTotalCountLimitMax: 2,
          },
          activityMarketingInfo: {
            activityExternalDeliveryChannelList: [
              {
                externalDeliveryChannelCode: 5,
                externalDeliveryChannelName: '新零售导购红包雨',
                externalDeliveryChannelType: 1,
              },
              {
                externalDeliveryChannelCode: 7,
                externalDeliveryChannelName: '新零售导购地推拉新',
                externalDeliveryChannelType: 1,
              },
            ],
            availableEndTime: 1698163199000,
            availableStartTime: 1696867200000,
            budgetId: '400000000005416533',
            budgetName: 'LQ红包预算',
            deliveryType: 3,
            inStoreDeliveryType: 1,
            outStoreDeliveryType: 1,
            userScope: 0,
          },
          activityRule: [
            {
              condition: 10000,
              discount: 100,
              platformSubsidy: 50,
            },
          ],
          activityType: 1000012,
          attributes: {},
          exchangePeriodDTOList: [
            {
              closeTime: '05:00:59',
              openTime: '04:00:00',
            },
            {
              closeTime: '06:00:59',
              openTime: '05:01:00',
            },
          ],
          periodDTOList: [
            {
              closeTime: '01:00:59',
              openTime: '00:00:00',
            },
            {
              closeTime: '02:00:59',
              openTime: '01:01:00',
            },
            {
              closeTime: '03:00:59',
              openTime: '02:01:00',
            },
          ],
          weekday: '0,1,2,3,4,5,6',
        };

        const baseInfo = {
          createdUserId: 'WB899541',
          createdUserName: '张宇航',
          beginTime: '2023-10-09T16:00:00.000Z',
          endTime: '2023-10-22T15:59:59.000Z',
          name: 'test-日常正招全部时段-无业务线',
          signUpShopType: 0,
        };
        const formData = formatViewJson2MarketPlay({
          // @ts-ignore
          viewDto,
          baseInfo,
        });

        expect(formData).toStrictEqual({
          _signupShopType: '0',
          creator: '{"name":"张宇航","id":"WB899541"}',
          name: 'test-日常正招全部时段-无业务线',
          rule: '{"saleFixed":{"condition":10000,"discount":100,"elemeSubsidy":50,"agentSubsidy":0}}',
          userScope: '0',
          effectiveChannel: '3',
          couponValid:
            '{"validRange":{"start":"2023-10-10 00:00:00","end":"2023-10-24 23:59:59"}}',
          exchangeTimeRangeList:
            '[{"start":"04:00","end":"05:00"},{"start":"05:01","end":"06:00"}]',
          couponStock: '{"totalLimit":{"minValue":100,"maxValue":200}}',
          drawLimit: '{"totalLimit":{"minValue":1,"maxValue":2}}',
          couponStockFixed: '{"totalLimit":{"value":200}}',
          drawLimitFixed: '{"totalLimit":{"value":2}}',
          budget: '{"budgetName":"LQ红包预算","budgetId":"400000000005416533"}',
          externalDeliveryChannel:
            '[{"externalDeliveryChannelCode":5,"externalDeliveryChannelName":"新零售导购红包雨","externalDeliveryChannelType":1},{"externalDeliveryChannelCode":7,"externalDeliveryChannelName":"新零售导购地推拉新","externalDeliveryChannelType":1}]',
          inStoreDeliveryType: '1',
          outStoreDeliveryType: '1',

          launchChannel: undefined,
        });
      });

      test('日常-正招-全部时段-不允许取消-有业务线-区间门槛-代理商补贴-不含外投渠道-红包天-无预算', () => {
        const viewDto = {
          activityLimitRule: {
            dayCountLimit: 100,
            dayCountLimitMax: 101,
            userDayCountLimit: 1,
            userDayCountLimitMax: 2,
          },
          activityMarketingInfo: {
            activityExternalDeliveryChannelList: [],
            availableDays: 3,
            budgetId: 'null',
            deliveryType: 3,
            inStoreDeliveryType: 1,
            outStoreDeliveryType: 2,
            userScope: 0,
          },
          activityRule: [
            {
              agentSubsidyMax: 100,
              conditionMax: 20000,
              conditionMin: 10000,
              discountMax: 200,
              discountMin: 100,
              platformSubsidyMax: 0,
            },
          ],
          activityType: 1000012,
          attributes: {},
          exchangePeriodDTOList: [
            {
              closeTime: '23:59:59',
              openTime: '00:00:00',
            },
          ],
          periodDTOList: [
            {
              closeTime: '23:59:59',
              openTime: '00:00:00',
            },
          ],
          weekday: '2',
        };

        const baseInfo = {
          createdUserId: 'WB899541',
          createdUserName: '张宇航',
          beginTime: '2023-10-09T16:00:00.000Z',
          endTime: '2023-10-10T15:59:59.000Z',
          name: 'test-日常正招全部时段-无业务线',
          signUpShopType: 2,
        };
        const formData = formatViewJson2MarketPlay({
          // @ts-ignore
          viewDto,
          baseInfo,
        });

        expect(formData).toStrictEqual({
          _signupShopType: '2',
          creator: '{"name":"张宇航","id":"WB899541"}',
          name: 'test-日常正招全部时段-无业务线',
          rule: '{"saleRange":{"minCondition":10000,"maxCondition":20000,"minDiscount":100,"maxDiscount":200,"maxElemeSubsidy":0,"maxAgentSubsidy":100}}',
          userScope: '0',
          effectiveChannel: '3',
          couponValid: '{"fromDraw":{"value":"3"}}',
          exchangeTimeRangeList: '[{"start":"00:00","end":"23:59"}]',
          couponStock: '{"dayLimit":{"minValue":100,"maxValue":101}}',
          drawLimit: '{"dayLimit":{"minValue":1,"maxValue":2}}',
          couponStockFixed: '{"dayLimit":{"value":101}}',
          drawLimitFixed: '{"dayLimit":{"value":2}}',
          budget: '{}',
          externalDeliveryChannel: '[]',
          inStoreDeliveryType: '1',
          outStoreDeliveryType: '2',

          launchChannel: undefined,
        });
      });

      test('日常-反圈-全部时段-聊天购-红包天-使用时段全天-有预算', () => {
        const viewDto = {
          activityLimitRule: {
            dayCountLimit: 100,
            dayCountLimitMax: 100,
            userDayCountLimit: 1,
            userDayCountLimitMax: 1,
          },
          activityMarketingInfo: {
            activityExternalDeliveryChannelList: [
              {
                externalDeliveryChannelCode: 82,
                externalDeliveryChannelName: '聊天购',
                externalDeliveryChannelType: 2,
              },
            ],
            availableDays: 3,
            bizUseChannel: '10',
            budgetId: '400000000005416533',
            budgetName: 'LQ红包预算',
            couponName: 'test-测试红包',
            deliveryType: 3,
            inStoreDeliveryType: 1,
            outStoreDeliveryType: 1,
            redirectUrl: 'www.taobao.com',
            useStoreType: 1,
            userScope: 0,
          },
          activityRule: [
            {
              condition: 10000,
              discount: 10,
              platformSubsidy: 10,
            },
          ],
          activityType: 1000012,
          attributes: {},
          exchangePeriodDTOList: [
            {
              closeTime: '23:59:59',
              openTime: '00:00:00',
            },
          ],
          periodDTOList: [
            {
              closeTime: '23:59:59',
              openTime: '00:00:00',
            },
          ],
          weekday: '3',
        };

        const baseInfo = {
          createdUserId: 'WB899541',
          createdUserName: '张宇航',
          beginTime: '2023-10-10T16:00:00.000Z',
          endTime: '2023-10-11T15:59:59.000Z',
          name: 'test-全部时段-聊天购',
          signUpShopType: 0,
          activitySignType: 'REVERSE_SIGN' as ActivitySignType,
        };
        const formData = formatViewJson2MarketPlay({
          // @ts-ignore
          viewDto,
          baseInfo,
        });

        expect(formData).toStrictEqual({
          _signupShopType: '0',
          creator: '{"name":"张宇航","id":"WB899541"}',
          name: 'test-全部时段-聊天购',
          rule: '{"saleFixed":{"condition":10000,"discount":10,"elemeSubsidy":10,"agentSubsidy":0}}',
          userScope: '0',
          effectiveChannel: '3',
          couponValid: '{"fromDraw":{"value":"3"}}',
          exchangeTimeRangeList: '[{"start":"00:00","end":"23:59"}]',
          couponStock: '{"dayLimit":{"minValue":100,"maxValue":100}}',
          drawLimit: '{"dayLimit":{"minValue":1,"maxValue":1}}',
          couponStockFixed: '{"dayLimit":{"value":100}}',
          drawLimitFixed: '{"dayLimit":{"value":1}}',
          budget: '{"budgetName":"LQ红包预算","budgetId":"400000000005416533"}',
          externalDeliveryChannel:
            '[{"externalDeliveryChannelCode":82,"externalDeliveryChannelName":"聊天购","externalDeliveryChannelType":2}]',
          inStoreDeliveryType: '1',
          outStoreDeliveryType: '1',
          couponName: '{"value":"test-测试红包"}',
          bizUseChannel: '10',
          redirectUrl: 'www.taobao.com',
          useStoreType: '1',

          launchChannel: undefined,
        });
      });

      test('日常-反圈-区间时段-非聊天购-红包区间-使用时段区间-有预算', () => {
        const viewDto = {
          activityLimitRule: {
            totalCountLimit: 100,
            totalCountLimitMax: 100,
            userTotalCountLimit: 1,
            userTotalCountLimitMax: 1,
          },
          activityMarketingInfo: {
            activityExternalDeliveryChannelList: [],
            availableEndTime: 1698249599000,
            availableStartTime: 1696953600000,
            bizUseChannel: '0',
            budgetId: '400000000005416533',
            budgetName: 'LQ红包预算',
            couponName: 'test-测试红包',
            deliveryType: 1,
            inStoreDeliveryType: 1,
            outStoreDeliveryType: 2,
            userScope: 0,
          },
          activityRule: [
            {
              condition: 10000,
              discount: 1,
              platformSubsidy: 1,
            },
          ],
          activityType: 1000012,
          attributes: {},
          exchangePeriodDTOList: [
            {
              closeTime: '04:00:59',
              openTime: '03:00:00',
            },
            {
              closeTime: '05:00:59',
              openTime: '04:01:00',
            },
          ],
          periodDTOList: [
            {
              closeTime: '01:00:59',
              openTime: '00:00:00',
            },
            {
              closeTime: '02:00:59',
              openTime: '01:01:00',
            },
            {
              closeTime: '03:00:59',
              openTime: '02:01:00',
            },
          ],
          weekday: '0,1,2,3,4,5,6',
        };

        const baseInfo = {
          createdUserId: 'WB899541',
          createdUserName: '张宇航',
          beginTime: '2023-10-10T16:00:00.000Z',
          endTime: '2023-10-25T15:59:59.000Z',
          name: 'test-多时段-非聊天购',
          signUpShopType: 0,
          activitySignType: 'REVERSE_SIGN' as ActivitySignType,
        };
        const formData = formatViewJson2MarketPlay({
          // @ts-ignore
          viewDto,
          baseInfo,
        });

        expect(formData).toStrictEqual({
          _signupShopType: '0',
          creator: '{"name":"张宇航","id":"WB899541"}',
          name: 'test-多时段-非聊天购',
          rule: '{"saleFixed":{"condition":10000,"discount":1,"elemeSubsidy":1,"agentSubsidy":0}}',
          userScope: '0',
          effectiveChannel: '1',
          couponValid:
            '{"validRange":{"start":"2023-10-11 00:00:00","end":"2023-10-25 23:59:59"}}',
          exchangeTimeRangeList:
            '[{"start":"03:00","end":"04:00"},{"start":"04:01","end":"05:00"}]',
          couponStock: '{"totalLimit":{"minValue":100,"maxValue":100}}',
          drawLimit: '{"totalLimit":{"minValue":1,"maxValue":1}}',
          couponStockFixed: '{"totalLimit":{"value":100}}',
          drawLimitFixed: '{"totalLimit":{"value":1}}',
          budget: '{"budgetName":"LQ红包预算","budgetId":"400000000005416533"}',
          externalDeliveryChannel: '[]',
          inStoreDeliveryType: '1',
          outStoreDeliveryType: '2',
          couponName: '{"value":"test-测试红包"}',
          bizUseChannel: '0',

          redirectUrl: undefined,
          useStoreType: undefined,
          launchChannel: undefined,
        });
      });
    });
  });

  describe('分享领券', () => {
    describe('formatMarketPlay2ViewJson4ShareCoupon', () => {
      test('全时段-固定门槛-固定日期-无代理商补贴-无预算', () => {
        const formData = {
          outStoreDeliveryType: '2',
          budget: '{}',
          _signupShopType: '0',
          weekday: '0,1,2,3,4,5,6',
          userScope: '0',
          activityType: '0',
          inStoreDeliveryType: '1',
          name: 'test-分享领券-固定门槛-固定日期',
          creator: '{"id":"WB899541","prefix":"wb-zyh899541","name":"张宇航"}',
          _playId: '保存后自动生成',
          dateRange:
            '{"start":"2023-10-12 00:00:00","end":"2023-10-12 23:59:59"}',
          timeRange: '[{"start":"00:00:00","end":"23:59:59"}]',
          couponStock: '{"dayLimit":{"minValue":1,"maxValue":1,"value":""}}',
          couponName: '优惠红包',
          childType: '6',
          couponValid: '{"fromDraw":{"value":3}}',
          drawLimit:
            '{"dayLimit":{"minValue":1,"maxValue":"","value":""},"totalLimit":{"minValue":2,"maxValue":"","value":""}}',
          _workspace_uuid_: 'ExukovQrae6xWn2rt4NgG',
          effectiveChannel: '3',
          rule: '{"saleFixed":{"condition":10000,"discount":400,"elemeSubsidy":0}}',
          shareLimit: 6,
        };

        const viewJson = formatMarketPlay2ViewJson4ShareCoupon({
          // @ts-ignore
          formData,
        });

        expect(viewJson).toStrictEqual({
          activityLimitRule: {
            dayCountLimit: 1,
            dayCountLimitMax: 1,
            userDayCountLimit: 1,
            userDayCountLimitMax: 1,
            userTotalCountLimit: 2,
            userTotalCountLimitMax: 2,

            totalCountLimit: undefined,
            totalCountLimitMax: undefined,
          },
          activityMarketingInfo: {
            availableDays: 3,
            deliveryType: 3,
            inStoreDeliveryType: '1',
            outStoreDeliveryType: '2',
            helpPeopleNumForGetCoupon: 6,
            userScope: 0,

            availableEndTime: undefined,
            availableStartTime: undefined,
            budgetId: undefined,
            budgetName: undefined,
          },
          activityRule: [
            {
              conditionType: 2,
              condition: 10000,
              discountType: 1,
              discount: 400,
              platformSubsidy: 0,

              agentSubsidy: undefined,
            },
          ],
        });
      });

      test('全时段-固定门槛-固定日期-无代理商补贴-无预算-异常场景：无预算', () => {
        const formData = {
          outStoreDeliveryType: '2',
          budget: null,
          _signupShopType: '0',
          weekday: '0,1,2,3,4,5,6',
          userScope: '0',
          activityType: '0',
          inStoreDeliveryType: '1',
          name: 'test-分享领券-固定门槛-固定日期',
          creator: '{"id":"WB899541","prefix":"wb-zyh899541","name":"张宇航"}',
          _playId: '保存后自动生成',
          dateRange:
            '{"start":"2023-10-12 00:00:00","end":"2023-10-12 23:59:59"}',
          timeRange: '[{"start":"00:00:00","end":"23:59:59"}]',
          couponStock: '{"dayLimit":{"minValue":1,"maxValue":1,"value":""}}',
          couponName: '优惠红包',
          childType: '6',
          couponValid: '{"fromDraw":{"value":3}}',
          drawLimit:
            '{"dayLimit":{"minValue":1,"maxValue":"","value":""},"totalLimit":{"minValue":2,"maxValue":"","value":""}}',
          _workspace_uuid_: 'ExukovQrae6xWn2rt4NgG',
          effectiveChannel: '3',
          rule: '{"saleFixed":{"condition":10000,"discount":400,"elemeSubsidy":0}}',
          shareLimit: 6,
        };

        const viewJson = formatMarketPlay2ViewJson4ShareCoupon({
          // @ts-ignore
          formData,
        });

        expect(viewJson).toStrictEqual({
          activityLimitRule: {
            dayCountLimit: 1,
            dayCountLimitMax: 1,
            userDayCountLimit: 1,
            userDayCountLimitMax: 1,
            userTotalCountLimit: 2,
            userTotalCountLimitMax: 2,

            totalCountLimit: undefined,
            totalCountLimitMax: undefined,
          },
          activityMarketingInfo: {
            availableDays: 3,
            deliveryType: 3,
            inStoreDeliveryType: '1',
            outStoreDeliveryType: '2',
            helpPeopleNumForGetCoupon: 6,
            userScope: 0,

            availableEndTime: undefined,
            availableStartTime: undefined,
            budgetId: undefined,
            budgetName: undefined,
          },
          activityRule: [
            {
              conditionType: 2,
              condition: 10000,
              discountType: 1,
              discount: 400,
              platformSubsidy: 0,

              agentSubsidy: undefined,
            },
          ],
        });
      });

      test('全时段-区间门槛-区间日期-有代理商补贴-有预算', () => {
        const formData = {
          outStoreDeliveryType: '2',
          budget: '{"budgetId":"400000000005416533","budgetName":"LQ红包预算"}',
          _signupShopType: '2',
          weekday: '0,1,2,3,4,5,6',
          userScope: '0',
          activityType: '0',
          inStoreDeliveryType: '1',
          name: 'test-分享领券-区间门槛-区间日期',
          creator: '{"id":"WB899541","prefix":"wb-zyh899541","name":"张宇航"}',
          _playId: '保存后自动生成',
          dateRange:
            '{"start":"2023-10-12 00:00:00","end":"2023-10-12 23:59:59"}',
          timeRange: '[{"start":"00:00:00","end":"23:59:59"}]',
          couponStock: '{"totalLimit":{"minValue":1,"maxValue":1,"value":""}}',
          couponName: '优惠红包',
          childType: '6',
          couponValid:
            '{"validRange":{"start":"2023-10-12 00:00:00","end":"2023-10-13 23:59:59"}}',
          drawLimit:
            '{"dayLimit":{"minValue":2,"maxValue":"","value":""},"totalLimit":{"minValue":3,"maxValue":"","value":""}}',
          _workspace_uuid_: 'ExukovQrae6xWn2rt4NgG',
          effectiveChannel: '3',
          rule: '{"saleRange":{"minCondition":10000,"maxCondition":20000,"minDiscount":100,"maxDiscount":200,"maxElemeSubsidy":50,"maxAgentSubsidy":50}}',
          shareLimit: 7,
        };

        const viewJson = formatMarketPlay2ViewJson4ShareCoupon({
          // @ts-ignore
          formData,
        });

        expect(viewJson).toStrictEqual({
          activityLimitRule: {
            totalCountLimit: 1,
            totalCountLimitMax: 1,
            userDayCountLimit: 2,
            userDayCountLimitMax: 2,
            userTotalCountLimit: 3,
            userTotalCountLimitMax: 3,

            dayCountLimit: undefined,
            dayCountLimitMax: undefined,
          },
          activityMarketingInfo: {
            availableStartTime: 1697040000000,
            availableEndTime: 1697212799000,
            budgetId: '400000000005416533',
            budgetName: 'LQ红包预算',
            deliveryType: 3,
            inStoreDeliveryType: '1',
            outStoreDeliveryType: '2',
            helpPeopleNumForGetCoupon: 7,
            userScope: 0,

            availableDays: undefined,
          },
          activityRule: [
            {
              conditionType: 2,
              conditionMin: 10000,
              conditionMax: 20000,
              discountType: 1,
              discountMin: 100,
              discountMax: 200,
              platformSubsidyMax: 50,
              agentSubsidyMax: 50,
            },
          ],
        });
      });
    });

    describe('formatViewJson2MarketPlay4ShareCoupon', () => {
      test('全时段-固定门槛-固定日期-无代理商补贴-无预算', () => {
        const viewDto = {
          activityLimitRule: {
            dayCountLimit: 1,
            dayCountLimitMax: 1,
            userDayCountLimit: 1,
            userDayCountLimitMax: 1,
            userTotalCountLimit: 2,
            userTotalCountLimitMax: 2,
          },
          activityMarketingInfo: {
            activityExternalDeliveryChannelList: [],
            availableDays: 3,
            budgetId: 'null',
            deliveryType: 3,
            helpPeopleNumForGetCoupon: 6,
            inStoreDeliveryType: 1,
            outStoreDeliveryType: 2,
            userScope: 0,
          },
          activityRule: [
            {
              condition: 10000,
              discount: 400,
              platformSubsidy: 0,
            },
          ],
          activityType: 1000020,
          attributes: {},
        };

        const baseInfo = {
          createdUserId: 'WB899541',
          createdUserName: '张宇航',
          beginTime: '',
          endTime: '',
          name: 'test-分享领券-固定门槛-固定日期',
          signUpShopType: 0,
        };
        const formData = formatViewJson2MarketPlay4ShareCoupon({
          // @ts-ignore
          viewDto,
          baseInfo,
        });

        expect(formData).toStrictEqual({
          _signupShopType: '0',
          creator: '{"name":"张宇航","id":"WB899541"}',
          name: 'test-分享领券-固定门槛-固定日期',
          rule: '{"saleFixed":{"condition":10000,"discount":400,"elemeSubsidy":0,"agentSubsidy":0}}',
          userScope: '0',
          inStoreDeliveryType: '1',
          outStoreDeliveryType: '2',
          effectiveChannel: '3',
          couponValid: '{"fromDraw":{"value":"3"}}',
          couponStock: '{"dayLimit":{"minValue":1,"maxValue":1}}',
          drawLimit:
            '{"dayLimit":{"minValue":1,"maxValue":1},"totalLimit":{"minValue":2,"maxValue":2}}',
          budget: '{}',
          shareLimit: '6',
        });
      });

      test('全时段-区间门槛-区间日期-有代理商补贴-有预算', () => {
        const viewDto = {
          activityLimitRule: {
            totalCountLimit: 1,
            totalCountLimitMax: 1,
            userDayCountLimit: 2,
            userDayCountLimitMax: 2,
            userTotalCountLimit: 3,
            userTotalCountLimitMax: 3,
          },
          activityMarketingInfo: {
            activityExternalDeliveryChannelList: [],
            availableEndTime: 1697212799000,
            availableStartTime: 1697040000000,
            budgetId: '400000000005416533',
            budgetName: 'LQ红包预算',
            deliveryType: 3,
            helpPeopleNumForGetCoupon: 7,
            inStoreDeliveryType: 1,
            outStoreDeliveryType: 2,
            userScope: 0,
          },
          activityRule: [
            {
              agentSubsidy: 50,
              conditionMin: 10000,
              conditionMax: 20000,
              discountMin: 100,
              discountMax: 200,
              platformSubsidyMax: 50,
              agentSubsidyMax: 50,
            },
          ],
          activityType: 1000020,
          attributes: {},
        };

        const baseInfo = {
          createdUserId: 'WB899541',
          createdUserName: '张宇航',
          beginTime: '',
          endTime: '',
          name: 'test-分享领券-区间门槛-区间日期',
          signUpShopType: 2,
        };
        const formData = formatViewJson2MarketPlay4ShareCoupon({
          // @ts-ignore
          viewDto,
          baseInfo,
        });

        expect(formData).toStrictEqual({
          _signupShopType: '2',
          creator: '{"name":"张宇航","id":"WB899541"}',
          name: 'test-分享领券-区间门槛-区间日期',
          rule: '{"saleRange":{"minCondition":10000,"maxCondition":20000,"minDiscount":100,"maxDiscount":200,"maxElemeSubsidy":50,"maxAgentSubsidy":50}}',
          userScope: '0',
          inStoreDeliveryType: '1',
          outStoreDeliveryType: '2',
          effectiveChannel: '3',
          couponValid:
            '{"validRange":{"start":"2023-10-12 00:00:00","end":"2023-10-13 23:59:59"}}',
          couponStock: '{"totalLimit":{"minValue":1,"maxValue":1}}',
          drawLimit:
            '{"dayLimit":{"minValue":2,"maxValue":2},"totalLimit":{"minValue":3,"maxValue":3}}',
          budget: '{"budgetName":"LQ红包预算","budgetId":"400000000005416533"}',
          shareLimit: '7',
        });
      });
    });
  });
});
