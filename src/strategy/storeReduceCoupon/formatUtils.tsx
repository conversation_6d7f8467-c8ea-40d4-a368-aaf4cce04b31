import moment from 'moment';
import {
  BaseInfoForCreationContext,
  BaseInfoForDetailsContext,
  MarketPlay,
  MarketPlayForPlayData,
  MarketPlayForPlayData4InverseSelection,
  StoreReduceCouponRule,
} from './constant';
import { isValueSet, parseJsonSafe } from './utils';

/**
 * 全店满减券 - 创建活动站换逻辑， 玩法配置语法 转 viewJson
 */
export const formatMarketPlay2ViewJson = ({
  formData,
  baseinfo,
}: {
  formData: MarketPlayForPlayData;
  baseinfo: BaseInfoForCreationContext;
}) => {
  const rule = parseJsonSafe(formData.rule);
  const budget = formData.budget ? parseJsonSafe(formData.budget) : null;
  const couponStock = parseJsonSafe(formData.couponStock);
  const drawLimit = parseJsonSafe(formData.drawLimit);
  const couponValidRule = parseJsonSafe(formData.couponValid);
  const exchangeTimeRangeList = parseJsonSafe(formData.exchangeTimeRangeList);
  const externalDeliveryChannel =
    formData.externalDeliveryChannel &&
    parseJsonSafe(formData.externalDeliveryChannel);
  const inStoreDeliveryType = formData.inStoreDeliveryType;
  const outStoreDeliveryType = formData.outStoreDeliveryType;

  const viewJson: StoreReduceCouponRule = {
    activityRule: [
      rule.saleFixed
        ? {
            conditionType: 2,
            condition: rule.saleFixed.condition,
            discountType: 1,
            discount: rule.saleFixed.discount,
            platformSubsidy: rule.saleFixed.elemeSubsidy,
            agentSubsidy: rule.saleFixed.agentSubsidy,
          }
        : {
            conditionType: 2,
            conditionMin: rule.saleRange.minCondition,
            conditionMax: rule.saleRange.maxCondition,
            discountType: 1,
            discountMin: rule.saleRange.minDiscount,
            discountMax: rule.saleRange.maxDiscount,
            platformSubsidyMax: rule.saleRange.maxElemeSubsidy,
            agentSubsidyMax: rule.saleRange.maxAgentSubsidy,
          },
    ],
    activityMarketingInfo: {
      userScope: +formData.userScope,
      deliveryType: +formData.effectiveChannel,
      budgetId: budget?.budgetId,
      budgetName: budget?.budgetName,
      availableDays: couponValidRule.fromDraw
        ? +couponValidRule.fromDraw.value
        : undefined,
      availableStartTime: couponValidRule.validRange
        ? moment(
            couponValidRule.validRange.start,
            'YYYY-MM-DD HH:mm:ss'
          ).valueOf()
        : undefined,
      availableEndTime: couponValidRule.validRange
        ? moment(
            couponValidRule.validRange.end,
            'YYYY-MM-DD HH:mm:ss'
          ).valueOf()
        : undefined,
      activityExternalDeliveryChannelList: externalDeliveryChannel,
      inStoreDeliveryType: inStoreDeliveryType as '1' | '2',
      outStoreDeliveryType: outStoreDeliveryType as '1' | '2',
    },
    activityLimitRule: {
      dayCountLimit: couponStock?.dayLimit?.minValue,
      dayCountLimitMax: couponStock?.dayLimit?.maxValue,
      totalCountLimit: couponStock?.totalLimit?.minValue,
      totalCountLimitMax: couponStock?.totalLimit?.maxValue,
      userDayCountLimit: drawLimit?.dayLimit?.minValue,
      userDayCountLimitMax: drawLimit?.dayLimit?.maxValue,
      userTotalCountLimit: drawLimit?.totalLimit?.minValue,
      userTotalCountLimitMax: drawLimit?.totalLimit?.maxValue,
    },
    periodDTOList: (baseinfo?.activityTime.batchRange.list || []).map(
      (r: any) => {
        return {
          openTime: r.start + ':00',
          closeTime: r.end + ':59',
        };
      }
    ),
    exchangePeriodDTOList: exchangeTimeRangeList.map((r: any) => {
      return {
        openTime: r.start,
        closeTime: r.end,
      };
    }),
    weekday: (baseinfo?.activityTime.weeks || []).join(','),
  };

  return viewJson;
};

/**
 * 全店满减券 - 反圈详情展示， viewJson 转 玩法配置语法
 */
export const formatMarketPlay2ViewJson4InverseSelection = ({
  formData,
  baseinfo,
}: {
  formData: MarketPlayForPlayData4InverseSelection;
  baseinfo: BaseInfoForCreationContext;
}) => {
  const rule = parseJsonSafe(formData.rule);
  const budget = formData.budget ? parseJsonSafe(formData.budget) : null;
  const couponStock = parseJsonSafe(formData.couponStockFixed);
  const drawLimit = parseJsonSafe(formData.drawLimitFixed);
  const couponValidRule = parseJsonSafe(formData.couponValid);
  const couponName = parseJsonSafe(formData.couponName);
  const exchangeTimeRangeList = parseJsonSafe(formData.exchangeTimeRangeList);
  const externalDeliveryChannel =
    formData.externalDeliveryChannel &&
    parseJsonSafe(formData.externalDeliveryChannel);
  const inStoreDeliveryType = formData.inStoreDeliveryType;
  const outStoreDeliveryType = formData.outStoreDeliveryType;

  const viewJson = {
    activityRule: [
      {
        conditionType: 2,
        condition: rule.saleFixed.condition,
        discountType: 1,
        discount: rule.saleFixed.discount,
        platformSubsidy: rule.saleFixed.elemeSubsidy,
        agentSubsidy: rule.saleFixed.agentSubsidy,
      },
    ],
    activityMarketingInfo: {
      userScope: +formData.userScope,
      budgetId: budget?.budgetId,
      deliveryType: +formData.effectiveChannel,
      budgetName: budget?.budgetName,
      availableDays: couponValidRule.fromDraw
        ? +couponValidRule.fromDraw.value
        : undefined,
      availableStartTime: couponValidRule.validRange
        ? moment(
            couponValidRule.validRange.start,
            'YYYY-MM-DD HH:mm:ss'
          ).valueOf()
        : undefined,
      availableEndTime: couponValidRule.validRange
        ? moment(
            couponValidRule.validRange.end,
            'YYYY-MM-DD HH:mm:ss'
          ).valueOf()
        : undefined,
      activityExternalDeliveryChannelList: externalDeliveryChannel,
      inStoreDeliveryType: inStoreDeliveryType,
      outStoreDeliveryType: outStoreDeliveryType,

      // 反圈 - 聊天购相关字段
      bizUseChannel: formData?.bizUseChannel
        ? formData?.bizUseChannel + ''
        : undefined,
      couponName: couponName?.value || undefined,
      redirectUrl: formData?.redirectUrl || undefined,
      useStoreType: isValueSet(formData?.useStoreType)
        ? +(formData?.useStoreType as 1 | 0)
        : undefined,
    },
    activityLimitRule: {
      dayCountLimit: couponStock.dayLimit?.value,
      dayCountLimitMax: couponStock.dayLimit?.value,
      totalCountLimit: couponStock.totalLimit?.value,
      totalCountLimitMax: couponStock.totalLimit?.value,
      userDayCountLimit: drawLimit.dayLimit?.value,
      userDayCountLimitMax: drawLimit.dayLimit?.value,
      userTotalCountLimit: drawLimit.totalLimit?.value,
      userTotalCountLimitMax: drawLimit.totalLimit?.value,
    },
    periodDTOList: (baseinfo?.activityTime?.batchRange?.list || []).map(
      (r: any) => {
        return {
          openTime: r.start + ':00',
          closeTime: r.end + ':59',
        };
      }
    ),
    exchangePeriodDTOList: exchangeTimeRangeList?.map((r: any) => {
      return {
        openTime: r.start,
        closeTime: r.end,
      };
    }),
    weekday: (baseinfo?.activityTime?.weeks || []).join(','),
  };

  return viewJson;
};


/**
 * 全店满减券 - 详情展示， viewJson 转 玩法配置语法
 */
export const formatViewJson2MarketPlay = ({
  viewDto,
  baseInfo,
}: {
  viewDto: StoreReduceCouponRule;
  baseInfo: BaseInfoForDetailsContext;
}) => {
  const {
    activityLimitRule,
    activityMarketingInfo,
    activityRule,
    exchangePeriodDTOList,
  } = viewDto;

  let creator = {}; // 创建人名称、创建人id
  const name = baseInfo.name; // 玩法名称
  const rule: any = {};
  let userScope = ''; // 用户类型
  let effectiveChannel = ''; // 生效渠道
  let launchChannel = ''; // 投放渠道
  const couponValidRule: any = {};
  const exchangeTimeRangeList: any = [];
  const couponStock: any = {};
  const drawLimit: any = {};
  const couponStockFixed: any = {};
  const drawLimitFixed: any = {};
  let budget = {}; //  预算
  let externalDeliveryChannel; // 限定渠道
  let inStoreDeliveryType = ''; // 店内投放方式
  let outStoreDeliveryType = ''; // 店外投放方式

  const firstRule = activityRule?.[0];
  if (isValueSet(firstRule?.discount)) {
    rule.saleFixed = {
      condition: firstRule.condition,
      discount: firstRule.discount,
      elemeSubsidy: firstRule.platformSubsidy,
      agentSubsidy: firstRule.agentSubsidy || 0,
    };
  } else if (isValueSet(firstRule?.discountMin)) {
    rule.saleRange = {
      minCondition: firstRule.conditionMin,
      maxCondition: firstRule.conditionMax,
      minDiscount: firstRule.discountMin,
      maxDiscount: firstRule.discountMax,
      maxElemeSubsidy: firstRule.platformSubsidyMax,
      maxAgentSubsidy: firstRule.agentSubsidyMax,
    };
  }
  if (isValueSet(activityMarketingInfo.userScope)) {
    userScope = activityMarketingInfo.userScope + '';
  }
  if (isValueSet(activityMarketingInfo.deliveryType)) {
    effectiveChannel = activityMarketingInfo.deliveryType + '';
  }
  if (isValueSet(activityMarketingInfo.deliveryChannel)) {
    launchChannel = activityMarketingInfo.deliveryChannel + '';
  }

  // 限定渠道
  if (isValueSet(activityMarketingInfo.activityExternalDeliveryChannelList)) {
    externalDeliveryChannel =
      activityMarketingInfo.activityExternalDeliveryChannelList;
  }
  // 店内投放方式
  if (isValueSet(activityMarketingInfo.inStoreDeliveryType)) {
    inStoreDeliveryType = activityMarketingInfo.inStoreDeliveryType + '';
  }
  // 店外投放方式
  if (isValueSet(activityMarketingInfo.outStoreDeliveryType)) {
    outStoreDeliveryType = activityMarketingInfo.outStoreDeliveryType + '';
  }

  if (isValueSet(activityMarketingInfo.availableDays)) {
    couponValidRule.fromDraw = {
      value: activityMarketingInfo.availableDays + '',
    };
  } else if (isValueSet(activityMarketingInfo.availableStartTime)) {
    couponValidRule.validRange = {
      start: moment(activityMarketingInfo.availableStartTime).format(
        'YYYY-MM-DD HH:mm:ss'
      ),
      end: moment(activityMarketingInfo.availableEndTime).format(
        'YYYY-MM-DD HH:mm:ss'
      ),
    };
  }
  if (exchangePeriodDTOList?.length) {
    exchangePeriodDTOList.forEach((p) => {
      exchangeTimeRangeList.push({
        start: p.openTime.slice(0, 'HH:mm'.length),
        end: p.closeTime.slice(0, 'HH:mm'.length),
      });
    });
  } else {
    // 没有设置兑换时段，则认定是全天
    exchangeTimeRangeList.push({ start: '00:00', end: '23:59' });
  }
  if (isValueSet(activityLimitRule.dayCountLimit)) {
    couponStock.dayLimit = {
      minValue: activityLimitRule.dayCountLimit,
      maxValue: activityLimitRule.dayCountLimitMax,
    };
    couponStockFixed.dayLimit = {
      value: activityLimitRule.dayCountLimitMax,
    };
  }
  if (isValueSet(activityLimitRule.totalCountLimit)) {
    couponStock.totalLimit = {
      minValue: activityLimitRule.totalCountLimit,
      maxValue: activityLimitRule.totalCountLimitMax,
    };
    couponStockFixed.totalLimit = {
      value: activityLimitRule.totalCountLimitMax,
    };
  }
  if (isValueSet(activityLimitRule.userDayCountLimit)) {
    drawLimit.dayLimit = {
      minValue: activityLimitRule.userDayCountLimit,
      maxValue: activityLimitRule.userDayCountLimitMax,
    };
    drawLimitFixed.dayLimit = {
      value: activityLimitRule.userDayCountLimitMax,
    };
  }
  if (isValueSet(activityLimitRule.userTotalCountLimit)) {
    drawLimit.totalLimit = {
      minValue: activityLimitRule.userTotalCountLimit,
      maxValue: activityLimitRule.userTotalCountLimitMax,
    };
    drawLimitFixed.totalLimit = {
      value: activityLimitRule.userTotalCountLimitMax,
    };
  }
  if (
    isValueSet(baseInfo.createdUserId) &&
    isValueSet(baseInfo.createdUserName)
  ) {
    creator = {
      name: `${baseInfo.createdUserName}`,
      id: baseInfo.createdUserId,
    };
  }
  if (
    isValueSet(activityMarketingInfo.budgetName) &&
    isValueSet(activityMarketingInfo.budgetId)
  ) {
    budget = {
      budgetName: activityMarketingInfo.budgetName,
      budgetId: activityMarketingInfo.budgetId + '',
    };
  }

  const isOldActivity = activityMarketingInfo?.deliveryChannel;

  let fields: MarketPlay = {
    _signupShopType: baseInfo.signUpShopType + '',
    creator: JSON.stringify(creator),
    name: name,
    rule: JSON.stringify(rule),
    userScope,
    effectiveChannel,
    launchChannel: isOldActivity ? launchChannel : undefined,
    couponValid: JSON.stringify(couponValidRule),
    exchangeTimeRangeList: JSON.stringify(exchangeTimeRangeList),
    couponStock: JSON.stringify(couponStock),
    drawLimit: JSON.stringify(drawLimit),
    couponStockFixed: JSON.stringify(couponStockFixed), // 全能超市发券数量是固定值
    drawLimitFixed: JSON.stringify(drawLimitFixed), // 全能超市领券限制是固定值
    budget: JSON.stringify(budget),
    externalDeliveryChannel: isOldActivity
      ? undefined
      : JSON.stringify(externalDeliveryChannel),
    inStoreDeliveryType: isOldActivity ? undefined : inStoreDeliveryType,
    outStoreDeliveryType: isOldActivity ? undefined : outStoreDeliveryType,
  };

  if (baseInfo.activitySignType === 'REVERSE_SIGN') {
    // 反圈相关
    let couponName: any = {}; // 券名称
    let bizUseChannel = undefined; // 核销渠道
    let redirectUrl = undefined; // 跳转链接
    let useStoreType = undefined; // 使用门店类型

    if (isValueSet(activityMarketingInfo.couponName)) {
      couponName.value = activityMarketingInfo.couponName + '';
    }
    if (isValueSet(activityMarketingInfo.bizUseChannel)) {
      bizUseChannel = activityMarketingInfo.bizUseChannel + '';
    }
    if (isValueSet(activityMarketingInfo.redirectUrl)) {
      redirectUrl = activityMarketingInfo.redirectUrl + '';
    }
    if (isValueSet(activityMarketingInfo.useStoreType)) {
      useStoreType = activityMarketingInfo.useStoreType + '';
    }

    fields = {
      ...fields,
      couponName: JSON.stringify(couponName),
      bizUseChannel, // 核销渠道
      redirectUrl, // 跳转链接
      useStoreType, // 门店类型
    };
  }

  return fields;
};

/**
 * 分享领券 - 创建活动站换逻辑， 玩法配置语法 转 viewJson
 */
export const formatMarketPlay2ViewJson4ShareCoupon = ({
  formData,
}: {
  formData: MarketPlayForPlayData & { shareLimit: number | string };
}) => {
  const rule = parseJsonSafe(formData.rule);
  const budget = formData.budget ? parseJsonSafe(formData.budget) : null;
  const couponStock = parseJsonSafe(formData.couponStock);
  const drawLimit = parseJsonSafe(formData.drawLimit);
  const couponValidRule = parseJsonSafe(formData.couponValid);
  const inStoreDeliveryType = formData.inStoreDeliveryType as '1' | '2';
  const outStoreDeliveryType = formData.outStoreDeliveryType as '1' | '2';

  const viewJson: StoreReduceCouponRule = {
    // 领券限制
    activityLimitRule: {
      dayCountLimit: couponStock.dayLimit?.minValue,
      dayCountLimitMax: couponStock.dayLimit?.maxValue,
      totalCountLimit: couponStock.totalLimit?.minValue,
      totalCountLimitMax: couponStock.totalLimit?.maxValue,
      userDayCountLimit: drawLimit.dayLimit?.minValue,
      userDayCountLimitMax: drawLimit.dayLimit?.minValue, // 分享领券不支持区间，用户只能输入一个，max 需要根据最小值补全
      userTotalCountLimit: drawLimit.totalLimit?.minValue,
      userTotalCountLimitMax: drawLimit.totalLimit?.minValue, // 分享领券不支持区间，用户只能输入一个，max 需要根据最小值补全
    },
    activityMarketingInfo: {
      availableDays: couponValidRule.fromDraw
        ? +couponValidRule.fromDraw.value
        : undefined,
      availableStartTime: couponValidRule.validRange
        ? moment(
            couponValidRule.validRange.start,
            'YYYY-MM-DD HH:mm:ss'
          ).valueOf()
        : undefined,
      availableEndTime: couponValidRule.validRange
        ? moment(
            couponValidRule.validRange.end,
            'YYYY-MM-DD HH:mm:ss'
          ).valueOf()
        : undefined,
      budgetId: budget?.budgetId,
      budgetName: budget?.budgetName,
      deliveryType: +formData?.effectiveChannel,
      inStoreDeliveryType,
      outStoreDeliveryType,
      helpPeopleNumForGetCoupon: +formData?.shareLimit,
      userScope: +formData?.userScope,
    },
    activityRule: [
      rule.saleFixed
        ? {
            conditionType: 2,
            condition: rule.saleFixed.condition,
            discountType: 1,
            discount: rule.saleFixed.discount,
            platformSubsidy: rule.saleFixed.elemeSubsidy,
            agentSubsidy: rule.saleFixed.agentSubsidy,
          }
        : {
            conditionType: 2,
            conditionMin: rule.saleRange.minCondition,
            conditionMax: rule.saleRange.maxCondition,
            discountType: 1,
            discountMin: rule.saleRange.minDiscount,
            discountMax: rule.saleRange.maxDiscount,
            platformSubsidyMax: rule.saleRange.maxElemeSubsidy,
            agentSubsidyMax: rule.saleRange.maxAgentSubsidy,
          },
    ],
  };

  return viewJson;
};

/**
 * 分享领券 - 详情展示， viewJson 转 玩法配置语法
 */
export const formatViewJson2MarketPlay4ShareCoupon = ({
  viewDto,
  baseInfo,
}: {
  viewDto: StoreReduceCouponRule;
  baseInfo: BaseInfoForDetailsContext;
}) => {
  const { activityLimitRule, activityMarketingInfo, activityRule } = viewDto;

  let creator = {}; // 创建人名称、创建人id
  const name = baseInfo.name; // 玩法名称
  const rule: any = {};
  let userScope = ''; // 用户类型
  let effectiveChannel = ''; // 生效渠道/核销渠道
  const couponValidRule: any = {};
  const couponStock: any = {};
  const drawLimit: any = {};
  let budget = {}; //  预算
  let inStoreDeliveryType = ''; // 店内投放方式
  let outStoreDeliveryType = ''; // 店外投放方式
  let shareLimit = '';

  const firstRule = activityRule?.[0];
  if (isValueSet(firstRule?.discount)) {
    rule.saleFixed = {
      condition: firstRule.condition,
      discount: firstRule.discount,
      elemeSubsidy: firstRule.platformSubsidy,
      agentSubsidy: firstRule.agentSubsidy || 0,
    };
  } else if (isValueSet(firstRule?.discountMin)) {
    rule.saleRange = {
      minCondition: firstRule.conditionMin,
      maxCondition: firstRule.conditionMax,
      minDiscount: firstRule.discountMin,
      maxDiscount: firstRule.discountMax,
      maxElemeSubsidy: firstRule.platformSubsidyMax,
      maxAgentSubsidy: firstRule.agentSubsidyMax,
    };
  }
  if (isValueSet(activityMarketingInfo.userScope)) {
    userScope = activityMarketingInfo.userScope + '';
  }
  if (isValueSet(activityMarketingInfo.deliveryType)) {
    effectiveChannel = activityMarketingInfo.deliveryType + '';
  }
  // 店内投放方式
  if (isValueSet(activityMarketingInfo.inStoreDeliveryType)) {
    inStoreDeliveryType = activityMarketingInfo.inStoreDeliveryType + '';
  }
  // 店外投放方式
  if (isValueSet(activityMarketingInfo.outStoreDeliveryType)) {
    outStoreDeliveryType = activityMarketingInfo.outStoreDeliveryType + '';
  }

  if (isValueSet(activityMarketingInfo.availableDays)) {
    couponValidRule.fromDraw = {
      value: activityMarketingInfo.availableDays + '',
    };
  } else if (isValueSet(activityMarketingInfo.availableStartTime)) {
    couponValidRule.validRange = {
      start: moment(activityMarketingInfo.availableStartTime).format(
        'YYYY-MM-DD HH:mm:ss'
      ),
      end: moment(activityMarketingInfo.availableEndTime).format(
        'YYYY-MM-DD HH:mm:ss'
      ),
    };
  }
  if (isValueSet(activityLimitRule.dayCountLimit)) {
    couponStock.dayLimit = {
      minValue: activityLimitRule.dayCountLimit,
      maxValue: activityLimitRule.dayCountLimitMax,
    };
  }
  if (isValueSet(activityLimitRule.totalCountLimit)) {
    couponStock.totalLimit = {
      minValue: activityLimitRule.totalCountLimit,
      maxValue: activityLimitRule.totalCountLimitMax,
    };
  }
  if (isValueSet(activityLimitRule.userDayCountLimit)) {
    drawLimit.dayLimit = {
      minValue: activityLimitRule.userDayCountLimit,
      maxValue: activityLimitRule.userDayCountLimitMax,
    };
  }
  if (isValueSet(activityLimitRule.userTotalCountLimit)) {
    drawLimit.totalLimit = {
      minValue: activityLimitRule.userTotalCountLimit,
      maxValue: activityLimitRule.userTotalCountLimitMax,
    };
  }
  if (
    isValueSet(baseInfo.createdUserId) &&
    isValueSet(baseInfo.createdUserName)
  ) {
    creator = {
      name: `${baseInfo.createdUserName}`,
      id: baseInfo.createdUserId,
    };
  }
  if (
    isValueSet(activityMarketingInfo.budgetName) &&
    isValueSet(activityMarketingInfo.budgetId)
  ) {
    budget = {
      budgetName: activityMarketingInfo.budgetName,
      budgetId: activityMarketingInfo.budgetId + '',
    };
  }
  if (isValueSet(activityMarketingInfo.helpPeopleNumForGetCoupon)) {
    shareLimit = activityMarketingInfo.helpPeopleNumForGetCoupon + '';
  }

  return {
    _signupShopType: baseInfo.signUpShopType + '',
    creator: JSON.stringify(creator),
    name: name,
    rule: JSON.stringify(rule),
    userScope,
    inStoreDeliveryType,
    outStoreDeliveryType,
    effectiveChannel,
    couponValid: JSON.stringify(couponValidRule),
    couponStock: JSON.stringify(couponStock),
    drawLimit: JSON.stringify(drawLimit),
    budget: JSON.stringify(budget),
    shareLimit,
  };
};
