import { maskMS } from "../../common";
import { ActivityTypeEnum } from "../../constants";
import { CreationContext, ManageOrgSelectDateTs } from "../../models";
import * as api from "../../api";
import BigNumber from "bignumber.js";
import { StoreReduceCouponRule } from "./constant";
import { Moment } from "moment";
import { confirmMktRuleError } from "../utils";


export function isValueSet(v: any) {
  return v !== null && v !== undefined && v !== "";
}
export function parseJsonSafe(JSONobj: any) {
  let res;
  try {
    res = JSON.parse(JSONobj);
  } catch (e) {
    res = JSONobj;
  }
  return res;
}

interface BaseInfoTS {
  manageOrgList: ManageOrgSelectDateTs[];
  activityTime: {
    start: Moment;
    end: Moment;
    weeks: number[];
  };
}

function divide(v: number | undefined) {
  if (isValueSet(v)) {
    return new BigNumber(v || 0).dividedBy(100).toNumber();
  } else {
    return undefined;
  }
}

// 获取校验规则基础数据
function getMktRuleBaseCondition(
  context: CreationContext,
  baseinfo: BaseInfoTS
) {
  return {
    activityType: ActivityTypeEnum.STORE_REDUCTION,
    investmentType: context?.investmentType,
    investmentSchema: context?.investmentSchema,
    investmentActivityType: context?.isSingleActivity ? 0 : 1, // 单活动0
    // 管理业态
    manageOrgList: baseinfo.manageOrgList,
    beginTime: baseinfo.activityTime.start.toDate().getTime(),
    endTime: maskMS(baseinfo.activityTime.end.toDate().getTime()), // 活动结束时间
  };
}

// 查询是否不符合规则
export async function checkMktRulesForDiscountBusinessFormAndConfirmIfNeed(
  creationContext: CreationContext,
  viewJson: Partial<StoreReduceCouponRule>,
  baseinfo: BaseInfoTS
) {
  const discountRange = isValueSet(viewJson.activityRule?.[0]?.discount)
    ? ([
        divide(viewJson.activityRule?.[0]?.discount),
        divide(viewJson.activityRule?.[0]?.discount),
      ] as number[])
    : ([
        divide(viewJson.activityRule?.[0].discountMin),
        divide(viewJson.activityRule?.[0].discountMax),
      ] as number[]);
  const conditionRange = isValueSet(viewJson?.activityRule?.[0]?.condition)
    ? ([
        divide(viewJson.activityRule?.[0]?.condition),
        divide(viewJson.activityRule?.[0]?.condition),
      ] as number[])
    : ([
        divide(viewJson.activityRule?.[0].conditionMin),
        divide(viewJson.activityRule?.[0].conditionMax),
      ] as number[]);
  const maxPlatformSubsidy = isValueSet(
    viewJson.activityRule?.[0]?.platformSubsidy
  )
    ? (divide(viewJson?.activityRule?.[0]?.platformSubsidy) as number)
    : (divide(viewJson?.activityRule?.[0]?.platformSubsidyMax) as number);
  const maxAgentSubsidy = isValueSet(viewJson?.activityRule?.[0]?.agentSubsidy)
    ? (divide(viewJson?.activityRule?.[0]?.agentSubsidy) as number)
    : (divide(viewJson?.activityRule?.[0]?.agentSubsidyMax) as number);

  const params = {
    // 校验条件
    condition: {
      ...getMktRuleBaseCondition(creationContext, baseinfo),
      discountRange, // 优惠范围   --元
      conditionRange, // 门槛范围  --元
      maxPlatformSubsidy, // 平台最高补贴
      maxAgentSubsidy, // 代理商补贴
      weekDays: baseinfo?.activityTime?.weeks?.join(","), // 星期时段
    },
    // 校验值
    value: {
      maxPlatformSubsidy, // 平台最高补贴
      userDayCountLimitRange: viewJson?.activityLimitRule?.userDayCountLimit
        ? ([
            viewJson.activityLimitRule?.userDayCountLimit,
            viewJson.activityLimitRule?.userDayCountLimitMax,
          ] as number[])
        : undefined, // 用户每日限购
      userTotalCountLimitRange: viewJson?.activityLimitRule?.userTotalCountLimit
        ? ([
            viewJson.activityLimitRule?.userTotalCountLimit,
            viewJson.activityLimitRule?.userTotalCountLimitMax,
          ] as number[])
        : undefined, // 用户总限购
      conditionRange, // 门槛范围  --元
    },
  };
  const res = await api.subActivity.checkActDiscountByMarketingRule(params);
  const { result: success, marketingRuleCheckErrorType: errType } =
    res?.data || {};
  if (success || !errType) {
    return { errType: false };
  }
  if (errType === "multi_rule") {
    // 跨业态
    return { errType };
  }
  if (errType === "inconformity_rule") {
    const errorList =
      res?.data?.ruleValueCheckResultList?.filter(
        (item) => item.success === false
      ) || [];
    const userConfirm = await confirmMktRuleError({
      title: "当前填写值不符合行业规则",
      content: (
        <div>
          {errorList?.map((item) => {
            return (
              <div key={item?.mktVerifyRuleCheckType as string}>
                {item?.errorMsg}
              </div>
            );
          })}
        </div>
      ),
      confirmBtnText: "仍提交，升级审批",
    });
    return { errType, userConfirm };
  }
  // impossible
  return { errType: null };
}

// 查询是否跨业态
export async function checkMktRulesForMultiBusinessFormAndConfirmIfNeed(
  creationContext: CreationContext,
  baseinfo: BaseInfoTS
) {
  const params = {
    // 校验条件
    condition: {
      ...getMktRuleBaseCondition(creationContext, baseinfo),
    },
  };
  const res = await api.subActivity.checkActDiscountByMarketingRule(params);
  const { result: success, marketingRuleCheckErrorType: errType } =
    res?.data || {};
  if (success || errType !== "multi_rule") {
    // 和后端确认过了，只要不是这个错误，接口报错也忽略
    return { conflicted: false };
  }
  const userConfirm = await confirmMktRuleError({
    title: "当前跨主营类目不符合行业规则",
    content: res?.data.failReason as string,
    confirmBtnText: "进行下一步，提交后升级审批",
    showConfirmBtnTips: true,
  });
  return { conflicted: true, userConfirm };
}
