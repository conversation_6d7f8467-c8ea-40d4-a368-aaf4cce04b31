import { ActivitySignType } from '../../constants';
export interface BusinessCategory {
  businessFormId: string;
  businessFormName: string;
}

interface BusinessLine {  
  businessLineId: string;
  businessLineName: string;
}

export interface BusinessData {
  businessLineList: BusinessLine[];
  businessFormList: BusinessCategory[]
}

export interface StoreReduceCouponRule {
  activityRule: Array<{
    conditionType: 2; // 写死成 2
    condition?: number;
    conditionMin?: number;
    conditionMax?: number;
    discountType: 1; // 写死成 1
    discount?: number;
    discountMin?: number;
    discountMax?: number;
    platformSubsidy?: number;
    platformSubsidyMax?: number;
    agentSubsidy?: number;
    agentSubsidyMax?: number;
  }>;
  activityMarketingInfo: {
    userScope: number;
    deliveryType: number;
    deliveryChannel?: number;
    couponName?: string; // 反圈有用 正招没用
    budgetId?: number;
    budgetName?: string;
    availableDays?: number;
    availableStartTime?: number | string;
    availableEndTime?: number | string;
    activityExternalDeliveryChannelList?: {
      externalDeliveryChannelName: string;
      externalDeliveryChannelCode: string;
      externalDeliveryChannelType: 1 | 2; // 1：独享、2：非独享
    }[]; // 限定渠道
    inStoreDeliveryType: '1' | '2'; // 店内投放方式
    outStoreDeliveryType: '1' | '2'; // 店外投放方式
    helpPeopleNumForGetCoupon?: number; // 助力人数

    // 投放渠道相关
    bizUseChannel?: string; // "10"
    redirectUrl?: string; // 'www.alibaba.com'
    useStoreType?: 0 | 1; // 0 无限制，1 未动销聊天购门店
  };
  activityLimitRule: {
    dayCountLimit?: number;
    dayCountLimitMax?: number;
    totalCountLimit?: number;
    totalCountLimitMax?: number;
    userDayCountLimit?: number;
    userDayCountLimitMax?: number;
    userTotalCountLimit?: number;
    userTotalCountLimitMax?: number;
  };
  exchangePeriodDTOList?: { openTime: string; closeTime: string }[]; // 全店满减券兑换时间
  periodDTOList?: { openTime: string; closeTime: string }[];
  weekday?: string;
}

export interface MarketPlayForPlayData {
  outStoreDeliveryType: string;
  externalDeliveryChannel?: {
    externalDeliveryChannelName: string;
    externalDeliveryChannelCode: string;
    externalDeliveryChannelType: 1 | 2; // 1：独享、2：非独享
  }[];
  budget?: {
    budgetId: string;
    budgetName: string;
  };
  userScope: string;
  configValidationRules?: { loading: boolean };
  launchChannel?: string;
  inStoreDeliveryType: string;
  exchangeTimeRangeList: string;
  name: string;
  creator: {
    id: string;
    name: string;
  };
  dateRange: { start: string; end: string }[]; // 格式 "2023-10-09 00:00:00"
  bizUseChannel: number; // 业务场景
  couponValid: {
    // 红包使用时间（券有效期）
    fromDraw?: {
      value: number;
    };
    validRange?: {
      start: string;
      end: string;
    };
  };
  effectiveChannel: string; // 核销场景
  rule?: {
    saleFixed?: {
      condition: number;
      discount: number;
      elemeSubsidy: number;
      agentSubsidy?: number;
    };
    saleRange?: {
      minCondition: number;
      maxCondition: number;
      minDiscount: number;
      maxDiscount: number;
      maxElemeSubsidy: number;
      maxAgentSubsidy: number;
    }
  };
  couponStock?: {
    dayLimit: {
      maxValue: number
      minValue: number;
      value?: number;
    };
    totalLimit: {
      maxValue: number
      minValue: number;
      value?: number;
    };
  };
  couponStockFixed?: {
    dayLimit: {
      value: number;
    };
    totalLimit: {
      value: number;
    };
  };
  drawLimit?: {
    dayLimit: {
      maxValue: number
      minValue: number;
      value?: number;
    };
    totalLimit: {
      maxValue: number
      minValue: number;
      value?: number;
    };
  };
  drawLimitFixed?: {
    dayLimit: {
      value: number;
    };
    totalLimit: {
      value: number;
    };
  };
  businessFormIds?: BusinessCategory;  
  businessLineId?: {
    businessLineId: string;
    businessLineName: string;
  };
}

export interface MarketPlayForPlayData4InverseSelection extends MarketPlayForPlayData{
  couponName: {
    value: string;
  };
  redirectUrl?: string;
  useStoreType?: 0 | 1; // 0 无限制，1 未动销聊天购门店
}

export interface BaseInfoForDetailsContext {
  beginTime: string;
  createdUserId: string;
  createdUserName: string;
  endTime: string;
  name: string;
  signUpShopType: number;
  activitySignType?: ActivitySignType;
  subsidyType?: string;
  businessLineList?: BusinessLine[];
}

export interface MarketPlay {
  _signupShopType: string;
  creator: string;
  name: string;
  rule: string;
  userScope: string;
  effectiveChannel: string;
  launchChannel?: string;
  couponValid: string;
  exchangeTimeRangeList: string;
  couponStock: string;
  drawLimit: string;
  couponStockFixed: string; // 全能超市发券数量是固定值
  drawLimitFixed: string; // 全能超市领券限制是固定值
  budget: string;
  externalDeliveryChannel?: string;
  inStoreDeliveryType?: string;
  outStoreDeliveryType?: string;
  couponName?: string,
  bizUseChannel?: string, // 核销渠道
  redirectUrl?: string, // 跳转链接
  useStoreType?: string, // 门店类型
}

export interface BaseInfoForCreationContext {
  activityTime: {
    batchRange: {
      list: Array<{ start: string; end: string }>;
    };
    weeks: (string | number)[];
  };
}
