import { Message } from "@alifd/next";

import * as api from "../../api";
import { maskMS } from "../../common";
import { ActivityTypeEnum, ISCH, ITY, InvestmentTypeEnum } from "../../constants";
import {
  ActivityStrategy,
  CheckNextContext,
  CreationContext,
  InvestmentActivityDetailDTO,
} from "../../models";
import { matchRecipe } from "../../utils";
import { CableBase } from "../base";
import {
  checkNextTimeForAlpha,
  genericCreateActivity,
  gotoLegacyActivityDetailByDefault,
} from "../mixins";
import { auditServiceForCoupon } from "../service";
import { StoreReduceCouponRule } from "./constant";
import {
  isValueSet,
  checkMktRulesForDiscountBusinessFormAndConfirmIfNeed, 
  checkMktRulesForMultiBusinessFormAndConfirmIfNeed,
  parseJsonSafe,
} from './utils';
import {
  formatMarketPlay2ViewJson,
  formatMarketPlay2ViewJson4InverseSelection,
  formatMarketPlay2ViewJson4ShareCoupon,
  formatViewJson2MarketPlay, 
  formatViewJson2MarketPlay4ShareCoupon,
} from './formatUtils';


const debug = require("debug")("zs:strategy:StoreReduceCoupon");

function renderActivityRuleForTableLegacy(marketPlay: any) {
  const fields = marketPlay.instance.fields;
  const rule = parseJsonSafe(fields.rule);
  const { saleFixed, saleRange } = rule; // 固定门槛 , 优惠力度
  let ret = "-";
  if (saleFixed) {
    ret = `满${saleFixed.condition / 100} 减${
      saleFixed.discount / 100
    }元，平台补贴${saleFixed.elemeSubsidy / 100}元`;
    if (saleFixed.agentSubsidy) {
      return (ret += `，代理商补贴${saleFixed.agentSubsidy / 100}元`);
    }
  }
  if (saleRange) {
    if (saleRange.minCondition === saleRange.maxCondition) {
      // 门槛金额最大值最小值相等，合并区间，只展示一个门槛金额
      ret = `满${saleRange.minCondition / 100}元，减${
        saleRange.minDiscount / 100
      } - ${saleRange.maxDiscount / 100}元，平台补贴最高${
        saleRange.maxElemeSubsidy / 100
      }元`;
      if (saleRange.maxAgentSubsidy) {
        ret += `，代理商补贴最高${saleRange.maxAgentSubsidy / 100}元`;
      }
    } else {
      ret = `满${saleRange.minCondition / 100} - ${
        saleRange.maxCondition / 100
      }元，减${saleRange.minDiscount / 100} - ${
        saleRange.maxDiscount / 100
      }元，平台补贴最高${saleRange.maxElemeSubsidy / 100}元`;
      if (saleRange.maxAgentSubsidy) {
        ret += `，代理商补贴最高${saleRange.maxAgentSubsidy / 100}元`;
      }
    }
  }
  return ret;
}

function renderActivityRuleForTable(viewJson: string) {
  try {
    const data: StoreReduceCouponRule = JSON.parse(viewJson);
    const firstRule = data.activityRule[0];
    if (!firstRule) {
      return "";
    }

    let isRangeActivity = false; // 是否是区间招商
    // @ts-ignore
    if (+firstRule.conditionMax > 0 && +firstRule.conditionMin > 0) {
      isRangeActivity = true;
    }
    const r = firstRule;
    if (!isRangeActivity) {
      return (
        <>
          满{(r.condition as number) / 100}元， 减{(r.discount as number) / 100}
          元， 平台补贴{(r.platformSubsidy as number) / 100}元
          {+(r.agentSubsidy as any) > 0 ? (
            <>，代理商补贴{(r.agentSubsidy as number) / 100}元</>
          ) : null}
        </>
      );
    } else {
      return (
        <>
          满{(r.conditionMin as number) / 100}-
          {(r.conditionMax as number) / 100}元， 减
          {(r.discountMin as number) / 100}-{(r.discountMax as number) / 100}
          元， 平台补贴最高{(r.platformSubsidyMax as number) / 100}元
          {+(r.agentSubsidyMax as any) > 0 ? (
            <>，代理商补贴最高{(r.agentSubsidyMax as number) / 100}元</>
          ) : null}
        </>
      );
    }
  } catch (e) {
    console.error(e);
    return "";
  }
}

function hasAgentSubsidy(playdata: StoreReduceCouponRule) {
  let flag = false;
  try {
    playdata.activityRule.forEach((r) => {
      if (
        (isValueSet(r.agentSubsidyMax) && (r.agentSubsidyMax as number) > 0) ||
        (isValueSet(r.agentSubsidy) && (r.agentSubsidy as number) > 0)
      ) {
        flag = true;
      }
    });
  } catch (e) {
    console.error(e);
  }

  return flag;
}

async function cableCheckNext(
  ctx: CheckNextContext,
  creationContext: CreationContext,
  history: any
) {
  const { step, dataStore } = ctx;
  if (!dataStore._session) {
    dataStore._session = {};
  }
  if (step.name === "baseinfo") {
    const timeError = await checkNextTimeForAlpha(
      { step, dataStore },
      creationContext
    );
    if (timeError) {
      return timeError;
    }
  }

  const basePayload = (baseInfo: any) => {
    if (!baseInfo) return {};

    return {
      // 活动类型信息
      activityType: creationContext.activityType,
      templateId: creationContext.templateId,
      investmentType: creationContext.investmentType,
      investmentSchema: creationContext.investmentSchema,
      subsidyType: creationContext.subsidyType,

      // 基本信息
      name: baseInfo.name,
      description: baseInfo.description,
      remark: baseInfo.remark,
      beginTime: baseInfo.activityTime.start.toDate().getTime(),
      endTime: maskMS(baseInfo.activityTime.end.toDate().getTime()),
      allowCancel: baseInfo.allowCancel,
      signUpShopType: baseInfo.signUpShopType,
      signUpStartTime: baseInfo.signupTime.start.toDate().getTime(),
      signUpEndTime: maskMS(baseInfo.signupTime.end.toDate().getTime()),
      manageOrgList: baseInfo?.manageOrgList,
    };
  };

  if (step.name === 'baseinfo') {
    const baseinfo = dataStore?.data?.baseinfo;
    const needBusinessLineAndCategory =
      matchRecipe(creationContext, ITY.RICHANG, ISCH.RICHANG, true) &&
      baseinfo?.manageOrgList?.length > 0
    // 重置 auditUpgradeForMultiMktRule，避免重新回到第一步重新选择主营类目的情况
    dataStore._session.auditUpgradeForMultiMktRule = undefined;
    if (needBusinessLineAndCategory) {
      // 校验是否跨主营类目，并且返回弹窗点击结果
      const { conflicted, userConfirm } = await checkMktRulesForMultiBusinessFormAndConfirmIfNeed(creationContext, baseinfo);
      if (!conflicted) {
        // 没有错误信息，不需要拦截进入下一步
        return;
      }
      if (userConfirm) {
        // 有错误，并且用户二次确认了，确认需提交审批
        dataStore._session.auditUpgradeForMultiMktRule = true;
        // 不需要拦截进入下一步
        return;
      } else {
        // 有错误，单用户未二次确认，页面停留在当前步骤
        return { silent: true }
      }
    }
  }

  if (step.name === "playdata") {
    const { baseinfo, scope, playdata } = dataStore.data;
    console.log(dataStore, "dataStore11");

    const formData = playdata.snapshot.data; const viewJson: Partial<StoreReduceCouponRule> = formatMarketPlay2ViewJson({ formData, baseinfo });

    const payload = {
      ...basePayload(baseinfo),

      // 招商范围信息
      shopPoolId: scope.pool.value,
      shopPoolName: scope.pool.label,
      // 玩法信息
      viewJson: JSON.stringify(viewJson),
      // 不限制库存
      stockType: 0,
    };

    try {
      // 创建活动之前如果选择主营类目，需要校验中控招商配置
      const needBusinessLineAndCategory =
        matchRecipe(creationContext, ITY.RICHANG, ISCH.RICHANG, true) &&
        baseinfo?.manageOrgList?.length > 0;

        let auditUpgradeForMultiMktRule: undefined | boolean; // 是否跨业态
        let auditUpgradeForCheckMktRuleUnPass: undefined | boolean; // 是否跨规则
        // 判断是否支持主营类目
        if (needBusinessLineAndCategory) {
          if (dataStore?._session?.auditUpgradeForMultiMktRule) {
            // 判断第一步是否跨业态，如果第一步跨业态，只要第一步点了二次确认，就将设置后的参数传给后端，后端去做后续拦截校验
            // 风险：第二步的时候业态改为不冲突，第三步规则建议组件会取到规则，并根据规则校验，但是不符合规则这里不去check，不会弹突破规则的弹窗（因为第一步已经弹过一次，需要重新刷新或者返回第一步重选业态）；
            auditUpgradeForMultiMktRule = true;
          } else {
            const { userConfirm, errType } = await checkMktRulesForDiscountBusinessFormAndConfirmIfNeed(creationContext, viewJson, baseinfo);
            if (!errType) {
              // 如果接口检查规则没有错误，啥也不做，继续走下边创建流程。
            } else {
              // 走到这个分支说明第一步没有跨业态，无需关注 errType === 'multi_rule'分支，只做自己的校验
              // 判断错误类型，如果错误类型为'inconformity_rule'，则说明规则错误，规则需要升级审批，设置 auditUpgradeForCheckMktRuleUnPass 为 true
              if (errType === 'multi_rule') {
                // 说明第一步业态无冲突，创建流程中规则被更改，此时点断不做任何处理，继续调用创建接口，创建报错，展示后端返回的报错信息。
              } else if (errType === 'inconformity_rule') {
                if (userConfirm) {
                  // 玩法不满足行业规则，用户二次确认了，继续向下执行创建流程
                  auditUpgradeForCheckMktRuleUnPass = true;
                } else {
                  // 玩法不满足行业规则，用户未二次确认，页面停留在当前步骤，中断创建流程
                  return { silent: true };
                }
              } else {
                // 理论不会走到这个分支，每个枚举必须对应自己的分支
              }
            }
          }
        }
      
      return genericCreateActivity(
        { ...payload, auditUpgradeForMultiMktRule, auditUpgradeForCheckMktRuleUnPass },
        history,
        creationContext
      );
    } catch (e: any) {
      return { message: e?.message || '系统异常' };
    }
  }

  // 算法招商
  if (step.name === "batchcreate") {
    const { baseinfo, batchcreate } = dataStore.data;

    const payload: any = {
      ...basePayload(baseinfo),

      // 上传文件路径
      uploadPath: batchcreate.file.url,
    };

    debug("create subActivity", payload);

    // 创建活动前先校验参数
    const validateRes = await api.subActivity.validateBatchCreate(payload);

    if (validateRes.success) {
      // 校验通过进行批量创建
      const res = await api.subActivity.batchCreateInvestmentActivity(payload);
      if (res.success) {
        Message.success("创建成功");
        history.push("/activity");
      } else {
        return { message: res.errorMessage };
      }
    } else {
      return { message: validateRes.errorMessage };
    }
  }
}

async function cableCheckNextForInverseSelection(
  ctx: CheckNextContext,
  creationContext: CreationContext,
  history: any
) {
  const { step, dataStore } = ctx;
  const basePayload = (baseInfo: any) => {
    if (!baseInfo) return {};

    return {
      // 活动类型信息
      activityType: creationContext.activityType,
      activitySignType: creationContext.activitySignType || "REVERSE_SIGN", // TODO: 有改动  写死反圈
      templateId: creationContext.templateId,
      investmentType: creationContext.investmentType,
      investmentSchema: creationContext.investmentSchema,
      subsidyType: creationContext.subsidyType,

      // 基本信息
      name: baseInfo.name,
      description: baseInfo.description,
      remark: baseInfo.remark,
      beginTime: baseInfo.activityTime.start.toDate().getTime(),
      endTime: maskMS(baseInfo.activityTime.end.toDate().getTime()),
      allowCancel: baseInfo.allowCancel,
      signUpShopType: baseInfo.signUpShopType,
      // 管理业态
      manageOrgList: baseInfo.manageOrgList,

      // 反圈活动没有报名时间，被圈中后会自动报名
      // signUpStartTime: baseInfo.signupTime.start.toDate().getTime(),
      // signUpEndTime: maskMS(baseInfo.signupTime.end.toDate().getTime()),
    };
  };

  if (step.name === "playdata") {
    const { baseinfo, scope, playdata } = dataStore.data;
    console.log(dataStore, "dataStore11");

    const formData = playdata.snapshot.data;
    const viewJson = formatMarketPlay2ViewJson4InverseSelection({formData, baseinfo});

    const payload = {
      ...basePayload(baseinfo),

      // 招商范围信息
      shopPoolId: scope.pool.value,
      shopPoolName: scope.pool.label,
      // 玩法信息
      viewJson: JSON.stringify(viewJson),
      // 不限制库存
      stockType: 0,
    };
    return genericCreateActivity(payload, history, creationContext);
  }
}

async function cableCheckNextForExchangeBean() {
  return { message: "不支持创建吃货豆活动" };
}

async function cableCheckNextForShareCoupon(
  ctx: CheckNextContext,
  creationContext: CreationContext,
  history: any
) {
  const { step, dataStore } = ctx;
  const basePayload = (baseInfo: any) => {
    if (!baseInfo) return {};

    return {
      // 活动类型信息
      activityType: creationContext.activityType,
      templateId: creationContext.templateId,
      investmentType: creationContext.investmentType,
      investmentSchema: creationContext.investmentSchema,
      subsidyType: creationContext.subsidyType,

      // 基本信息
      name: baseInfo.name,
      description: baseInfo.description,
      remark: baseInfo.remark,
      beginTime: baseInfo.activityTime.start.toDate().getTime(),
      endTime: maskMS(baseInfo.activityTime.end.toDate().getTime()),
      allowCancel: baseInfo.allowCancel,
      signUpShopType: baseInfo.signUpShopType,
      signUpStartTime: baseInfo.signupTime.start.toDate().getTime(),
      signUpEndTime: maskMS(baseInfo.signupTime.end.toDate().getTime()),
      // 管理业态
      manageOrgList: baseInfo.manageOrgList,
    };
  };

  // 分享领券不支持算法招商，只支持普通的活动创建流程
  if (step.name === "playdata") {
    const { baseinfo, scope, playdata } = dataStore.data;

    const formData = playdata.snapshot.data;
    const viewJson = formatMarketPlay2ViewJson4ShareCoupon({ formData });

    const payload = {
      ...basePayload(baseinfo),

      // 招商范围信息
      shopPoolId: scope.pool.value,
      shopPoolName: scope.pool.label,
      // 玩法信息
      viewJson: JSON.stringify(viewJson),
      // 不限制库存
      stockType: 0,
    };
    return genericCreateActivity(payload, history, creationContext);
  }
}

// 全店满减红包
export const StoreReduceCoupon: ActivityStrategy = {
  activityType: ActivityTypeEnum.STORE_REDUCTION,
  cable: ({ activitySignType }) => {
    if (activitySignType === "REVERSE_SIGN") {
      // 反圈， 如果招商方式是 反圈，走新编排配置(model)，新创建接口(checkNext)， 否则统一走老的正向流程
      return {
        ...CableBase,
        workspace: "ExukovQrae6xWn2rt4NgG",
        model: "72NxDbmES5L93ieASmDAr8",
        checkNext: cableCheckNextForInverseSelection,
        auditService: auditServiceForCoupon,
      };
    } else {
      return {
        ...CableBase,
        workspace: "ExukovQrae6xWn2rt4NgG",
        model: "anEmFBUEMMA7H0SBSYqYuW",
        checkNext: cableCheckNext,
        auditService: auditServiceForCoupon,
      };
    }
  },
  hasAgentSubsidy,
  isOfflineEnabled: function (state: number): boolean {
    // 活动状态: 1:未开始 2:活动中 3:活动暂停 4:活动取消 5:活动结束 8: 预算熔断
    return [1, 2, 3, 8].includes(state);
  },
  isSupportAgent: true,
  isDingtalkLinkEnabled: () => true,
  hasAuditMemberList: () => false,
  isSupportModifyStock: () => false,
  isDetailInviteStatsOnMainActivityTableVisible: true,
  renderPlayDataInfoForTableView: function (): JSX.Element {
    return <></>;
  },
  renderActivityRuleForTable,
  gotoLegacyDetail: function (
    act: InvestmentActivityDetailDTO,
    scene: "lookup" | "examine"
  ): void {
    gotoLegacyActivityDetailByDefault(act, scene);
  },
  renderActivityRuleForTableLegacy: function (marketPlay: {
    instance: { fields: any };
  }) {
    return renderActivityRuleForTableLegacy(marketPlay);
  },
  async marketPlayMapper(viewJson: string, baseInfo: any) {
    try {
      const viewDto: StoreReduceCouponRule = JSON.parse(viewJson);

      let model = {};
      // 转换逻辑包含反圈相关字段
      const fields = formatViewJson2MarketPlay({
        viewDto,
        baseInfo,
      });

      // 正招走老全店满减券组件
      model = await api.subActivity.getComponentDataModel(
        "ExukovQrae6xWn2rt4NgG",
        "3KCj1Bopl3Cah6PrQDFT5P"
      );

      const marketPlay = {
        instance: { fields },
        model: model,
      };

      if (baseInfo.activitySignType === "REVERSE_SIGN") {
        // 反圈走新组件 - 全店满减券
        marketPlay.model = await api.subActivity.getComponentDataModel(
          "3oNNWxkJYzc6eWQyKz5Lp2",
          "486c6vlFTCG5F6jxIZNqUW"
        );
      }
      return marketPlay;
    } catch (e) {
      return null;
    }
  },
};

export const ExchangeBeanCoupon: ActivityStrategy = {
  activityType: ActivityTypeEnum.STORE_FULL_REDUCTION_BEANS_EXCHANGE_COUPON,
  cable: {
    ...CableBase,
    workspace: "ExukovQrae6xWn2rt4NgG",
    model: "7Yl6vTfBm0I9lanIQH3lOu",
    checkNext: cableCheckNextForExchangeBean,
    auditService: auditServiceForCoupon,
  },
  isDetailInviteStatsOnMainActivityTableVisible: true,
  isSupportAgent: true,

  gotoLegacyDetail: function (
    act: InvestmentActivityDetailDTO,
    scene: "lookup" | "examine"
  ): void {
    gotoLegacyActivityDetailByDefault(act, scene);
  },
  hasAgentSubsidy,
  hasAuditMemberList: () => false,
  isDingtalkLinkEnabled: () => true,
  isOfflineEnabled: function (state: number): boolean {
    // 活动状态: 1:未开始 2:活动中 3:活动暂停 4:活动取消 5:活动结束 8: 预算熔断
    return [1, 2, 3, 8].includes(state);
  },
  isSupportModifyStock: () => false,
  marketPlayMapper: async (viewJson: string, baseInfo: any, options: any) => {
    const { investmentType } = baseInfo || {};
    if (investmentType === InvestmentTypeEnum.SUANFA) {
      try {
        const viewDto = JSON.parse(viewJson) || {};
        const {
          activityLimitRule = {},
          activityMarketingInfo = {},
          activityRule = [],
        } = viewDto || {};
        const creator: any = {}; // 创建人名称、创建人id
        let name = ""; // 玩法名称
        let _signupShopType = ""; // 商户类型
        const rule: any = {}; // 优惠形式
        let userScope = ""; // 用户类型
        let effectiveChannel = ""; // 核销渠道
        let launchChannel = ""; // 投放渠道
        const couponValid: any = {}; // 券有效期
        const couponStock: any = {}; // 券库存
        const drawLimit: any = {}; // 领券数量
        const budget: any = {}; //  预算
        let model: any = {};

        if (
          isValueSet(activityLimitRule.dayCountLimit) &&
          isValueSet(activityLimitRule.dayCountLimitMax)
        ) {
          couponStock.dayLimit = {
            minValue: activityLimitRule.dayCountLimit,
            maxValue: activityLimitRule.dayCountLimitMax,
          };
        }
        if (
          isValueSet(activityLimitRule.totalCountLimit) &&
          isValueSet(activityLimitRule.totalCountLimitMax)
        ) {
          couponStock.totalLimit = {
            minValue: activityLimitRule.totalCountLimit,
            maxValue: activityLimitRule.totalCountLimitMax,
          };
        }
        if (
          isValueSet(activityLimitRule.userDayCountLimit) &&
          isValueSet(activityLimitRule.userDayCountLimitMax)
        ) {
          drawLimit.dayLimit = {
            minValue: activityLimitRule.userDayCountLimit,
            maxValue: activityLimitRule.userDayCountLimitMax,
          };
        }
        if (
          isValueSet(activityLimitRule.userTotalCountLimit) &&
          isValueSet(activityLimitRule.userTotalCountLimitMax)
        ) {
          drawLimit.totalLimit = {
            minValue: activityLimitRule.userTotalCountLimit,
            maxValue: activityLimitRule.userTotalCountLimitMax,
          };
        }
        if (
          isValueSet(baseInfo.createdUserId) &&
          isValueSet(baseInfo.createdUserName)
        ) {
          creator.name = `${baseInfo.createdUserName}`;
          creator.id = baseInfo.createdUserId;
        }
        if (isValueSet(baseInfo.name)) {
          name = baseInfo.name;
        }
        if (isValueSet(baseInfo.signUpShopType)) {
          _signupShopType = baseInfo.signUpShopType + "";
        }
        if (activityRule && isValueSet(activityRule[0])) {
          if (
            isValueSet(activityRule[0].condition) &&
            isValueSet(activityRule[0].discount) &&
            isValueSet(activityRule[0].platformSubsidy)
          ) {
            // rule.conditions = "saleFixed";
            rule.saleFixed = {
              condition: activityRule[0].condition,
              discount: activityRule[0].discount,
              elemeSubsidy: activityRule[0].platformSubsidy,
            };
            // 可报名商户类型 0： 无限制， 1: 直营， 2： 非直营
            if (
              isValueSet(baseInfo.signUpShopType) &&
              baseInfo.signUpShopType === 2
            ) {
              rule.saleFixed.agentSubsidy = activityRule[0]?.agentSubsidy || 0;
            }
          }

          if (
            isValueSet(activityRule[0].conditionMin) &&
            isValueSet(activityRule[0].conditionMax) &&
            isValueSet(activityRule[0].discountMin) &&
            isValueSet(activityRule[0].discountMax) &&
            isValueSet(activityRule[0].platformSubsidyMax)
          ) {
            rule.saleRange = {
              minCondition: activityRule[0].conditionMin,
              maxCondition: activityRule[0].conditionMax,
              minDiscount: activityRule[0].discountMin,
              maxDiscount: activityRule[0].discountMax,
              maxElemeSubsidy: activityRule[0].platformSubsidyMax,
            };
            // 可报名商户类型 0： 无限制， 1: 直营， 2： 非直营
            if (
              isValueSet(baseInfo.signUpShopType) &&
              baseInfo.signUpShopType === 2
            ) {
              rule.saleRange.maxAgentSubsidy =
                activityRule[0]?.agentSubsidyMax || 0;
            }
          }
        }
        if (isValueSet(activityMarketingInfo.userScope)) {
          userScope = activityMarketingInfo.userScope + "";
        }
        if (isValueSet(activityMarketingInfo.deliveryType)) {
          effectiveChannel = activityMarketingInfo.deliveryType + "";
        }
        if (isValueSet(activityMarketingInfo.deliveryChannel)) {
          launchChannel = activityMarketingInfo.deliveryChannel + "";
        }
        if (isValueSet(activityMarketingInfo.availableDays)) {
          couponValid.fromDraw = {
            value: activityMarketingInfo.availableDays,
          };
        }
        if (
          isValueSet(activityMarketingInfo.availableStartTime) &&
          isValueSet(activityMarketingInfo.availableEndTime)
        ) {
          couponValid.validRange = {
            start: activityMarketingInfo.availableStartTime,
            end: activityMarketingInfo.availableEndTime,
          };
        }

        if (
          isValueSet(activityMarketingInfo.budgetName) &&
          isValueSet(activityMarketingInfo.budgetId)
        ) {
          budget.budgetName = activityMarketingInfo.budgetName;
          budget.budgetId = activityMarketingInfo.budgetId;
        }
        model = await api.subActivity.getComponentDataModel(
          "6v4M8woywiK9aecPk9yWLh",
          "2SbOKijfGP79GbR9EonZn9"
        );

        const marketPlay = {
          instance: {
            fields: {
              creator: JSON.stringify(creator),
              name: name,
              couponStock: JSON.stringify(couponStock),
              drawLimit: JSON.stringify(drawLimit),
              _signupShopType,
              rule: JSON.stringify(rule),
              userScope,
              effectiveChannel,
              launchChannel,
              couponValid: JSON.stringify(couponValid),
              budget: JSON.stringify(budget),
            },
          },
          model: model,
        };
        return marketPlay;
      } catch (e) {
        return null;
      }
    } else {
      const { marketPlay } = options || {};
      const model = await api.subActivity.getComponentDataModel(
        "6v4M8woywiK9aecPk9yWLh",
        "2SbOKijfGP79GbR9EonZn9"
      );

      return {
        instance: marketPlay?.instance,
        model: model,
      };
    }
  },
  renderPlayDataInfoForTableView: function (): JSX.Element {
    return <></>;
  },
  renderActivityRuleForTable,
  renderActivityRuleForTableLegacy: function (marketPlay: {
    instance: { fields: any };
  }) {
    return renderActivityRuleForTableLegacy(marketPlay);
  },
};

/**
 * 分享领券和全店满减券、吃货豆相比，活动创建流程有比较大的区别
 * - 分享领券不支持算法招商，全店满减券、吃货豆支持
 * - 前端创建页面上的玩法表单，不再和全店满减券、吃货豆共用（从这个需求之后：https://yuque.alibaba-inc.com/docs/share/bf85c77e-0daf-4541-9396-d4ffc6d31756?#）
 * - 后端处理玩法逻辑依然不变，依然使用全店满减券、吃货豆所用的组件 model 来处理表单数据
 *
 * 除了创建流程，其他流程没有区别
 */
export const ShareCoupon: ActivityStrategy = {
  activityType: ActivityTypeEnum.STORE_FULL_SHARE_COUPON,
  cable: {
    ...CableBase,
    workspace: "ExukovQrae6xWn2rt4NgG",
    model: "5PJKzPdKMfs9g3zVUmcKYr",
    checkNext: cableCheckNextForShareCoupon,
    auditService: auditServiceForCoupon,
  },
  hasAgentSubsidy,
  isOfflineEnabled: function (state: number): boolean {
    // 活动状态: 1:未开始 2:活动中 3:活动暂停 4:活动取消 5:活动结束 8: 预算熔断
    return [1, 2, 3, 8].includes(state);
  },
  isSupportAgent: true,
  isDingtalkLinkEnabled: () => true,
  hasAuditMemberList: () => false,
  isSupportModifyStock: () => false,
  isDetailInviteStatsOnMainActivityTableVisible: true,
  marketPlayMapper: async (viewJson: string, baseInfo: any, options: any) => {
    try {
      const viewDto: StoreReduceCouponRule = JSON.parse(viewJson) || {};
      const { activityMarketingInfo } = viewDto;

      if (activityMarketingInfo.deliveryChannel) {
        const { marketPlay } = options || {};
        const model = await api.subActivity.getComponentDataModel(
          "ExukovQrae6xWn2rt4NgG",
          "9kd87XDsv3Y5O88qqyuAeh"
        );

        return {
          instance: marketPlay?.instance,
          model: model,
        };
      } else {
        
        const fields = formatViewJson2MarketPlay4ShareCoupon({
          viewDto,
          baseInfo,
        });

        const model = await api.subActivity.getComponentDataModel(
          "ExukovQrae6xWn2rt4NgG",
          "9kd87XDsv3Y5O88qqyuAeh"
        );

        const marketPlay = {
          instance: { fields },
          model: model,
        };
        return marketPlay;
      }
    } catch (e) {
      return null;
    }
  },
  renderPlayDataInfoForTableView: function (): JSX.Element {
    return <></>;
  },
  renderActivityRuleForTable,
  gotoLegacyDetail: function (
    act: InvestmentActivityDetailDTO,
    scene: "lookup" | "examine"
  ): void {
    gotoLegacyActivityDetailByDefault(act, scene);
  },
  renderActivityRuleForTableLegacy: function (marketPlay: {
    instance: { fields: any };
  }) {
    return renderActivityRuleForTableLegacy(marketPlay);
  },
};
