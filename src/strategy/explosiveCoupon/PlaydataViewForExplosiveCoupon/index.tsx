import { useState, useEffect } from 'react';
import * as api from '../../../api';
import { transformViewJsonToPlayStep } from '../helper'
import { ViewJsonType } from '../type'
import './style.scss';

const renderRangeNumber = (value?: { min?: number, max?: number }, unit: string = '') => {
  if (value?.min === value?.max) return `${value?.min} ${unit}`
  return `${value?.min}  ${unit} - ${value?.max}  ${unit}`
}


const PlaydataViewForExplosiveCoupon: React.FC<any> = ({ viewJson }) => {
  let ready = false;
  let viewJsonObj: ViewJsonType = {} as ViewJsonType;

  try {
    viewJsonObj = JSON.parse(viewJson);
    if (viewJsonObj == null) return null;
    ready = true;
  } catch (error) {
    console.error(error);
  }

  if (!ready) {
    return null;
  }

  const {
    basicInvestment: {
      hasCondition,
      condition,
      shopSubsidy,
      dailyStock,
    },
    shopSupplement: {
      shopSupplementEnabled,

      userScopeSupplementEnabled,
      userScopeSupplementSubsidy,
      userScopeSupplementDailyStock,

      timeSupplementEnabled,
      timeSupplement0To10Amount,
      timeSupplement10To14Amount,
      timeSupplement14To17Amount,
      timeSupplement17To21Amount,
      timeSupplement21ToEndAmount,
      timeSupplementDailyStock,
    } = {}
  } = transformViewJsonToPlayStep(viewJsonObj)
  return (
    <div className="zs-explosive-coupon-play-data-detail">

      <div className="play-data-card" >
        <div className='play-data-title'>基础出资：{hasCondition === 1 ? '有门槛' : '无门槛'}</div>
        <div className='play-data-content'>
          {condition ?
            <div className='play-data-content-item'>
              <div >基础门槛：</div>
              <div >{renderRangeNumber(condition, '元')}</div>
            </div>
            : null}
          <div className='play-data-content-item'>
            <div >抵扣金额：</div>
            <div >{renderRangeNumber(shopSubsidy, '元')}</div>
          </div>
          <div className='play-data-content-item'>
            <div >每日库存：</div>
            <div >{renderRangeNumber(dailyStock, '个')} </div>
          </div>
        </div>
      </div>
      {shopSupplementEnabled === 1 ?
        <div className="play-data-card" >
          <div className='play-data-title'>商家追补：有追补</div>
          {userScopeSupplementEnabled ?
            <div className='play-data-content'>
              <div className='play-data-content-title' >追补条件：门店新客</div>
              <div className='play-data-content-item'>
                <div >抵扣金额：</div>
                <div >{renderRangeNumber(userScopeSupplementSubsidy, '元')} </div>
              </div>
              <div className='play-data-content-item'>
                <div >每日库存：</div>
                <div >{renderRangeNumber(userScopeSupplementDailyStock, '个')} </div>
              </div>
            </div>
            : null}
          {timeSupplementEnabled ?
            <div className='play-data-content'>
              <div className='play-data-content-title' >追补条件：特殊时段</div>
              {timeSupplement0To10Amount ?
                <div className='play-data-content-item'>
                  <div className='play-data-content-item-time'>00:00-10:00追加金额：</div>
                  <div >{renderRangeNumber(timeSupplement0To10Amount, '元')}</div>
                </div> : null}
              {timeSupplement10To14Amount ?
                <div className='play-data-content-item'>
                  <div className='play-data-content-item-time'>10:00-14:00追加金额：</div>
                  <div >{renderRangeNumber(timeSupplement10To14Amount, '元')}</div>
                </div> : null}
              {timeSupplement14To17Amount ?
                <div className='play-data-content-item'>
                  <div className='play-data-content-item-time'>14:00-17:00追加金额：</div>
                  <div >{renderRangeNumber(timeSupplement14To17Amount, '元')}</div>
                </div> : null}
              {timeSupplement17To21Amount ?
                <div className='play-data-content-item'>
                  <div className='play-data-content-item-time'>17:00-21:00追加金额：</div>
                  <div >{renderRangeNumber(timeSupplement17To21Amount, '元')}</div>
                </div> : null}
              {timeSupplement21ToEndAmount ?
                <div className='play-data-content-item'>
                  <div className='play-data-content-item-time'>21:00-23:59追加金额：</div>
                  <div >{renderRangeNumber(timeSupplement21ToEndAmount, '元')}</div>
                </div> : null}

              <div className='play-data-content-item'>
                <div >每日库存：</div>
                <div >{renderRangeNumber(timeSupplementDailyStock, '个')}</div>
              </div>
            </div>
            : null}

        </div> : null
      }

    </div>
  );
};

const PlaydataViewForExplosiveCouponWrap: React.FC<any> = ({ activityInfo }) => {
  const [detail, setDetail] = useState<any>();

  useEffect(() => {
    api.subActivity.getDetail(activityInfo.activityId).then((res) => {
      setDetail(res.data);
    });
  }, [activityInfo.activityId]);
  return (
    <div className="zs-act-info">
      <div className="zs-act-area">
        <div className="zs-act-area-title" style={{ marginBottom: 30 }}>
          玩法设置
        </div>
        <div className="zs-act-section">
          <PlaydataViewForExplosiveCoupon viewJson={detail?.viewJson} />
        </div>
      </div>
    </div>
  );
};

export default PlaydataViewForExplosiveCouponWrap;
