/**
 * 爆单红包  1000038
 */

import { maskMS, parseJsonSafe } from '../../common';
import { ActivityTypeEnum } from '../../constants';
import { ActivityStrategy } from '../../models';
import { CableBase } from '../base';
import { genericCreateActivity } from '../mixins';
import { auditServiceForCoupon } from '../service';
import { transformViewJsonToPlayStep, transformPlayStepToViewJson } from './helper';
import { PlayStepValueType } from './type'
import { renderItemExtView } from './renderItemExtView'

const renderRangeNumber = (value?: { min?: number, max?: number }, unit: string = '') => {
  if (!value) return '-'
  if (value.min === value.max) return `${value.min} ${unit}`
  return `${value.min}  ${unit} - ${value.max}  ${unit}`
}

const ExplosiveCoupon: ActivityStrategy = {
  activityType: ActivityTypeEnum.BAO_DAN_HONG_BAO,
  generation: 'v2',
  cable: {
    ...CableBase,
    workspace: 'ExukovQrae6xWn2rt4NgG',
    model: '6D97ve5y5Hp6PJv2PuxjIt',
    async checkNext({ step, dataStore }, creationContext, history) {
      if (step.name === 'playdata') {
        const { baseinfo, scope } = dataStore.data;
        const viewJson = transformPlayStepToViewJson(dataStore?.data?.playdata)

        const payload = {
          // 活动类型信息
          templateId: creationContext.templateId,
          investmentType: creationContext.investmentType,
          investmentSchema: creationContext.investmentSchema,
          activityType: ActivityTypeEnum.BAO_DAN_HONG_BAO,
          // 基本信息
          name: baseinfo.name,
          description: baseinfo.description,
          remark: baseinfo.remark,
          beginTime: baseinfo.activityTime.start.toDate().getTime(),
          endTime: maskMS(baseinfo.activityTime.end.toDate().getTime()),
          allowCancel: baseinfo.allowCancel,

          signUpShopType: baseinfo.signUpShopType,
          signUpStartTime: baseinfo.signupTime.start.toDate().getTime(),
          signUpEndTime: maskMS(baseinfo.signupTime.end.toDate().getTime()),
          subsidyType: creationContext?.subsidyType,
          manageOrgList: baseinfo?.manageOrgList,
          // 授权审核人
          auditMemberList:
            baseinfo.auditMemberList?.length > 0
              ? baseinfo.auditMemberList
              : null,

          // 招商范围信息
          shopPoolId: scope.pool.value,
          shopPoolName: scope.pool.label,

          // 玩法信息
          viewJson: JSON.stringify(viewJson),
        };

        return await genericCreateActivity(payload, history, creationContext);
      }
    },
    auditService: auditServiceForCoupon,
  },

  hasAgentSubsidy() {
    return false;
  },
  isOfflineEnabled: function (): boolean {
    // 下线权限由后端控制
    return true;
  },
  isDingtalkLinkEnabled: () => false,
  noAssociatedActScene: true,
  hasAuditMemberList: () => true,
  isSupportModifyStock: () => false,
  isDetailInviteStatsOnMainActivityTableVisible: true,
  renderPlayDataInfoForTableView: function (): JSX.Element {
    return <></>;
  },
  renderActivityRuleForTable(viewJson: string) {
    try {
      const viewDtoToCopy = parseJsonSafe(viewJson);
      const {
        basicInvestment: {
          hasCondition,
          condition,
          shopSubsidy,
          dailyStock,
        },
        shopSupplement: {
          userScopeSupplementEnabled,
          userScopeSupplementSubsidy,
          userScopeSupplementDailyStock,
          timeSupplementEnabled,
        } = {}
      }: PlayStepValueType = transformViewJsonToPlayStep(viewDtoToCopy)

      return (
        <div>
          <div>
            {hasCondition ? <span>满 {renderRangeNumber(condition, '元')}，</span> : null}
            <span>优惠 {renderRangeNumber(shopSubsidy, '元')}，</span>
            <span>库存 {renderRangeNumber(dailyStock, '个')}；</span>
          </div>
          {userScopeSupplementEnabled ? (
            <div>
              <span>门店新客追补 {renderRangeNumber(userScopeSupplementSubsidy, '元')}，</span>
              <span>库存 {renderRangeNumber(userScopeSupplementDailyStock, '个')}；</span>
            </div>
          ) : null}
          {timeSupplementEnabled ? (
            <div>分时段追补（请前往活动详情查看）</div>
          ) : null}
        </div>
      );
    } catch (e) {
      console.error(e);
      return '';
    }
  },

  renderGroupItemExtView: (record) => {
    return renderItemExtView(record);
  },
  renderOfflineGroupItemExtView: ({ record }) => {
    return renderItemExtView(record);
  },

  isSupportAgent: false,
  gotoLegacyDetail: function (): void {
    return undefined;
  },
  renderActivityRuleForTableLegacy: function () {
    return '';
  },
};

export default ExplosiveCoupon;
