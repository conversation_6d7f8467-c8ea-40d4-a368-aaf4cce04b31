import { transformPlayStepTo<PERSON>iew<PERSON><PERSON>, transformViewJsonToPlayStep } from "./helper";

describe("explosiveCoupon", () => {
  describe("helper", () => {

    describe("transformPlayStepToViewJson", () => {
      test("爆单红包-无门槛-无追补", () => {
        const viewJson = transformPlayStepToViewJson({
          "basicInvestment": {
            "hasCondition": 0,
            "shopSubsidy": {
              "min": 3,
              "max": 30
            },
            "dailyStock": {
              "min": 0,
              "max": 9999
            }
          },
          "shopSupplement": {
            "shopSupplementEnabled": 0,
            "userScopeSupplementEnabled": false,
            "timeSupplementEnabled": false
          }
        });
        expect(viewJson).toStrictEqual({
          "activityType": 1000038,
          "activityRule": [
            {
              "amountScene": "A_WITHOUT_THRESHOLD",
              "activityLimitRule": {
                "dayCountLimit": 0,
                "dayCountLimitMax": 9999
              },
              "couponRule": {
                "conditionMaxCny": undefined,
                "conditionMinCny": undefined,
                "shopSubsidyMinCny": "3",
                "shopSubsidyMaxCny": "30"
              }
            }
          ]
        });
      });

      test("爆单红包-有门槛-无追补", () => {
        const viewJson = transformPlayStepToViewJson({
          "basicInvestment": {
            "hasCondition": 1,
            "shopSubsidy": {
              "min": 3,
              "max": 30
            },
            "dailyStock": {
              "min": 0,
              "max": 9999
            },
            "condition": {
              "min": 300,
              "max": 400
            }
          },
          "shopSupplement": {
            "shopSupplementEnabled": 0,
            "userScopeSupplementEnabled": false,
            "timeSupplementEnabled": false
          }
        });
        expect(viewJson).toStrictEqual({
          "activityType": 1000038,
          "activityRule": [
            {
              "amountScene": "A_WITH_THRESHOLD",
              "activityLimitRule": {
                "dayCountLimit": 0,
                "dayCountLimitMax": 9999
              },
              "couponRule": {
                "conditionMinCny": "300",
                "conditionMaxCny": "400",
                "shopSubsidyMinCny": "3",
                "shopSubsidyMaxCny": "30"
              }
            }
          ]
        });
      });

      test("爆单红包-有门槛-有新客追补", () => {
        const viewJson = transformPlayStepToViewJson({
          "basicInvestment": {
            "hasCondition": 1,
            "shopSubsidy": {
              "min": 3,
              "max": 30
            },
            "dailyStock": {
              "min": 0,
              "max": 9999
            },
            "condition": {
              "min": 300,
              "max": 400
            }
          },
          "shopSupplement": {
            "shopSupplementEnabled": 1,
            "userScopeSupplementEnabled": true,
            "timeSupplementEnabled": false,
            "userScopeSupplementSubsidy": {
              "min": 30,
              "max": 40
            },
            "userScopeSupplementDailyStock": {
              "min": 20,
              "max": 20
            }
          }
        });
        expect(viewJson).toStrictEqual({
          "activityType": 1000038,
          "activityRule": [
            {
              "amountScene": "A_WITH_THRESHOLD",
              "activityLimitRule": {
                "dayCountLimit": 0,
                "dayCountLimitMax": 9999
              },
              "couponRule": {
                "conditionMinCny": "300",
                "conditionMaxCny": "400",
                "shopSubsidyMinCny": "3",
                "shopSubsidyMaxCny": "30"
              }
            },
            {
              "amountScene": "Y_WITH_USER_SCOPE",
              "activityLimitRule": {
                "dayCountLimit": 20,
                "dayCountLimitMax": 20
              },
              "activityMarketingInfo": {
                "userScope": 4
              },
              "couponRule": {
                "shopSubsidyMinCny": "30",
                "shopSubsidyMaxCny": "40"
              }
            }
          ]
        });
      });

      test("爆单红包-有门槛-有时间追补", () => {
        const viewJson = transformPlayStepToViewJson({
          "basicInvestment": {
            "hasCondition": 1,
            "shopSubsidy": {
              "min": 3,
              "max": 30
            },
            "dailyStock": {
              "min": 0,
              "max": 9999
            },
            "condition": {
              "min": 300,
              "max": 400
            }
          },
          "shopSupplement": {
            "shopSupplementEnabled": 1,
            "userScopeSupplementEnabled": false,
            "timeSupplementEnabled": true,
            "timeSupplement0To10Amount": {
              "min": 30,
              "max": 40
            },
            "timeSupplementDailyStock": {
              "min": 20,
              "max": 20
            },
            "timeSupplement10To14Amount": {
              "min": 20,
              "max": 20
            },
            "timeSupplement14To17Amount": {
              "min": 20,
              "max": 20
            },
            "timeSupplement17To21Amount": {
              "min": 30,
              "max": 30
            },
            "timeSupplement21ToEndAmount": {
              "min": 40,
              "max": 40
            }
          }
        });
        expect(viewJson).toStrictEqual({
          "activityType": 1000038,
          "activityRule": [
            {
              "amountScene": "A_WITH_THRESHOLD",
              "activityLimitRule": {
                "dayCountLimit": 0,
                "dayCountLimitMax": 9999
              },
              "couponRule": {
                "conditionMinCny": "300",
                "conditionMaxCny": "400",
                "shopSubsidyMinCny": "3",
                "shopSubsidyMaxCny": "30"
              }
            },
            {
              "amountScene": "Y_WITH_TIME",
              "activityLimitRule": {
                "dayCountLimit": 20,
                "dayCountLimitMax": 20
              },
              "couponPeriodAndRule": [
                {
                  "periodTime": "00:00:00_10:00:00",
                  "shopSubsidyMinCny": "30",
                  "shopSubsidyMaxCny": "40"
                },
                {
                  "periodTime": "10:00:00_14:00:00",
                  "shopSubsidyMinCny": "20",
                  "shopSubsidyMaxCny": "20"
                },
                {
                  "periodTime": "14:00:00_17:00:00",
                  "shopSubsidyMinCny": "20",
                  "shopSubsidyMaxCny": "20"
                },
                {
                  "periodTime": "17:00:00_21:00:00",
                  "shopSubsidyMinCny": "30",
                  "shopSubsidyMaxCny": "30"
                },
                {
                  "periodTime": "21:00:00_23:59:59",
                  "shopSubsidyMinCny": "40",
                  "shopSubsidyMaxCny": "40"
                }
              ]
            }
          ]
        });
      });

      test("爆单红包-有门槛-有新客+时间追补", () => {
        const viewJson = transformPlayStepToViewJson({
          "basicInvestment": {
            "hasCondition": 1,
            "shopSubsidy": {
              "min": 3,
              "max": 30
            },
            "dailyStock": {
              "min": 0,
              "max": 9999
            },
            "condition": {
              "min": 300,
              "max": 400
            }
          },
          "shopSupplement": {
            "shopSupplementEnabled": 1,
            "userScopeSupplementEnabled": true,
            "timeSupplementEnabled": true,
            "timeSupplement0To10Amount": {
              "min": 30,
              "max": 40
            },
            "timeSupplementDailyStock": {
              "min": 20,
              "max": 20
            },
            "timeSupplement10To14Amount": {
              "min": 20,
              "max": 20
            },
            "timeSupplement14To17Amount": {
              "min": 20,
              "max": 20
            },
            "timeSupplement17To21Amount": {
              "min": 30,
              "max": 30
            },
            "timeSupplement21ToEndAmount": {
              "min": 40,
              "max": 40
            },
            "userScopeSupplementSubsidy": {
              "min": 20,
              "max": 20
            },
            "userScopeSupplementDailyStock": {
              "min": 30,
              "max": 30
            }
          }
        });
        expect(viewJson).toStrictEqual({
          "activityType": 1000038,
          "activityRule": [
            {
              "amountScene": "A_WITH_THRESHOLD",
              "activityLimitRule": {
                "dayCountLimit": 0,
                "dayCountLimitMax": 9999
              },
              "couponRule": {
                "conditionMinCny": "300",
                "conditionMaxCny": "400",
                "shopSubsidyMinCny": "3",
                "shopSubsidyMaxCny": "30"
              }
            },
            {
              "amountScene": "Y_WITH_USER_SCOPE",
              "activityLimitRule": {
                "dayCountLimit": 30,
                "dayCountLimitMax": 30
              },
              "activityMarketingInfo": {
                "userScope": 4
              },
              "couponRule": {
                "shopSubsidyMinCny": "20",
                "shopSubsidyMaxCny": "20"
              }
            },
            {
              "amountScene": "Y_WITH_TIME",
              "activityLimitRule": {
                "dayCountLimit": 20,
                "dayCountLimitMax": 20
              },
              "couponPeriodAndRule": [
                {
                  "periodTime": "00:00:00_10:00:00",
                  "shopSubsidyMinCny": "30",
                  "shopSubsidyMaxCny": "40"
                },
                {
                  "periodTime": "10:00:00_14:00:00",
                  "shopSubsidyMinCny": "20",
                  "shopSubsidyMaxCny": "20"
                },
                {
                  "periodTime": "14:00:00_17:00:00",
                  "shopSubsidyMinCny": "20",
                  "shopSubsidyMaxCny": "20"
                },
                {
                  "periodTime": "17:00:00_21:00:00",
                  "shopSubsidyMinCny": "30",
                  "shopSubsidyMaxCny": "30"
                },
                {
                  "periodTime": "21:00:00_23:59:59",
                  "shopSubsidyMinCny": "40",
                  "shopSubsidyMaxCny": "40"
                }
              ]
            }
          ]
        });
      });
    });

    describe('transformViewJsonToPlayStep', () => {
      test("爆单红包-无门槛-无追补", () => {
        const viewJson = transformViewJsonToPlayStep({
          "activityRule": [
            {
              "activityLimitRule": {
                "dayCountLimit": 1,
                "dayCountLimitMax": 7997
              },
              "amountScene": "A_WITHOUT_THRESHOLD",
              "couponRule": {
                "shopSubsidyMaxCny": "984",
                "shopSubsidyMinCny": "1"
              }
            }
          ],
          "activityType": 1000038
        })

        expect(viewJson).toStrictEqual({
          "basicInvestment": {
            "hasCondition": 0,
            "condition": undefined,
            "shopSubsidy": {
              "min": 1,
              "max": 984
            },
            "dailyStock": {
              "min": 1,
              "max": 7997
            }
          },
          "shopSupplement": {
            "shopSupplementEnabled": 0,
            "userScopeSupplementEnabled": false,
            "userScopeSupplementSubsidy": {
              min: undefined,
              max: undefined
            },
            "userScopeSupplementDailyStock": {
              min: undefined,
              max: undefined
            },
            "timeSupplementEnabled": false,
            "timeSupplement0To10Amount": undefined,
            "timeSupplement10To14Amount": undefined,
            "timeSupplement14To17Amount": undefined,
            "timeSupplement17To21Amount": undefined,
            "timeSupplement21ToEndAmount": undefined,
            "timeSupplementDailyStock": {
              min: undefined,
              max: undefined
            }
          }
        })
      })

      test("爆单红包-有门槛-有新客+时间追补", () => {
        const viewJson = transformViewJsonToPlayStep({
          "activityRule": [
            {
              "activityLimitRule": {
                "dayCountLimit": 1,
                "dayCountLimitMax": 9999
              },
              "activityMarketingInfo": {
                "userScope": 4
              },
              "amountScene": "Y_WITH_USER_SCOPE",
              "couponRule": {
                "shopSubsidyMaxCny": "8",
                "shopSubsidyMinCny": "0"
              }
            },
            {
              "activityLimitRule": {
                "dayCountLimit": 0,
                "dayCountLimitMax": 9997
              },
              "amountScene": "Y_WITH_TIME",
              "couponPeriodAndRule": [
                {
                  "periodTime": "00:00:00_10:00:00",
                  "shopSubsidyMaxCny": "12",
                  "shopSubsidyMinCny": "1"
                },
                {
                  "periodTime": "10:00:00_14:00:00",
                  "shopSubsidyMaxCny": "14",
                  "shopSubsidyMinCny": "3"
                },
                {
                  "periodTime": "14:00:00_17:00:00",
                  "shopSubsidyMaxCny": "16",
                  "shopSubsidyMinCny": "5"
                },
                {
                  "periodTime": "17:00:00_21:00:00",
                  "shopSubsidyMaxCny": "17",
                  "shopSubsidyMinCny": "6"
                },
                {
                  "periodTime": "21:00:00_23:59:59",
                  "shopSubsidyMaxCny": "18",
                  "shopSubsidyMinCny": "7"
                }
              ]
            },
            {
              "activityLimitRule": {
                "dayCountLimit": 0,
                "dayCountLimitMax": 9999
              },
              "amountScene": "A_WITH_THRESHOLD",
              "couponRule": {
                "conditionMaxCny": "800",
                "conditionMinCny": "500",
                "shopSubsidyMaxCny": "30",
                "shopSubsidyMinCny": "3"
              }
            }
          ],
          "activityType": 1000038,
        });
        expect(viewJson).toStrictEqual({
          "basicInvestment": {
            "hasCondition": 1,
            "condition": {
              "min": 500,
              "max": 800
            },
            "shopSubsidy": {
              "min": 3,
              "max": 30
            },
            "dailyStock": {
              "min": 0,
              "max": 9999
            }
          },
          "shopSupplement": {
            "shopSupplementEnabled": 1,
            "userScopeSupplementEnabled": true,
            "userScopeSupplementSubsidy": {
              "min": 0,
              "max": 8
            },
            "userScopeSupplementDailyStock": {
              "min": 1,
              "max": 9999
            },
            "timeSupplementEnabled": true,
            "timeSupplement0To10Amount": {
              "min": 1,
              "max": 12
            },
            "timeSupplement10To14Amount": {
              "min": 3,
              "max": 14
            },
            "timeSupplement14To17Amount": {
              "min": 5,
              "max": 16
            },
            "timeSupplement17To21Amount": {
              "min": 6,
              "max": 17
            },
            "timeSupplement21ToEndAmount": {
              "min": 7,
              "max": 18
            },
            "timeSupplementDailyStock": {
              "min": 0,
              "max": 9997
            }
          }
        })
      })


    })
  });
});
