import * as React from 'react';
import { Field, Button, Message } from '@alifd/next'
import BigNumber from 'bignumber.js'
import BasicInvestmentForm from './basicInvestmentForm';
import ShopSupplementForm from './shopSupplementForm';
import { PlayStepValueType } from '../type'
import './style.scss';


interface Props {
  value?: PlayStepValueType;
  onChange?: (v: PlayStepValueType) => void;
  onEvent?: (e: any) => void;
}
const PlayStepForExplosiveCoupon: React.FC<Props> = ({
  value,
  onChange,
  onEvent,
}) => {
  let shopSupplementField: any
  const basicInvestmentField = Field.useField({
    values: value?.basicInvestment,
    onChange: (name: string, val: any) => {
      if (name === 'hasCondition' && val === 0) {
        basicInvestmentField.remove('condition')
      }
      if (onChange) {
        onChange({
          basicInvestment: basicInvestmentField.getValues(),
          shopSupplement: shopSupplementField.getValues(),
        })
      }
    }
  })

  shopSupplementField = Field.useField({
    values: value?.shopSupplement,
    onChange: (name: string, val: any) => {
      if (name === 'shopSupplementEnabled') {
        if (val === 1) {
          shopSupplementField.setValues({
            userScopeSupplementEnabled: true,
            timeSupplementEnabled: false
          })
        }
        if (val === 0) {
          shopSupplementField.setValues({
            userScopeSupplementEnabled: false,
            timeSupplementEnabled: false
          })
          shopSupplementField.remove([
            'userScopeSupplementSubsidy',
            'userScopeSupplementDailyStock',
            'timeSupplement0To10Amount',
            'timeSupplement10To14Amount',
            'timeSupplement14To17Amount',
            'timeSupplement17To21Amount',
            'timeSupplement21ToEndAmount',
            'timeSupplementDailyStock',
          ])
        }
      }
      if (name === 'userScopeSupplementEnabled' && val === false) {
        shopSupplementField.remove(['userScopeSupplementSubsidy', 'userScopeSupplementDailyStock'])
      }
      if (name === 'timeSupplementEnabled' && val === false) {
        shopSupplementField.remove([
          'timeSupplement0To10Amount',
          'timeSupplement10To14Amount',
          'timeSupplement14To17Amount',
          'timeSupplement17To21Amount',
          'timeSupplement21ToEndAmount',
          'timeSupplementDailyStock',
        ])
      }

      if (onChange) {
        onChange({
          basicInvestment: basicInvestmentField.getValues(),
          shopSupplement: shopSupplementField.getValues(),
        })
      }
    }
  })

  const onSubmit = async () => {
    const { errors: basicInvestmentErr, values: basicValues } = await basicInvestmentField.validatePromise()
    const { errors: shopSupplementErr, values: shopSupplementValues } = await shopSupplementField.validatePromise()
    if (basicInvestmentErr || shopSupplementErr) return false;
    if (basicValues.hasCondition === 1) {
      const maxTimeSupplementValue = Math.max(
        shopSupplementValues?.timeSupplement0To10Amount?.max || 0,
        shopSupplementValues?.timeSupplement10To14Amount?.max || 0,
        shopSupplementValues?.timeSupplement14To17Amount?.max || 0,
        shopSupplementValues?.timeSupplement17To21Amount?.max || 0,
        shopSupplementValues?.timeSupplement21ToEndAmount?.max || 0,
      )
      const maxSubsidySum = new BigNumber(basicValues.shopSubsidy?.max)
        .plus(shopSupplementValues.userScopeSupplementSubsidy?.max || 0)
        .plus(maxTimeSupplementValue)
      if (maxSubsidySum > basicValues.condition.min) {
        return Message.error('基础抵扣金额最大值 + 新客抵扣金额最大值 + 时段追补最大值 <=  基础门槛最小值')
      }
    }
    if (onEvent) onEvent({ type: 'next' });
  }

  return (
    <div className="zs-explosive-coupon-play-step">
      <BasicInvestmentForm field={basicInvestmentField} />
      <ShopSupplementForm field={shopSupplementField} />
      <div className='zs-explosive-coupon-play-step-footer'>
        <Button
          data-test-id="scope-info-step-cancel-btn"
          onClick={() => onEvent && onEvent({ type: 'cancel' })}
        >
          取消
        </Button>
        &nbsp;&nbsp;
        <Button
          data-test-id="scope-info-step-prev-btn"
          onClick={() => onEvent && onEvent({ type: 'prev' })}
        >
          上一步
        </Button>
        &nbsp;&nbsp;
        <Button
          type="primary"
          data-test-id="scope-info-step-next-btn"
          onClick={onSubmit}
        >
          发布
        </Button>
      </div>
    </div>
  );
};

export default PlayStepForExplosiveCoupon;
