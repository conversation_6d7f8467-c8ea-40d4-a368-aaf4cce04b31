
import { Field, Form, Radio } from '@alifd/next'
import { FormCard, RangeNumberInput, InnerLabelSwitch } from './widgets';
import { rangeNumberValidate } from './utils';
import { isValueSet } from '../../../common';

interface Props {
  field: Field
}

const formItemLayout = {
  labelCol: { span: 3, style: { color: '#333' } },
};
const formItemLayoutItem = {
  labelCol: { span: 7, style: { color: '#333' } }
};

export default function ShopSupplementForm({ field }: Props) {

  const shopSupplementEnabledValidate = (_rule: any, value: number) => {
    return new Promise((resolve, reject) => {
      const { userScopeSupplementEnabled, timeSupplementEnabled }: any = field.getValues()
      if (value === 1 && !userScopeSupplementEnabled && !timeSupplementEnabled) {
        reject('请填写至少一项追补条件')
        return
      }
      resolve(true);
    })
  }

  const validatortimeSupplement = (_rule: any, value: boolean) => {
    return new Promise((resolve, reject) => {
      if (value) {
        const {
          timeSupplement0To10Amount, timeSupplement10To14Amount, timeSupplement14To17Amount,
          timeSupplement17To21Amount, timeSupplement21ToEndAmount,
        }: any = field.getValues()
        if (
          (isValueSet(timeSupplement0To10Amount?.min) && isValueSet(timeSupplement0To10Amount?.max)) ||
          (isValueSet(timeSupplement10To14Amount?.min) && isValueSet(timeSupplement10To14Amount?.max)) ||
          (isValueSet(timeSupplement14To17Amount?.min) && isValueSet(timeSupplement14To17Amount?.max)) ||
          (isValueSet(timeSupplement17To21Amount?.min) && isValueSet(timeSupplement17To21Amount?.max)) ||
          (isValueSet(timeSupplement21ToEndAmount?.min) && isValueSet(timeSupplement21ToEndAmount?.max))
        ) {
          resolve(true);
          return
        }
        reject('请填写至少一项追补条件')
      }


      resolve(true);
    })
  }

  return (
    <Form field={field} labelAlign='left' {...formItemLayout} useLabelForErrorMessage >
      <FormCard label="商家追补">
        <Form.Item required asterisk={false} requiredMessage='商家追补 是必填字端' validator={shopSupplementEnabledValidate}>
          <Radio.Group
            dataSource={[
              { label: '有追补', value: 1 },
              { label: '无追补', value: 0 }
            ]}
            name='shopSupplementEnabled'
          />
        </Form.Item>
      </FormCard>
      {field.getValue('shopSupplementEnabled') === 1 ?
        <div className='shop-supplement-content-box'>

          <div className='shop-supplement-content-item'>
            <Form.Item>
              <InnerLabelSwitch
                label="追补条件: 门店新客"
                name='userScopeSupplementEnabled'
              />
            </Form.Item>
            {field.getValue('userScopeSupplementEnabled') ?
              <div className='userScopeSupplement'>
                <Form.Item
                  {...formItemLayoutItem}
                  label="抵扣金额"
                  validator={(rule, value) => rangeNumberValidate(rule, value, true)}
                >
                  <RangeNumberInput
                    name="userScopeSupplementSubsidy"
                    min={1}
                    max={9999}
                    unit='元'
                  />
                </Form.Item>
                <Form.Item
                  {...formItemLayoutItem}
                  label="每日库存"
                  validator={(rule, value) => rangeNumberValidate(rule, value, true)}
                >
                  <RangeNumberInput
                    name="userScopeSupplementDailyStock"
                    min={1}
                    max={9999}
                    unit='个'
                  />
                </Form.Item>
              </div> : null
            }
          </div>

          <div className='shop-supplement-content-item' >
            <Form.Item validator={validatortimeSupplement}>
              <InnerLabelSwitch
                label="追补条件: 特殊时段"
                name='timeSupplementEnabled'
              />
            </Form.Item>
            {field.getValue('timeSupplementEnabled') ?
              <div className='timeSupplement' >
                <Form.Item
                  {...formItemLayoutItem}
                  label="00:00-10:00追加金额"
                  validator={(rule, value) => rangeNumberValidate(rule, value, false)}
                >
                  <RangeNumberInput
                    name="timeSupplement0To10Amount"
                    min={1}
                    max={9999}
                    unit='元'
                  />
                </Form.Item>
                <Form.Item
                  {...formItemLayoutItem}
                  label="10:00-14:00追加金额"
                  validator={(rule, value) => rangeNumberValidate(rule, value, false)}
                >
                  <RangeNumberInput
                    name="timeSupplement10To14Amount"
                    min={1}
                    max={9999}
                    unit='元'
                  />
                </Form.Item>
                <Form.Item
                  {...formItemLayoutItem}
                  label="14:00-17:00追加金额"
                  validator={(rule, value) => rangeNumberValidate(rule, value, false)}
                >
                  <RangeNumberInput
                    name="timeSupplement14To17Amount"
                    min={1}
                    max={9999}
                    unit='元'
                  />
                </Form.Item>
                <Form.Item
                  {...formItemLayoutItem}
                  label="17:00-21:00追加金额"
                  validator={(rule, value) => rangeNumberValidate(rule, value, false)}
                >
                  <RangeNumberInput
                    name="timeSupplement17To21Amount"
                    min={1}
                    max={9999}
                    unit='元'
                  />
                </Form.Item>
                <Form.Item
                  {...formItemLayoutItem}
                  label="21:00-23:59追加金额"
                  validator={(rule, value) => rangeNumberValidate(rule, value, false)}
                >
                  <RangeNumberInput
                    name="timeSupplement21ToEndAmount"
                    min={1}
                    max={9999}
                    unit='元'
                  />
                </Form.Item>
                <Form.Item
                  {...formItemLayoutItem}
                  label="每日库存"
                  validator={(rule, value) => rangeNumberValidate(rule, value, true)}
                >
                  <RangeNumberInput
                    name="timeSupplementDailyStock"
                    min={1}
                    max={9999}
                    unit='个'
                  />
                </Form.Item>
              </div> : null
            }
          </div>

        </div> : null}
    </Form>
  )
}