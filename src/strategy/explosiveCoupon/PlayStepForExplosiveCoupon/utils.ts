
import { isValueSet } from "../../../common";
interface RangeNumberValue {
  min?: number;
  max?: number;
}

export const rangeNumberValidate = (rule: any, value: RangeNumberValue, isRequired: boolean) => {
  return new Promise((resolve, reject) => {
    const isMinValueSet = isValueSet(value?.min);
    const isMaxValueSet = isValueSet(value?.max);
    
    if (isRequired && !isMinValueSet && !isMaxValueSet) {
      reject(`${rule.aliasName} 不能为空`);
      return;
    }

    if ((isMinValueSet && !isMaxValueSet) || (!isMinValueSet && isMaxValueSet)) {
      reject('请补充完整的区间');
      return;
    }

    // @ts-ignore
    if (isMinValueSet && isMaxValueSet && value.min > value.max) {
      reject('最小值不能大于最大值');
      return;
    }

    resolve(true);
  });
}