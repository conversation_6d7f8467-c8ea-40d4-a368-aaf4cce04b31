
import { Form, Radio, Field } from '@alifd/next'
import { FormCard, RangeNumberInput } from './widgets';
import { rangeNumberValidate } from './utils'

interface Props {
  field: Field
}
const formItemLayout = {
  labelCol: { span: 3, style: { color: '#333' } },
};

export default function BasicInvestmentForm({ field }: Props) {


  return (
    <FormCard label="基础补贴" required >
      <Form field={field} labelAlign='left' {...formItemLayout} useLabelForErrorMessage >
        <Form.Item required asterisk={false} requiredMessage='门槛 是必填字端'>
          <Radio.Group
            dataSource={[
              { label: '有门槛', value: 1 },
              { label: '无门槛', value: 0 }
            ]}
            name='hasCondition'
          />
        </Form.Item>

        {field.getValue('hasCondition') === 1 ?
          <Form.Item label="基础门槛" validator={(rule, value) => rangeNumberValidate(rule, value, true)}>
            <RangeNumberInput
              name="condition"
              min={1}
              max={9999}
              unit='元'
            />
          </Form.Item> : null
        }

        <Form.Item label="补贴金额" validator={(rule, value) => rangeNumberValidate(rule, value, true)}>
          <RangeNumberInput
            name="shopSubsidy"
            min={1}
            max={9999}
            unit='元'
          />
        </Form.Item>
        <Form.Item label="每日库存" validator={(rule, value) => rangeNumberValidate(rule, value, true)}>
          <RangeNumberInput
            name="dailyStock"
            min={1}
            max={9999}
            unit='个'
          />
        </Form.Item>
      </Form>
    </FormCard>
  )
}