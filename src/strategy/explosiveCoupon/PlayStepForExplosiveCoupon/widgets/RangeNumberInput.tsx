import { NumberPicker } from "@alifd/next";

interface Value {
  min?: number;
  max?: number;
}

interface Props {
  name?: string;
  disabled?: boolean;
  min?: number;
  max?: number;
  unit?: string;
  precision?: number
  value?: Value;

  onChange?: (value: Value) => void;
}

export default function RangeNumberInput({
  disabled,
  min: pickerMin,
  max: pickerMax,
  unit,
  precision,
  value,
  onChange
}: Props) {

  const handleChange = (key: 'min' | 'max', pickerValue: number) => {
    if (!onChange) return
    onChange({ ...value || {}, [key]: pickerValue, })
  }

  return (
    <div>
      <NumberPicker
        style={{ width: 140 }}
        disabled={disabled}
        min={pickerMin}
        max={pickerMax}
        innerAfter={unit}
        precision={precision}
        value={value?.min}
        onChange={(v) => {
          handleChange('min', v)
        }}
      />
      <span style={{ margin: "0 8px" }}>&nbsp;&nbsp;-&nbsp;&nbsp;</span>
      <NumberPicker
        style={{ width: 140 }}
        disabled={disabled}
        min={pickerMin}
        max={pickerMax}
        innerAfter={unit}
        value={value?.max}
        onChange={(v) => {
          handleChange('max', v)
        }}
      />
    </div>
  );
}