import './index.scss'

export default function FormCard({ label, children, required, style, labelStyle }: any) {
  return (
    <div className="explosive-coupon-form-card" style={{ ...style }}>
      <div className='explosive-coupon-form-card-label' style={{ ...labelStyle }}>
        {required && <span className="explosive-coupon-form-card-label-required">*</span>}
        {label}:
      </div>
      <div className='explosive-coupon-form-card-content'>{children}</div>
    </div>
  );
};