import { isValueSet } from '../../common';
import { ActivityTypeEnum } from '../../constants'
import {
  PlayStepValueType, ViewJsonType, ActivityRuleType, CouponPeriodRuleType, PeriodTimeType,
  AmountScenceAType, AmountScenceYUserScopeType, AmountScenceYTimeType,
} from './type'


const formatNumberToString = (value?: number) => {
  if (isValueSet(value)) return '' + value;
  return undefined
}

export const transformPlayStepToViewJson = (value: PlayStepValueType): ViewJsonType => {
  const { basicInvestment, shopSupplement } = value;

  const activityRule: ActivityRuleType[] = [
    {
      amountScene: basicInvestment.hasCondition ? "A_WITH_THRESHOLD" : 'A_WITHOUT_THRESHOLD',
      activityLimitRule: {
        dayCountLimit: basicInvestment.dailyStock?.min,
        dayCountLimitMax: basicInvestment.dailyStock?.max,
      },
      couponRule: {
        conditionMinCny: basicInvestment.hasCondition ? formatNumberToString(basicInvestment.condition?.min) : undefined,
        conditionMaxCny: basicInvestment.hasCondition ? formatNumberToString(basicInvestment.condition?.max) : undefined,
        shopSubsidyMinCny: formatNumberToString(basicInvestment.shopSubsidy?.min),
        shopSubsidyMaxCny: formatNumberToString(basicInvestment.shopSubsidy?.max),
      }
    }
  ]

  if (shopSupplement?.shopSupplementEnabled === 1 && shopSupplement?.userScopeSupplementEnabled) {
    activityRule.push({
      amountScene: "Y_WITH_USER_SCOPE",
      activityLimitRule: {
        dayCountLimit: shopSupplement.userScopeSupplementDailyStock?.min,
        dayCountLimitMax: shopSupplement.userScopeSupplementDailyStock?.max,
      },
      activityMarketingInfo: {
        userScope: 4, // 新客
      },
      couponRule: {
        shopSubsidyMinCny: formatNumberToString(shopSupplement.userScopeSupplementSubsidy?.min),
        shopSubsidyMaxCny: formatNumberToString(shopSupplement.userScopeSupplementSubsidy?.max),
      }
    })
  }

  if (shopSupplement?.shopSupplementEnabled === 1 && shopSupplement?.timeSupplementEnabled) {
    activityRule.push({
      amountScene: "Y_WITH_TIME",
      activityLimitRule: {
        dayCountLimit: shopSupplement.timeSupplementDailyStock?.min,
        dayCountLimitMax: shopSupplement.timeSupplementDailyStock?.max,
      },
      couponPeriodAndRule: [
        {
          periodTime: '00:00:00_10:00:00',
          shopSubsidyMinCny: formatNumberToString(shopSupplement.timeSupplement0To10Amount?.min),
          shopSubsidyMaxCny: formatNumberToString(shopSupplement.timeSupplement0To10Amount?.max),
        },
        {
          periodTime: '10:00:00_14:00:00',
          shopSubsidyMinCny: formatNumberToString(shopSupplement.timeSupplement10To14Amount?.min),
          shopSubsidyMaxCny: formatNumberToString(shopSupplement.timeSupplement10To14Amount?.max),
        },
        {
          periodTime: '14:00:00_17:00:00',
          shopSubsidyMinCny: formatNumberToString(shopSupplement.timeSupplement14To17Amount?.min),
          shopSubsidyMaxCny: formatNumberToString(shopSupplement.timeSupplement14To17Amount?.max),
        },
        {
          periodTime: '17:00:00_21:00:00',
          shopSubsidyMinCny: formatNumberToString(shopSupplement.timeSupplement17To21Amount?.min),
          shopSubsidyMaxCny: formatNumberToString(shopSupplement.timeSupplement17To21Amount?.max),
        },
        {
          periodTime: '21:00:00_23:59:59',
          shopSubsidyMinCny: formatNumberToString(shopSupplement.timeSupplement21ToEndAmount?.min),
          shopSubsidyMaxCny: formatNumberToString(shopSupplement.timeSupplement21ToEndAmount?.max),
        },
      ].filter((item) => isValueSet(item.shopSubsidyMinCny)) as CouponPeriodRuleType[]
    })
  }

  return {
    activityType: ActivityTypeEnum.BAO_DAN_HONG_BAO,
    activityRule
  }

}

const formatStringToNumber = (value?: string) => {
  if (isValueSet(value)) {
    return Number(value)
  }
  return undefined
}

const getTimeSupplementInfoByPeriodTime = (value: PeriodTimeType, data: CouponPeriodRuleType[]) => {
  const periodTimeInfo = data?.find((item) => item.periodTime === value);
  return periodTimeInfo ?
    {
      min: formatStringToNumber(periodTimeInfo?.shopSubsidyMinCny),
      max: formatStringToNumber(periodTimeInfo?.shopSubsidyMaxCny),
    }
    : undefined
}


export const transformViewJsonToPlayStep = (value: ViewJsonType): PlayStepValueType => {

  const basicInvestmentInfo = value?.activityRule?.find((item) => ['A_WITH_THRESHOLD', 'A_WITHOUT_THRESHOLD'].includes(item.amountScene)) as AmountScenceAType
  const userScope4SupplementInfo = value?.activityRule?.find((item) => item.amountScene === 'Y_WITH_USER_SCOPE' && item.activityMarketingInfo.userScope === 4) as AmountScenceYUserScopeType
  const timeSupplementInfo = value?.activityRule?.find((item) => item.amountScene === 'Y_WITH_TIME') as AmountScenceYTimeType

  const playStepValue: PlayStepValueType = {
    basicInvestment: {
      hasCondition: basicInvestmentInfo?.amountScene === 'A_WITH_THRESHOLD' ? 1 : 0,
      condition: isValueSet(basicInvestmentInfo?.couponRule?.conditionMinCny) ? {
        min: formatStringToNumber(basicInvestmentInfo?.couponRule?.conditionMinCny),
        max: formatStringToNumber(basicInvestmentInfo?.couponRule?.conditionMaxCny),
      } : undefined,
      shopSubsidy: {
        min: formatStringToNumber(basicInvestmentInfo?.couponRule?.shopSubsidyMinCny),
        max: formatStringToNumber(basicInvestmentInfo?.couponRule?.shopSubsidyMaxCny),
      },
      dailyStock: {
        min: basicInvestmentInfo?.activityLimitRule?.dayCountLimit,
        max: basicInvestmentInfo?.activityLimitRule?.dayCountLimitMax
      }
    },
    shopSupplement: {
      shopSupplementEnabled: (isValueSet(userScope4SupplementInfo) || isValueSet(timeSupplementInfo)) ? 1 : 0,

      userScopeSupplementEnabled: isValueSet(userScope4SupplementInfo),
      userScopeSupplementSubsidy: {
        min: formatStringToNumber(userScope4SupplementInfo?.couponRule?.shopSubsidyMinCny),
        max: formatStringToNumber(userScope4SupplementInfo?.couponRule?.shopSubsidyMaxCny),
      },
      userScopeSupplementDailyStock: {
        min: userScope4SupplementInfo?.activityLimitRule?.dayCountLimit,
        max: userScope4SupplementInfo?.activityLimitRule?.dayCountLimitMax,
      },

      timeSupplementEnabled: isValueSet(timeSupplementInfo),
      timeSupplement0To10Amount: getTimeSupplementInfoByPeriodTime('00:00:00_10:00:00', timeSupplementInfo?.couponPeriodAndRule),
      timeSupplement10To14Amount: getTimeSupplementInfoByPeriodTime('10:00:00_14:00:00', timeSupplementInfo?.couponPeriodAndRule),
      timeSupplement14To17Amount: getTimeSupplementInfoByPeriodTime('14:00:00_17:00:00', timeSupplementInfo?.couponPeriodAndRule),
      timeSupplement17To21Amount: getTimeSupplementInfoByPeriodTime('17:00:00_21:00:00', timeSupplementInfo?.couponPeriodAndRule),
      timeSupplement21ToEndAmount: getTimeSupplementInfoByPeriodTime('21:00:00_23:59:59', timeSupplementInfo?.couponPeriodAndRule),
      timeSupplementDailyStock: {
        min: timeSupplementInfo?.activityLimitRule?.dayCountLimit,
        max: timeSupplementInfo?.activityLimitRule?.dayCountLimitMax,
      },

    }
  }

  return playStepValue
}