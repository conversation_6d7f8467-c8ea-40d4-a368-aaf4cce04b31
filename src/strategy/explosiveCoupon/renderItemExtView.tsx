import { Balloon, Button } from '@alifd/next';
import { isValueSet } from '../../common';
import './style.scss';

type AmountScence =
  'A_WITH_THRESHOLD' // 商户基础出资_有门槛
  | 'A_WITHOUT_THRESHOLD' // 商户基础出资_无门槛
  | 'Y_WITH_USER_SCOPE' // 商户追加出资_分人群追加
  | 'Y_WITH_TIME'; // 商户追加出资_分时段追加

interface PlayRuleInfo {
  conditionCny?: string; // 门槛
  shopSubsidyCny?: string;// 出资
  agentSubsidyCny?: string;// 城代出资
  periodTime?: string;// 时段追加独有
}

interface AmountRule {
  amountScene: AmountScence;
  userScope?: 4; // 4为新客
  failReason?: string;// 失败原因
  limitRule: {
    dayCountLimit?: number;// 库存
  };
  playRuleList?: PlayRuleInfo[];
}

interface SignUpActivityAmountSceneRuleType {
  baseAmountRule: AmountRule;
  raiseAmountRule: {
    raiseAmountRuleDetailList?: AmountRule[]
  };
}
type RenderValueKeys =
  'baseAmountCondition' | 'baseAmountShopSubsidy' | 'baseAmountDailyStock' | 
  'userScope4FailReason' | 'userScope4ShopSubsidy' | 'userScope4DailyStockValue' |
  'raiseAmountPeriodTimeFailReason' | 'raiseAmountPeriodTime' | 'raiseAmountPeriodTimeDailyStock';

type playRuleListType = PlayRuleInfo[];

const titleStyle = { fontSize: 14, margin: '10px 0' };
const labelStyle = { width: 130, paddingRight: 10, fontSize: 12, lineHeight: '16px' };
const valueStyle = { width: 80, fontSize: 12, lineHeight: '16px' };

const getRuleInfoBykey = (
  key: RenderValueKeys,
  data: SignUpActivityAmountSceneRuleType,
) => {
  const renderObj = {
    // 基础门槛
    baseAmountCondition: () => data?.baseAmountRule?.playRuleList?.[0]?.conditionCny,
    // 基础出资
    baseAmountShopSubsidy: () => data?.baseAmountRule?.playRuleList?.[0]?.shopSubsidyCny,
    // 基础库存
    baseAmountDailyStock: () => data?.baseAmountRule?.limitRule?.dayCountLimit,
    // 新客失败原因
    userScope4FailReason: () => {
      const userScope4Info = data?.raiseAmountRule?.raiseAmountRuleDetailList
        ?.find(item => (item.userScope === 4 && item.amountScene === 'Y_WITH_USER_SCOPE'));
      return userScope4Info?.failReason;
    },
    // 新客出资
    userScope4ShopSubsidy: () => {
      const userScope4Info = data?.raiseAmountRule?.raiseAmountRuleDetailList
        ?.find(item => (item.userScope === 4 && item.amountScene === 'Y_WITH_USER_SCOPE'));
      return userScope4Info?.playRuleList?.[0]?.shopSubsidyCny;
    },
    // 新客库存
    userScope4DailyStockValue: () => {
      const userScope4Info = data?.raiseAmountRule?.raiseAmountRuleDetailList
        ?.find(item => (item.userScope === 4 && item.amountScene === 'Y_WITH_USER_SCOPE'));
      return userScope4Info?.limitRule?.dayCountLimit;
    },
    // 时段追加失败原因
    raiseAmountPeriodTimeFailReason: () => {
      const periodTimeInfo = data?.raiseAmountRule?.raiseAmountRuleDetailList
        ?.find(item => (item.amountScene === 'Y_WITH_TIME'));
      return periodTimeInfo?.failReason;
    },
    // 时段追加信息
    raiseAmountPeriodTime: () => {
      const periodTimeInfo = data?.raiseAmountRule?.raiseAmountRuleDetailList
        ?.find(item => (item.amountScene === 'Y_WITH_TIME'));
      return periodTimeInfo?.playRuleList;
    },
    // 时段信息库存
    raiseAmountPeriodTimeDailyStock: () => {
      const periodTimeInfo = data?.raiseAmountRule?.raiseAmountRuleDetailList
        ?.find(item => (item.amountScene === 'Y_WITH_TIME'));
      return periodTimeInfo?.limitRule?.dayCountLimit;
    },
  };

  return renderObj[key]();
};



export const renderPeriodTime = (playRuleList: playRuleListType, timeDailyStock: any) => {
  if (!Array.isArray(playRuleList)) return '';

  return (
    <Balloon
      closable={false}
      triggerType="click"
      trigger={<Button text type="primary" size="small" >点击查看</Button>}
      align="t"
    >
      <div style={{ display: 'flex', marginBottom: 4 }}>
        <div style={labelStyle}>特殊时段</div>
        <div style={valueStyle}>追补资金</div>
      </div>
      {playRuleList.map((item) => (
        <div key={item.periodTime} style={{ display: 'flex' }} >
          <div style={labelStyle}> {item.periodTime}</div>
          <div style={valueStyle}>{item.shopSubsidyCny}元</div>
        </div>
      ))}
      <div style={{ display: 'flex' }}>
        <div style={labelStyle}>每日库存</div>
        <div style={valueStyle}>{timeDailyStock}个</div>
      </div>
    </Balloon>
  );
};

export function renderItemExtView({ signUpActivityAmountSceneRule }: any) {

  // 基础出资
  const baseAmountConditionValue = getRuleInfoBykey('baseAmountCondition', signUpActivityAmountSceneRule)
  const baseAmountShopSubsidyValue = getRuleInfoBykey('baseAmountShopSubsidy', signUpActivityAmountSceneRule)
  const baseAmountDailyStockValue = getRuleInfoBykey('baseAmountDailyStock', signUpActivityAmountSceneRule)
  // 新客追补
  const userScope4FailReasonValue = getRuleInfoBykey('userScope4FailReason', signUpActivityAmountSceneRule)
  const userScope4ShopSubsidyValue = getRuleInfoBykey('userScope4ShopSubsidy', signUpActivityAmountSceneRule)
  const userScope4DailyStockValue = getRuleInfoBykey('userScope4DailyStockValue', signUpActivityAmountSceneRule)
  // 时段追补
  const raiseAmountPeriodTimeFailReasonValue = getRuleInfoBykey('raiseAmountPeriodTimeFailReason', signUpActivityAmountSceneRule)
  const raiseAmountPeriodTimeValue: any = getRuleInfoBykey('raiseAmountPeriodTime', signUpActivityAmountSceneRule)
  const raiseAmountPeriodTimeDailyStockValue: any = getRuleInfoBykey('raiseAmountPeriodTimeDailyStock', signUpActivityAmountSceneRule)
  return (
    <Balloon
      closable={false}
      triggerType="click"
      trigger={<Button text type="primary" size="small" >点击查看券报名信息</Button>}
      align="t"
    >
      <div style={titleStyle}> 基础出资:</div>
      {isValueSet(baseAmountConditionValue) ? (
        <div style={{ display: 'flex' }}>
          <div style={labelStyle}>基础门槛：</div>
          <div style={valueStyle}>{baseAmountConditionValue}元</div>
        </div>
      ) : null}
      <div style={{ display: 'flex' }}>
        <div style={labelStyle}>基础出资：</div>
        <div style={valueStyle}>{baseAmountShopSubsidyValue}元</div>
      </div>
      <div style={{ display: 'flex' }}>
        <div style={labelStyle}>每日库存</div>
        <div style={valueStyle}>{baseAmountDailyStockValue}个</div>
      </div>
      {isValueSet(userScope4ShopSubsidyValue) ?
        <div >
          <div style={titleStyle}>门店新客:
            {isValueSet(userScope4FailReasonValue) ? (
              <span style={{ color: '#f52743' }} >&nbsp;&nbsp;({userScope4FailReasonValue})</span>
            ) : null}
          </div>
          <div style={{ display: 'flex' }}>
            <div style={labelStyle}>追补资金：</div>
            <div style={valueStyle}>{userScope4ShopSubsidyValue}元</div>
          </div>
          <div style={{ display: 'flex' }}>
            <div style={labelStyle}>每日库存</div>
            <div style={valueStyle}>{userScope4DailyStockValue}个</div>
          </div>
        </div>
        : null}
      {Array.isArray(raiseAmountPeriodTimeValue) ?
        <div>
          <div style={titleStyle}>特殊时段:
            {isValueSet(raiseAmountPeriodTimeFailReasonValue) ? (
              <span style={{ color: '#f52743' }}>&nbsp;&nbsp;({raiseAmountPeriodTimeFailReasonValue})</span>
            ) : null}
          </div>
          {(raiseAmountPeriodTimeValue).map((item: PlayRuleInfo) => (
            <div key={item.periodTime} style={{ display: 'flex' }}>
              <div style={labelStyle}> {item.periodTime}</div>
              <div style={valueStyle}>{item.shopSubsidyCny}元</div>
            </div>
          ))}
          <div style={{ display: 'flex' }}>
            <div style={labelStyle}>每日库存</div>
            <div style={valueStyle}>{raiseAmountPeriodTimeDailyStockValue}个</div>
          </div>
        </div> : null
      }
    </Balloon>
  );
}