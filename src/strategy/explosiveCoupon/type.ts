
// playStep 组件类型
export type RangeValueType = {
  min?: number;
  max?: number;
}

export interface PlayStepValueType {
  basicInvestment: {
    hasCondition?: 0 | 1;
    condition?: { min?: number; max?: number; };
    shopSubsidy?: { min?: number; max?: number; };
    dailyStock?: { min?: number; max?: number; };
  }
  shopSupplement?: {
    shopSupplementEnabled?: 0 | 1;

    userScopeSupplementEnabled?: boolean;
    userScopeSupplementSubsidy?: RangeValueType;
    userScopeSupplementDailyStock?: RangeValueType;

    timeSupplementEnabled?: boolean;
    timeSupplement0To10Amount?: RangeValueType;
    timeSupplement10To14Amount?: RangeValueType;
    timeSupplement14To17Amount?: RangeValueType;
    timeSupplement17To21Amount?: RangeValueType;
    timeSupplement21ToEndAmount?: RangeValueType;
    timeSupplementDailyStock?: RangeValueType;
  }
}

// viewJsonType api传递
type ActivityLimitRule = {
  dayCountLimit?: number;
  dayCountLimitMax?: number;
};

type CouponRule = {
  conditionMinCny?: string;
  conditionMaxCny?: string;
  shopSubsidyMinCny?: string;
  shopSubsidyMaxCny?: string;
};

// 'A_WITH_THRESHOLD'  商户基础出资_有门槛  | 'A_WITHOUT_THRESHOLD'  商户基础出资_无门槛
export type AmountScenceAType = {
  amountScene: "A_WITH_THRESHOLD" | "A_WITHOUT_THRESHOLD";
  activityLimitRule: ActivityLimitRule;
  couponRule: CouponRule;
};

export type AmountScenceYUserScopeType = {
  amountScene: "Y_WITH_USER_SCOPE";
  activityLimitRule: ActivityLimitRule;
  activityMarketingInfo: {
    userScope: 4;// 4 门店新课
  };
  couponRule: CouponRule;
};

export type PeriodTimeType = '00:00:00_10:00:00' | '10:00:00_14:00:00' | '14:00:00_17:00:00' |
  '17:00:00_21:00:00' | '21:00:00_23:59:59'
export type CouponPeriodRuleType = {
  periodTime: PeriodTimeType;
  shopSubsidyMinCny?: string;
  shopSubsidyMaxCny?: string;
};
export type AmountScenceYTimeType = {
  amountScene: "Y_WITH_TIME";
  activityLimitRule: ActivityLimitRule;
  couponPeriodAndRule: CouponPeriodRuleType[];
};

export type ActivityRuleType = AmountScenceAType | AmountScenceYUserScopeType | AmountScenceYTimeType;

export interface ViewJsonType {
  activityType: 1000038;
  activityRule: ActivityRuleType[];
};
