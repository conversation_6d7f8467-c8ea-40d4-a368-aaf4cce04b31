import * as React from "react";
import * as api from "../../../api";
import { FollowHeartPlaydata } from "../type";
import { UserScopeView } from "../../../components/formControls/UserScopeSelect";
import { DeliveryTypeView } from "../../../components/formControls/DeliveryTypeSelect";
import { BudgetPickerDetail } from "@alife/carbon-biz-kunlun-zs/lib/components/BudgetPicker/BudgetPicker";
import { UserBuyLimitView } from "../../../components/formControls/UserBuyLimit";

interface Props {
  activityInfo: { activityId: number | string };
  env: "daily" | "pre" | "prod";
}

const Field: React.FC<any> = ({ label, children, extra }) => {
  return (
    <div className="zs-act-field" style={{ lineHeight: "32px" }}>
      <label htmlFor="">{label}</label>
      <div>{children}</div>
      {extra ? <div className="zs-act-field-extra">{extra}</div> : null}
    </div>
  );
};

const PlayViewForHeart: React.FC<Props> = ({ activityInfo, env }) => {
  const { activityId } = activityInfo;
  const [viewJson, setViewJson] = React.useState<FollowHeartPlaydata>();

  React.useEffect(() => {
    const getDetail = async () => {
      const res = await api.subActivity.getDetail(activityId);

      if (res?.data?.viewJson) {
        try {
          setViewJson(JSON.parse(res?.data?.viewJson) || "");
        } catch (e: any) {
          console.error(
            e.message || "parse viewJson err in CommodityRulesView"
          );
        }
      }
    };

    getDetail();
  }, [activityId]);

  if (!viewJson) {
    return null;
  }

  return (
    <div className="zs-act-info">
      <div className="zs-act-area">
        <div className="zs-act-area-title" style={{ marginBottom: 30 }}>
          玩法设置
        </div>
        <div className="zs-act-section">
          <Field label="优惠规则：">
            <a
              href={viewJson?.commodityListFilePath}
              target="_blank"
              style={{ color: "#409EFF" }}
            >
              下载套餐明细{">"}
              {">"}
            </a>
          </Field>
          <Field label="限制人群:" name="userScope">
            <UserScopeView
              value={+viewJson?.activityMarketingInfo?.userScope!}
            />
          </Field>
          <Field label="用户购买限定:">
            <UserBuyLimitView
              value={viewJson.activityLimitRule}
              userDayCountLimitText="每个套餐每人每天限购"
              userTotalCountLimitText="每个套餐每人活动期间限购"
            />
          </Field>
          <Field label="生效渠道:">
            <DeliveryTypeView
              value={+viewJson?.activityMarketingInfo?.deliveryType}
            />
          </Field>
          <Field label="预算:">
            {viewJson?.activityMarketingInfo.budgetId ? (
              <BudgetPickerDetail
                env={{
                  env,
                  view: "detail",
                  detailContext: {
                    activityId: activityInfo.activityId as number,
                  },
                }}
                value={{
                  budgetId: viewJson?.activityMarketingInfo.budgetId,
                  budgetName: viewJson?.activityMarketingInfo.budgetName!,
                }}
              />
            ) : (
              "-"
            )}
          </Field>
        </div>
      </div>
    </div>
  );
};

export default PlayViewForHeart;
