import { aslcClient } from "../../api";

interface QueryCombineTemplateListParams {
  searchKey: string;
  activityBeginTime: Date;
  activityEndTime: Date;
}

export const ComboService = {
  async queryCombineTemplateList(params: QueryCombineTemplateListParams) {
    const res = await aslcClient.fetch({
      apiKey:
        "ele-newretail-investment.NewretailInvestmentYundingCommonService.searchCombineTemplateList",
      params: { ...params },
    });
    return res;
  },
};
