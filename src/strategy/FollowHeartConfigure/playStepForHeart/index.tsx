import * as React from "react";

import { <PERSON><PERSON>, Field, Form, Message, Radio } from "@alifd/next";

import { isValueSet } from "../ComboConfigure/helper";
import { resolveEnv } from "../../../common";
import CheckboxWithInput from "../../../components/formControls/CheckboxWithInput";
import { PlayDataTs } from "../type";
import { UserScopeEnum, UserScopeSelect } from "../../../components/formControls/UserScopeSelect";
import "./style.scss";
import { BudgetPickerEdit } from "@alife/carbon-biz-kunlun-zs/lib/components/BudgetPicker/BudgetPicker";
import { ActivityTypeEnum } from "../../../constants";

interface PropsTs {
  value?: PlayDataTs;
  onChange: (v?: PlayDataTs) => void;
  onEvent: (e: any) => void;
  view: "edit" | "detail";
  budgetRequired?: boolean; // 是否需要预算
  budgetParams?: {
    subsidyType: "b" | "p" | "c" | "d";
    bizDate: number;
    templateId: string;
  };
}
const formItemLayout = {
  labelCol: {
    span: 7,
  },
  wrapperCol: {
    span: 16,
  },
};

function wrapCallback(_callback: any) {
  let callback = _callback;
  if (!callback) {
    callback = (msg: string) => {
      if (msg) {
        throw new Error(msg);
      }
    };
  }
  return callback;
}
const PlayStepForHeart: React.FC<PropsTs> = ({
  value,
  onChange,
  onEvent,
  view,
  budgetParams,
  budgetRequired
}) => {
  let field = Field.useField({
    values: {
      userScope: 0,
      userLimit: {
        totalLimit: "",
      },
      deliveryType: "1",
    },
    onChange: () => {
      onChange(field.getValues());
    },
  });

  return (
    <div className="zs-play-for-heart">
      <Form
        {...formItemLayout}
        isPreview={view === "detail"}
        style={{ maxWidth: "800px" }}
      >
        <Form.Item label="限制人群:" required={true}>
          <UserScopeSelect
            {...field.init("userScope")}
            options={[
              { label: "全部顾客", value: UserScopeEnum.ALL },
            ]}
          />
        </Form.Item>
        <Form.Item label="用户购买限定:" required={true}>
          <CheckboxWithInput
            {...field.init("userLimit", {
              rules: [
                {
                  required: true,
                  message: "请输入",
                },
                {
                  validator: (_r: any, v: any, _callback) => {
                    let callback = wrapCallback(_callback);
                    if ("totalLimit" in v) {
                      if (!isValueSet(v.totalLimit)) {
                        callback("请输入每个套餐每人活动期间限购");
                        return;
                      } else if (+v.totalLimit <= 0 || +v.totalLimit > 9999) {
                        callback("每个套餐每人活动期间限购范围是1-9999");
                        return;
                      }
                    }
                    if ("dayLimit" in v) {
                      if (!isValueSet(v.dayLimit)) {
                        callback("请输每个套餐每人每天限购");
                        return;
                      } else if (+v.dayLimit <= 0 || +v.dayLimit > 9999) {
                        callback("每个套餐每人每天限购范围是1-9999");
                        return;
                      }
                    }
                    if ("totalLimit" in v && "dayLimit" in v) {
                      if (+v.dayLimit > +v.totalLimit) {
                        callback("每个套餐每人每天限购不能大于每个套餐每人活动期间限购");
                        return;
                      }
                    }
                    if (!("totalLimit" in v) && !("dayLimit" in v)) {
                      callback("请填写用户购买限定");
                    }
                    callback();
                  },
                },
              ],
            })}
            options={[
              { label: "每个套餐每人每天限购", key: "dayLimit" },
              { label: "每个套餐每人活动期间限购", key: "totalLimit" },
            ]}
            unit="件"
          />
        </Form.Item>
        <Form.Item label="生效渠道:" required={true}>
          <Radio.Group {...field.init("deliveryType")}>
            <Radio value="1">仅外卖生效</Radio>
          </Radio.Group>
        </Form.Item>
        <Form.Item label="预算:">
          <BudgetPickerEdit
            style={{ width: 400 }}
            {...field.init("budget", {
              rules: [
                {
                  required: budgetRequired === true,
                  message: "请选择预算",
                },
                {
                  validator: (r: any, v: any, _callback) => {
                    let callback = wrapCallback(_callback);
                    if (
                      !isValueSet(v?.budgetId) &&
                      budgetRequired === true
                    ) {
                      callback("请选择预算");
                    } else if (
                      budgetRequired !== true &&
                      isValueSet(v?.budgetId)
                    ) {
                      callback("无平台补贴，请清空预算");
                    } else {
                      callback();
                    }
                  },
                },
              ],
            })}
            disabled={false}
            hasClear={true}
            value={value?.budget ? value.budget : undefined}
            env={resolveEnv()}
            bizDate={budgetParams?.bizDate}
            budgetType="ACTIVITY"
            templateId={budgetParams?.templateId}
            subsidyType={budgetParams?.subsidyType}
            activityType={ActivityTypeEnum.MATCH_AS_YOU_PLEASE}
          />
        </Form.Item>
        <Form.Item label=" ">
          <div style={{ padding: "30px 0px 20px" }}>
            <Button
              data-test-id="scope-info-step-cancel-btn"
              onClick={() => onEvent && onEvent({ type: "cancel" })}
            >
              取消
            </Button>
            &nbsp;&nbsp;
            <Button
              data-test-id="scope-info-step-prev-btn"
              onClick={() => onEvent && onEvent({ type: "prev" })}
            >
              上一步
            </Button>
            &nbsp;&nbsp;
            <Button
              type="primary"
              data-test-id="scope-info-step-next-btn"
              onClick={async () => {
                const r = await field.validatePromise();
                console.log("validate result", r);
                if (r.errors && Object.keys(r.errors).length > 0) {
                  const key = Object.keys(r.errors)[0];
                  const msg = r.errors[key].errors[0];
                  Message.error(msg as string);
                } else {
                  onEvent({ type: "next" });
                }
              }}
            >
              发布
            </Button>
          </div>
        </Form.Item>
      </Form>
    </div>
  );
};

export default PlayStepForHeart;
