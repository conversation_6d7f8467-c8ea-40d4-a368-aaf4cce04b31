import * as React from "react";
import ComboSelect from "./ComboSelect";
import { CombineValue } from "../type";
import { useMemo } from "react";
import { Button, Message, Tab } from "@alifd/next";
import ComboDisplay from "./ComboDisplay";
import ComboTable from "./ComboTable";
import { isValueSet } from "./helper";
import "./style.scss";
interface Props {
  value?: CombineValue[];
  onChange: (v?: CombineValue[]) => void;
  onEvent: (e: any) => void;
  activityBeginTime?: string;
  activityEndTime?: string;
}

function validateCombineValues(values?: CombineValue[]): {
  msg: string;
  combineTemplateId?: number;
}[] {
  const errors: {
    msg: string;
    combineTemplateId: number;
  }[] = [];
  if (!Array.isArray(values) || (values || []).length === 0) {
    return [
      {
        msg: "请至少选择一个活动套餐",
      },
    ];
  }

  values.forEach((combine) => {
    combine.templateSalePackList.forEach((salePack) => {
      salePack.combineTemplateSubItemList.forEach((subItem) => {
        if (!isValueSet(subItem?.platformSubsidyMax)) {
          errors.push({
            msg: `分类${salePack.salePackageTitle}中商品${subItem.combineTemplateSubItemTitle}单品补贴金额未填写`,
            combineTemplateId: combine.combineTemplateId,
          });
        } else {
          if (
            subItem?.platformSubsidyMax! < 0 ||
            subItem?.platformSubsidyMax! > 99999
          ) {
            errors.push({
              msg: `单品补贴金额范围是0-99999`,
              combineTemplateId: combine.combineTemplateId,
            });
          }
        }

        if (!isValueSet(subItem?.discount)) {
          errors.push({
            msg: `分类${salePack.salePackageTitle}中商品${subItem.combineTemplateSubItemTitle}活动价未填写`,
            combineTemplateId: combine.combineTemplateId,
          });
        } else {
          if (subItem?.discount! < 0.01 || subItem?.discount! > 99999) {
            errors.push({
              msg: `活动价范围是0.01-99999`,
              combineTemplateId: combine.combineTemplateId,
            });
          }
        }

        if (subItem?.quantityLimit) {
          if (
            !isValueSet(subItem?.totalCountLimitMin) &&
            !isValueSet(subItem?.totalCountLimitMax)
          ) {
            errors.push({
              msg: `分类${salePack.salePackageTitle}中商品${subItem.combineTemplateSubItemTitle}活动份数最少填写一个上限或者下限`,
              combineTemplateId: combine.combineTemplateId,
            });
          }
          if (isValueSet(subItem?.totalCountLimitMin)) {
            if (
              subItem?.totalCountLimitMin! < 1 ||
              subItem?.totalCountLimitMin! > 99999
            ) {
              errors.push({
                msg: `活动份数填写范围是1-99999`,
                combineTemplateId: combine.combineTemplateId,
              });
            }
          }
          if (isValueSet(subItem?.totalCountLimitMax)) {
            if (
              subItem?.totalCountLimitMax! < 1 ||
              subItem?.totalCountLimitMax! > 99999
            ) {
              errors.push({
                msg: `活动份数填写范围是1-99999`,
                combineTemplateId: combine.combineTemplateId,
              });
            }
          }
          if (
            isValueSet(subItem?.totalCountLimitMax) &&
            isValueSet(subItem?.totalCountLimitMin)
          ) {
            if (subItem?.totalCountLimitMin! > subItem?.totalCountLimitMax!) {
              errors.push({
                msg: `分类${salePack.salePackageTitle}中商品${subItem.combineTemplateSubItemTitle}活动份数下限不能大于上限`,
                combineTemplateId: combine.combineTemplateId,
              });
            }
          }
        }
      });
    });
  });

  return errors;
}

const ComboConfigure: React.FC<Props> = ({
  activityBeginTime,
  activityEndTime,
  value,
  onChange,
  onEvent,
}) => {
  const tabList = useMemo(() => {
    if (Array.isArray(value) && value?.length > 0) {
      return value;
    } else {
      return [];
    }
  }, [value]);
  const [tab, setTab] = React.useState(`${value?.[0]?.combineTemplateId}`);
  const onSubmit = async () => {
    const validationErrors = validateCombineValues(value);
    if (validationErrors.length > 0) {
      Message.error(validationErrors[0].msg);
      if (validationErrors[0].combineTemplateId) {
        setTab(`${validationErrors[0].combineTemplateId}`);
      }
      return;
    }

    if (onEvent) onEvent({ type: "next" });
  };
  return (
    <div className="zs-combo-configure">
      <Message type="notice">
        每个分组下最多允许2个商品活动价高于设置最低活动价
      </Message>
      <div className="zs-combo-configure-content">
        <div className="zs-combo-configure-select">
          选择活动套餐：
          <ComboSelect
            activityBeginTime={activityBeginTime}
            activityEndTime={activityEndTime}
            value={value}
            onChange={(selectData: CombineValue[] | undefined) => {
              if ((selectData || []).length > 20) {
                Message.error("最多选择20个套餐");
                return;
              }
              // 在修改选择套餐的时候，保留已经填写过套餐的数据
              if (
                !Array.isArray(value) ||
                value.length === 0 ||
                !selectData ||
                (selectData || []).length === 0
              ) {
                onChange(selectData);
                setTab(`${selectData?.[0]?.combineTemplateId}`);
                return;
              }

              const selectIdsSet = new Set(
                selectData.map((item) => item.combineTemplateId)
              );

              // 过滤 value，只保留 selectIdsSet 中存在的元素
              const filteredValue = value.filter((item) =>
                selectIdsSet.has(item.combineTemplateId)
              );

              // 合并 selectIdsSet 中不在 value 中的元素
              const selectToAdd = selectData.filter(
                (item) =>
                  !filteredValue.find(
                    (aItem) =>
                      aItem.combineTemplateId === item.combineTemplateId
                  )
              );
              const newValue = [...filteredValue, ...selectToAdd];
              onChange(newValue);
              setTab(`${newValue[0].combineTemplateId}`);
            }}
          />
        </div>
        {tabList.length > 0 ? (
          <div className="zs-combo-configure-table">
            <Tab
              activeKey={tab}
              onChange={(v) => {
                setTab(v as string);
              }}
            >
              {tabList.map((item) => (
                <Tab.Item
                  key={item.combineTemplateId}
                  title={item.combineTemplateTitle}
                >
                  <div className="zs-combo-configure-table-content">
                    <ComboDisplay detail={item} />
                    {item.templateSalePackList.map((v) => (
                      <div
                        className="zs-combo-configure-table-content-item"
                        key={v?.salePackageId}
                      >
                        <div className="zs-combo-configure-table-content-item-title">
                          分类：{v.salePackageTitle}
                          <span style={{ color: "#222222", fontSize: "12px" }}>
                            （分类ID：{v.salePackageId}）
                          </span>
                          <span
                            style={{
                              color: "rgb(153, 153, 153)",
                              fontSize: "12px",
                            }}
                          >
                            顾客任选 {v.minSelectNum} 份
                          </span>
                        </div>
                        <ComboTable
                          detail={v.combineTemplateSubItemList}
                          onChange={(tableValue) => {
                            const updatedValues = value!.map((combine) => {
                              if (
                                combine.combineTemplateId ===
                                item.combineTemplateId
                              ) {
                                // 找到对应的 套餐
                                const updatedTemplateSalePackList =
                                  combine.templateSalePackList.map((pack) => {
                                    if (
                                      pack.salePackageId === v.salePackageId
                                    ) {
                                      // 找到对应的 分类
                                      return {
                                        ...pack,
                                        combineTemplateSubItemList: tableValue,
                                      };
                                    }
                                    return pack;
                                  });
                                return {
                                  ...combine,
                                  templateSalePackList:
                                    updatedTemplateSalePackList,
                                };
                              }
                              return combine;
                            });
                            onChange(updatedValues);
                          }}
                        />
                      </div>
                    ))}
                  </div>
                </Tab.Item>
              ))}
            </Tab>
          </div>
        ) : null}
        <div className="zs-combo-configure-footer">
          <Button
            data-test-id="scope-info-step-cancel-btn"
            onClick={() => onEvent && onEvent({ type: "cancel" })}
          >
            取消
          </Button>
          &nbsp;&nbsp;
          <Button
            data-test-id="scope-info-step-prev-btn"
            onClick={() => {
              Message.warning("修改活动时间会影响套餐选择范围");
              onEvent && onEvent({ type: "prev" });
            }}
          >
            上一步
          </Button>
          &nbsp;&nbsp;
          <Button
            type="primary"
            data-test-id="scope-info-step-next-btn"
            onClick={onSubmit}
          >
            下一步
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ComboConfigure;
