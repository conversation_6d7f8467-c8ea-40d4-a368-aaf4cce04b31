import * as React from "react";
import { useState } from "react";
import { useDebounceEffect } from "ahooks";
import { Message, Select } from "@alifd/next";
import { ComboService } from "../service";
import { CombineDetail, CombineValue } from "../type";

interface ComboSelectPropsTs {
  value?: CombineDetail[];
  onChange: (v: CombineValue[] | undefined) => void;
  activityBeginTime?: string;
  activityEndTime?: string;
}

const ComboSelect: React.FC<ComboSelectPropsTs> = ({
  value,
  onChange,
  activityBeginTime,
  activityEndTime,
}) => {
  const [dataSource, setDataSource] = useState<CombineDetail[]>([]);
  const [search, setSearch] = useState<string>("");

  useDebounceEffect(() => {
     setDataSource([])
    if (search && activityBeginTime && activityEndTime) {
      ComboService.queryCombineTemplateList({
        searchKey: search,
        activityBeginTime: new Date(activityBeginTime),
        activityEndTime: new Date(activityEndTime),
      })
        .then((res) => {
          let newData = res.data || [];
          if (Array.isArray(value)) {
            newData = [...value, ...(res.data || [])];
          }
          const uniqueArr = Array.from(
            new Set(
              newData.map((item: CombineDetail) => item.combineTemplateId)
            )
          ).map((id) =>
            newData.find((item: CombineDetail) => item.combineTemplateId === id)
          );
          setDataSource(uniqueArr);
        })
        .catch((err) => {
          console.log(err);
          Message.error("套餐查询失败");
        });
    }
  }, [activityBeginTime, activityEndTime, search]);

  return (
    <div className="zs-base-info-business-form-item">
      <Select
        showSearch
        hasClear
        mode="multiple"
        placeholder="请输入套餐名称或ID"
        onSearch={(v) => setSearch(v)}
        value={
          Array.isArray(value)
            ? (value || []).map((v) => v.combineTemplateId)
            : []
        }
        filterLocal={false}
        onChange={(v) => {
          const result = dataSource.filter((item) => {
            return ((v as number[]) || []).includes(item?.combineTemplateId);
          });
          onChange(result);
        }}
        dataSource={dataSource.map((item: any) => {
          return {
            label: item.combineTemplateTitle,
            value: item.combineTemplateId,
          };
        })}
        style={{ width: 300, marginRight: 8 }}
      />
    </div>
  );
};

export default ComboSelect;
