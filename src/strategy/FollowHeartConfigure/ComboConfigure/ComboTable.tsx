import * as React from "react";
import "./style.scss";
import { CombineTemplateSubItemValue } from "../type";
import { Table } from "@alifd/next";
import ValueInput from "./TableComponents/PriceLikeValueInput";
import TotalCount from "./TableComponents/TotalCount";
import { isValueSet } from "./helper";

interface PropsTs {
  detail: CombineTemplateSubItemValue[];
  onChange: (v: CombineTemplateSubItemValue[]) => void;
}

function verify(range: [number, number], value: number, detail?: CombineTemplateSubItemValue[]) {
  const INVALID_RANGE_RESPONSE = {
    state: false,
    content: `范围${range[0]}-${range[1]}`,
  };
  
  if (!isValueSet(value)) {
    return INVALID_RANGE_RESPONSE;
  }

  if (value < range[0] || value > range[1]) {
    return {
      state: true,
      content: INVALID_RANGE_RESPONSE.content,
    };
  }
  const priceList = detail?.map(item => item?.discount).filter(Boolean);
  if(detail && (priceList || []).length > 3) {
    const minPrice = Math.min(...(priceList as number[]));
    const higherPrices = detail.filter(item => (item?.discount ?? 0.01) > minPrice);
  
    if (higherPrices.length > 2 && value > minPrice) {
      return {
        state: true,
        content: "活动价不得高于当前分组下设置最低活动价",
      };
    }
  }

  return {
    state: false,
    content: INVALID_RANGE_RESPONSE.content,
  };
}

const ComboTable: React.FC<PropsTs> = ({ detail, onChange }) => {
  return (
    <div className="zs-combo-table">
      <Table hasBorder={false} dataSource={detail}>
        <Table.Column
          dataIndex="combineTemplateSubItemId"
          title="商品"
          cell={(_value, _index, record) => {
            return (
              <div style={{ fontSize: "14px" }}>
                {record.combineTemplateSubItemTitle}
                <div style={{ color: "#999", fontSize: "12px" }}>
                  条形码：{record.combineTemplateSubItemId}
                </div>
              </div>
            );
          }}
        />
        <Table.Column
          dataIndex="quantity"
          title="每份件数"
          cell={(v: number) => {
            return <div style={{ fontSize: "14px" }}>×{v}</div>;
          }}
        />
        <Table.Column
          dataIndex="price"
          title="活动份数"
          cell={(_v: number, _index, record) => {
            return (
              <TotalCount
                min={1}
                max={99999}
                precision={0}
                value={{
                  totalCountLimitMin: record?.totalCountLimitMin,
                  totalCountLimitMax: record?.totalCountLimitMax,
                  quantityLimit: record?.quantityLimit,
                }}
                onChange={(v) => {
                  const updatedValues = detail.map((item) => {
                    if (
                      item.combineTemplateSubItemId ===
                      record.combineTemplateSubItemId
                    ) {
                      return {
                        ...item,
                        ...v,
                      };
                    }
                    return item;
                  });
                  onChange(updatedValues);
                }}
              />
            );
          }}
        />
        <Table.Column
          dataIndex="discount"
          title="活动价"
          cell={(_v: number, _index, record) => {
            return (
              <ValueInput
                precision={2}
                min={0.01}
                max={99999}
                value={record?.discount}
                hasError={verify([0.01, 99999],record?.discount, detail)}
                onChange={(v) => {
                  const updatedValues = detail.map((item) => {
                    if (
                      item.combineTemplateSubItemId ===
                      record.combineTemplateSubItemId
                    ) {
                      return {
                        ...item,
                        discount: v as number,
                        discountType: 3,
                      };
                    }
                    return item;
                  });
                  onChange(updatedValues);
                }}
              />
            );
          }}
        />
        <Table.Column
          dataIndex="platformSubsidyMax"
          title="补贴金额"
          cell={(_v: number, _index, record) => {
            return (
              <ValueInput
                precision={2}
                min={0}
                max={99999}
                hasError={verify([0, 99999],record?.platformSubsidyMax)}
                value={record?.platformSubsidyMax}
                onChange={(v) => {
                  const updatedValues = detail.map((item) => {
                    if (
                      item.combineTemplateSubItemId ===
                      record.combineTemplateSubItemId
                    ) {
                      return {
                        ...item,
                        platformSubsidyMax: v as number,
                      };
                    }
                    return item;
                  });
                  onChange(updatedValues);
                }}
              />
            );
          }}
        />
      </Table>
    </div>
  );
};

export default ComboTable;
