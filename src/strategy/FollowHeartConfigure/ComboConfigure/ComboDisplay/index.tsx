import * as React from "react";
import "./style.scss";

interface PropsTs {
  detail: {
    combineTemplateId: number;
    combineTemplateTitle: string;
    mainPic: string;
  };
}

const ComboDisplay: React.FC<PropsTs> = ({ detail }) => {
  const { combineTemplateId, combineTemplateTitle, mainPic } = detail;

  return (
    <div className="zs-combo-display">
      <div className="zs-combo-display-img">
        <img src={mainPic} alt="" className="zs-combo-display-img-item" />
      </div>
      <div className="zs-combo-display-detail">
        <div className="zs-combo-display-detail-title">
          {combineTemplateTitle}
        </div>
        <div className="zs-combo-display-detail-id">
          套餐条形码：{combineTemplateId}
        </div>
      </div>
    </div>
  );
};

export default ComboDisplay;
