import "./style.scss";

import React, { useMemo } from "react";
import classNames from "classnames";
import { NumberPicker, Radio } from "@alifd/next";

const Tip: React.FC<{ content: JSX.Element; hasError?: boolean }> = ({
  content,
  hasError,
}) => {
  return (
    <div
      className={classNames({
        "zs-price-like-value-input-tip": true,
        "zs-error": hasError,
      })}
    >
      {content}
    </div>
  );
};

function isValueSet(v: any) {
  return v !== "" && v !== null && v !== undefined;
}

interface Props {
  value?: {
    quantityLimit?: boolean | undefined;
    totalCountLimitMin?: number | undefined;
    totalCountLimitMax?: number | undefined;
  };
  style?: React.CSSProperties;
  precision?: number;
  hasError?: boolean;
  onChange: (v: any) => void;
  min: number;
  max: number;
}

const TotalCount: React.FC<Props> = ({
  style,
  value,
  onChange,
  precision,
  min,
  max,
}) => {
  const hasRange = isValueSet(min) && isValueSet(max);
  const hasError = useMemo(() => {
    if (
      isValueSet(value?.totalCountLimitMin)
    ) {
      return {
        state: value?.totalCountLimitMin! < min || value?.totalCountLimitMin! > max,
        content: `范围${min}~${max}`,
      };
    }
    if (
      isValueSet(value?.totalCountLimitMin) &&
      isValueSet(value?.totalCountLimitMax)
    ) {
      if (value?.totalCountLimitMin! > value?.totalCountLimitMax!) {
        return {
          state: true,
          content: `上限须大于等于下限`,
        };
      }
    }
    return {
      state: false,
      content: `范围${min}~${max}`,
    };
  }, [value?.totalCountLimitMin, value?.totalCountLimitMax, min, max]);
  const hasErrorMax = useMemo(() => {
    if (isValueSet(value?.totalCountLimitMax)) {
      return (
        value?.totalCountLimitMax! < min || value?.totalCountLimitMax! > max
      );
    }
    return false;
  }, [value?.totalCountLimitMin, value?.totalCountLimitMax, min, max]);

  return (
    <>
      <Radio.Group value={value?.quantityLimit ? 1 : 0} onChange={(v) => {
        if(v === 0) {
          onChange({
            quantityLimit: false,
            totalCountLimitMin: undefined,
            totalCountLimitMax: undefined,
          });
        } else {
          onChange({
            quantityLimit: true 
          });
        }
      }}>
        <Radio value={0}>不限</Radio>
        <Radio value={1}>自定义</Radio>
      </Radio.Group>
      {value?.quantityLimit ? (
        <div className="zs-total-count">
          <NumberPicker
            style={style}
            precision={precision}
            value={value?.totalCountLimitMin}
            placeholder="下限"
            onChange={(theValue) => {
              onChange({
                ...value,
                totalCountLimitMin: theValue,
              });
            }}
            state={hasError.state ? "error" : undefined}
          />{" "}
          ~{" "}
          <NumberPicker
            style={style}
            precision={precision}
            value={value?.totalCountLimitMax}
            placeholder="上限"
            onChange={(theValue) => {
              onChange({
                ...value,
                totalCountLimitMax: theValue,
              });
            }}
            state={hasErrorMax ? "error" : undefined}
          />
          {hasRange ? (
            <Tip
              hasError={hasError.state || hasErrorMax}
              content={<>{hasError?.content}</>}
            />
          ) : null}
        </div>
      ) : null}
    </>
  );
};

export default TotalCount;
