import "./style.scss";

import React from "react";
import classNames from "classnames";
import { NumberPicker } from "@alifd/next";
import { isValueSet } from "../../helper";

const Tip: React.FC<{ content: JSX.Element; hasError?: boolean }> = ({
  content,
  hasError,
}) => {
  return (
    <div
      className={classNames({
        "zs-price-like-value-input-tip": true,
        "zs-error": hasError,
      })}
    >
      {content}
    </div>
  );
};

interface Props {
  value?: number;
  style?: React.CSSProperties;
  precision?: number;
  hasError?: {
    state?: boolean;
    content?: string;
  };
  onChange: (v: string | number | null | undefined) => void;
  min: number;
  max: number;
}

const ValueInput: React.FC<Props> = ({
  style,
  value,
  onChange,
  precision,
  min,
  max,
  hasError,
}) => {
  const hasRange = isValueSet(min) && isValueSet(max);
  return (
    <>
      <NumberPicker
        style={style}
        precision={precision}
        value={value}
        placeholder="请输入"
        onChange={(theValue) => onChange(theValue)}
        state={hasError?.state ? "error" : undefined}
      />
      {hasRange ? (
        <Tip
          hasError={hasError?.state}
          content={<>{hasError?.content}</>}
        />
      ) : null}
    </>
  );
};

export default ValueInput;
