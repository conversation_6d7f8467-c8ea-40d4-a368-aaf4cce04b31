
export interface CombineTemplateSubItem {
    combineTemplateSubItemId: number;
    combineTemplateSubItemTitle: string;
    quantity: number; // 件数;
    position: number; // 子品顺序;
}

interface TemplateSalePack {
    salePackageId: number;
    salePackageTitle: string;
    position: number; // 售卖包顺序;
    combineTemplateSubItemList: CombineTemplateSubItem[];
    minSelectNum: number;
}

export interface CombineDetail {
    combineTemplateId: number;
    combineTemplateTitle: string;
    mainPic: string;
    templateSalePackList: TemplateSalePack[];
}

export interface CombineTemplateSubItemValue {
    combineTemplateSubItemId: number;
    combineTemplateSubItemTitle: string;
    quantity: number; // 件数;
    position: number; // 子品顺序;
    platformSubsidyMax?: number;//元，子品平台最高补贴（注意不是单品）
    discount?: number; // 元，子品活动价（注意不是单品）
    discountType?: number;
    totalCountLimitMin?: number; // 子品活动最小份数
    totalCountLimitMax?: number;  // 子品活动最大份数 
    quantityLimit?: boolean; // 是否限制子品数量(前端定义)
}


interface TemplateSalePackValue {
    minSelectNum: number;
    salePackageId: number;
    salePackageTitle: string;
    position: number; // 售卖包顺序;
    combineTemplateSubItemList: CombineTemplateSubItemValue[];
}

export interface CombineValue {
    combineTemplateId: number;
    combineTemplateTitle: string;
    mainPic: string;
    templateSalePackList: TemplateSalePackValue[];
}

export interface PlayDataTs {
    userScope: string;
    userLimit: {
        totalLimit?: number;
        dayLimit?: number;
    };
    deliveryType: string;
    budget?: {
        budgetId: string;
        budgetName: string;
    };

}

export interface FollowHeartPlaydata {
    activityLimitRule: {
        userTotalCountLimit?: number;
        userDayCountLimit?: number;
    };
    activityMarketingInfo: {
        budgetId?: string;
        budgetName?: string;
        deliveryType: string;
        userScope: string;
    };
    activityType: number;
    weekday: string;
    periodList?: {
        openTime: string;
        closeTime: string;
    }[];
    commodityListFilePath?: string;
}
interface TargetCombineTemplateSubItem {
    combineTemplateSubItemId: number;
    combineTemplateSubItemTitle: string;
    quantity: number;
    platformSubsidyMax?: number;
    discount?: number;
    discountType?: number;
    totalCountLimitMin?: number;
    totalCountLimitMax?: number;
}

interface TargetTemplateSalePack {
    salePackageId: number;
    salePackageTitle: string;
    combineTemplateSubItemList: TargetCombineTemplateSubItem[];
}

export interface TargetCombineTemplate {
    combineTemplateId: number;
    combineTemplateTitle: string;
    templateSalePackList: TargetTemplateSalePack[];
}