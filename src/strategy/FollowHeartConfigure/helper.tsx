
import { ActivityTypeEnum } from "../../constants";
import { FollowHeartPlaydata, PlayDataTs } from "./type";

function isValueSet(v: any) {
  return v !== "" && v !== null && v !== undefined;
}


interface Budget {
  budgetId?: string;
  budgetName?: string;
}


interface FollowHeartBaseInfo {
  activityTime: {
    weeks: number[];
    batchRange: {
      type: "all" | "custom";
      list: { start: string; end: string }[];
    };
  };
}

export function transformFormData2ViewJson(options: {
  baseinfo: FollowHeartBaseInfo;
  formData: PlayDataTs;
}): FollowHeartPlaydata {
  const { userLimit, budget, deliveryType, userScope } = options.formData;
  return {
    activityType: ActivityTypeEnum.MATCH_AS_YOU_PLEASE,
    activityLimitRule: {
      userTotalCountLimit: isValueSet(userLimit.totalLimit)
        ? +userLimit.totalLimit!
        : undefined,
      userDayCountLimit: isValueSet(userLimit.dayLimit)
        ? +userLimit.dayLimit!
        : undefined,
    },
    activityMarketingInfo: {
      budgetId: budget?.budgetId,
      budgetName: budget?.budgetName,
      deliveryType: deliveryType,
      userScope: userScope,
    },
    weekday: options.baseinfo.activityTime.weeks.join(","),
    periodList: options.baseinfo?.activityTime?.batchRange?.list?.map((item) => {
      return {
        openTime: item.start.slice(0, "HH:mm".length) + ":00",
        closeTime: item.end.slice(0, "HH:mm".length) + ":59",
      };
    }),
  };
}

export function transformViewJson2FormData(options: {
  viewJson: string;
}): PlayDataTs | null {
  try {
    const playdata: FollowHeartPlaydata = JSON.parse(options.viewJson);

    let userLimit: {
      dayLimit?: number;
      totalLimit?: number;
    } = {};
    if (isValueSet(playdata.activityLimitRule.userDayCountLimit)) {
      userLimit.dayLimit = playdata.activityLimitRule.userDayCountLimit;
    }
    if (isValueSet(playdata.activityLimitRule.userTotalCountLimit)) {
      userLimit.totalLimit = playdata.activityLimitRule.userTotalCountLimit;
    }
    let budget: Budget | null = null;
    if (
      isValueSet(playdata.activityMarketingInfo.budgetId) &&
      isValueSet(playdata.activityMarketingInfo.budgetName)
    ) {
      budget = {
        budgetId: playdata.activityMarketingInfo.budgetId,
        budgetName: playdata.activityMarketingInfo.budgetName,
      };
    }
    const ret: PlayDataTs = {
      userScope: `${playdata.activityMarketingInfo.userScope}`,
      userLimit: userLimit,
      deliveryType: `${playdata.activityMarketingInfo.deliveryType}`,
      budget: budget ? budget : undefined,
    };

    return ret;
  } catch (e) {
    return null;
  }
}


