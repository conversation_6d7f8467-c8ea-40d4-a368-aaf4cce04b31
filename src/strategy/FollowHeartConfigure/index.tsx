import { Message } from "@alifd/next";

import * as api from "../../api";
import { maskMS } from "../../common";
import loadingDialog from "../../components/LoadingDialog";
import { ActivityTypeEnum } from "../../constants";
import { ActivityStrategy } from "../../models";
import { CableBase } from "../base";
import {
  checkNextTimeForAlpha,
} from "../mixins";
import { transformFormData2ViewJson, transformViewJson2FormData } from "./helper";



const FollowHeartConfigure: ActivityStrategy = {
  activityType: ActivityTypeEnum.MATCH_AS_YOU_PLEASE,
  generation: "v2",
  cable: {
    ...CableBase,
    workspace: "ExukovQrae6xWn2rt4NgG",
    model: "15CGxTsbE8S6ZxJtfEkUos",
    async checkNext(checkNextContext, creationContext, history) {
      const { step, dataStore } = checkNextContext;
      if (step.name === "baseinfo") {
        const timeError = await checkNextTimeForAlpha(
          checkNextContext,
          creationContext
        );
        if (timeError) {
          return timeError;
        }
      }
      if (step.name === "playdata") {
        const { baseinfo, scope, comboConfigure, playdata } = dataStore.data;
        const viewJson = transformFormData2ViewJson({
          baseinfo,
          formData: playdata
        })
        
        const payload: any = {
          // 活动类型信息
          templateId: creationContext.templateId,
          investmentType: creationContext.investmentType,
          investmentSchema: creationContext.investmentSchema,
          activityType: ActivityTypeEnum.MATCH_AS_YOU_PLEASE,

          // 基本信息
          name: baseinfo.name,
          remark: baseinfo.remark,
          description: baseinfo.description,
          beginTime: baseinfo.activityTime.start.toDate().getTime(),
          endTime: maskMS(baseinfo.activityTime.end.toDate().getTime()),
          allowCancel: baseinfo.allowCancel,
          signUpShopType: baseinfo.signUpShopType,
          signUpStartTime: baseinfo.signupTime.start.toDate().getTime(),
          signUpEndTime: maskMS(baseinfo.signupTime.end.toDate().getTime()),
          subsidyType: creationContext?.subsidyType,
          manageOrgList: baseinfo?.manageOrgList,
          // 招商范围信息
          shopPoolId: scope.pool.value,
          shopPoolName: scope.pool.label,

          auditMemberList:
            baseinfo.auditMemberList?.length > 0
              ? baseinfo.auditMemberList
              : null,
          // 套餐配置信息
          combineTemplateList: comboConfigure,

          // 玩法设置
          viewJson: JSON.stringify(viewJson),
        }

        const { hide } = loadingDialog.show({
          message: "活动创建中...",
        });
        try {
          const res = await api.subActivity.create(payload);
          if (res.success) {
            Message.success("创建成功");
            if (creationContext.activitySceneId) {
              history.push(
                "/sub-activity/" +
                  res.data +
                  "?activitySceneId=" +
                  creationContext.activitySceneId +
                  "&activityPlanId=" +
                  creationContext.activityPlanId
              );
            } else {
              history.push("/sub-activity/" + res.data);
            }
          } else {
            return { message: res.errorMessage };
          }
        } catch (e: any) {
          return { message: e?.message || "系统异常" };
        } finally {
          hide();
        }
      }
    },
  },
  hasAgentSubsidy() {
    return false;
  },
  isSupportAgent: false,
  isOfflineEnabled: function (state: number): boolean {
    // 活动状态: 1:未开始 2:活动中 3:活动暂停 4:活动取消 5:活动结束 8: 预算熔断
    return [1, 2, 3, 8].includes(state);
  },
  isDingtalkLinkEnabled: () => true,
  hasAuditMemberList: () => true,
  isSupportModifyStock: () => false,
  isItemAuditStatsVisible: true,
  isDetailInviteStatsOnMainActivityTableVisible: true,
  renderPlayDataInfoForTableView: function (): JSX.Element {
    return <></>;
  },
  gotoLegacyDetail: function (): void {},
  renderActivityRuleForTable() {
    return ''
  },
  renderActivityRuleForTableLegacy: function () {},
  async marketPlayMapper(viewJson: any) {
    return transformViewJson2FormData(viewJson)
  },
};

export default FollowHeartConfigure;
