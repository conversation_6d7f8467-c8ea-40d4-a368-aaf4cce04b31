import moment from 'moment';
import { v4 as uuid4 } from 'uuid';

import { isValueSet, parseJsonSafe } from '../../common';
import { ActivityTypeEnum } from '../../constants';
import { CreationContext } from '../../models';

import {
  BaseInfoForCreationContext,
  BaseInfoForMarketPlay,
  MarketPlayForPlayData,
  TimeRangeFromMarketPlay,
  ViewJsonForPlayData,
} from './constants';

export const formatMarketPlay2ViewJson = ({
  formData,
  baseinfo,
}: {
  formData: MarketPlayForPlayData;
  baseinfo: BaseInfoForMarketPlay;
}) => {
  const budget = formData.budget && parseJsonSafe(formData.budget);
  const categoryName = formData.categoryName;
  const userScope = formData.userScope;
  let overlying = undefined
  if (userScope === '0') {
    overlying = formData.overlying === '1';
  }
  const inStoreDeliveryType = formData.inStoreDeliveryType;
  const outStoreDeliveryType = formData.outStoreDeliveryType;
  const externalDeliveryChannel =
    formData.externalDeliveryChannel &&
    parseJsonSafe(formData.externalDeliveryChannel);
  const vipScene = formData.vipScene;
  const couponStock = parseJsonSafe(formData.couponStock);
  const couponName = parseJsonSafe(formData.couponName);
  const drawLimit = parseJsonSafe(formData.drawLimit);
  const logoImg = formData.logoImg;
  const couponActiveDate = parseJsonSafe(formData.couponActiveDate);
  const effectiveChannel = formData.effectiveChannel;
  const rule = parseJsonSafe(formData.rule);

  const viewJson = {
    activityType: ActivityTypeEnum.ITEM_CATEGORY_COUPON,
    activityLimitRule: {
      // 发券数量
      dayCountLimit: couponStock.dayLimit?.value, // 每日活动库存
      totalCountLimit: couponStock.totalLimit?.value, // 总活动库存
      // 领券限制
      userDayCountLimit: drawLimit.dayLimit?.value, // 每用户每商品每天限购X个
      userTotalCountLimit: drawLimit.totalLimit?.value, // 每个用户每个商品限购X个
    },
    activityMarketingInfo: {
      availableDays: isValueSet(couponActiveDate.days?.value) ? couponActiveDate.days.value : undefined,
      availableStartTime: couponActiveDate.range?.start || undefined, // "YYYY-MM-DD HH:mm:ss"
      availableEndTime: couponActiveDate.range?.end || undefined, // "YYYY-MM-DD HH:mm:ss"
      budgetId: budget?.budgetId,
      budgetName: budget?.budgetName,
      couponLogo: logoImg,
      couponName: couponName?.value,
      deliveryType: effectiveChannel, // 业务场景
      activityExternalDeliveryChannelList: externalDeliveryChannel,
      inStoreDeliveryType: inStoreDeliveryType,
      outStoreDeliveryType: outStoreDeliveryType,
      storeCategoryForApp: categoryName,
      userScope: userScope,
      vipScene: vipScene,
      overlying: overlying,
    },
    activityRule: {
      condition: rule?.condition,
      conditionType: 'SATISFY_X_AMOUNT',
      discount: rule?.discount,
      discountType: 'REDUCE',
      platformSubsidy: rule?.elemeSubsidy,
      shopSubsidy: rule?.shopSubsidy,
    },
    periodDTOList: (baseinfo?.activityTime?.batchRange?.list || []).map(
      (r: any) => {
        return {
          openTime: r?.start + ':00',
          closeTime: r?.end + ':59',
        };
      }
    ),
    weekday: (baseinfo?.activityTime?.weeks || []).join(','),
  };
  return viewJson;
};

/**
 * 详情展示， viewJson 转 玩法配置语法
 */
export const formatViewJson2MarketPlay = ({
  viewDto,
  baseInfo,
}: {
  viewDto: ViewJsonForPlayData;
  baseInfo: BaseInfoForCreationContext;
}) => {
  const { activityLimitRule, activityMarketingInfo, activityRule } = viewDto;

  let budget;
  let creator;
  let categoryName;
  let overlying;
  let couponActiveDate;
  let couponName;
  let couponStock = {};
  let dateRange;
  let drawLimit = {};
  let effectiveChannel;
  let externalDeliveryChannel;
  let inStoreDeliveryType;
  let logoImg;
  let outStoreDeliveryType;
  let rule;
  let userScope;
  let vipScene;


  // 预算信息
  if (
    isValueSet(activityMarketingInfo.budgetId) &&
    isValueSet(activityMarketingInfo.budgetName)
  ) {
    budget = {
      budgetId: activityMarketingInfo.budgetId,
      budgetName: activityMarketingInfo.budgetName,
      businessLineList: baseInfo?.businessLineList,
      subsidyType: baseInfo?.subsidyType,
    };
  }

  // 店内分类名称
  if (activityMarketingInfo.storeCategoryForApp) {
    categoryName = activityMarketingInfo.storeCategoryForApp;
  }

  // 创建人
  if (
    isValueSet(baseInfo?.createdUserId) &&
    isValueSet(baseInfo?.createdUserName)
  ) {
    creator = {
      name: `${baseInfo.createdUserName}`,
      id: baseInfo.createdUserId,
    };
  }

  // 红包使用时间
  if (isValueSet(activityMarketingInfo.availableDays)) {
    couponActiveDate = {
      days: { value: activityMarketingInfo.availableDays },
    };
  } else if (
    isValueSet(activityMarketingInfo.availableStartTime) &&
    isValueSet(activityMarketingInfo.availableEndTime)
  ) {
    couponActiveDate = {
      range: {
        start: moment(activityMarketingInfo.availableStartTime).format(
          'YYYY-MM-DD HH:mm:ss'
        ),
        end: moment(activityMarketingInfo.availableEndTime).format(
          'YYYY-MM-DD HH:mm:ss'
        ),
      },
    };
  }

  // 券名称(红包外部名称)
  if (activityMarketingInfo.couponName) {
    couponName = {
      value: activityMarketingInfo.couponName,
    };
  }

  // 券库存（发券数量）
  if (isValueSet(activityLimitRule.dayCountLimit)) {
    couponStock = {
      dayLimit: { value: activityLimitRule.dayCountLimit },
    };
  }
  if (isValueSet(activityLimitRule.totalCountLimit)) {
    couponStock = {
      ...couponStock,
      totalLimit: { value: activityLimitRule.totalCountLimit },
    };
  }

  // 红包发放时间
  if (baseInfo?.beginTime && baseInfo?.endTime) {
    dateRange = {
      start: moment(baseInfo.beginTime).format('YYYY-MM-DD HH:mm:ss'),
      end: moment(baseInfo.endTime).format('YYYY-MM-DD HH:mm:ss'),
    };
  }

  // 领券限制
  if (isValueSet(activityLimitRule.userDayCountLimit)) {
    drawLimit = {
      dayLimit: { value: activityLimitRule.userDayCountLimit },
    };
  }
  if (isValueSet(activityLimitRule.userTotalCountLimit)) {
    drawLimit = {
      ...drawLimit,
      totalLimit: { value: activityLimitRule.userTotalCountLimit },
    };
  }

  // 业务场景
  if (activityMarketingInfo.deliveryType) {
    effectiveChannel = activityMarketingInfo.deliveryType + '';
  }

  // 限定渠道
  if (
    activityMarketingInfo.activityExternalDeliveryChannelList &&
    activityMarketingInfo.activityExternalDeliveryChannelList.length > 0
  ) {
    externalDeliveryChannel =
      activityMarketingInfo.activityExternalDeliveryChannelList;
  }

  // 店内投放方式
  if (activityMarketingInfo.inStoreDeliveryType) {
    inStoreDeliveryType = activityMarketingInfo.inStoreDeliveryType + '';
  }

  // 图片
  if (activityMarketingInfo.couponLogo) {
    logoImg = activityMarketingInfo.couponLogo;
  }

  // 店外投放方式
  if (activityMarketingInfo.outStoreDeliveryType) {
    outStoreDeliveryType = activityMarketingInfo.outStoreDeliveryType + '';
  }

  // 优惠金额
  if (
    isValueSet(activityRule.condition) &&
    isValueSet(activityRule.discount) &&
    isValueSet(activityRule.platformSubsidy) &&
    isValueSet(activityRule.shopSubsidy)
  ) {
    rule = {
      condition: activityRule.condition,
      discount: activityRule.discount,
      elemeSubsidy: activityRule.platformSubsidy,
      shopSubsidy: activityRule.shopSubsidy,
    };
  }

  // 用户类型
  if (isValueSet(activityMarketingInfo.userScope)) {
    userScope = activityMarketingInfo.userScope + '';
  }

  // 目前只有 全部人群才能配置overlying
  if (userScope === '0' && isValueSet(activityMarketingInfo.overlying)) {
    overlying = activityMarketingInfo.overlying ? '1' : '0'
  }

  // 会员场景
  if (activityMarketingInfo.vipScene) {
    vipScene = activityMarketingInfo.vipScene + '';
  }

  return {
    budget: JSON.stringify(budget),
    categoryName,
    overlying,
    couponActiveDate: JSON.stringify(couponActiveDate),
    couponName: JSON.stringify(couponName),
    couponStock: JSON.stringify(couponStock),
    creator: JSON.stringify(creator),
    dateRange: JSON.stringify(dateRange),
    drawLimit: JSON.stringify(drawLimit),
    effectiveChannel,
    externalDeliveryChannel: JSON.stringify(externalDeliveryChannel),
    inStoreDeliveryType,
    logoImg,
    outStoreDeliveryType,
    rule: JSON.stringify(rule),
    userScope,
    vipScene
  };
};

function resolveTimeBatchRangeBatch(value: string) {
  try {
    const rangeList: TimeRangeFromMarketPlay = JSON.parse(value);
    if (rangeList.length === 1) {
      const range = rangeList[0];
      if (range.start === '00:00:00' && range.end === '23:59:59') {
        return {
          type: 'all',
          list: [{ start: '00:00', end: '23:59', key: uuid4() }],
        };
      } else {
        return {
          type: 'custom',
          list: [{
            start: range.start.slice(0, 'HH:mm'.length),
            end: range.end.slice(0, 'HH:mm'.length),
            key: uuid4()
          }],
        };
      }
    } else {
      return {
        type: 'custom',
        list: rangeList.map((r) => {
          return {
            key: uuid4(),
            start: r.start.slice(0, 'HH:mm'.length),
            end: r.end.slice(0, 'HH:mm'.length),
          };
        }),
      };
    }
  } catch (e) {
    console.error(e);
    return {
      type: 'all',
      list: [
        {
          key: uuid4(),
          start: '00:00',
          end: '23:59',
        },
      ],
    };
  }
}

export async function resolveCopyDataForViewJson(
  creationContext: CreationContext
) {
  let viewJson: ViewJsonForPlayData;
  try {
    viewJson =
      creationContext.activity &&
      JSON.parse(creationContext.activity?.viewJson);
    const { periodDTOList, weekday } = viewJson || {};
    const timeRangeBatch = resolveTimeBatchRangeBatch(
      JSON.stringify(
        periodDTOList.map((item) => {
          return { start: item.openTime, end: item.closeTime };
        })
      )
    );
    const baseInfo = {
      beginTime: creationContext.activity?.beginTime,
      endTime: creationContext.activity?.endTime,
    } as BaseInfoForCreationContext;

   
    return {
      baseinfo: {
        name: creationContext.activity?.name,
        description: creationContext.activity?.description,
        remark: creationContext.activity?.remark,
        activityTime: {
          start: moment(creationContext.activity?.beginTime),
          end: moment(creationContext.activity?.endTime),
          weeks: weekday
            .split(',')
            .filter(Boolean)
            .map((d: string) => +d),
          batchRange: timeRangeBatch,
        },
        auditMemberList: creationContext.activity?.auditMemberList || [],
        signupTime: {
          start: moment(creationContext.activity?.signUpStartTime),
          end: moment(creationContext.activity?.signUpEndTime),
        },
        allowCancel: creationContext.activity?.allowCancel,
        signUpShopType: creationContext.activity?.signUpShopType,
      },
      scope: {
        // 复制活动，门店池、报名规则、自动审核规则均要清空
        commodityAuditType: 1,
      },
      playdata: {
        fields: {
          ...formatViewJson2MarketPlay({ viewDto: viewJson, baseInfo }),
          couponStock: JSON.stringify({}), // 复制要清空库存限制
          drawLimit: JSON.stringify({}), // 复制要清空领券限制
          externalDeliveryChannel: undefined, // 复制要清空限定方式
          budget: undefined, // 复制活动要清空预算
        },
      },
    };
  } catch (e) {
    console.error(e);
    return {};
  }
}
