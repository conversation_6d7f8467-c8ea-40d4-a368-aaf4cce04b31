export interface BaseInfoForCreationContext {
  beginTime: string;
  createdUserId?: string;
  createdUserName?: string;
  endTime: string;
  subsidyType?: string;
  businessLineList?: {
    businessLineName?: string; // 业务线名称
    businessLineId?: number; // 业务线Id,
  }[];
}

export interface BaseInfoForMarketPlay {
  activityTime: {
    batchRange: {
      list: Array<{ start: string; end: string }>;
    };
    weeks: (string | number)[];
  };
}

export interface MarketPlayForPlayData {
  outStoreDeliveryType: string;
  externalDeliveryChannel?: {
    externalDeliveryChannelName: string;
    externalDeliveryChannelCode: number;
    externalDeliveryChannelType: 1 | 2; // 1：独享、2：非独享
  }[];
  budget?: {
    budgetId: string;
    budgetName: string;
  };
  weekday: string;
  categoryName: string;
  overlying: string;//'1'-同享 '0'-专享
  userScope: string;
  inStoreDeliveryType: string;
  creator: {
    name: string;
    id: string;
  };
  dateRange: {
    start: string;
    end: string;
  };
  vipScene?: string;
  timeRange: [
    {
      start: string;
      end: string;
    }
  ];
  couponStock: {
    totalLimit?: {
      value: number;
    };
    dayLimit?: {
      value: number;
    };
  };
  couponName: {
    value: string;
  };
  drawLimit: {
    totalLimit?: {
      value: number;
    };
    dayLimit?: {
      value: number;
    };
  };
  logoImg: '';
  couponActiveDate: {
    days?: {
      value: number;
    };
    range?: {
      start: string;
      end: string;
    };
  };
  effectiveChannel: string;
  rule: {
    condition: number;
    discount: number;
    elemeSubsidy: number;
    shopSubsidy: number;
  };
}

export interface ViewJsonForPlayData {
  activityLimitRule: {
    dayCountLimit?: number;
    totalCountLimit?: number;
    userDayCountLimit?: number;
    userTotalCountLimit?: number;
  };
  activityMarketingInfo: {
    activityExternalDeliveryChannelList?: [
      {
        externalDeliveryChannelName: string;
        externalDeliveryChannelCode: string;
        externalDeliveryChannelType: number;
      }
    ];
    availableDays?: number;
    availableStartTime?: string;
    availableEndTime?: string;
    budgetId: string;
    budgetName: string;
    couponLogo: string;
    couponName: string;
    deliveryChannel?: number; // 投放渠道，老活动有该字段
    deliveryType: number;
    inStoreDeliveryType: number;
    outStoreDeliveryType: number;
    storeCategoryForApp: string;
    overlying?: boolean;//true-同享 false-专享
    userScope: number;
    vipScene: number;
  };
  activityRule: {
    condition: number;
    conditionType: 'SATISFY_X_AMOUNT';
    discount: number;
    discountType: 'REDUCE';
    platformSubsidy: number;
    shopSubsidy: number;
  };
  activityType: number;
  periodDTOList: [
    {
      closeTime: string;
      openTime: string;
    }
  ];
  weekday: string;
}

export type TimeRangeFromMarketPlay = {
  start: string;
  end: string;
}[];
