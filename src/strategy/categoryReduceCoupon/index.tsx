import { Message, Dialog, Button, NumberPicker } from "@alifd/next";

import * as api from "../../api";
import loadingDialog from "../../components/LoadingDialog";
import { ActivityTypeEnum, ISCH, ITY, InvestmentSchemaEnum, InvestmentTypeEnum } from "../../constants";
import {
  ActivityStrategy,
  InvestmentActivityDetailDTO,
  InvestmentActivityDTO,
} from "../../models";
import { CableBase, resolveCreateActivityCommonData } from "../base";
import { matchRecipe } from "../../utils";
import {
  checkNextCommoditySignupRuleForAlpha,
  checkNextTimeForAlpha,
  checkValidateCopyActivityResult,
  gotoLegacyActivityDetailByDefault,
  isOfflineEnabledByDefault,
  resolveCommodityAuditProps,
} from "../mixins";

import { MarketPlayForPlayData, ViewJsonForPlayData } from "./constants";
import {
  formatMarketPlay2ViewJson,
  formatViewJson2MarketPlay,
  resolveCopyDataForViewJson,
} from "./utils";
import {
  checkMktRulesForDiscountBusinessFormAndConfirmIfNeed,
  checkMktRulesForMultiBusinessFormAndConfirmIfNeed,
} from './checkMktRules'

const debug = require("debug")("zs:strategy:CategoryReduceCoupon");

/**
 * 品类满减券
 */
const CategoryReduceCoupon: ActivityStrategy = {
  activityType: ActivityTypeEnum.ITEM_CATEGORY_COUPON,
  cable: {
    ...CableBase,
    workspace: "ExukovQrae6xWn2rt4NgG",
    model: "qtbcIoAkpb95UYn8FolTM",
    async checkNext(checkNextContext, creationContext, history) {
      const { step, dataStore } = checkNextContext;
      if (!dataStore._session) {
        dataStore._session = {};
      }

      if (step.name === 'baseinfo') {
        const baseinfo = dataStore?.data?.baseinfo;
        const needBusinessLineAndCategory =
          matchRecipe(creationContext, ITY.RICHANG, ISCH.RICHANG, true) &&
          baseinfo?.manageOrgList?.length > 0 
        // 重置 auditUpgradeForMultiMktRule，避免重新回到第一步重新选择业务线和业态的情况
        dataStore._session.auditUpgradeForMultiMktRule = undefined;
        if (needBusinessLineAndCategory) {
          // 校验是否跨业态，并且返回弹窗点击结果
          const { conflicted, userConfirm } = await checkMktRulesForMultiBusinessFormAndConfirmIfNeed(creationContext, baseinfo);
          if (!conflicted) {
            // 没有错误信息，不需要拦截进入下一步
            return;
          }
          if (userConfirm) {
            // 有错误，并且用户二次确认了，确认需提交审批
            dataStore._session.auditUpgradeForMultiMktRule = true;
            // 不需要拦截进入下一步
            return;
          } else {
            // 有错误，单用户未二次确认，页面停留在当前步骤
            return { silent: true }
          }
        }
      }
      if(step.name === "baseinfo") {
        const timeError = await checkNextTimeForAlpha(checkNextContext, creationContext);
        if(timeError) {
          return timeError;
        }
      }
      if(step.name === "scope") {
        const commoditySignupRuleError = await checkNextCommoditySignupRuleForAlpha(checkNextContext, creationContext);
        if(commoditySignupRuleError) {
          return commoditySignupRuleError;
        }
      }

      if (step.name === "playdata") {
        const { scope, baseinfo, playdata } = dataStore.data;
        const commonData: any = resolveCreateActivityCommonData(
          ActivityTypeEnum.ITEM_CATEGORY_COUPON,
          checkNextContext,
          creationContext
        );
        const commodityAuditProps: any = resolveCommodityAuditProps(scope);

        delete commonData.dataMap;
        const formData: MarketPlayForPlayData = playdata.snapshot.data;

        // 玩法编排语法 转为 viewJson 格式
        const viewJson = formatMarketPlay2ViewJson({ formData, baseinfo });

        const payload: any = {
          ...commonData,

          // 授权审核人
          auditMemberList:
            baseinfo.auditMemberList?.length > 0
              ? baseinfo.auditMemberList
              : null,
          // 自动审核规则
          ...commodityAuditProps,
          // 降套补 商品清退规则
          commodityRemoveCondition: { removeCondition: scope.removeCondition },
          // 报名规则
          reviewRuleId: scope.rule?.ruleId,
          reviewRuleName: scope.rule?.name,

          // 玩法设置
          viewJson: JSON.stringify(viewJson),
        };

        const { hide, updateMessage } = loadingDialog.show({
          message: creationContext.copy ? "活动信息校验中..." : "活动创建中...",
        });

        try {
          // 只支持单活动、日常模板、日常招商
          const needBusinessLineAndCategory =
            matchRecipe(creationContext, ITY.RICHANG, ISCH.RICHANG, true) &&
            (baseinfo?.manageOrgList || []).length > 0;

          let auditUpgradeForCheckMktRuleUnPass: undefined | boolean;
          let auditUpgradeForMultiMktRule: undefined | boolean;
          // 判断是否支持业务线与业态
          if (needBusinessLineAndCategory) {
            if (dataStore?._session?.auditUpgradeForMultiMktRule) {
              // 判断第一步是否跨业态，如果第一步跨业态，只要第一步点了二次确认，就将设置后的参数传给后端，后端去做后续拦截校验
              // 风险：第二步的时候业态改为不冲突，第三步规则建议组件会取到规则，并根据规则校验，但是不符合规则这里不去check，不会弹突破规则的弹窗（因为第一步已经弹过一次，需要重新刷新或者返回第一步重选业态）；
              auditUpgradeForMultiMktRule = true;
            } else {
              const { userConfirm, errType } = await checkMktRulesForDiscountBusinessFormAndConfirmIfNeed(creationContext, viewJson, baseinfo);
              if (!errType) {
                // 如果接口检查规则没有错误，啥也不做，继续走下边创建流程。
              } else {
                // 走到这个分支说明第一步没有跨业态，无需关注 errType === 'multi_rule'分支，只做自己的校验
                // 判断错误类型，如果错误类型为'inconformity_rule'，则说明规则错误，规则需要升级审批，设置 auditUpgradeForCheckMktRuleUnPass 为 true
                if (errType === 'multi_rule') {
                  // 说明第一步业态无冲突，创建流程中规则被更改，此时点断不做任何处理，继续调用创建接口，创建报错，展示后端返回的报错信息。
                } else if (errType === 'inconformity_rule') {
                  if (userConfirm) {
                    // 玩法不满足行业规则，用户二次确认了，继续向下执行创建流程
                    auditUpgradeForCheckMktRuleUnPass = true;
                  } else {
                    // 玩法不满足行业规则，用户未二次确认，页面停留在当前步骤，中断创建流程
                    return { silent: true };
                  }
                } else {
                  // 理论不会走到这个分支，每个枚举必须对应自己的分支
                }
              }
            }
          }

          if (creationContext.copy) {
            const res = await api.subActivity.validateCopyActivityData({
              copyActivityId: creationContext.activity?.activityId,
              auditUpgradeForMultiMktRule, // 是否跨业态
              auditUpgradeForCheckMktRuleUnPass, // 是否跨规则
              ...payload,
            });
            const copyError = checkValidateCopyActivityResult(res);
            if (copyError) {
              return copyError;
            }
            updateMessage("活动创建中...");
          }
          debug("create subActivity", payload);
          const res = await api.subActivity.create({
            copyActivityId: creationContext.activity?.activityId,
            auditUpgradeForMultiMktRule, // 是否跨业态
            auditUpgradeForCheckMktRuleUnPass, // 是否跨规则
            ...payload,
          });
          if (res.success) {
            Message.success("创建成功");
            if (creationContext.activitySceneId) {
                  history.push(
                    "/sub-activity/" +
                      res.data +
                      "?activitySceneId=" +
                      creationContext.activitySceneId +
                      "&activityPlanId=" +
                      creationContext.activityPlanId
                  );
                } else {
                  history.push("/sub-activity/" + res.data);
                }
          } else {
            return { message: res.errorMessage };
          }
        } catch (e: any) {
          return { message: e?.message || "系统异常" };
        } finally {
          hide();
        }
      }
    },
  },
  async handleModifyStock(record, options) {
    try {
      // @ts-ignore
      let _stock: any = this._resolveStockLimit(record);
      let value: any;
      let dayLimit: any;
      let content: any;

      const OldDialog = ({ stock }: { stock: number }) => {
        return (
          <div>
            <p>总库存: {stock}</p>
            <p>
              修改总库存 &nbsp; &nbsp;
              <NumberPicker
                placeholder="请输入库存"
                style={{ width: 200 }}
                min={stock}
                onChange={(v: any) => {
                  value = v;
                }}
                max={99999999}
              />
            </p>
            <p style={{ color: "red" }}>注意！只能增加不能减少</p>
          </div>
        );
      };

      const NewDialog = ({
        stock,
      }: {
        stock: { totalCountLimit?: number; dayCountLimit?: number };
      }) => {
        return (
          <div>
            {stock.totalCountLimit && <p>总库存: {stock.totalCountLimit}</p>}
            {stock.totalCountLimit && (
              <p>
                修改总库存 &nbsp; &nbsp;
                <NumberPicker
                  placeholder="请输入库存"
                  style={{ width: 200 }}
                  min={stock.totalCountLimit}
                  onChange={(v: any) => {
                    value = v;
                  }}
                  max={99999999}
                />
              </p>
            )}

            {stock.dayCountLimit && <p>每日库存: {stock.dayCountLimit}</p>}
            {stock.dayCountLimit && (
              <p>
                修改每日库存 &nbsp; &nbsp;
                <NumberPicker
                  placeholder="请输入库存"
                  style={{ width: 200 }}
                  min={stock.dayCountLimit}
                  onChange={(v: any) => {
                    dayLimit = v;
                  }}
                  max={99999999}
                />
              </p>
            )}

            <p style={{ color: "red" }}>注意！只能增加不能减少</p>
          </div>
        );
      };

      if (record.isNewActivity) {
        content = <NewDialog stock={_stock} />;
      } else {
        content = <OldDialog stock={_stock} />;
      }

      const dialog = Dialog.show({
        title: "修改库存",
        footer: [
          <Button
            style={{ marginRight: 10 }}
            type="primary"
            onClick={async () => {
              try {
                let res: any;
                if (record.isNewActivity) {
                  if (_stock.totalCountLimit && !value) {
                    Message.error("请填写总库存");
                    return;
                  }
                  if (_stock.dayCountLimit && !dayLimit) {
                    Message.error("请填写每日库存");
                    return;
                  }
                  if (
                    _stock.dayCountLimit &&
                    _stock.totalCountLimit &&
                    dayLimit > value
                  ) {
                    Message.error("每日库存数量应该小于等于总库存数量");
                    return;
                  }
                  let viewJson: ViewJsonForPlayData = {} as ViewJsonForPlayData;
                  try {
                    viewJson = record.viewJson && JSON.parse(record.viewJson);
                  } catch (e) {
                    console.error(e);
                  }

                  // 新版使用viewJson表单的没有 deliveryChannel字段。 有 deliveryChannel字段说明是老活动走老链路
                  if (viewJson?.activityMarketingInfo?.deliveryChannel) {
                    const r = await api.subActivity.getDetail(
                      record.activityId as number
                    );
                    const detail: InvestmentActivityDTO = r.data;
                    const marketPlay =
                      detail.investmentActivityDetailDTO.marketPlay;
                    const couponStock = JSON.parse(
                      marketPlay?.instance.fields.couponStock
                    );
                    if (marketPlay?.instance?.fields?.couponStock) {
                      marketPlay.instance.fields.couponStock = JSON.stringify({
                        ...couponStock,
                        totalLimit: value ? { value } : undefined,
                        dayLimit: dayLimit ? { value: dayLimit } : undefined,
                      });
                    }
                    res = await api.subActivity.updateStock(
                      record.activityId as number,
                      {
                        instanceId: marketPlay?.instance.id,
                        resource: {
                          componentModelUuid: "5M9DwTHtrO9aUlLnhUVCS7",
                          data: marketPlay?.instance.fields,
                          type: "insert",
                        },
                      }
                    );
                  } else {
                    res = await api.subActivity.updateStockForViewJson(
                      record.activityId,
                      value,
                      dayLimit
                    );
                  }
                } else {
                  if (!value) {
                    Message.error("请填写库存");
                    return;
                  }
                  const marketPlay = record.marketPlay;
                  if (marketPlay?.instance?.fields?.couponStock) {
                    const couponStock = JSON.parse(
                      marketPlay?.instance.fields.couponStock
                    );
                    marketPlay.instance.fields.couponStock = JSON.stringify({
                      ...couponStock,
                      totalLimit: { value },
                    });
                  }
                  res = await api.legacy.updateStock({
                    instanceId: marketPlay?.instance.id,
                    customData: {
                      activityId: record.activityId as number,
                    },
                    marketData: {
                      componentModelUuid: "5M9DwTHtrO9aUlLnhUVCS7",
                      type: "insert",
                      data: marketPlay?.instance.fields,
                    },
                  });
                }
                if (res.success) {
                  dialog.hide();
                  options.onSuccess();
                } else {
                  options.onFail(res.errorMessage as string);
                }
              } catch (e: any) {
                options.onFail(e.message as string);
              }
            }}
          >
            确认
          </Button>,
          <Button onClick={() => dialog.hide()}>取消</Button>,
        ],
        content: content,
      });
    } catch (e) {
      console.error(e);
    }
    // await updateTotalStockLimitByDefault.call(this, record, '5M9DwTHtrO9aUlLnhUVCS7', options)
  },
  isSupportModifyStock(record): boolean {
    try {
      // @ts-ignore
      const stock = this._resolveStockLimit(record);
      if (record.isNewActivity) {
        return !!(stock.totalCountLimit || stock.dayCountLimit);
      } else {
        return isNaN(+stock) ? false : !!+stock;
      }
    } catch (e) {
      console.error(e);
      return false;
    }
  },
  hasAgentSubsidy() {
    return false;
  },
  isSupportAgent: false,
  isItemAuditStatsVisible: true,
  isOfflineEnabled: isOfflineEnabledByDefault,
  isCopyEnabled({ investmentType, viewJson }) {
    if (investmentType !== InvestmentTypeEnum.RICHANG) {
      return false;
    }
    try {
      if (!viewJson) {
        return false;
      }
      const playdata: ViewJsonForPlayData = JSON.parse(viewJson);
      if (
        playdata.activityMarketingInfo.vipScene &&
        [1, 2].includes(playdata.activityMarketingInfo.vipScene)
      ) {
        // 会员活动不允许复制
        return false;
      }
      return true;
    } catch (e) {
      console.error(e);
      return false;
    }
  },
  async resolveCopyData(creationContext) {
    return resolveCopyDataForViewJson(creationContext);
  },
  async resolvePartialCreateData(createContext) {
    if (
      createContext.investmentType === InvestmentTypeEnum.RICHANG &&
      createContext.investmentSchema === InvestmentSchemaEnum.CARD_SALES
    ) {
      return {
        playdata: {
          fields: {
            inStoreDeliveryType: '2', // 店内不支持投放
            outStoreDeliveryType: '1' // 店外支持投放
          }
        }
      }
    } else {
      // 非站外卡券售卖场景，无需通过这个能力设置初始值
      return undefined
    }
  },
  isDingtalkLinkEnabled: () => true,
  hasAuditMemberList: () => true,
  // @ts-ignore
  _resolveStockLimit(record: any): {
    totalCountLimit?: number;
    dayCountLimit?: number;
  } {
    // 获取库存对象 新活动，返回库存对象，老活动，返回number
    if (record.isNewActivity) {
      const _playdata: ViewJsonForPlayData = JSON.parse(
        record.viewJson as string
      );
      return {
        totalCountLimit: _playdata.activityLimitRule.totalCountLimit,
        dayCountLimit: _playdata.activityLimitRule.dayCountLimit,
      };
    } else {
      const marketPlay = record.marketPlay;
      const stock = JSON.parse(marketPlay?.instance?.fields?.couponStock);
      return stock.totalLimit?.value;
    }
  },
  isDetailInviteStatsOnMainActivityTableVisible: true,

  // viewJson 转组件语法
  marketPlayMapper: async function (viewJson, baseInfo) {
    try {
      const viewDto: ViewJsonForPlayData = JSON.parse(viewJson) || {};

      const { activityMarketingInfo } = viewDto;
      if (activityMarketingInfo?.deliveryChannel) {
        return;
      }

      const fields = formatViewJson2MarketPlay({ viewDto, baseInfo });
      let model = {};

      // launchChannel 投放渠道
      model = await api.subActivity.getComponentDataModel(
        "ExukovQrae6xWn2rt4NgG",
        "4PpTmTM4hnh9TcW31xoSZz"
      );
      const marketPlay = {
        instance: { fields },
        model: model,
      };
      return marketPlay;
    } catch (error) {
      return null;
    }
  },

  renderPlayDataInfoForTableView: function (): JSX.Element {
    return <></>;
  },
  renderActivityRuleForTable(viewJson: any) {
    try {
      const playdata: ViewJsonForPlayData = JSON.parse(viewJson);
      const rule = playdata.activityRule;
      return `满 ${rule.condition / 100} 元，优惠 ${rule.discount / 100
        } 元，平台补贴 ${rule.platformSubsidy / 100} 元，商户补贴 ${rule.shopSubsidy / 100
        } 元`;
    } catch (e) {
      console.error(e);
      return "";
    }
  },
  gotoLegacyDetail: function (
    act: InvestmentActivityDetailDTO,
    scene: "lookup" | "examine"
  ): void {
    gotoLegacyActivityDetailByDefault(act, scene);
  },
  renderActivityRuleForTableLegacy: function (marketPlay: {
    instance: { fields: any };
  }) {
    const fields = marketPlay?.instance?.fields;
    try {
      const rule = JSON.parse(fields.rule);
      const ret = `满 ${rule.condition / 100} 元，优惠 ${rule.discount / 100
        } 元，平台补贴 ${rule.elemeSubsidy / 100} 元,商户补贴 ${rule.shopSubsidy / 100
        } 元`;
      return ret;
    } catch (e) {
      return "";
    }
  },
};

export default CategoryReduceCoupon;
