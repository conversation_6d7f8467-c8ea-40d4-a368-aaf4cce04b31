// @ts-ignore
import { handleAPI, clearAPI } from "@ali/alsc-gateway-web-client";
import moment from "moment";
import {
  formatMarketPlay2ViewJson,
  formatViewJson2MarketPlay,
  resolveCopyDataForViewJson,
} from "./utils";
import {
  ViewJsonForPlayData,
} from './constants';


describe("categoryReduceCoupon", () => {
  describe("helper", () => {
    describe("formatMarketPlay2ViewJson", () => {
      test("日常模版一有预算+用户类型：全部用户", () => {
        const viewJson = formatMarketPlay2ViewJson({
          baseinfo: {
            activityTime: {
              weeks: [0, 1, 2, 3, 4, 5, 6],
              batchRange: {
                list: [{ start: "00:00", end: "23:59" }],
              },
            },
          },
          formData: {
            timeRange: [
              {
                start: "",
                end: "",
              },
            ],
            outStoreDeliveryType: "2",
            externalDeliveryChannel: [
              {
                externalDeliveryChannelType: 1,
                externalDeliveryChannelCode: 2,
                externalDeliveryChannelName: "全部渠道",
              },
            ],
            budget: {
              budgetId: "400000000004734089",
              budgetName: "招商红包预算单品叠加",
            },
            weekday: "0,1,2,3,4,5,6",
            categoryName: "11",
            userScope: "0",
            inStoreDeliveryType: "1",
            creator: { id: "329684", name: "郭宇帆" },
            dateRange: {
              start: "2023-09-13 00:00:00",
              end: "2023-09-13 23:59:59",
            },
            couponStock: { totalLimit: { value: 1 } },
            couponName: { value: "红包名称" },
            drawLimit: { totalLimit: { value: 1 } },
            logoImg: "",
            couponActiveDate: { days: { value: 3 } },
            effectiveChannel: "1",
            rule: {
              condition: 2000,
              discount: 100,
              elemeSubsidy: 0,
              shopSubsidy: 100,
            },
            overlying: "1"
          },
        });
        expect(viewJson).toStrictEqual({
          activityLimitRule: {
            dayCountLimit: undefined,
            totalCountLimit: 1,
            userDayCountLimit: undefined,
            userTotalCountLimit: 1,
          },
          activityMarketingInfo: {
            activityExternalDeliveryChannelList: [
              {
                externalDeliveryChannelCode: 2,
                externalDeliveryChannelName: "全部渠道",
                externalDeliveryChannelType: 1,
              },
            ],
            availableDays: 3,
            availableEndTime: undefined,
            availableStartTime: undefined,
            budgetId: "400000000004734089",
            budgetName: "招商红包预算单品叠加",
            couponLogo: "",
            couponName: "红包名称",
            deliveryType: "1",
            inStoreDeliveryType: "1",
            outStoreDeliveryType: "2",
            storeCategoryForApp: "11",
            userScope: "0",
            vipScene: undefined,
            overlying: true
          },
          activityRule: {
            condition: 2000,
            conditionType: "SATISFY_X_AMOUNT",
            discount: 100,
            discountType: "REDUCE",
            platformSubsidy: 0,
            shopSubsidy: 100,
          },
          activityType: 1000018,
          periodDTOList: [
            {
              closeTime: "23:59:59",
              openTime: "00:00:00",
            },
          ],
          weekday: "0,1,2,3,4,5,6",
        });
      });
      test("站外卡券售卖-无预算+用户类型：全部用户", () => {
        const viewJson = formatMarketPlay2ViewJson({
          baseinfo: {
            activityTime: {
              weeks: [0, 1, 2, 3, 4, 5, 6],
              batchRange: {
                list: [{ start: "00:00", end: "23:59" }],
              },
            },
          },
          formData: {
            outStoreDeliveryType: "1",
            // @ts-ignore
            externalDeliveryChannel:
              '[{"externalDeliveryChannelType":2,"externalDeliveryChannelCode":84,"externalDeliveryChannelName":"站外卡券售卖"}]',
            // @ts-ignore
            budget: "{}",
            weekday: "0,1,2,3,4,5,6",
            categoryName: "22",
            userScope: "0",
            inStoreDeliveryType: "2",
            name: "yc-站外卡券售卖-品类券-回归",
            // @ts-ignore
            creator: '{"id":"213137","prefix":"yc213137","name":"杨辰"}',
            // @ts-ignore
            dateRange:
              '{"start":"2023-11-29 00:00:00","end":"2023-12-31 23:59:59"}',
            // @ts-ignore
            couponStock: '{"totalLimit":{"value":1}}',
            // @ts-ignore
            couponName: '{"value":"22"}',
            // @ts-ignore
            drawLimit: '{"totalLimit":{"value":1}}',
            logoImg: "",
            // @ts-ignore
            couponActiveDate: '{"days":{"value":1}}',
            _workspace_uuid_: "ExukovQrae6xWn2rt4NgG",
            effectiveChannel: "1",
            // @ts-ignore
            rule: '{"condition":300,"discount":100,"elemeSubsidy":0,"shopSubsidy":100}',
            overlying: '0'
          },
        });
        expect(viewJson).toStrictEqual({
          activityType: 1000018,
          activityLimitRule: {
            dayCountLimit: undefined,
            totalCountLimit: 1,
            userDayCountLimit: undefined,
            userTotalCountLimit: 1,
          },
          activityMarketingInfo: {
            availableDays: 1,
            availableEndTime: undefined,
            availableStartTime: undefined,
            couponLogo: "",
            couponName: "22",
            deliveryType: "1",
            activityExternalDeliveryChannelList: [
              {
                externalDeliveryChannelCode: 84,
                externalDeliveryChannelName: "站外卡券售卖",
                externalDeliveryChannelType: 2,
              },
            ],
            budgetId: undefined,
            budgetName: undefined,
            inStoreDeliveryType: "2",
            outStoreDeliveryType: "1",
            storeCategoryForApp: "22",
            userScope: "0",
            vipScene: undefined,
            overlying: false
          },
          activityRule: {
            condition: 300,
            conditionType: "SATISFY_X_AMOUNT",
            discount: 100,
            discountType: "REDUCE",
            platformSubsidy: 0,
            shopSubsidy: 100,
          },
          periodDTOList: [
            {
              closeTime: "23:59:59",
              openTime: "00:00:00",
            },
          ],
          weekday: "0,1,2,3,4,5,6",
        });
      });
      test("日常模版一无预算+用户类型：商家会员", () => {
        const viewJson = formatMarketPlay2ViewJson({
          baseinfo: {
            activityTime: {
              weeks: [0, 1, 2, 3, 4, 5, 6],
              batchRange: {
                list: [{ start: "00:00", end: "23:59" }],
              },
            },
          },
          formData: {
            timeRange: [
              {
                start: "",
                end: "",
              },
            ],
            outStoreDeliveryType: "2",
            budget: undefined,
            vipScene: "1",
            weekday: "0,1,2,3,4,5,6",
            categoryName: "11",
            userScope: "0",
            inStoreDeliveryType: "1",
            creator: { id: "329684", name: "郭宇帆" },
            dateRange: {
              start: "2023-09-13 00:00:00",
              end: "2023-09-13 23:59:59",
            },
            couponStock: { totalLimit: { value: 10 }, dayLimit: { value: 6 } },
            couponName: { value: "红包名称" },
            drawLimit: { totalLimit: { value: 5 }, dayLimit: { value: 4 } },
            logoImg: "",
            couponActiveDate: {
              range: {
                start: "2023-09-14 00:00:00",
                end: "2023-09-29 23:59:59",
              },
            },
            effectiveChannel: "1",
            rule: {
              condition: 2000,
              discount: 100,
              elemeSubsidy: 0,
              shopSubsidy: 100,
            },
            overlying: "1"
          },
        });
        expect(viewJson).toStrictEqual({
          activityLimitRule: {
            dayCountLimit: 6,
            totalCountLimit: 10,
            userDayCountLimit: 4,
            userTotalCountLimit: 5,
          },
          activityMarketingInfo: {
            activityExternalDeliveryChannelList: undefined,
            availableDays: undefined,
            availableEndTime: "2023-09-29 23:59:59",
            availableStartTime: "2023-09-14 00:00:00",
            budgetId: undefined,
            budgetName: undefined,
            couponLogo: "",
            couponName: "红包名称",
            deliveryType: "1",
            inStoreDeliveryType: "1",
            outStoreDeliveryType: "2",
            storeCategoryForApp: "11",
            userScope: "0",
            vipScene: "1",
            overlying: true,
          },
          activityRule: {
            condition: 2000,
            conditionType: "SATISFY_X_AMOUNT",
            discount: 100,
            discountType: "REDUCE",
            platformSubsidy: 0,
            shopSubsidy: 100,
          },
          activityType: 1000018,
          periodDTOList: [
            {
              closeTime: "23:59:59",
              openTime: "00:00:00",
            },
          ],
          weekday: "0,1,2,3,4,5,6",
        });
      });
    });

    describe("formatViewJson2MarketPlay", () => {
      test("日常模版一无预算+用户类型：商家会员", () => {
        const viewJson: ViewJsonForPlayData = {
          activityLimitRule: {
            dayCountLimit: 6,
            totalCountLimit: 10,
            userDayCountLimit: 4,
            userTotalCountLimit: 5,
          },
          activityMarketingInfo: {
            activityExternalDeliveryChannelList: undefined,
            availableDays: undefined,
            availableEndTime: "2023-09-29 23:59:59",
            availableStartTime: "2023-09-14 00:00:00",
            budgetId: '',
            budgetName: '',
            couponLogo: "",
            couponName: "红包名称",
            deliveryType: 1,
            inStoreDeliveryType: 1,
            outStoreDeliveryType: 2,
            storeCategoryForApp: "11",
            userScope: 0,
            vipScene: 1,
            overlying: true,
          },
          activityRule: {
            condition: 2000,
            conditionType: "SATISFY_X_AMOUNT",
            discount: 100,
            discountType: "REDUCE",
            platformSubsidy: 0,
            shopSubsidy: 100,
          },
          activityType: 1000018,
          periodDTOList: [
            {
              closeTime: "23:59:59",
              openTime: "00:00:00",
            },
          ],
          weekday: "0,1,2,3,4,5,6",
        };
        const baseInfo = {
          createdUserId: "329684",
          createdUserName: "郭宇帆",
          beginTime: "2024-07-02T16:00:00.000Z",
          endTime: "2024-07-06T15:59:59.000Z"
        };
        // @ts-ignore
        const formData = formatViewJson2MarketPlay({
          viewDto: viewJson,
          baseInfo,
        });
        expect(formData).toStrictEqual({
          budget: undefined,
          categoryName: "11",
          overlying: '1',
          couponActiveDate: '{"range":{"start":"2023-09-14 00:00:00","end":"2023-09-29 23:59:59"}}',
          couponName: '{"value":"红包名称"}',
          couponStock: '{"dayLimit":{"value":6},"totalLimit":{"value":10}}',
          creator: '{"name":"郭宇帆","id":"329684"}',
          dateRange: '{"start":"2024-07-03 00:00:00","end":"2024-07-06 23:59:59"}',
          drawLimit: '{"dayLimit":{"value":4},"totalLimit":{"value":5}}',
          effectiveChannel: "1",
          externalDeliveryChannel: undefined,
          inStoreDeliveryType: "1",
          logoImg: undefined,
          outStoreDeliveryType: "2",
          rule: '{"condition":2000,"discount":100,"elemeSubsidy":0,"shopSubsidy":100}',
          userScope: "0",
          vipScene: "1",
        });
      });
      test("日常模版一有预算+用户类型：全部用户", () => {
        const viewDto = {
          activityLimitRule: {
            dayCountLimit: undefined,
            totalCountLimit: 1,
            userDayCountLimit: undefined,
            userTotalCountLimit: 1,
          },
          activityMarketingInfo: {
            activityExternalDeliveryChannelList: [
              {
                externalDeliveryChannelCode: 2,
                externalDeliveryChannelName: "全部渠道",
                externalDeliveryChannelType: 1,
              },
            ],
            availableDays: 3,
            availableEndTime: undefined,
            availableStartTime: undefined,
            budgetId: "400000000004734089",
            budgetName: "招商红包预算单品叠加",
            couponLogo: "",
            couponName: "红包名称",
            deliveryType: "1",
            inStoreDeliveryType: "1",
            outStoreDeliveryType: "2",
            overlying: true,
            storeCategoryForApp: "11",
            userScope: "0",
            vipScene: undefined,
          },
          activityRule: {
            condition: 2000,
            conditionType: "SATISFY_X_AMOUNT",
            discount: 100,
            discountType: "REDUCE",
            platformSubsidy: 0,
            shopSubsidy: 100,
          },
          activityType: 1000018,
          periodDTOList: [
            {
              closeTime: "23:59:59",
              openTime: "00:00:00",
            },
          ],
          weekday: "0,1,2,3,4,5,6",
        };
        const baseInfo = {
          createdUserId: "329684",
          createdUserName: "郭宇帆",
        };
        // @ts-ignore
        const formData = formatViewJson2MarketPlay({ viewDto, baseInfo });
        expect(formData).toStrictEqual({
          outStoreDeliveryType: "2",
          externalDeliveryChannel:
            '[{"externalDeliveryChannelCode":2,"externalDeliveryChannelName":"全部渠道","externalDeliveryChannelType":1}]',
          budget:
            '{"budgetId":"400000000004734089","budgetName":"招商红包预算单品叠加"}',
          categoryName: "11",
          userScope: "0",
          inStoreDeliveryType: "1",
          creator: '{"name":"郭宇帆","id":"329684"}',
          dateRange: undefined,
          couponStock: '{"totalLimit":{"value":1}}',
          couponName: '{"value":"红包名称"}',
          drawLimit: '{"totalLimit":{"value":1}}',
          logoImg: undefined,
          couponActiveDate: '{"days":{"value":3}}',
          effectiveChannel: "1",
          overlying: '1',
          vipScene: undefined,
          rule: '{"condition":2000,"discount":100,"elemeSubsidy":0,"shopSubsidy":100}',
        });
      });
    });

    // 新版本复制
    describe('resolveCopyDataForViewJson', () => {
      beforeEach(() => {
        handleAPI(
          "ele-newretail-investment.NewretailInvestmentActivityShopPoolYundingService.getShopPoolList",
          async () => ({
            data: [{
              "noChooseReason": null,
              "shopCount": 10,
              "poolId": 364561,
              "choose": null,
              "class": "me.ele.newretail.investment.service.dto.InvestmentActivityShopPoolDTO",
              "poolName": "zzg测试店铺112-免息"
            }]
          })
        );
      });

      afterEach(() => {
        clearAPI();
      });

      test("日常招商-日常招商-新数据复制功能", async () => {
        const creationContext = {
          "copy": true,
          "createActiivtyStartTime": 1720079968171,
          "createActivitySessionId": "653a5bd3-1966-4745-8736-c217f79e3570",
          "isSingleActivity": true,
          "activityType": 1000018,
          "investmentType": 2,
          "investmentSchema": 0,
          "fineGrainedActivityTimeDisabled": true,
          "subsidyType": "p",
          "activity": {
            "activityId": 6343024004,
            "createdUserId": "222146",
            "createdUserName": "赵志刚",
            "name": "商品品类券",
            "remark": "",
            "description": "商品品类券活动描述",
            "beginTime": "2024-07-03T16:00:00.000Z",
            "endTime": "2024-07-07T15:59:59.000Z",
            "signUpStartTime": "2024-07-03T16:00:00.000Z",
            "signUpEndTime": "2024-07-07T15:59:59.000Z",
            "allowCancel": 1 as any,
            "signUpShopType": 0,
            "shopPoolId": 420513,
            "shopPoolName": "zzg测试店铺112-免息",
            "reviewRuleId": 8940,
            "reviewRuleName": "仅上架商品可以报名",
            "marketPlay": null,
            "signUpMinCycleTime": null,
            "signUpMaxCycleTime": null,
            "stockType": 0,
            "businessLineList": [{
              "businessLineName": undefined,
              "businessLineId": undefined
            }],
            "businessFormList": undefined,
            "auditMemberList": [],
            "viewJson": "{\"activityLimitRule\":{\"dayCountLimit\":99,\"totalCountLimit\":999,\"userDayCountLimit\":5,\"userTotalCountLimit\":20},\"activityMarketingInfo\":{\"activityExternalDeliveryChannelList\":[{\"externalDeliveryChannelCode\":1,\"externalDeliveryChannelName\":\"全部渠道\",\"externalDeliveryChannelType\":2}],\"availableDays\":2,\"couponLogo\":\"https://img.alicdn.com/imgextra/i2/175656/O1CN011reV1VA2V6rBhOz_!!175656-0-eleretailmkt.jpg\",\"couponName\":\"品类红包\",\"deliveryType\":3,\"inStoreDeliveryType\":1,\"outStoreDeliveryType\":1,\"overlying\":true,\"storeCategoryForApp\":\"品类红包\",\"userScope\":0},\"activityRule\":{\"condition\":1300,\"discount\":300,\"platformSubsidy\":0,\"shopSubsidy\":300},\"activityType\":1000018,\"attributes\":{\"version\":\"3.0\"},\"periodDTOList\":[{\"closeTime\":\"02:02:59\",\"openTime\":\"01:01:00\"},{\"closeTime\":\"04:04:59\",\"openTime\":\"03:03:00\"},{\"closeTime\":\"23:58:59\",\"openTime\":\"05:05:00\"}],\"weekday\":\"0,1,2,3,4,5,6\"}"
          },
          "customAttributes": {}
        }
        const formData = await resolveCopyDataForViewJson(creationContext)
        expect(formData).toStrictEqual({
          "baseinfo": {
            "name": "商品品类券",
            "description": "商品品类券活动描述",
            "remark": '',
            "activityTime": {
              "start": moment("2024-07-03T16:00:00.000Z"),
              "end": moment("2024-07-07T15:59:59.000Z"),
              "weeks": [
                0,
                1,
                2,
                3,
                4,
                5,
                6
              ],
              "batchRange": {
                "type": "custom",
                "list": [
                  {
                    "key": expect.any(String),
                    "start": "01:01",
                    "end": "02:02"
                  },
                  {
                    "key": expect.any(String),
                    "start": "03:03",
                    "end": "04:04"
                  },
                  {
                    "key": expect.any(String),
                    "start": "05:05",
                    "end": "23:58"
                  }
                ]
              }
            },
            "auditMemberList": [],
            "signupTime": {
              "start": moment("2024-07-03T16:00:00.000Z"),
              "end": moment("2024-07-07T15:59:59.000Z")
            },
            "allowCancel": 1,
            "signUpShopType": 0
          },
          "scope": {
            "commodityAuditType": 1
          },
          "playdata": {
            "fields": {
              "categoryName": "品类红包",
              "overlying": "1",
              "couponActiveDate": "{\"days\":{\"value\":2}}",
              "couponName": "{\"value\":\"品类红包\"}",
              "couponStock": "{}",
              "dateRange": "{\"start\":\"2024-07-04 00:00:00\",\"end\":\"2024-07-07 23:59:59\"}",
              "drawLimit": "{}",
              "effectiveChannel": "3",
              "inStoreDeliveryType": "1",
              "logoImg": "https://img.alicdn.com/imgextra/i2/175656/O1CN011reV1VA2V6rBhOz_!!175656-0-eleretailmkt.jpg",
              "outStoreDeliveryType": "1",
              "rule": "{\"condition\":1300,\"discount\":300,\"elemeSubsidy\":0,\"shopSubsidy\":300}",
              "budget": undefined,
              "creator": undefined,
              "externalDeliveryChannel": undefined,
              "userScope": "0",
              "vipScene": undefined,
            }
          }
        })
      })
    })

  });
});
