import BigNumber from 'bignumber.js';
import * as api from '../../api';
import { maskMS } from '../../common';
import { ActivityTypeEnum } from '../../constants';
import { CreationContext, ManageOrgSelectDateTs } from '../../models';
import { confirmMktRuleError  } from '../utils';
interface BaseInfoTS {
  manageOrgList: ManageOrgSelectDateTs[];
  activityTime: any;
}

// 获取需要校验的规则的基础信息
function getMktRuleBaseCondition(
  context: CreationContext,
  baseinfo: BaseInfoTS
) {
  return {
    activityType: ActivityTypeEnum.ITEM_CATEGORY_COUPON,
    investmentType: context?.investmentType,
    investmentSchema: context?.investmentSchema,
    investmentActivityType: context?.isSingleActivity ? 0 : 1, // 单活动0
    manageOrgList: baseinfo.manageOrgList,
    beginTime: baseinfo.activityTime.start.toDate().getTime(),
    endTime: maskMS(baseinfo.activityTime.end.toDate().getTime()), // 活动结束时间
  };
}


// 查询填写值是否不符合中控推荐规则
export async function checkMktRulesForDiscountBusinessFormAndConfirmIfNeed(
  creationContext: CreationContext,
  viewJson: any,
  baseinfo: BaseInfoTS
) {
  const params = {
    // 校验条件
    condition: {
      ...getMktRuleBaseCondition(creationContext, baseinfo),
      discountRange: [
        new BigNumber(viewJson?.activityRule?.discount).dividedBy(100).toNumber(),
        new BigNumber(viewJson?.activityRule?.discount).dividedBy(100).toNumber(),
      ], // 优惠范围   --元
      conditionRange: [
        new BigNumber(viewJson?.activityRule?.condition).dividedBy(100).toNumber(),
        new BigNumber(viewJson?.activityRule?.condition).dividedBy(100).toNumber(),
      ], // 门槛范围  --元
      maxPlatformSubsidy:
      new BigNumber(viewJson?.activityRule?.platformSubsidy).dividedBy(100).toNumber(), // 平台最高补贴
      weekDays: baseinfo?.activityTime?.weeks?.join(','), // 星期时段——用来计算单用户平台补贴金额
    },
    // 校验值
    value: {
      maxPlatformSubsidy:
      new BigNumber(viewJson?.activityRule?.platformSubsidy).dividedBy(100).toNumber(), // 平台最高补贴
      userDayCountLimitRange: viewJson?.activityLimitRule
        ?.userDayCountLimit
        ? [
            viewJson?.activityLimitRule?.userDayCountLimit,
            viewJson?.activityLimitRule?.userDayCountLimit,
          ]
        : undefined, // 用户每日限购
      userTotalCountLimitRange: viewJson?.activityLimitRule?.userTotalCountLimit ? [
        viewJson?.activityLimitRule?.userTotalCountLimit,
        viewJson?.activityLimitRule?.userTotalCountLimit,
      ] : undefined, // 用户总限购
      conditionRange: [
        new BigNumber(viewJson?.activityRule?.condition).dividedBy(100).toNumber(),
        new BigNumber(viewJson?.activityRule?.condition).dividedBy(100).toNumber(),
      ], // 门槛范围  --元
    },
  };
  const res = await api.subActivity.checkActDiscountByMarketingRule(params);
  const { result: success, marketingRuleCheckErrorType: errType } =
    res?.data || {};
  if (success || !errType) {
    return { errType: false };
  }
  if (errType === "multi_rule") {
    // 跨业态
    return { errType: "multi_rule" };
  }
  if (errType === "inconformity_rule") {
    // 玩法信息不满足行业规则，需要二次提醒
    const errorList =
      res?.data?.ruleValueCheckResultList?.filter(
        (item) => item.success === false
      ) || [];
    const userConfirm = await confirmMktRuleError({
      title: "当前填写值不符合行业规则",
      content: (
        <div>
          {errorList?.map((item) => {
            return (
              <div key={item?.mktVerifyRuleCheckType as string}>
                {item?.errorMsg}
              </div>
            );
          })}
        </div>
      ),
      confirmBtnText: "仍提交，升级审批",
    });
    return { errType, userConfirm };
  }
  // impossible
  return { errType: null };
}

// 查询是否跨业态
export async function checkMktRulesForMultiBusinessFormAndConfirmIfNeed(
  creationContext: CreationContext,
  baseinfo: BaseInfoTS
) {
  const params = {
    // 校验条件
    condition: {
      ...getMktRuleBaseCondition(creationContext, baseinfo),
    },
  };
  const res = await api.subActivity.checkActDiscountByMarketingRule(params);
  const { result: success, marketingRuleCheckErrorType: errType } =
    res?.data || {};
  if (success || errType !== "multi_rule") {
    // 和后端确认过了，只要不是这个错误，接口报错也忽略
    return { conflicted: false };
  }
  const userConfirm = await confirmMktRuleError({
    title: "当前跨主营类目不符合行业规则",
    content: res?.data.failReason as string,
    confirmBtnText: "进行下一步，提交后升级审批",
    showConfirmBtnTips: true,
  });
  return { conflicted: true, userConfirm };
}
