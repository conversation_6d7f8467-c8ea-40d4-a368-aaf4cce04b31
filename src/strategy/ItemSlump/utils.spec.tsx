import { InvestmentSchemaEnum } from "../../constants";
import { formatViewJson2MarkPlay, formatMarketPlay2ViewJson } from "./utils";

describe("itemSale", () => {
  describe("helper", () => {
    describe("formatMarketPlay2ViewJson", () => {
      // 商品直降活动，以下两种细分活动场景，玩法表单这部分的逻辑是完全一样的，所以只需要验证日常招商即可
      // - 招商类型:日常招商-招商模版:商品互斥招商
      // - 招商类型:营销IP招商-招商模版:爆款超值价
      test("招商类型:日常招商-招商模版:商品互斥招商-固定招商-无补贴和预算", () => {
        const viewJson = formatMarketPlay2ViewJson({
          baseinfo: {
            activityTime: {
              weeks: [0, 1, 2, 3, 4, 5, 6],
              batchRange: {
                type: "all",
                list: [{ start: "00:00", end: "23:59" }],
              },
            },
          },
          formData: {
            preheatTime: null,
            saleRule: '{"saleFixed":{"price":10000,"elemeSubsidy":0}}',
            budget: "{}",
            weekday: "0,1,2,3,4,5,6",
            userScope: "0",
            name: "yc-特价-日常招商",
            creator: '{"id":"213137","prefix":"yc213137","name":"杨辰"}',
            itemDiscountType: "1",
            dateRange:
              '{"start":"2023-07-17 00:00:00","end":"2023-07-31 23:59:59"}',
            timeRange: '[{"start":"00:00:00","end":"23:59:59"}]',
            userLimit: '{"dayLimit":{"value":"1"},"totalLimit":{"value":2}}',
            _workspace_uuid_: "6v4M8woywiK9aecPk9yWLh",
            deliveryType: "1",
            callbackChannelCode: "1",
          },
          investmentSchema:
            InvestmentSchemaEnum.ITEM_MUTUAL_EXCLUSION_INVESTMENT,
        });
        expect(viewJson).toStrictEqual({
          activityLimitRule: {
            userDayCountLimit: "1",
            userTotalCountLimit: 2,
          },
          activityMarketingInfo: {
            budgetId: undefined,
            budgetName: undefined,
            callbackChannelCode: '1',
            deliveryType: "1",
            userScope: "0",
          },
          activityRule: {
            discount: 10000,
            discountType: 3,
            platformSubsidyMax: 0,
          },
          activityType: 1000033,
          period: [
            {
              closeTime: "23:59:59",
              openTime: "00:00:00",
              weekday: "0,1,2,3,4,5,6",
            },
          ],
        });
      });
      test("招商类型:日常招商-招商模版:日常招商-固定招商-多生效时段-无补贴和预算", () => {
        const viewJson = formatMarketPlay2ViewJson({
          baseinfo: {
            activityTime: {
              weeks: [0, 1, 2, 3, 4, 5, 6],
              batchRange: {
                type: "custom",
                list: [
                  {
                    start: "00:00",
                    end: "03:00",
                  },
                  {
                    start: "18:00",
                    end: "20:00",
                  },
                  {
                    start: "22:00",
                    end: "23:59",
                  },
                ],
              },
            },
          },
          formData: {
            preheatTime: null,
            saleRule: '{"saleFixed":{"price":10000,"elemeSubsidy":0}}',
            budget: "{}",
            weekday: "0,1,2,3,4,5,6",
            userScope: "9",
            name: "yc-特价-日常招商-固定招商",
            creator: '{"id":"213137","prefix":"yc213137","name":"杨辰"}',
            itemDiscountType: "1",
            dateRange:
              '{"start":"2023-07-18 00:00:00","end":"2023-07-31 23:59:59"}',
            timeRange:
              '[{"start":"00:00:00","end":"03:00:59"},{"start":"18:00:00","end":"20:00:59"},{"start":"22:00:00","end":"23:59:59"}]',
            userLimit: '{"dayLimit":{"value":"1"}}',
            _workspace_uuid_: "6v4M8woywiK9aecPk9yWLh",
            deliveryType: "1",
            callbackChannelCode: "1",
          },
          investmentSchema: InvestmentSchemaEnum.RICHANG,
        });
        expect(viewJson).toStrictEqual({
          activityType: 1000033,
          activityLimitRule: {
            userDayCountLimit: "1",
            userTotalCountLimit: undefined,
          },
          activityMarketingInfo: {
            budgetId: undefined,
            budgetName: undefined,
            callbackChannelCode: '1',
            deliveryType: "1",
            userScope: "9",
          },
          activityRule: {
            discountType: 3,
            discount: 10000,
            platformSubsidyMax: 0,
          },
          period: [
            {
              openTime: "00:00:00",
              closeTime: "03:00:59",
              weekday: "0,1,2,3,4,5,6",
            },
            {
              openTime: "18:00:00",
              closeTime: "20:00:59",
              weekday: "0,1,2,3,4,5,6",
            },
            {
              openTime: "22:00:00",
              closeTime: "23:59:59",
              weekday: "0,1,2,3,4,5,6",
            },
          ],
        });
      });
      test("日常招商-固定招商-含补贴和预算", () => {
        const viewJson = formatMarketPlay2ViewJson({
          baseinfo: {
            activityTime: {
              weeks: [0, 1, 2, 3, 4, 5, 6],
              batchRange: {
                type: "all",
                list: [{ start: "00:00", end: "23:59" }],
              },
            },
          },
          formData: {
            preheatTime: null,
            saleRule: '{"saleFixed":{"price":10000,"elemeSubsidy":200}}',
            budget: '{"budgetId":"foobar","budgetName":"foobar"}',
            weekday: "0,1,2,3,4,5,6",
            userScope: "0",
            name: "yc-特价-日常招商",
            creator: '{"id":"213137","prefix":"yc213137","name":"杨辰"}',
            itemDiscountType: "1",
            dateRange:
              '{"start":"2023-07-17 00:00:00","end":"2023-07-31 23:59:59"}',
            timeRange: '[{"start":"00:00:00","end":"23:59:59"}]',
            userLimit: '{"dayLimit":{"value":"1"},"totalLimit":{"value":2}}',
            _workspace_uuid_: "6v4M8woywiK9aecPk9yWLh",
            deliveryType: "1",
            callbackChannelCode: "1",
          },
          investmentSchema: InvestmentSchemaEnum.RICHANG,
        });
        expect(viewJson).toStrictEqual({
          activityLimitRule: {
            userDayCountLimit: "1",
            userTotalCountLimit: 2,
          },
          activityMarketingInfo: {
            budgetId: "foobar",
            budgetName: "foobar",
            callbackChannelCode: '1',
            deliveryType: "1",
            userScope: "0",
          },
          activityRule: {
            discount: 10000,
            discountType: 3,
            platformSubsidyMax: 200,
          },
          activityType: 1000033,
          period: [
            {
              closeTime: "23:59:59",
              openTime: "00:00:00",
              weekday: "0,1,2,3,4,5,6",
            },
          ],
        });
      });
      test("日常招商-区间招商-无补贴和预算-限制人群:医药新客-应用场景:频道投放", () => {
        const viewJson = formatMarketPlay2ViewJson({
          baseinfo: {
            activityTime: {
              weeks: [0, 1, 2, 3, 4, 5, 6],
              batchRange: {
                type: "all",
                list: [{ start: "00:00", end: "23:59" }],
              },
            },
          },
          formData: {
            preheatTime: null,
            saleRule:
              '{"saleRange":{"minPrice":20000,"maxPrice":30000,"minDiscount":5.5,"elemeSubsidy":0}}',
            budget: "{}",
            weekday: "0,1,2,3,4,5,6",
            userScope: "9",
            name: "yc-特价活动",
            creator: '{"id":"213137","prefix":"yc213137","name":"杨辰"}',
            itemDiscountType: "1",
            dateRange:
              '{"start":"2023-07-17 00:00:00","end":"2023-07-31 23:59:59"}',
            timeRange: '[{"start":"00:00:00","end":"23:59:59"}]',
            userLimit: '{"totalLimit":{"value":1}}',
            _workspace_uuid_: "6v4M8woywiK9aecPk9yWLh",
            deliveryType: "1",
            callbackChannelCode: '2',
          },
          investmentSchema: InvestmentSchemaEnum.RICHANG,
        });
        expect(viewJson).toStrictEqual({
          activityLimitRule: {
            userDayCountLimit: undefined,
            userTotalCountLimit: 1,
          },
          activityMarketingInfo: {
            budgetId: undefined,
            budgetName: undefined,
            callbackChannelCode: '2',
            deliveryType: "1",
            userScope: "9",
          },
          activityRule: {
            discountMax: 30000,
            discountMin: 20000,
            discountType: 3,
            minDiscountPercentForItemSalePrice: 550,
            platformSubsidyMax: 0,
          },
          activityType: 1000033,
          period: [
            {
              closeTime: "23:59:59",
              openTime: "00:00:00",
              weekday: "0,1,2,3,4,5,6",
            },
          ],
        });
      });

      test("营销ip-爆款超值价-区间招商-无补贴和预算-限制人群:医药新客", () => {
        const viewJson = formatMarketPlay2ViewJson({
          baseinfo: {
            activityTime: {
              weeks: [0, 1, 2, 3, 4, 5, 6],
              batchRange: {
                type: "all",
                list: [{ start: "00:00", end: "23:59" }],
              },
            },
          },
          formData: {
            preheatTime: null,
            saleRule:
              '{"saleRange":{"minPrice":20000,"maxPrice":30000,"minDiscount":5.5,"elemeSubsidy":0}}',
            budget: "{}",
            weekday: "0,1,2,3,4,5,6",
            userScope: "9",
            name: "yc-特价活动",
            creator: '{"id":"213137","prefix":"yc213137","name":"杨辰"}',
            itemDiscountType: "1",
            dateRange:
              '{"start":"2023-07-17 00:00:00","end":"2023-07-31 23:59:59"}',
            timeRange: '[{"start":"00:00:00","end":"23:59:59"}]',
            userLimit: '{"totalLimit":{"value":1}}',
            _workspace_uuid_: "ExukovQrae6xWn2rt4NgG",
            deliveryType: "1",
            callbackChannelCode: "1",
          },
          investmentSchema: InvestmentSchemaEnum.HOT_VALUE_PRICE,
        });
        expect(viewJson).toStrictEqual({
          activityLimitRule: {
            userDayCountLimit: undefined,
            userTotalCountLimit: 1,
          },
          activityMarketingInfo: {
            budgetId: undefined,
            budgetName: undefined,
            deliveryType: "1",
            userScope: "9",
            callbackChannelCode: "1",
          },
          activityRule: {
            discountMax: 30000,
            discountMin: 20000,
            discountType: 3,
            minDiscountPercentForItemSalePrice: 550,
            platformSubsidyMax: 0,
          },
          activityType: 1000033,
          period: [
            {
              closeTime: "23:59:59",
              openTime: "00:00:00",
              weekday: "0,1,2,3,4,5,6",
            },
          ],
          timeLimitType: 0,
        });
      });
    });

    describe("formatViewJson2MarkPlay", () => {
      // 商品直降活动，以下两种细分活动场景，玩法表单这部分的逻辑是完全一样的，所以只需要验证日常招商即可
      // - 招商类型:日常招商-招商模版:商品互斥招商
      // - 招商类型:营销IP招商-招商模版:爆款超值价
      test("招商类型:日常招商-招商模版:日常招商-固定招商-无补贴和预算-无应用场景", () => {
        const viewJson = JSON.stringify({
          activityLimitRule: {
            userDayCountLimit: 1,
            userTotalCountLimit: 2,
          },
          activityMarketingInfo: {
            budgetId: "null", // 后端会有这种异常数据
            deliveryType: 1,
            userScope: 0,
          },
          activityRule: {
            discount: 10000,
            discountType: 3,
            platformSubsidyMax: 0,
          },
          activityType: 1000033,
          periodDTOList: [
            {
              weekday: "0,1,2,3,4,5,6", // 实际数据里，后端会在 periodDTOList 中加入 weekday 字段
              closeTime: "23:59:59",
              openTime: "00:00:00",
            },
          ],
          weekday: "0,1,2,3,4,5,6",
        });
        const baseinfo = {
          createdUserId: "213137",
          createdUserName: "杨辰",
        };
        const formData = formatViewJson2MarkPlay(viewJson, baseinfo);
        expect(formData).toStrictEqual({
          saleRule: '{"saleFixed":{"price":10000,"elemeSubsidy":0}}',
          userScope: "0",
          budget: "{}",
          creator: '{"name":"杨辰","id":"213137"}',
          deliveryType: "1",
          callbackChannelCode: '1',
          userLimit: '{"dayLimit":{"value":"1"},"totalLimit":{"value":"2"}}',
        });
      });
      test("招商类型:日常招商-招商模版:日常招商-固定招商-无补贴和预算", () => {
        const viewJson = JSON.stringify({
          activityLimitRule: {
            userDayCountLimit: 1,
            userTotalCountLimit: 2,
          },
          activityMarketingInfo: {
            budgetId: "null", // 后端会有这种异常数据
            deliveryType: 1,
            userScope: 0,
            callbackChannelCode: 1,
          },
          activityRule: {
            discount: 10000,
            discountType: 3,
            platformSubsidyMax: 0,
          },
          activityType: 1000033,
          periodDTOList: [
            {
              weekday: "0,1,2,3,4,5,6", // 实际数据里，后端会在 periodDTOList 中加入 weekday 字段
              closeTime: "23:59:59",
              openTime: "00:00:00",
            },
          ],
          weekday: "0,1,2,3,4,5,6",
        });
        const baseinfo = {
          createdUserId: "213137",
          createdUserName: "杨辰",
        };
        const formData = formatViewJson2MarkPlay(viewJson, baseinfo);
        expect(formData).toStrictEqual({
          saleRule: '{"saleFixed":{"price":10000,"elemeSubsidy":0}}',
          userScope: "0",
          budget: "{}",
          creator: '{"name":"杨辰","id":"213137"}',
          deliveryType: "1",
          callbackChannelCode: '1',
          userLimit: '{"dayLimit":{"value":"1"},"totalLimit":{"value":"2"}}',
        });
      });
      test("招商类型:日常招商-招商模版:日常招商-固定招商-用户购买限定：每人活动期间限购-应用类型：频道投放", () => {
        const viewJson = JSON.stringify({
          activityLimitRule: {
            userTotalCountLimit: 2,
          },
          activityMarketingInfo: {
            budgetId: "null", // 后端会有这种异常数据
            deliveryType: 1,
            userScope: 0,
            callbackChannelCode: 2,
          },
          activityRule: {
            discount: 10000,
            discountType: 3,
            platformSubsidyMax: 0,
          },
          activityType: 1000033,
          periodDTOList: [
            {
              weekday: "0,1,2,3,4,5,6", // 实际数据里，后端会在 periodDTOList 中加入 weekday 字段
              closeTime: "23:59:59",
              openTime: "00:00:00",
            },
          ],
          weekday: "0,1,2,3,4,5,6",
        });
        const baseinfo = {
          createdUserId: "213137",
          createdUserName: "杨辰",
        };
        const formData = formatViewJson2MarkPlay(viewJson, baseinfo);
        expect(formData).toStrictEqual({
          callbackChannelCode: '2',
          saleRule: '{"saleFixed":{"price":10000,"elemeSubsidy":0}}',
          userScope: "0",
          budget: "{}",
          creator: '{"name":"杨辰","id":"213137"}',
          deliveryType: "1",
          userLimit: '{"totalLimit":{"value":"2"}}',
        });
      });
      test("招商类型:日常招商-招商模版:日常招商-区间招商-含补贴和预算", () => {
        const viewJson = JSON.stringify({
          activityLimitRule: {
            userDayCountLimit: 1,
            userTotalCountLimit: 2,
          },
          activityMarketingInfo: {
            budgetId: "foobar",
            budgetName: "foobar",
            deliveryType: 1,
            userScope: 0,
            callbackChannelCode: 1,
          },
          activityRule: {
            discountMin: 20000,
            discountMax: 30000,
            discountType: 3,
            minDiscountPercentForItemSalePrice: 550,
            platformSubsidyMax: 200,
          },
          activityType: 1000033,
          periodDTOList: [
            {
              weekday: "0,1,2,3,4,5,6", // 实际数据里，后端会在 periodDTOList 中加入 weekday 字段
              closeTime: "00:00:59",
              openTime: "00:00:00",
            },
          ],
          weekday: "0,1,2,3,4,5,6",
        });
        const baseinfo = {
          createdUserId: "213137",
          createdUserName: "杨辰",
        };
        const formData = formatViewJson2MarkPlay(viewJson, baseinfo);
        expect(formData).toStrictEqual({
          callbackChannelCode: '1',
          saleRule:
            '{"saleRange":{"minPrice":20000,"maxPrice":30000,"minDiscount":5.5,"elemeSubsidy":200}}',
          userScope: "0",
          budget: '{"budgetName":"foobar","budgetId":"foobar"}',
          creator: '{"name":"杨辰","id":"213137"}',
          deliveryType: "1",
          userLimit: '{"dayLimit":{"value":"1"},"totalLimit":{"value":"2"}}',
        });
      });      
      test("招商类型:日常招商-招商模版:日常招商-固定招商-无补贴和预算-应用类型:超市便利", () => {
        const viewJson = JSON.stringify({
          activityLimitRule: {
            userDayCountLimit: 1,
            userTotalCountLimit: 2,
          },
          activityMarketingInfo: {
            budgetId: "null", // 后端会有这种异常数据
            deliveryType: 1,
            userScope: 0,
            callbackChannelCode: "2",
          },
          activityRule: {
            discount: 10000,
            discountType: 3,
            platformSubsidyMax: 0,
          },
          activityType: 1000033,
          periodDTOList: [
            {
              weekday: "0,1,2,3,4,5,6", // 实际数据里，后端会在 periodDTOList 中加入 weekday 字段
              closeTime: "23:59:59",
              openTime: "00:00:00",
            },
          ],
          weekday: "0,1,2,3,4,5,6",
        });
        const baseinfo = {
          createdUserId: "213137",
          createdUserName: "杨辰",
        };
        const formData = formatViewJson2MarkPlay(viewJson, baseinfo);
        expect(formData).toStrictEqual({
          saleRule: '{"saleFixed":{"price":10000,"elemeSubsidy":0}}',
          userScope: "0",
          budget: "{}",
          creator: '{"name":"杨辰","id":"213137"}',
          deliveryType: "1",
          callbackChannelCode: "2",
          userLimit: '{"dayLimit":{"value":"1"},"totalLimit":{"value":"2"}}',
        });
      });
    });
  });
});
