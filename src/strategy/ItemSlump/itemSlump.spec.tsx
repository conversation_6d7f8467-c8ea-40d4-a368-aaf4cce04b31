import axios from "axios";
import AxiosMockAdapter from "axios-mock-adapter";

import strategy from ".";

describe("itemSlump", () => {
  describe("resolveCopyData", () => {
    let axiosMock: AxiosMockAdapter;

    beforeEach(() => {
      axiosMock = new AxiosMockAdapter(axios);
      axiosMock.onGet(/\/mkt\/.*/).reply(200, {
        version: "v1",
        content: [
          {
            type: "paragraph",
            children: [
              {
                text: "多时段测试",
              },
            ],
          },
        ],
        text: "多时段测试",
      });
    });

    afterEach(() => {
      axiosMock.reset();
    });

    function normalizeCopyData(copyData: any) {
      copyData.baseinfo.activityTime.batchRange.list.forEach((item: any) => {
        delete item.key;
      });
      copyData.baseinfo.activityTime.start =
        copyData.baseinfo.activityTime.start.toISOString();
      copyData.baseinfo.activityTime.end =
        copyData.baseinfo.activityTime.end.toISOString();
      copyData.baseinfo.signupTime.start =
        copyData.baseinfo.signupTime.start.toISOString();
      copyData.baseinfo.signupTime.end =
        copyData.baseinfo.signupTime.end.toISOString();
    }

    test("日常招商-招商模版: 商品互斥招商", async () => {
      const creationContext = {
        copy: true,
        createActiivtyStartTime: 1690274575595,
        createActivitySessionId: "3467f611-c7f9-4c77-b5aa-e3640f1274e3",
        customAttributes: {
          callbackChannelDeliveryPermission: false 
        },
        isSingleActivity: true,
        activityType: 1000033,
        investmentType: 2,
        investmentSchema: 18,
        fineGrainedActivityTimeDisabled: true,
        activity: {
          activityId: 6323986003,
          createdUserId: "329684",
          createdUserName: "郭宇帆",
          name: "多时段测试",
          remark: "",
          description: "多时段测试",
          beginTime: "2023-07-24T16:00:00.000Z",
          endTime: "2023-07-31T15:59:59.000Z",
          signUpStartTime: "2023-07-24T16:00:00.000Z",
          signUpEndTime: "2023-07-31T15:59:59.000Z",
          allowCancel: 1 as 1 | 2,
          signUpShopType: 0,
          shopPoolId: 271111,
          shopPoolName: "shz测试门店池",
          reviewRuleId: 1602001,
          reviewRuleName: "zzg商品规则验证",
          marketPlay: null,
          signUpMinCycleTime: null,
          signUpMaxCycleTime: null,
          stockType: 1,
          auditMemberList: [],
          viewJson:
            '{"activityLimitRule":{"userDayCountLimit":1},"activityMarketingInfo":{"budgetId":"null","deliveryType":1,"userScope":0},"activityRule":{"discount":300,"discountType":3,"platformSubsidyMax":0},"activityType":1000033,"attributes":{"hasRichDescription":"on","version":"3.0","richDescriptionURL":"https://bwm-private-b.oss-cn-hangzhou.aliyuncs.com/mkt/da1b38990b6b26cb418240bbd48b7431?Expires=2005888858&OSSAccessKeyId=LTAIhP07XE91RzLJ&Signature=djHbO80SZJ49Bru10mcSbcoqhXo%3D"},"period":{"closeTime":"01:06:59","openTime":"01:03:00","weekday":"1,2,3,4,6"},"periodDTOList":[{"closeTime":"01:06:59","openTime":"01:03:00","weekday":"1,2,3,4,6"},{"closeTime":"16:00:59","openTime":"03:00:00","weekday":"1,2,3,4,6"}],"weekday":"1,2,3,4,6"}',
        },
      };
      const copyData = await strategy.resolveCopyData!(creationContext);
      normalizeCopyData(copyData);
      expect(copyData).toStrictEqual({
        baseinfo: {
          name: "多时段测试",
          richDescription: {
            version: "v1",
            content: [
              {
                type: "paragraph",
                children: [
                  {
                    text: "多时段测试",
                  },
                ],
              },
            ],
            text: "多时段测试",
          },
          remark: "",
          activityTime: {
            start: "2023-07-24T16:00:00.000Z",
            end: "2023-07-31T15:59:59.000Z",
            weeks: [1, 2, 3, 4, 6],
            batchRange: {
              type: "custom",
              list: [
                {
                  start: "01:03",
                  end: "01:06",
                },
                {
                  start: "03:00",
                  end: "16:00",
                },
              ],
            },
          },
          auditMemberList: [],
          signupTime: {
            start: "2023-07-24T16:00:00.000Z",
            end: "2023-07-31T15:59:59.000Z",
          },
          allowCancel: 1,
          signUpShopType: 0,
        },
        scope: {
          commodityAuditType: 1,
        },
        playdata: {
          fields: {
            budget: undefined,
            callbackChannelCode: '1',
            userScope: "0",
            creator: "{}",
            deliveryType: "1",
            saleRule: '{"saleFixed":{"price":300,"elemeSubsidy":0}}',
            userLimit: '{"dayLimit":{"value":"1"}}',
          },
        },
        stock: {
          stockType: 1,
          range: {},
        },
      });
    });
  });
});
