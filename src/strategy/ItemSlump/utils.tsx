import BigNumber from "bignumber.js";
import { isValueSet, parseJsonSafe } from "../../common";
import { ActivityTypeEnum, InvestmentSchemaEnum } from "../../constants";

interface ItemSlumpPlaydata {
  activityLimitRule: {
    userDayCountLimit?: number;
    userTotalCountLimit?: number;
  };
  activityMarketingInfo: {
    budgetId?: string;
    budgetName?: string;
    deliveryType: number;
    userScope: number;
    callbackChannelCode?: number;
  };
  activityType: number;

  activityRule: {
    discount?: number; // 城市免单固定是1
    discountType: 3; // 前端写死（凤兮）
    platformSubsidyMax?: number;
    discountMin?: number;
    discountMax?: number;
    minDiscountPercentForItemSalePrice?: number;
    platformSubsidyRateUpperLimit?: number;
  };
}

// 商品直降活动类似商品特价，支持多时段，活动详情后端返回的viewJson多时段为periodDTOList
export interface ItemSlumpViewPlaydata extends ItemSlumpPlaydata {
  periodDTOList: {
    openTime: string;
    closeTime: string;
  }[];
  weekday: string;
}

// 商品直降活动类似商品特价，支持多时段，创建的时候传给后端period
export interface ItemSlumpCreatePlaydata extends ItemSlumpPlaydata {
  period: {
    openTime: string;
    closeTime: string;
    weekday: string;
  }[];
  timeLimitType?: 0 | 1;
}
// 多时段类型
const timeLimitType = {
  'all': 0,
  'custom': 1,
}
export const formatViewJson2MarkPlay = (viewJson: any, baseInfo: any) => {
  const viewDto: ItemSlumpPlaydata = JSON.parse(viewJson) || {};
  const { activityRule, activityLimitRule, activityMarketingInfo } =
    viewDto;
  let saleRule = {}; // 优惠形式
  let userLimit = {};
  let userScope = ""; // 用户类型
  let callbackChannelCode = undefined // 应用场景
  let deliveryType = ""; // 生效渠道
  let budget = {}; //  预算
  let creator = {}; // 创建人名称、创建人id

  if (isValueSet(activityRule.discount)) {
    saleRule = {
      saleFixed: {
        price: activityRule.discount,
        elemeSubsidy: activityRule?.platformSubsidyMax,
      },
    };
  }
  if (isValueSet(activityRule.discountMin)) {
    saleRule = {
      saleRange: {
        minPrice: activityRule.discountMin,
        maxPrice: activityRule.discountMax,
        minDiscount: activityRule?.minDiscountPercentForItemSalePrice ? new BigNumber(activityRule?.minDiscountPercentForItemSalePrice).dividedBy(100)
        .toNumber() : undefined,
        elemeSubsidy: activityRule?.platformSubsidyMax,
      },
    };
  }

  if (
    isValueSet(baseInfo.createdUserId) &&
    isValueSet(baseInfo.createdUserName)
  ) {
    creator = {
      name: `${baseInfo.createdUserName}`,
      id: baseInfo.createdUserId,
    };
  }
  if (isValueSet(activityLimitRule?.userDayCountLimit)) {
    userLimit = {
      dayLimit: {
        value: activityLimitRule.userDayCountLimit + "",
      },
    };
  }
  if (isValueSet(activityLimitRule?.userTotalCountLimit)) {
    userLimit = {
      ...userLimit,
      totalLimit: {
        value: activityLimitRule.userTotalCountLimit + "",
      },
    };
  }
  if (
    isValueSet(activityLimitRule?.userTotalCountLimit) &&
    isValueSet(activityLimitRule?.userDayCountLimit)
  ) {
    userLimit = {
      dayLimit: {
        value: activityLimitRule.userDayCountLimit + "",
      },
      totalLimit: {
        value: activityLimitRule.userTotalCountLimit + "",
      },
    };
  }
  if (isValueSet(activityMarketingInfo?.deliveryType)) {
    deliveryType = activityMarketingInfo.deliveryType + "";
  }

  if (isValueSet(activityMarketingInfo?.userScope)) {
    userScope = activityMarketingInfo.userScope + "";
  }

  // 1 通用场景、2 超市便利(老)/频道投放（新）
  if (activityMarketingInfo?.callbackChannelCode) {
    callbackChannelCode = activityMarketingInfo.callbackChannelCode + "";
  } else {
    // 老活动没有 callbackChannelCode 字段，需要补全为 1 通用场景
    callbackChannelCode = '1';
  }

  if (isValueSet(activityMarketingInfo.budgetName)) {
    budget = {
      budgetName: activityMarketingInfo?.budgetName,
      budgetId: activityMarketingInfo.budgetId + "",
    };
  }
  return {
    creator: JSON.stringify(creator),
    userLimit: JSON.stringify(userLimit),
    callbackChannelCode,
    userScope,
    deliveryType: deliveryType,
    budget: JSON.stringify(budget),
    saleRule: JSON.stringify(saleRule),
    platformSubsidyRateUpperLimit:  viewDto?.activityRule?.platformSubsidyRateUpperLimit,
  };
};

export const formatMarketPlay2ViewJson = ({ formData, baseinfo, investmentSchema }: any) => {
  const budget = parseJsonSafe(formData?.budget);
  const saleRule = parseJsonSafe(formData.saleRule);
  const userLimit = parseJsonSafe(formData.userLimit);
  const deliveryType = formData.deliveryType;
  const userScope = formData.userScope;

  const viewJson: ItemSlumpCreatePlaydata = {
   
    activityLimitRule: {
      userDayCountLimit: userLimit?.dayLimit?.value,
      userTotalCountLimit: userLimit?.totalLimit?.value,
    },
    activityMarketingInfo: {
      budgetId: budget?.budgetId,
      budgetName: budget?.budgetName,
      deliveryType: deliveryType,
      userScope: userScope,
      callbackChannelCode: formData.callbackChannelCode,  // 应用场景 1 通用场景、2 超市便利(老)/频道投放（新）
    },
    activityRule: saleRule?.saleFixed
      ? {
          discount: saleRule?.saleFixed?.price,
          platformSubsidyMax: saleRule?.saleFixed?.elemeSubsidy,
          discountType: 3, // 前端写死
          platformSubsidyRateUpperLimit: baseinfo?.platformSubsidyRateUpperLimit,
        }
      : {
          platformSubsidyRateUpperLimit: baseinfo?.platformSubsidyRateUpperLimit,
          discountMin: saleRule?.saleRange?.minPrice,
          discountMax: saleRule?.saleRange?.maxPrice,
          platformSubsidyMax: saleRule?.saleRange?.elemeSubsidy,
          minDiscountPercentForItemSalePrice: new BigNumber(
            saleRule?.saleRange?.minDiscount
          )
            .multipliedBy(100)
            .toNumber(),
          discountType: 3, // 前端写死
        },
    activityType: ActivityTypeEnum.ITEM_SLUMP,
    period: (baseinfo?.activityTime.batchRange.list || []).map((r: any) => {
      return {
        openTime: r.start + ":00",
        closeTime: r.end + ":59",
        weekday: (baseinfo.activityTime.weeks || []).join(","),
      };
    }),

  };
  
  if (investmentSchema === InvestmentSchemaEnum.HOT_VALUE_PRICE) {
    // 爆款折扣的时候，需要传这个字段，其他场景不传
    // 多时段类型，type: 'all' | 'custom', 如果自定义时间设置了 00:00 - 23:59, 也按照自定义传 'custom'
    viewJson.timeLimitType = timeLimitType[baseinfo?.activityTime.batchRange.type as 'all' || 'custom'] as 0 | 1; 
  }
  return viewJson;
};
