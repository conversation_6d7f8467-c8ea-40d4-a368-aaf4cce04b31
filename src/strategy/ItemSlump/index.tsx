import { Message } from "@alifd/next";
import moment from "moment";

import * as api from "../../api";
import { isValueSet } from "../../common";
import loadingDialog from "../../components/LoadingDialog";
import {
  ActivityTypeEnum,
  InvestmentSchemaEnum,
  InvestmentTypeEnum,
} from "../../constants";
import { ActivityStrategy } from "../../models";
import {
  CableBase,
  resolveCreateActivityCommonData,
  resolveTimeRangeBatchFromMarketplayValue,
} from "../base";
import {
  resolveCommodityAuditProps,
  checkValidateCopyActivityResult,
  checkAndUploadRichDescription,
  resolveRichDescription,
  checkNextTimeForAlpha,
  checkNextCommoditySignupRuleForAlpha,
  checkMktRulesForMultiBusinessFormAndConfirmIfNeed,
  checkMarketRules,
} from "../mixins";
import { formatMarketPlay2ViewJson, formatViewJson2MarkPlay, ItemSlumpViewPlaydata } from "./utils";


const ItemSlump: ActivityStrategy = {
  activityType: ActivityTypeEnum.ITEM_SLUMP,
  cable: {
    ...CableBase,
    workspace: "ExukovQrae6xWn2rt4NgG",
    model: "1zkrBd7nVf4aUP1wlQmTL2",
    async checkNext(checkNextContext, creationContext, history) {
      const { step, dataStore } = checkNextContext;
      if (!dataStore._session) {
        dataStore._session = {};
      }
      if (step.name === "baseinfo") {
        if (dataStore.data.baseinfo?.richDescription) {
          const { hide } = loadingDialog.show({
            message: "活动信息检查中...",
          });
          try {
            const error = await checkAndUploadRichDescription({
              richDescription: dataStore.data.baseinfo?.richDescription,
              session: dataStore._session,
            });
            if (error) {
              return error;
            }
          } finally {
            hide();
          }
        }
      }
      if (step.name === "baseinfo") {
        // 重置 auditUpgradeForMultiMktRule，避免重新回到第一步重新选择业务线和业态的情况
        dataStore._session.auditUpgradeForMultiMktRule = undefined;
        // 校验是否跨业态，并且返回弹窗点击结果
        const { conflicted, userConfirm } =
          await checkMktRulesForMultiBusinessFormAndConfirmIfNeed(
            creationContext,
            dataStore.data.baseinfo
          );
        if (!conflicted) {
          // 没有错误信息，不需要拦截进入下一步
          return;
        }
        if (userConfirm) {
          // 有错误，并且用户二次确认了，确认需提交审批
          dataStore._session.auditUpgradeForMultiMktRule = true;
          // 不需要拦截进入下一步
          return;
        } else {
          // 有错误，单用户未二次确认，页面停留在当前步骤
          return { silent: true };
        }
      }
      if(step.name === "baseinfo") {
        const timeError = await checkNextTimeForAlpha(checkNextContext, creationContext);
        if(timeError) {
          return timeError;
        }
      }
      if(step.name === "scope") {
        const commoditySignupRuleError = await checkNextCommoditySignupRuleForAlpha(checkNextContext, creationContext);
        if(commoditySignupRuleError) {
          return commoditySignupRuleError;
        }
      }
      if (step.name === "stock") {
        const { baseinfo, stock, scope, playdata } = dataStore.data;
        const commonData: any = resolveCreateActivityCommonData(
          ActivityTypeEnum.ITEM_SLUMP,
          checkNextContext,
          creationContext
        );
        delete commonData.dataMap;
        const formData: any = playdata.snapshot.data;
        const viewJson: any = formatMarketPlay2ViewJson({ formData, baseinfo, investmentSchema: creationContext?.investmentSchema });
        const commodityAuditProps: any = resolveCommodityAuditProps(scope);

        const payload: any = {
          ...commonData,

          // 自动审核规则
          ...commodityAuditProps,

          // 降套补 商品清退规则
          commodityRemoveCondition: {
            removeCondition: scope.removeCondition,
          },

          auditMemberList:
            baseinfo.auditMemberList?.length > 0
              ? baseinfo.auditMemberList
              : null,

          // 报名规则
          reviewRuleId: scope.rule?.ruleId,
          reviewRuleName: scope.rule?.name,

          // 玩法设置
          viewJson: JSON.stringify(viewJson),

          // 库存设置
          stockType: stock.stockType,
          stockMin: stock.range.start,
          stockMax: stock.range.end,
          requestId: stock.stockType === 2 ? stock.requestId : undefined,
        };
        const isModifyPriceEnabled = true;
        if (isModifyPriceEnabled) {
          if (
            baseinfo.modifyRules &&
            baseinfo.modifyRules.indexOf("merchant_modify_activity_price") !==
              -1
          ) {
            payload.activityModifyRules = [
              {
                activityModifyRuleType: "merchant_modify_activity_price",
                isSupport: true,
              },
            ];
          }
        }

        const { hide, updateMessage } = loadingDialog.show({
          message: creationContext.copy ? "活动信息校验中..." : "活动创建中...",
        });

        if (dataStore._session?.ossURL) {
          const ossURL = dataStore._session?.ossURL;
          payload.description = baseinfo.richDescription.text;
          payload.attributes = {
            ...payload.attributes,
            hasRichDescription: "on",
            richDescriptionURL: ossURL,
          };
        }

        try {
          const {
            interrupt,
            auditUpgradeForCheckMktRuleUnPass,
            auditUpgradeForMultiMktRule,
          } = await checkMarketRules(
            dataStore,
            creationContext,
            JSON.stringify(viewJson)
          );
          if (interrupt) {
            return { silent: true };
          }

          payload.auditUpgradeForCheckMktRuleUnPass =
            auditUpgradeForCheckMktRuleUnPass;
          payload.auditUpgradeForMultiMktRule = auditUpgradeForMultiMktRule;   
           
          if (creationContext.copy) {
            const res = await api.subActivity.validateCopyActivityData({
              copyActivityId: creationContext.activity?.activityId,
              ...payload,
            });
            const copyError = checkValidateCopyActivityResult(res);
            if (copyError) {
              return copyError;
            }
            updateMessage("活动创建中...");
          }
          const res = await api.subActivity.create(payload);
          if (res.success) {
            Message.success("创建成功");
            if (creationContext.activitySceneId) {
                  history.push(
                    "/sub-activity/" +
                      res.data +
                      "?activitySceneId=" +
                      creationContext.activitySceneId +
                      "&activityPlanId=" +
                      creationContext.activityPlanId
                  );
                } else {
                  history.push("/sub-activity/" + res.data);
                }
          } else {
            return { message: res.errorMessage };
          }
        } catch (e: any) {
          return { message: e?.message || "系统异常" };
        } finally {
          hide();
        }
      }
    },
  },

  customAttributesForCreationContext: async (options: {
    activityType: number;
    investmentType: number;
    investmentSchema: InvestmentSchemaEnum;
  }) => {
    // 判断用户是否在白名单内，在白名单内展示“频道投放”选项
    const res = await api.aslcClient.fetch({
      apiKey:
        "ele-newretail-investment.NewretailInvestmentYundingGrayService.isActivityCallBackChannelGrayUser",
      params: [options],
    });

    return {
      // 校验应用场景 - 频道投放 权限
      callbackChannelDeliveryPermission: res ? res.data : undefined,
    };
  },


  hasAgentSubsidy() {
    return false;
  },
  isSupportAgent: false,
  isOfflineEnabled: function (state: number): boolean {
    // 活动状态: 1:未开始 2:活动中 3:活动暂停 4:活动取消 5:活动结束 8: 预算熔断
    return [1, 2, 3, 8].includes(state);
  },
  isCopyEnabled({ investmentSchema, investmentType }) {
    const ishotValuePrice =
      investmentSchema === InvestmentSchemaEnum.HOT_VALUE_PRICE &&
      investmentType === InvestmentTypeEnum.MARKETING_IP_INVESTMENT;
    const isMutuallyExclusive =
      investmentSchema === InvestmentSchemaEnum.ITEM_MUTUAL_EXCLUSION_INVESTMENT &&
      investmentType === InvestmentTypeEnum.RICHANG;
    return ishotValuePrice || isMutuallyExclusive;
  },
  async resolveCopyData(creationContext) {
    let viewJson: ItemSlumpViewPlaydata;
    try {
      viewJson =
        creationContext.activity &&
        JSON.parse(creationContext.activity?.viewJson);
      const { periodDTOList, weekday } = viewJson || {};
      const timeRangeBatch = resolveTimeRangeBatchFromMarketplayValue(
        JSON.stringify(
          periodDTOList.map((item) => {
            return { start: item.openTime, end: item.closeTime };
          })
        )
      );
      const baseInfo = {
        beginTime: creationContext.activity?.beginTime,
        endTime: creationContext.activity?.endTime,
      };

      // 获取富文本描述信息
      const richDescription = await resolveRichDescription(creationContext);
      
      const fields = formatViewJson2MarkPlay(creationContext.activity?.viewJson, baseInfo);
      if (fields.callbackChannelCode === '2' && !creationContext?.customAttributes?.callbackChannelDeliveryPermission) {
        // 频道投放权限不足，默认使用 通用渠道
        fields.callbackChannelCode = '1';
      };

      return {
        baseinfo: {
          name: creationContext.activity?.name,
          richDescription,
          remark: creationContext.activity?.remark,
          activityTime: {
            start: moment(creationContext.activity?.beginTime),
            end: moment(creationContext.activity?.endTime),
            weeks: weekday
              .split(",")
              .filter(Boolean)
              .map((d: string) => +d),
            batchRange: timeRangeBatch,
          },
          auditMemberList: creationContext.activity?.auditMemberList || [],
          signupTime: {
            start: moment(creationContext.activity?.signUpStartTime),
            end: moment(creationContext.activity?.signUpEndTime),
          },
          allowCancel: creationContext.activity?.allowCancel,
          signUpShopType: creationContext.activity?.signUpShopType,
        },
        scope: {
          // 复制活动，门店池和报名规则均清空
          commodityAuditType: 1,
        },
        playdata: {
          fields: {
            ...fields,
            budget: undefined, // 复制活动要清空预算
          },
        },
        stock: {
          stockType: creationContext.activity?.stockType,
          range: {},
        },
      };
    } catch (e) {
      console.error(e);
      return {};
    }
  },
  isDingtalkLinkEnabled: () => true,
  hasAuditMemberList: () => true,
  isSupportModifyStock: () => false,
  isItemAuditStatsVisible: true,
  isDetailInviteStatsOnMainActivityTableVisible: true,
  renderPlayDataInfoForTableView: function (): JSX.Element {
    return <></>;
  },
  gotoLegacyDetail: function (): void {},
  renderActivityRuleForTable(
    viewJson: any,
  ) {
    try {
      const data: ItemSlumpViewPlaydata = JSON.parse(viewJson);
      let result = [];
      if (
        isValueSet(data.activityRule.discountMin) &&
        isValueSet(data.activityRule.discountMax)
      ) {
        // 区间招商
        result.push(
          // @ts-ignore
          `活动价格区间: ${data.activityRule.discountMin / 100} ~ ${data.activityRule.discountMax / 100
          }元`
        );
        result.push(
          `限制最低折扣: ${
            // @ts-ignore
            data.activityRule.minDiscountPercentForItemSalePrice / 100
          }`
        );
        result.push(
          // @ts-ignore
          `平台补贴上限: ${data.activityRule.platformSubsidyMax / 100}元`
        );
      } else if (isValueSet(data.activityRule.discount)) {
        // 固定招商
        // @ts-ignore
        result.push(`价格固定: 锁${data.activityRule.discount / 100}元`);
        result.push(
          // @ts-ignore
          `平台补贴上限: ${data.activityRule.platformSubsidyMax / 100}元`
        );
      }
      return (
        <>
          {result.map((r: any, i: number) => (
            <div key={i}>{r}</div>
          ))}
        </>
      );
    } catch (e) {
      console.error(e);
      return <></>;
    }
  },
  renderActivityRuleForTableLegacy: function () {
    return <></>;
  },
  marketPlayMapper: async function (
    viewJson: any,
    baseInfo: any,
  ) {
    try {
    const viewJsonView = formatViewJson2MarkPlay(viewJson, baseInfo);
      let model = await api.subActivity.getComponentDataModel(
        "ExukovQrae6xWn2rt4NgG",
        "94FnYrbGShp9l4D1slVdRM"
      );
      const marketPlay = {
        instance: {
          fields: {
            ...viewJsonView
          },
        },
        model: model,
      };
      return marketPlay;
    } catch (e) {
      return null;
    }
  },
};

export default ItemSlump;
