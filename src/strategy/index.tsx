import { ActivityStrategy } from '../models'
import DeliveryCoupon from './deliveryReduce'
import { StoreReduceCoupon, ExchangeBeanCoupon, ShareCoupon } from './storeReduceCoupon'
import StoreReduce from './storeReduce'
import FormatCoupon from './formatCoupon'
import ItemSale from './itemSale/index';
import ItemDiscount from './itemDiscount';
import ItemRealDiscount from './itemRealDiscount';
import BlastRedPacket from './blastRedPacket'
import CategoryReduce from './categoryReduce';
import CategoryReduceCoupon from './categoryReduceCoupon';
import ItemSpecialCoupon from './itemSpecialCoupon';
import NBuy from './NBuy'
import MultipleStoreCoupon from './multipleStoreCoupon'
import CrossStore from './crossStore'
import TripartiteMixture from './tripartiteMixture'
import MerchantEnterCoupon from './merchantEnterCoupon';
import LargeCoupon from './largeCoupon';
import NChooseM from './NChooseM';
import SendShoppingMoney from './sendShoppingMoney'
import ItemOverlayCoupon from './itemOverlayCoupon'
import ItemSlump from './ItemSlump';
import BuyGift from './buyGift';
import ItemDeliveryReduce from './itemDeliveryReduce'
import InterestInstallment from './interestInstallment';
import ExplosiveCoupon from './explosiveCoupon'
import ItemExchange from './itemExchange'
import DeliveryReduceTableWine from './deliveryReduceTableWine'
import DeliveryReduceEasyPurchase from './deliveryReduceEasyPurchase'
import ThreeSubsidyItemPromotion from './threeSubsidyItemPromotion'
import SubExplosiveCoupon from './ExplosiveCoupon2014'
import FollowHeartConfigure from './FollowHeartConfigure'
import itemFreeShippingCoupon from './itemFreeShippingCoupon'

const strategyList: ActivityStrategy[] = [
  BlastRedPacket,
  DeliveryCoupon,
  StoreReduceCoupon,
  ExchangeBeanCoupon,
  ShareCoupon,
  StoreReduce,
  FormatCoupon,
  MultipleStoreCoupon,
  ItemSale,
  CrossStore,
  ItemDiscount,
  ItemRealDiscount,
  CategoryReduce,
  CategoryReduceCoupon,
  ItemSpecialCoupon,
  NBuy,
  MerchantEnterCoupon,
  LargeCoupon,
  TripartiteMixture,
  NChooseM,
  SendShoppingMoney,
  ItemOverlayCoupon,
  ItemSlump,
  BuyGift,
  ItemDeliveryReduce,
  InterestInstallment,
  ExplosiveCoupon,
  ItemExchange,
  DeliveryReduceTableWine,
  DeliveryReduceEasyPurchase,
  ThreeSubsidyItemPromotion,
  SubExplosiveCoupon,
  FollowHeartConfigure,
  itemFreeShippingCoupon
]

export function getStrategy(activityType: any): ActivityStrategy | null {
  return strategyList.find(s => s.activityType === activityType) || null
}
