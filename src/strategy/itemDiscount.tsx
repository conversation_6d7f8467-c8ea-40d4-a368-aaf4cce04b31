import { Message } from "@alifd/next";

import { ActivityTypeEnum } from "../constants"
import { ActivityStrategy, InvestmentActivityDetailDTO } from "../models"
import * as api from "../api"
import { CableBase, resolveCreateActivityCommonData } from "./base"
import { gotoLegacyActivityDetailByDefault, resolveCommodityAuditProps } from "./mixins"
import { isValueSet } from "../common";

const debug = require('debug')('zs:strategy:ItemSale')

interface ItemDiscountPlaydata {
  activityType: number;
  period: {
    openTime: string;
    closeTime: string
    weekday: string;
  };
  activityRule: {
    discount: number;
    discountMin: number;
    discountMax: number;
    minDiscountPercentForItemSalePrice: number;
    platformSubsidyMax: number;
  }
}

// 新的真折扣活动配置是 itemRealDiscount，这个文件是老招商的活动配置
const ItemDiscount: ActivityStrategy = {
  activityType: ActivityTypeEnum.MKT_SALE_BASE,
  cable: {
    ...CableBase,
    workspace: 'ExukovQrae6xWn2rt4NgG',
    model: '3bGVRtAEetv91pLAXI2SjX',
    async checkNext(checkNextContext, creationContext, history) {
      const { step, dataStore } = checkNextContext
      console.log('checkNext', step, dataStore)
      if (step.name === 'stock') {
        const { stock, scope, baseinfo } = dataStore.data
        const commonData = resolveCreateActivityCommonData(
          ActivityTypeEnum.MKT_SALE_BASE,
          checkNextContext,
          creationContext
        )

        const commodityAuditProps: any = resolveCommodityAuditProps(scope || {}) || {};

        const payload: any = {
          ...commonData,

          // 自动审核规则
          ...commodityAuditProps,

          // 降套补 商品清退规则
          commodityRemoveCondition: { removeCondition: scope.removeCondition },
          
          auditMemberList: baseinfo.auditMemberList?.length > 0 ?  baseinfo.auditMemberList: null,

          // TODO: 支持 seller 库存设置
          stockType: stock.stockType,
          stockMin: stock.range.start,
          stockMax: stock.range.end
        }
        payload.dataMap.resource.componentModelUuid = '6hGvtVWiRWK8G3pztZS0AV'


        debug('create subActivity', payload)
        const res = await api.subActivity.create(payload);
        if (res.success) {
          Message.success('创建成功');
          if (creationContext.activitySceneId) {
                  history.push(
                    "/sub-activity/" +
                      res.data +
                      "?activitySceneId=" +
                      creationContext.activitySceneId +
                      "&activityPlanId=" +
                      creationContext.activityPlanId
                  );
                } else {
                  history.push("/sub-activity/" + res.data);
                }
        } else {
          return { message: res.errorMessage };
        }
      }
    }
  },
  hasAgentSubsidy() {
    return false
  },
  isSupportAgent: false,
  isOfflineEnabled: function (state: number): boolean {
    // 活动状态: 1:未开始 2:活动中 3:活动暂停 4:活动取消 5:活动结束 8: 预算熔断
    return [1, 2, 3, 8].includes(state)
  },
  isDingtalkLinkEnabled: () => true,
  hasAuditMemberList: () => true,
  isItemAuditStatsVisible: true,
  isSupportModifyStock: () => false,
  isDetailInviteStatsOnMainActivityTableVisible: false,
  renderPlayDataInfoForTableView: function (): JSX.Element {
    return <></>
  },
  gotoLegacyDetail: function (act: InvestmentActivityDetailDTO, scene: "lookup" | "examine"): void {
    gotoLegacyActivityDetailByDefault(act, scene)
  },
  renderActivityRuleForTable(viewJson: any) {
    try {
      const data: ItemDiscountPlaydata = JSON.parse(viewJson)
      let result = [];
      if (
        isValueSet(data.activityRule.discountMin) &&
        isValueSet(data.activityRule.discountMax)
      ) {
        // 区间招商
        result.push(`折扣区间 ${data.activityRule.discountMin} ~ ${data.activityRule.discountMax} 折，平台补贴 ${data.activityRule.minDiscountPercentForItemSalePrice}，补贴上限 ${data.activityRule.platformSubsidyMax / 100} 元`);
      } else if (isValueSet(data.activityRule.discount)) {
        // 固定招商
        result.push(`折扣 ${data.activityRule.discount} 折，平台补贴 ${data.activityRule.minDiscountPercentForItemSalePrice}，补贴上限 ${data.activityRule.platformSubsidyMax / 100} 元`);
      }
      return <>{result.map((r: any, i: number) => (<div key={i}>{r}</div>))}</>
    } catch (e) {
      console.error(e)
      return <></>;
    }
  },
  renderActivityRuleForTableLegacy: function (marketPlay: { instance: { fields: any } }) {
    const fields = marketPlay?.instance?.fields
    const discountRule = JSON.parse(fields.discountRule);

    let result = [];
    if (discountRule.discountFixed) {
      const { discount, elemeDiscount, maxElemeSubsidy } = discountRule.discountFixed;
      result.push(`折扣${discount}折，平台补贴${elemeDiscount}折，补贴上限${maxElemeSubsidy / 100}元`);
    } else {
      const { discountRange } = discountRule;
      result.push(`
    折扣区间 ${discountRange.minDiscount} - ${discountRange.maxDiscount} 折，平台补贴 ${discountRange.elemeDiscount} 折，补贴上限 ${discountRange.maxElemeSubsidy / 100} 元
    `);
    }

    return result.map((r: any, i: number) => (<div key={i}>{r}</div>))
  }
}

export default ItemDiscount

