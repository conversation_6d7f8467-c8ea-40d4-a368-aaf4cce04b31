import BigNumber from 'bignumber.js';

import { maskMS } from '../common';
import InfoItem from '../components/InfoItem';
import { ActivityTypeEnum } from "../constants";
import { ActivityStrategy, InvestmentActivityDetailDTO } from "../models"
import { CableBase } from "./base"
import { genericCreateActivity, gotoLegacyActivityDetailByDefault } from './mixins';
import { auditServiceForCoupon } from "./service"

interface FormatCouponRule {
  shopSubsidy: number;
  shopSubsidyMax: number;
  dayCountLimit: number;
  dayCountLimitMax: number;
}

const FormatCoupon: ActivityStrategy = {
  renderExtraInfoForMainActivityTable(record) {
    if (record?.investmentActivityDetailDTO?.poolId) {
      return (
        <InfoItem label="供给池ID">
          {record.investmentActivityDetailDTO.poolId}
        </InfoItem>
      )
    } else {
      return <></>
    }
  },
  renderExtraInfoForSubActivityTable(record) {
    if (record?.investmentActivityDetailDTO?.poolId) {
      return (
        <InfoItem label="供给池ID">
          {record.investmentActivityDetailDTO.poolId}
        </InfoItem>
      )
    } else {
      return <></>
    }
  },
  activityType: ActivityTypeEnum.BUSINESS_CATERING_ALLIANCE,
  cable: {
    ...CableBase,
    workspace: 'ExukovQrae6xWn2rt4NgG',
    model: '9463T2GXiHq5F2cbTXKRE9',
    async checkNext({ step, dataStore }, creationContext, history) {
      console.log('checkNext', step, dataStore);
      if (step.name === 'playdata') {
        const { baseinfo, playdata, scope } = dataStore.data;
        const payload = {
          // 活动类型信息
          templateId: creationContext.templateId,
          investmentType: creationContext.investmentType,
          investmentSchema: creationContext.investmentSchema,
          activityType: ActivityTypeEnum.BUSINESS_CATERING_ALLIANCE,

          // 基本信息
          name: baseinfo.name,
          description: baseinfo.description,
          remark: baseinfo.remark,
          beginTime: baseinfo.activityTime.start.toDate().getTime(),
          endTime: maskMS(baseinfo.activityTime.end.toDate().getTime()),
          allowCancel: baseinfo.allowCancel,
          signUpShopType: baseinfo.signUpShopType,
          signUpStartTime: baseinfo.signupTime.start.toDate().getTime(),
          signUpEndTime: maskMS(baseinfo.signupTime.end.toDate().getTime()),
          activityPeriodRequestList: [{
            weekday: baseinfo.activityTime.weeks.join(','),
            openTime: baseinfo.activityTime.range.start + ':00',
            closeTime: baseinfo.activityTime.range.end + ':00'
          }],
          subsidyType: creationContext?.subsidyType,
          manageOrgList: baseinfo?.manageOrgList,

          // 招商范围信息
          shopPoolId: scope.pool.value,
          shopPoolName: scope.pool.label,

          // 玩法信息
          viewJson: JSON.stringify({
            shopSubsidy: new BigNumber(playdata.shopSubsidy).multipliedBy(100).toNumber(),
            shopSubsidyMax: new BigNumber(playdata.shopSubsidyMax).multipliedBy(100).toNumber(),
            dayCountLimit: playdata.dayCountLimit,
            dayCountLimitMax: playdata.dayCountLimitMax,
          }),

          // 不限制库存
          stockType: 0,
        };

        return await genericCreateActivity(payload, history, creationContext);
      }
    },
    auditService: auditServiceForCoupon
  },
  hasAgentSubsidy() {
    return false;
  },
  isOfflineEnabled: function (state: number): boolean {
    // 业态红包和其他活动不太一样，活动进行中是不允许下线的
    // 活动状态: 1:未开始 2:活动中 3:活动暂停 4:活动取消 5:活动结束 8: 预算熔断
    return [1, 3, 8].includes(state);
  },
  isDingtalkLinkEnabled: () => false,
  hasAuditMemberList: () => false,
  isSupportModifyStock: () => false,
  isDetailInviteStatsOnMainActivityTableVisible: true,
  renderPlayDataInfoForTableView: function (): JSX.Element {
    return <></>;
  },
  renderActivityRuleForTable(viewJson: string) {
    try {
      const data: FormatCouponRule = JSON.parse(viewJson);
      return `商家出资金额 ${data.shopSubsidy / 100} - ${data.shopSubsidyMax / 100}元；单门店每日可消耗数量 ${data.dayCountLimit} - ${data.dayCountLimitMax}`;
    } catch (e) {
      console.error(e);
      return '';
    }
  },
  isSupportAgent: false,
  gotoLegacyDetail: function (act: InvestmentActivityDetailDTO, scene: 'lookup' | 'examine'): void {
    gotoLegacyActivityDetailByDefault(act, scene);
  },
  renderActivityRuleForTableLegacy: function (marketPlay: any, activity: any) {
    const subsidy = activity.playDataRuleDTOList[0].subsidy
    const Limit = activity.playLimitRuleDTO
    return `商家出资金额 ${subsidy.shopSubsidy / 100} - ${subsidy.shopSubsidyMax / 100}元；单门店每日可消耗数量 ${Limit.dayCountLimit} - ${Limit.dayCountLimitMax}`
  }
}

export default FormatCoupon
