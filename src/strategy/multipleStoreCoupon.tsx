import { ActivityTypeEnum } from "../constants";
import { ActivityStrategy, InvestmentActivityDetailDTO } from "../models";
import { CableBase } from "./base";
import { auditServiceForCoupon } from "./service";
import { checkNextTimeForAlpha, genericCreateActivity, gotoLegacyActivityDetailByDefault } from "./mixins";
import { maskMS } from "../common";

const MultipleStoreCoupon: ActivityStrategy = {
  activityType: ActivityTypeEnum.STORE_REDUCTION_MULTI_COUPON,
  cable: {
    ...CableBase,
    workspace: 'ExukovQrae6xWn2rt4NgG',
    model: '4dTY1TFhyUZ85YhkmfYJvw',
    async checkNext({ step, dataStore }, creationContext, history) {
      if (step.name === "baseinfo") {
        const timeError = await checkNextTimeForAlpha(
          { step, dataStore },
          creationContext
        );
        if (timeError) {
          return timeError;
        }
      }
      if (step.name === 'playdata') {
        const { baseinfo, playdata, scope } = dataStore.data;
        const payload = {
          // 活动类型信息
          templateId: creationContext.templateId,
          investmentType: creationContext.investmentType,
          investmentSchema: creationContext.investmentSchema,
          activityType: ActivityTypeEnum.STORE_REDUCTION_MULTI_COUPON,

          // 基本信息
          name: baseinfo.name,
          description: baseinfo.description,
          remark: baseinfo.remark,
          beginTime: baseinfo.activityTime.start.toDate().getTime(),
          endTime: maskMS(baseinfo.activityTime.end.toDate().getTime()),
          allowCancel: baseinfo.allowCancel,
          signUpShopType: baseinfo.signUpShopType,
          signUpStartTime: baseinfo.signupTime.start.toDate().getTime(),
          signUpEndTime: maskMS(baseinfo.signupTime.end.toDate().getTime()),
          activityPeriodRequestList: [{
            weekday: baseinfo.activityTime.weeks.join(','),
            openTime: baseinfo.activityTime.range.start + ':00',
            closeTime: baseinfo.activityTime.range.end + ':00'
          }],
          subsidyType: creationContext?.subsidyType,
          manageOrgList: baseinfo?.manageOrgList,

          // 招商范围信息
          shopPoolId: scope.pool.value,
          shopPoolName: scope.pool.label,
          activityBrandInfoDTOList: scope.brandList,

          // 玩法信息
          dataMap: {
            resource: {
              ...playdata.snapshot,
              componentModelUuid: '2alMPVXMRob9juLoKD4zMc'
            }
          },

          // 不限制库存
          stockType: 0,
        };
        return await genericCreateActivity(payload, history, creationContext);
      }
    },
    auditService: auditServiceForCoupon
  },
  isSupportAgent: false,
  isOfflineEnabled: function (state: number): boolean {
    // 活动状态: 1:未开始 2:活动中 3:活动暂停 4:活动取消 5:活动结束 8: 预算熔断
    return [1, 2, 3, 8].includes(state);
  },
  isDingtalkLinkEnabled: () => false,
  hasAuditMemberList: () => false,
  isSupportModifyStock: () => false,
  isDetailInviteStatsOnMainActivityTableVisible: true,
  renderPlayDataInfoForTableView: function (): JSX.Element {
    return <></>;
  },
  renderActivityRuleForTable() {
    return '';
  },
  gotoLegacyDetail: function (act: InvestmentActivityDetailDTO, scene: "lookup" | "examine"): void {
    gotoLegacyActivityDetailByDefault(act, scene);
  },
  renderActivityRuleForTableLegacy: function (marketPlay: { instance: { fields: any; }; }) {
    const fields = marketPlay?.instance?.fields;
    const rule1 = JSON.parse(fields.rule1);
    const rule2 = JSON.parse(fields.rule2);
    const rule3 = JSON.parse(fields.rule3);
    return (
      <>
        <div>
          <div>【第一张劵】</div>
          <div>满{rule1.saleFixed.condition / 100}减{rule1.saleFixed.discount / 100}，平台补贴{rule1.saleFixed.elemeSubsidy / 100}元</div>
        </div>
        <div>
          <div>【第二张劵】</div>
          <div>满{rule2.saleFixed.condition / 100}减{rule2.saleFixed.discount / 100}，平台补贴{rule2.saleFixed.elemeSubsidy / 100}元</div>
        </div>
        <div>
          <div>【第三张劵】</div>
          <div>满${rule3.saleFixed.condition / 100}减{rule3.saleFixed.discount / 100}，平台补贴${rule3.saleFixed.elemeSubsidy / 100}元
          </div>
        </div>
      </>
    );
    const text = `\n\n`;
    return text;
  }
};

export default MultipleStoreCoupon;
