import { ActivityTypeEnum } from "../constants"
import { BigNumber } from "bignumber.js";
import { ActivityStrategy } from "../models"
import { CableBase } from "./base"
import { auditServiceForCoupon } from "./service"

interface TripartiteMixtureType {
  activityType?:number;
  periods? :Array<{
    closeTime: string;
    openTime: string;
    weekday: string;
  }>;
  activityMarketingInfo: {
    userScope: number;  // 人群限制
    deliveryType: number; // 配送渠道
    deliveryChannel: number;  // 优惠渠道
    budgetId?: number;  // 预算id
    budgetName?: string;  // 预算名称
    availableDays?: number;   // 领取之日N天内有效
    availableStartTime?: number | string; // 券实例可用开始时间
    availableEndTime?: number | string; // 券实例可用结束时间
    couponLogo?: string; // 券图片
    couponName?: string; // 券名称
    brandBudgetId?: number; // 品牌预算id
    brandBudgetName?: string;  // 品牌预算名称
    storeCategoryForApp?: string; // 店内分类名称
  };

  couponGroupRuleList: Array<{
    settingId?: number;  //  活动实例id
    activityRule?: {    //  优惠规则
      condition?: number; // 门槛金额，单品券活动下无意义
      discount?: number;  // 优惠金额,单品券活动下，表示券后价，即一口价, 品类券活动下，表示优惠金额
      platformSubsidy?: number; // 平台补贴金额
      shopSubsidy?: number;  // 商户补贴金额，单品券活动下无意义
      brandSubsidy?: number;  // 品牌补贴
    };
    activityLimitRule?: {  //  限制条件
      totalCountLimit?: number; // 券库存限制规则
      dayCountLimit?: number;
      userTotalCountLimit?: number; // 券领券限制规则
      userDayCountLimit?: number;
    };
  }>
}

function isValueSet(v: any) {
  return v !== null && v !== undefined && v !== ''
}

function formatFenToYuan(money: number | string) {
  return new BigNumber(money).dividedBy(100).toNumber();
}

function renderActivityRuleForTable(viewJson: string) {
  try {
    const data: TripartiteMixtureType = JSON.parse(viewJson)

    let list = data?.couponGroupRuleList
    if (!list) {
      return ''
    }

    return <>
      {list?.map((item: any, i: number) => {
        let platformSubsidyStr: any = '';
        let brandSubsidyStr: any = '';
        let shopSubsidyStr: any = '';
        if (isValueSet(item?.activityRule?.platformSubsidy)) {
          platformSubsidyStr = <>,平台补贴 <span style={{wordBreak: 'break-word'}}>{formatFenToYuan(item?.activityRule?.platformSubsidy)}</span> 元</>
        }
        if (isValueSet(item?.activityRule?.brandSubsidy)) {
          brandSubsidyStr = <>,品牌补贴 <span style={{wordBreak: 'break-word'}}>{formatFenToYuan(item?.activityRule?.brandSubsidy)}</span> 元</>
        }
        if (isValueSet(item?.activityRule?.shopSubsidy)) {
          shopSubsidyStr = <>,商家补贴 <span style={{wordBreak: 'break-word'}}>{formatFenToYuan(item?.activityRule?.shopSubsidy)}</span> 元</>
        }

        return (
          <span
            key={item.activityRule.settingId}
            className="three-way-rule-base-font"
          >
            {i > 0 && <span>&nbsp;&nbsp;&nbsp;&nbsp;</span>}档位{i + 1}：满 <span style={{wordBreak: 'break-word'}}>{formatFenToYuan(item?.activityRule?.condition)}</span> 减{' '}
            <span style={{wordBreak: 'break-word'}}>{formatFenToYuan(item?.activityRule?.discount)}</span> 元{platformSubsidyStr}{brandSubsidyStr}{shopSubsidyStr}。
          </span>
        );
      })}
    </>
  } catch (e) {
    console.error(e)
    return ''
  }
}

function renderActivityRuleForTableLegacy(marketPlay: any) {
  const fields = marketPlay.instance.fields
  const rule = JSON.parse(fields.rule)
  const { saleFixed, saleRange } = rule; // 固定门槛 , 优惠力度
  let ret = '-'
  if (saleFixed) {
    ret = `满${saleFixed.condition / 100} 减${saleFixed.discount / 100}元，平台补贴${saleFixed.elemeSubsidy / 100}元`;
    if (saleFixed.agentSubsidy) {
      return ret += `，代理商补贴${saleFixed.agentSubsidy / 100}元`
    }
  }
  if (saleRange) {
    if (saleRange.minCondition === saleRange.maxCondition) {
      // 门槛金额最大值最小值相等，合并区间，只展示一个门槛金额
      ret = `满${saleRange.minCondition / 100}元，减${saleRange.minDiscount / 100} - ${saleRange.maxDiscount / 100}元，平台补贴最高${saleRange.maxElemeSubsidy / 100}元`;
      if (saleRange.maxAgentSubsidy) {
        ret += `，代理商补贴最高${saleRange.maxAgentSubsidy / 100}元`
      }
    } else {
      ret = `满${saleRange.minCondition / 100} - ${saleRange.maxCondition / 100}元，减${saleRange.minDiscount / 100} - ${saleRange.maxDiscount / 100}元，平台补贴最高${saleRange.maxElemeSubsidy / 100}元`;
      if (saleRange.maxAgentSubsidy) {
        ret += `，代理商补贴最高${saleRange.maxAgentSubsidy / 100}元`
      }
    }
  }
  return ret;
}

/**
 * 三方混补
 */
const TripartiteMixture: ActivityStrategy = {
  activityType: ActivityTypeEnum.TRIPARTITE_MIXTURE,
  cable: {
    ...CableBase,
    workspace: 'ExukovQrae6xWn2rt4NgG',
    model: 'ajPmhBKyTjA7szaIesD39B',
    // 三方混补没有创建流程
    checkNext: async function cableCheckNext() {
    },
    auditService: auditServiceForCoupon
  },
  isOfflineEnabled: () => false,
  isSupportAgent: false,
  isDingtalkLinkEnabled: () => false,
  isSupportModifyStock: () => false,
  isDetailInviteStatsOnMainActivityTableVisible: true,
  renderPlayDataInfoForTableView: function (): JSX.Element {
    return <></>;
  },
  renderActivityRuleForTable,
  gotoLegacyDetail: function (): void {
    // gotoLegacyActivityDetailByDefault(act, scene);
  },
  renderActivityRuleForTableLegacy: function (marketPlay: { instance: { fields: any; }; }) {
    return renderActivityRuleForTableLegacy(marketPlay);
  },
  checkBtnVisible: true,
  hasAuditMemberList: () => false,
}
export default TripartiteMixture

