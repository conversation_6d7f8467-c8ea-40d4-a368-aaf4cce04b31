import moment from "moment";
import { isValueSet, parseJsonSafe } from "../../common";
import { ActivityTypeEnum } from "../../constants";

// 详情出参数
interface DetailViewJSON {
  activityType: ActivityTypeEnum.ITEM_DELIVERY_REDUCTION;
  activityMarketingInfo: {
    budgetId?: string;
    budgetName?: string;
    userScope: number;
    deliveryType: number;
    trafficChannel?: number; // ET 新增字段，老活动没有
    bundingGroup?: string; // 关联免单券标记 ET之后的关联免单券需求后加字段，老活动没有。
  };
  activityRule: {
    discountCny: string,
    discountType: 1,
    platformSubsidyCny: string;
    shopSubsidyCny?: string; // 商家补贴，ET之后的关联免单券需求后加字段，老活动没有
  };
  activityLimitRule: {
    totalCountLimit?: number; // 活动期间总库存， ET 新增字段，老活动没有
    dayCountLimit?: number; // 活动期间每天库存， ET 新增字段，老活动没有
    userDayCountLimit?: number; // 每人每日限领， ET 新增字段，老活动没有
    userTotalCountLimit?: number; // 活动期间每人限领， ET 新增字段，老活动没有
  }
}


export const formatViewJson2MarketPlay = ({
  viewDto,
  baseInfo,
}: {
  viewDto: DetailViewJSON;
  baseInfo: {
    name?: string,
    createdUserId?: string,
    createdUserName?: string,
    beginTime?: string,
    endTime?: string,
  };
}) => {
  const {
    activityRule,
    activityMarketingInfo,
    activityLimitRule = {} 
  } = viewDto;

  let creator = {}; // 创建人名称、创建人id
  let dateRange = {};
  const name = baseInfo.name; // 玩法名称
  let rule: any = {};
  let userScope = ""; // 用户类型
  let budget = {}; //  预算
  let stockLimit = {};
  let userLimit = {};
  if (isValueSet(activityMarketingInfo.userScope)) {
    userScope = activityMarketingInfo.userScope + "";
  }
  if (
    isValueSet(baseInfo.createdUserId) &&
    isValueSet(baseInfo.createdUserName)
  ) {
    creator = {
      name: `${baseInfo.createdUserName}`,
      id: baseInfo.createdUserId,
    };
  }
  if (isValueSet(baseInfo.beginTime) && isValueSet(baseInfo.endTime)) {
    dateRange = {
      start: moment(baseInfo.beginTime).format("YYYY-MM-DD HH:mm:ss"),
      end: moment(baseInfo.endTime).format("YYYY-MM-DD HH:mm:ss"),
    };
  }
  if (isValueSet(activityRule.discountCny) && isValueSet(activityRule.platformSubsidyCny)) {
    rule = {
      discount: activityRule.discountCny,
      elemeSubsidy: activityRule.platformSubsidyCny,
      shopSubsidyCny: activityRule?.shopSubsidyCny
    };
  }
  if (
    isValueSet(activityMarketingInfo.budgetName) &&
    isValueSet(activityMarketingInfo.budgetId)
  ) {
    budget = {
      budgetName: activityMarketingInfo.budgetName,
      budgetId: "" + activityMarketingInfo.budgetId,
    };
  }
   if (isValueSet(activityLimitRule.dayCountLimit)) {
    stockLimit = {
      dayLimit: { value: activityLimitRule.dayCountLimit },
    };
  }
  if (isValueSet(activityLimitRule.totalCountLimit)) {
    stockLimit = {
      ...stockLimit,
      totalLimit: { value: activityLimitRule.totalCountLimit },
    };
  }
  if (isValueSet(activityLimitRule.userDayCountLimit)) {
    userLimit = {
      dayLimit: { value: activityLimitRule.userDayCountLimit },
    };
  }
  if (isValueSet(activityLimitRule.userTotalCountLimit)) {
    userLimit = {
      ...userLimit,
      totalLimit: { value: activityLimitRule.userTotalCountLimit },
    };
  }

  return {
    creator: JSON.stringify(creator),
    name: name,
    dateRange: JSON.stringify(dateRange),
    rule: JSON.stringify(rule),
    userScope,
    effectiveChannel: "" + activityMarketingInfo.deliveryType,
    budget: JSON.stringify(budget),
    trafficChannel: "" + activityMarketingInfo?.trafficChannel, 
    stockLimit: JSON.stringify(stockLimit),
    userLimit: JSON.stringify(userLimit),
    bundingGroup: activityMarketingInfo?.bundingGroup || "-",
  };
};

//提交参数
interface submitViewJSON extends DetailViewJSON {
  // 提交时的时间段
  period: {
    openTime: string;
    closeTime: string;
  }[];
  weekday: string;
}
export const formatMarketPlay2ViewJson = ({ baseinfo, formData }: {
  baseinfo: {
    activityTime: {
      weeks: number[];
      batchRange: {
        type: string;
        list: {
          start: string;
          end: string;
        }[];
      };
    };
  };
  formData: {
    budget: string;
    userScope: string;
    effectiveChannel: string;
    trafficChannel?: number;
    rule: string;
    bundingGroup?: string;
    stockLimit: {
      dayLimit?: {
        value: number;
      };
      totalLimit?: {
        value: number;
      };
    };
    userLimit: {
      dayLimit?: {
        value: number;
      };
      totalLimit?: {
        value: number;
      };
    };
  };
}): submitViewJSON => {

  const budget = formData.budget && parseJsonSafe(formData.budget);
  const userScope = Number(formData.userScope)
  const effectiveChannel = Number(formData.effectiveChannel)
  const rule = formData.rule && parseJsonSafe(formData.rule);
  const userLimit = formData.userLimit && parseJsonSafe(formData.userLimit);
  const stockLimit = formData.stockLimit && parseJsonSafe(formData.stockLimit);
  const trafficChannel = formData.trafficChannel;
  const bundingGroup = formData?.bundingGroup

  return {
    activityType: ActivityTypeEnum.ITEM_DELIVERY_REDUCTION,
    activityMarketingInfo: {
      budgetId: budget?.budgetId,
      budgetName: budget?.budgetName,
      userScope,
      deliveryType: effectiveChannel,
      trafficChannel: trafficChannel,
      bundingGroup,
    },
    activityRule: {
      discountType: 1, // 减钱 固定值
      discountCny: '' + rule?.discount,
      platformSubsidyCny: '' + rule?.elemeSubsidy,
      shopSubsidyCny: isValueSet(rule?.shopSubsidyCny) ?  '' + rule.shopSubsidyCny : undefined,
    },
    activityLimitRule: {
      dayCountLimit: stockLimit?.dayLimit?.value, // 每日活动库存
      totalCountLimit: stockLimit?.totalLimit?.value, // 总活动库存
      userDayCountLimit: userLimit?.dayLimit?.value, // 每用户每商品每天限购X个
      userTotalCountLimit: userLimit?.totalLimit?.value, // 每个用户每个商品限购X个
    },
    weekday: (baseinfo?.activityTime?.weeks || []).join(","),
    period: (baseinfo?.activityTime?.batchRange?.list || []).map(
      (r: any) => {
        return {
          openTime: r.start + ":00",
          closeTime: r.end + ":59",
        };
      }
    ),
  }
}
