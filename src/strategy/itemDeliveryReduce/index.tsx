/**
 * 运费满减(商品类)反圈  1000039
 */
import { Message } from "@alifd/next";
import * as api from "../../api"
import { maskMS, parseJsonSafe } from '../../common';
import { ActivityTypeEnum } from '../../constants';
import { ActivityStrategy } from '../../models';
import { CableBase } from '../base';
import { checkNextTimeForAlpha, genericCreateActivity } from '../mixins';
import { formatViewJson2MarketPlay, formatMarketPlay2ViewJson } from './formatUtils';
import BigNumber from "bignumber.js";


const ItemDeliveryReduce: ActivityStrategy = {
  activityType: ActivityTypeEnum.ITEM_DELIVERY_REDUCTION,
  generation: 'v2',
  cable: {
    ...CableBase,
    workspace: 'ExukovQrae6xWn2rt4NgG',
    model: '3aBPIYiOcly7Kzd7cGYfyt',
    async checkNext({ step, dataStore }, creationContext, history) {
      if (step.name === "baseinfo") {
        const timeError = await checkNextTimeForAlpha(
          { step, dataStore },
          creationContext
        );
        if (timeError) {
          return timeError;
        }
      }
      if (step.name === 'playdata') {
        const { baseinfo, scope, playdata } = dataStore.data;
        // 正常 discount 一定等于 elemeSubsidy + shopSubsidyCny。如果用到检查配置表单是否有问题
        const { discount, elemeSubsidy, shopSubsidyCny }: any = playdata
          ?.snapshot?.data?.rule
          ? parseJsonSafe(playdata.snapshot.data.rule)
          : {};
        if (
          !(new BigNumber(shopSubsidyCny).isEqualTo(
            new BigNumber(discount).minus(new BigNumber(elemeSubsidy))
          ))
        ) {
          Message.error("!商户补贴 = 运费减免金额 - 平台补贴，数据错误");
          return;
        }

        const viewJson = formatMarketPlay2ViewJson({
          baseinfo: {
            activityTime: baseinfo.activityTime
          },
          formData: playdata.snapshot.data
        })
        const payload = {
          // 活动类型信息
          templateId: creationContext.templateId,
          investmentType: creationContext.investmentType,
          investmentSchema: creationContext.investmentSchema,
          activityType: ActivityTypeEnum.ITEM_DELIVERY_REDUCTION,
          activitySignType: 'REVERSE_SIGN',
          // 基本信息
          name: baseinfo.name,
          description: baseinfo.description,
          remark: baseinfo.remark,
          beginTime: baseinfo.activityTime.start.toDate().getTime(),
          endTime: maskMS(baseinfo.activityTime.end.toDate().getTime()),
          allowCancel: baseinfo.allowCancel,
          subsidyType: creationContext?.subsidyType,
          manageOrgList: baseinfo?.manageOrgList,

          signUpShopType: baseinfo.signUpShopType,

          // 招商范围信息
          itemPoolIdList: scope.itemPoolIdList.map((item: any) => item.value),
          commodityAuditType: scope.commodityAuditType,
          itemPoolProductSource: 30,//#池子来源，30=选投商品池 固定值,后端用来区分校验商品池

          // 玩法信息
          viewJson: JSON.stringify(viewJson),
        };
        return await genericCreateActivity(payload, history, creationContext);
      }
    },
  },

  isOfflineEnabled: function (state: number): boolean {
    // 活动状态: 1:未开始 2:活动中 3:活动暂停 4:活动取消 5:活动结束 8: 预算熔断
    return [1, 2, 3, 8].includes(state);
  },

  isDingtalkLinkEnabled: () => false,
  hasAuditMemberList: () => false,
  isSupportModifyStock: () => false,
  isDetailInviteStatsOnMainActivityTableVisible: true,
  isSupportAgent: false,
  renderPlayDataInfoForTableView: function (): JSX.Element {
    return <></>;
  },
  renderActivityRuleForTable(viewJson: any) {
    if (!viewJson) {
      return '';
    }
    const data: any = JSON.parse(viewJson);

    return <div>减{data.activityRule.discountCny}元，平台补贴{data.activityRule.platformSubsidyCny}元</div>;
  },

  gotoLegacyDetail() {
    return undefined;
  },
  renderActivityRuleForTableLegacy() {
    return <></>;
  },

  async marketPlayMapper(viewJson: string, baseInfo: any) {
    try {
      const viewDto: any = JSON.parse(viewJson);
      let model = {};
      const fields = formatViewJson2MarketPlay({
        viewDto,
        baseInfo: {
          name: baseInfo.name,
          createdUserId: baseInfo.createdUserId,
          createdUserName: baseInfo.createdUserName,
          beginTime: baseInfo.beginTime,
          endTime: baseInfo.endTime,
        },
      });
      model = await api.subActivity.getComponentDataModel(
        "3oNNWxkJYzc6eWQyKz5Lp2",
        "6EpNCnXx1Y28xTPzDgOOVz"
      );
      const marketPlay = {
        instance: { fields },
        model: model,
      };
      return marketPlay;
    } catch (e) {
      return null;
    }
  },
  isHideSignUpInfo: true,
  addCustomStyle(styleTag, mode) {
    if(mode === 'detail') {
      return;
    }
    styleTag.setAttribute("data-inject", "custom-styles-item-delivery-reduce");

       styleTag.innerHTML = `
      [data-field-path="data.promptForPlay"] .label:after {
          display: none !important;
      }
    `;
  },
};

export default ItemDeliveryReduce;
