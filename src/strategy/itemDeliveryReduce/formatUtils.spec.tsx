import { formatViewJson2MarketPlay, formatMarketPlay2ViewJson } from "./formatUtils";

describe('itemDeliveryReduce', () => {
  describe("formatUtils", () => {

    describe('formatMarketPlay2ViewJson', () => {
      test('日常模版', () => {
        const viewJson = formatMarketPlay2ViewJson({
          "baseinfo": {
            "activityTime": {
              "weeks": [
                2,
                3
              ],
              "batchRange": {
                "type": "all",
                "list": [
                  {
                    "start": "00:00",
                    "end": "23:59"
                  }
                ]
              },
            },
          },
          "formData": {
            "effectiveChannel": "1",
            "rule": "{\"discount\":3,\"elemeSubsidy\":3}",
            "userScope": "9",
            "budget": "{\"budgetId\":\"400000000005876069\",\"budgetName\":\"招商zzg活动2023预算\"}"
          }
        })
        expect(viewJson).toStrictEqual({
          "activityType": 1000039,
          "activityMarketingInfo": {
            "budgetId": "400000000005876069",
            "budgetName": "招商zzg活动2023预算",
            "userScope": 9,
            "deliveryType": 1
          },
          "activityRule": {
            "discountType": 1,
            "discountCny": "3",
            "platformSubsidyCny": "3"
          },
          "weekday": "2,3",
          "period": [
            {
              "openTime": "00:00:00",
              "closeTime": "23:59:59"
            }
          ]
        })
      })
    })

    describe("formatViewJson2MarketPlay", () => {
      test("日常模版", () => {

        const baseInfo = {
          "name": "运费满减（商品类-无限制",
          "createdUserId": "WB807691",
          "createdUserName": "杜洋阳",
          "beginTime": "2024-01-29T16:00:00.000Z",
          "endTime": "2024-01-31T15:59:59.000Z",
        }

        const viewJson = formatViewJson2MarketPlay({
          "viewDto": {
            "activityMarketingInfo": {
              "budgetId": "400000000005876069",
              "budgetName": "招商zzg活动2023预算",
              "deliveryType": 1,
              "userScope": 9
            },
            "activityRule": {
              "discountCny": "1",
              "discountType": 1,
              "platformSubsidyCny": "1"
            },
            "activityType": 1000039,
          },
          "baseInfo": {
            name: baseInfo.name,
            createdUserId: baseInfo.createdUserId,
            createdUserName: baseInfo.createdUserName,
            beginTime: baseInfo.beginTime,
            endTime: baseInfo.endTime,
          }
        })
        expect(viewJson).toStrictEqual({
          "creator": "{\"name\":\"杜洋阳\",\"id\":\"WB807691\"}",
          "name": "运费满减（商品类-无限制",
          "dateRange": "{\"start\":\"2024-01-30 00:00:00\",\"end\":\"2024-01-31 23:59:59\"}",
          "rule": "{\"discount\":\"1\",\"elemeSubsidy\":\"1\"}",
          "userScope": "9",
          "effectiveChannel": "1",
          "budget": "{\"budgetName\":\"招商zzg活动2023预算\",\"budgetId\":\"400000000005876069\"}"
        })
      });
    })

  })
})