import { ActivityTypeEnum } from "../../constants";

export interface DeliveryReduceTableWineViewJson {
  activityType: ActivityTypeEnum.DELIVERY_REDUCE_TABLE_WINE;
  activityRule?: Array<{
    period: {
      weekday: string;
      openTime: string;
      closeTime: string;
    }
    rules: Array<{
      condition: number;
      discount: number;
      platformSubsidy: number;
    }>
  }>;
  activityMarketingInfo: {
    userScope: number;
    budgetId?: number;
    budgetName?: string;
    deliveryChannel: number;
  };
  activityLimitRule?: {
    userDayCountLimit: number;
  };
}