import { ActivityTypeEnum } from "../../constants";
import { formatViewJson2MarketPlay, formatMarketPlay2ViewJson } from "./formatUtils";

describe('itemDeliveryReduce', () => {
  describe("formatUtils", () => {

    describe('formatMarketPlay2ViewJson', () => {
      test('日常模版', () => {
        const viewJson = formatMarketPlay2ViewJson({
          "formData": {
            "userScope": "11",
            "deliveryChannel": "22",
            "periodAndRule": "[{\"weekday\":\"1,2,3,4,5,6\",\"timeRange\":{\"start\":\"00:00:00\",\"end\":\"23:59:59\"},\"rule\":[{\"condition\":10000,\"discount\":100,\"platformSubsidy\":100}]}]",
            "userDayOrderLimit": "{\"limit\":\"1\"}",
            "budget": "{\"budgetName\":\"杜洋阳招商活动预算640\",\"budgetId\":\"400000000007809540\"}"
          }
        })
        expect(viewJson).toStrictEqual({
          "activityType": ActivityTypeEnum.DELIVERY_REDUCE_TABLE_WINE,
          "activityMarketingInfo": {
            "budgetId": "400000000007809540",
            "budgetName": "杜洋阳招商活动预算640",
            "userScope": 11,
            "deliveryChannel": 22
          },
          "activityRule": [
            {
              "period": {
                "weekday": "1,2,3,4,5,6",
                "openTime": "00:00:00",
                "closeTime": "23:59:59"
              },
              "rules": [
                {
                  "condition": 10000,
                  "discount": 100,
                  "platformSubsidy": 100
                }
              ]
            }
          ],
          "activityLimitRule": {
            "userDayCountLimit": "1"
          }
        })
      })
    })

    describe("formatViewJson2MarketPlay", () => {
      test("日常模版", () => {

        const baseInfo = {
          "name": "test-餐酒",
          "createdUserId": "WB807691",
          "createdUserName": "杜洋阳",
          "beginTime": "2024-07-07T16:00:00.000Z",
          "endTime": "2024-07-13T15:59:59.000Z",
        }

        const viewJson = formatViewJson2MarketPlay({
          "viewDto": {
            "activityLimitRule": {
              "userDayCountLimit": 1
            },
            "activityMarketingInfo": {
              "budgetId": 400000000007809540,
              "budgetName": "杜洋阳招商活动预算640",
              "userScope": 0,
              "deliveryChannel": 22
            },
            "activityRule": [
              {
                "period": {
                  "closeTime": "23:59:59",
                  "openTime": "00:00:00",
                  "weekday": "1,2,3,4,5,6"
                },
                "rules": [
                  {
                    "condition": 10000,
                    "discount": 100,
                    "platformSubsidy": 100
                  }
                ]
              }
            ],
            "activityType": ActivityTypeEnum.DELIVERY_REDUCE_TABLE_WINE,
          },
          "baseInfo": {
            name: baseInfo.name,
            createdUserId: baseInfo.createdUserId,
            createdUserName: baseInfo.createdUserName,
            beginTime: baseInfo.beginTime,
            endTime: baseInfo.endTime,
          }
        })
        expect(viewJson).toStrictEqual({
          "creator": "{\"name\":\"杜洋阳\",\"id\":\"WB807691\"}",
          "name": "test-餐酒",
          "dateRange": "{\"start\":\"2024-07-08 00:00:00\",\"end\":\"2024-07-13 23:59:59\"}",
          "deliveryChannel": "22",
          "periodAndRule": "[{\"weekday\":\"1,2,3,4,5,6\",\"timeRange\":{\"start\":\"00:00:00\",\"end\":\"23:59:59\"},\"rule\":[{\"condition\":10000,\"discount\":100,\"platformSubsidy\":100}]}]",
          "userScope": "0",
          "userDayOrderLimit": "{\"limit\":1}",
          "budget": "{\"budgetName\":\"杜洋阳招商活动预算640\",\"budgetId\":\"400000000007809540\"}"
        })
      });
    })

  })
})