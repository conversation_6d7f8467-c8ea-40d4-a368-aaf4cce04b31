import BigNumber from "bignumber.js";

import { ActivityTypeEnum, InvestmentTypeEnum } from "../../constants";
import { ActivityStrategy, InvestmentActivityDetailDTO } from "../../models";
import { CableBase, resolveCreateActivityCommonData } from "../base";
import { 
  checkMarketRules,
  checkMktRulesForMultiBusinessFormAndConfirmIfNeed,
  checkNextCommoditySignupRuleForAlpha,
  checkNextTimeForAlpha,
  genericCreateActivity, 
  gotoLegacyActivityDetailByDefault,
  resolveCommodityAuditProps,
} from "../mixins";
import { isValueSet } from "../../common";
import { transformFormData2ViewJson } from "./helper";

interface ItemDiscountPlaydata {
  activityType: number;
  periodDTOList: {
    openTime: string;
    closeTime: string;
  }[]
  weekday: string;
  activityRule: {
    // 折扣单位换算关系 55 折 => 550, 8 折 => 800
    discount: number;
    discountMin: number;
    discountMax: number;
    platformSubsidyPercentForItemSalePrice: number;
    platformSubsidyMax: number;
  };
}

function d100(v: number) {
  return new BigNumber(v).dividedBy(100).toNumber();
}

const ItemDiscount: ActivityStrategy = {
  activityType: ActivityTypeEnum.SKU_REAL_DISCOUNT,
  cable: {
    ...CableBase,
    workspace: "ExukovQrae6xWn2rt4NgG",
    model: "3bGVRtAEetv91pLAXI2SjX",
    async checkNext(checkNextContext, creationContext, history) {
      const { step, dataStore } = checkNextContext;
       if (step.name === "baseinfo") {
         const timeError = await checkNextTimeForAlpha(
           { step, dataStore },
           creationContext
         );
         if (timeError) {
           return timeError;
         }
       }
      if (step.name === "baseinfo") {
        // 重置 auditUpgradeForMultiMktRule，避免重新回到第一步重新选择业务线和业态的情况
        dataStore._session.auditUpgradeForMultiMktRule = undefined;
        // 校验是否跨业态，并且返回弹窗点击结果
        const { conflicted, userConfirm } =
          await checkMktRulesForMultiBusinessFormAndConfirmIfNeed(
            creationContext,
            dataStore.data.baseinfo
          );
        if (!conflicted) {
          // 没有错误信息，不需要拦截进入下一步
          return;
        }
        if (userConfirm) {
          // 有错误，并且用户二次确认了，确认需提交审批
          dataStore._session.auditUpgradeForMultiMktRule = true;
          // 不需要拦截进入下一步
          return;
        } else {
          // 有错误，单用户未二次确认，页面停留在当前步骤
          return { silent: true };
        }
      }
       if (step.name === "scope") {
         const commoditySignupRuleError =
           await checkNextCommoditySignupRuleForAlpha(
             checkNextContext,
             creationContext
           );
         if (commoditySignupRuleError) {
           return commoditySignupRuleError;
         }
       }
      if (step.name === "stock") {
        const { stock, scope, baseinfo } = dataStore.data;
        const commonData = resolveCreateActivityCommonData(
          ActivityTypeEnum.SKU_REAL_DISCOUNT,
          checkNextContext,
          creationContext
        );

        const commodityAuditProps: any = resolveCommodityAuditProps(scope) || {};
        // 这里的 viewJson 目前只用于校验中控台行业规则，创建流程钱后端都用的组件，还没迁移viewJson
        const viewJsonDto = transformFormData2ViewJson({
          baseinfo,
          formData: dataStore.data.playdata.snapshot.data,
        });

        const payload: any = {
          ...commonData,

          auditMemberList:
            baseinfo.auditMemberList?.length > 0
              ? baseinfo.auditMemberList
              : null,
              
          // 自动审核规则
          ...commodityAuditProps,

          // 降套补 商品清退规则
          commodityRemoveCondition: { removeCondition: scope.removeCondition },

          // 报名规则
          reviewRuleId: scope.rule?.ruleId,
          reviewRuleName: scope.rule?.name,

          // 库存设置
          stockType: stock.stockType,
          stockMin: stock.range.start,
          stockMax: stock.range.end,
          requestId: stock.stockType === 2 ? stock.requestId : undefined,
        };
        if (creationContext.investmentType === InvestmentTypeEnum.LANMU) {
          // 栏目招商
          payload.signUpMinCycleTime = baseinfo.cycleTime.min;
          payload.signUpMaxCycleTime = baseinfo.cycleTime.max;
        }
        payload.dataMap.resource.componentModelUuid = "6hGvtVWiRWK8G3pztZS0AV";

        const {
          interrupt,
          auditUpgradeForCheckMktRuleUnPass,
          auditUpgradeForMultiMktRule,
        } = await checkMarketRules(
          dataStore,
          creationContext,
          JSON.stringify(viewJsonDto)
        );
        if (interrupt) {
          return { silent: true };
        }

        payload.auditUpgradeForCheckMktRuleUnPass = auditUpgradeForCheckMktRuleUnPass;
        payload.auditUpgradeForMultiMktRule = auditUpgradeForMultiMktRule;  

        return await genericCreateActivity(payload, history, creationContext);
      }
    },
  },
  hasAgentSubsidy() {
    return false;
  },
  isSupportAgent: false,
  isOfflineEnabled: function (state: number): boolean {
    // 活动状态: 1:未开始 2:活动中 3:活动暂停 4:活动取消 5:活动结束 8: 预算熔断
    return [1, 2, 3, 8].includes(state);
  },
  isDingtalkLinkEnabled: () => true,
  hasAuditMemberList: () => true,
  isItemAuditStatsVisible: true,
  isSupportModifyStock: () => false,
  isDetailInviteStatsOnMainActivityTableVisible: true,
  renderPlayDataInfoForTableView: function (): JSX.Element {
    return <></>;
  },
  gotoLegacyDetail: function (
    act: InvestmentActivityDetailDTO,
    scene: "lookup" | "examine"
  ): void {
    gotoLegacyActivityDetailByDefault(act, scene);
  },
  renderActivityRuleForTable(viewJson: any) {
    try {
      const data: ItemDiscountPlaydata = JSON.parse(viewJson);
      let result = [];
      if (
        isValueSet(data.activityRule.discountMin) &&
        isValueSet(data.activityRule.discountMax)
      ) {
        const {
          discountMin,
          discountMax,
          platformSubsidyPercentForItemSalePrice,
          platformSubsidyMax,
        } = data.activityRule;
        // 区间招商
        result.push(
          <>
            商品折扣区间 {d100(discountMin)} ~ {d100(discountMax)} 折， 平台补贴{" "}
            {d100(platformSubsidyPercentForItemSalePrice)} 折， 且平台补贴上限{" "}
            {d100(platformSubsidyMax)} 元
          </>
        );
      } else if (isValueSet(data.activityRule.discount)) {
        const {
          discount,
          platformSubsidyPercentForItemSalePrice,
          platformSubsidyMax,
        } = data.activityRule;
        // 固定招商
        result.push(
          <>
            商品折扣 {d100(discount)} 折，平台补贴
            {d100(
              platformSubsidyPercentForItemSalePrice
            )} 折，且平台补贴上限 {d100(platformSubsidyMax)} 元
          </>
        );
      }
      return (
        <>
          {result.map((r: any, i: number) => (
            <div key={i}>{r}</div>
          ))}
        </>
      );
    } catch (e) {
      console.error(e);
      return <></>;
    }
  },
  renderActivityRuleForTableLegacy: function () {
    return null;
  },
};

export default ItemDiscount;
