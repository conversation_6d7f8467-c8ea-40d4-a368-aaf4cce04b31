import { isValueSet, parseJsonSafe } from "common";
import { ActivityTypeEnum } from "../../constants";
import BigNumber from "bignumber.js";

interface ItemDiscountBaseInfo {
  activityTime: {
    weeks: number[];
    batchRange: {
      type: "all" | "custom";
      list: { start: string; end: string }[];
    };
  };
}

interface ItemDiscountFormData {
  budget: string;
  userLimit: string;
  discountRule: string;
  itemDiscountType: string;
  effectiveChannel: number;
  userScope: number;
}
interface ItemDiscountPlaydata {
  activityType: number;
  activityLimitRule: {
    userTotalCountLimit?: number;
    userDayCountLimit?: number;
  };
  activityMarketingInfo: {
    budgetId: string;
    budgetName: string;
    deliveryType: number;
    userScope: number;
  };
  activityRule: {
    discountType: number;
    discount?: number;
    discountMin?: number;
    discountMax?: number;
    platformSubsidyPercentForItemSalePrice: number;
    platformSubsidyMax: number;
  };
  weekday: string;
  periodList: {
    openTime: string;
    closeTime: string;
  }[];
}

interface Budget {
  budgetId: string;
  budgetName: string;
}

interface LimitValue {
  totalLimit?: {
    value: string;
  };
  dayLimit?: {
    value: string;
  };
}

interface discountRule {
  discountFixed?: {
    discount: number;
    elemeDiscount: number;
    maxElemeSubsidy: number;
  };
  discountRange?: {
    minDiscount: number;
    maxDiscount: number;
    maxElemeSubsidy: number;
    elemeDiscount: number;
  };
}
export function transformFormData2ViewJson(options: {
  baseinfo: ItemDiscountBaseInfo;
  formData: ItemDiscountFormData;
}): ItemDiscountPlaydata {
  const discountRule: discountRule = parseJsonSafe(
    options.formData.discountRule
  );
  const budget: Budget = options.formData.budget
    ? parseJsonSafe(options.formData.budget)
    : null;
  const userLimit: LimitValue = parseJsonSafe(options.formData.userLimit);
  const isRangeActivity = !isValueSet(discountRule.discountFixed?.discount);

  return {
    activityType: ActivityTypeEnum.SKU_REAL_DISCOUNT,
    activityLimitRule: {
      userTotalCountLimit: isValueSet(userLimit.totalLimit?.value)
        ? +userLimit.totalLimit?.value!
        : undefined,
      userDayCountLimit: isValueSet(userLimit.dayLimit?.value)
        ? +userLimit.dayLimit?.value!
        : undefined,
    },
    activityMarketingInfo: {
      budgetId: budget?.budgetId,
      budgetName: budget?.budgetName,
      deliveryType: +options.formData.effectiveChannel,
      userScope: +options.formData.userScope,
    },
    activityRule: {
      discountType: 2, // 前端写死成 2
      discount: !isRangeActivity
        ? discountRule.discountFixed?.discount
        : undefined,
      discountMin: isRangeActivity
        ? discountRule.discountRange?.minDiscount
        : undefined,
      discountMax: isRangeActivity
        ? discountRule.discountRange?.maxDiscount
        : undefined,
      platformSubsidyPercentForItemSalePrice: isRangeActivity
        ? discountRule.discountRange?.elemeDiscount!
        : discountRule.discountFixed?.elemeDiscount!,
      platformSubsidyMax: isRangeActivity
        ? new BigNumber(discountRule.discountRange?.maxElemeSubsidy!).toNumber()
        : new BigNumber(
            discountRule.discountFixed?.maxElemeSubsidy!
          ).toNumber(),
    },
    weekday: options.baseinfo.activityTime.weeks.join(","),
    periodList: options.baseinfo.activityTime.batchRange.list.map((item) => {
      return {
        openTime: item.start.slice(0, "HH:mm".length) + ":00",
        closeTime: item.end.slice(0, "HH:mm".length) + ":59",
      };
    }),
  };
}
