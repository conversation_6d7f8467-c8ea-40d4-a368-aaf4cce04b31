import { maskMS } from "../common";
import { ActivityTypeEnum } from "../constants";
import { ActivityStrategy, CheckNextContext, CreationContext } from "../models"
import { CableBase } from "./base";
import { genericCreateActivity } from "./mixins";
import { auditServiceForCoupon } from "./service"

interface MerchantEnterCouponRule {
  activityRule: Array<{
    condition?: number;
    discount?: number;
    platformSubsidy?: number;
  }>;
  activityMarketingInfo: {
    userScope: number;  // 固定 8
    deliveryType: number;
    deliveryChannel: number;
    couponName: string;
    budgetId?: number;
    budgetName?: string;
    availableDays?: number;
  };
  activityLimitRule: {
    totalCountLimit?: number;  // 固定7个9： 9,999,999 
    userTotalCountLimit?: number;  // 固定 1
  }
}

function renderActivityRuleForTable(viewJson: string) {
  try {
    const data: MerchantEnterCouponRule = JSON.parse(viewJson)
    const firstRule = data?.activityRule[0]
    if (!firstRule) {
      return ''
    }

    const r = firstRule
    return (
      <>
        满{r.condition as number / 100}元，
        减{r.discount as number / 100}元，
        平台补贴{r.platformSubsidy as number / 100}元
      </>
    )
  } catch (e) {
    console.error(e)
    return ''
  }
}

async function cableCheckNext(
  ctx: CheckNextContext,
  creationContext: CreationContext,
  history: any
) {
  const { step, dataStore } = ctx

  if (step.name === 'playdata') {
    const { baseinfo, scope, playdata } = dataStore.data

    const payload = {
      // 活动类型信息
      activityType: creationContext.activityType,
      templateId: creationContext.templateId,
      investmentType: creationContext.investmentType,
      investmentSchema: creationContext.investmentSchema,

      // 基本信息
      name: baseinfo.name,
      description: baseinfo.description,
      remark: baseinfo.remark,
      beginTime: baseinfo.activityTime.start.toDate().getTime(),
      endTime: maskMS(baseinfo.activityTime.end.toDate().getTime()),
      allowCancel: baseinfo.allowCancel,
      signUpShopType: baseinfo.signUpShopType,
      signUpStartTime: baseinfo.signupTime.start.toDate().getTime(),
      signUpEndTime: maskMS(baseinfo.signupTime.end.toDate().getTime()),
      subsidyType: creationContext?.subsidyType,
      manageOrgList: baseinfo?.manageOrgList,

      // 招商范围信息
      shopPoolId: scope.pool.value,
      shopPoolName: scope.pool.label,

      // 玩法信息
      dataMap: {
        resource: {
          ...playdata.snapshot,
          componentModelUuid: '2uwpH70W8Et5EIIy3gC6op'
        }
      },

      // 不限制库存
      stockType: 0
    }

    return await genericCreateActivity(payload, history, creationContext);
  }
}

const MerchantEnterCoupon: ActivityStrategy = {
  activityType: ActivityTypeEnum.MERCHANT_ENTER_COUPON,
  cable: {
    ...CableBase,
    workspace: 'ExukovQrae6xWn2rt4NgG',
    model: '9MkTiqHbaJV6wNTlMN9VmF',
    checkNext: cableCheckNext,
    auditService: auditServiceForCoupon
  },
  hasAgentSubsidy() { return false },
  isOfflineEnabled: function (state: number): boolean {
    // 活动状态: 1:未开始 2:活动中 3:活动暂停 4:活动取消 5:活动结束 8: 预算熔断
    return [1, 2, 3, 8].includes(state);
  },
  generation: 'v2',
  isSupportAgent: false,
  isDingtalkLinkEnabled: () => false,
  hasAuditMemberList: () => false,
  isSupportModifyStock: () => false,
  isDetailInviteStatsOnMainActivityTableVisible: true,
  renderPlayDataInfoForTableView: function (): JSX.Element {
    return <></>;
  },
  renderActivityRuleForTable,
  gotoLegacyDetail: function (): void {
    return;
  },
  renderActivityRuleForTableLegacy: function () {
    return null
  }
}

export default MerchantEnterCoupon;
