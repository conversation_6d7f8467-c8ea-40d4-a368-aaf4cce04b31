
import {
  ActivityTypeEnum,
} from "../../constants";
import { ActivityStrategy, InvestmentActivityDetailDTO } from "../../models";
import { CableBase,  } from "../base";
import {
  gotoLegacyActivityDetailByDefault,
} from "../mixins";


const ThreeSubsidyItemPromotion: ActivityStrategy = {
  activityType: ActivityTypeEnum.THREE_SUBSIDY_ITEM_PROMOTION,
  cable: {
    ...CableBase,
    workspace: 'ExukovQrae6xWn2rt4NgG',
    model: '1nR5MeL8hHZ6scnWGsQQFC',
    // 品牌商品特价没有创建流程
    checkNext: async function cableCheckNext() {
    },
  },

  hasAgentSubsidy() {
    return false;
  },
  isSupportAgent: false,
  isOfflineEnabled: function (state: number): boolean {
    // 活动状态: 1:未开始 2:活动中 3:活动暂停 4:活动取消 5:活动结束 8: 预算熔断
    return [1, 2, 3, 8].includes(state);
  },
  isCopyEnabled() {
    return false;
  },
  isDingtalkLinkEnabled: () => true,
  hasAuditMemberList: () => true,
  isSupportModifyStock: () => false,
  isItemAuditStatsVisible: true,
  isDetailInviteStatsOnMainActivityTableVisible: true,
  renderPlayDataInfoForTableView: function (): JSX.Element {
    return <></>;
  },
  gotoLegacyDetail: function (
    act: InvestmentActivityDetailDTO,
    scene: "lookup" | "examine"
  ): void {
    gotoLegacyActivityDetailByDefault(act, scene);
  },
  renderActivityRuleForTable(
  ) {
    return <>-</>;
  },
  renderActivityRuleForTableLegacy: function () {
    return <></>;
  }
};

export default ThreeSubsidyItemPromotion;
