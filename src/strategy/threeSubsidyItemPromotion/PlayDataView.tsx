import React, { useEffect, useState } from "react";
import { Subsidy, deliveryTypeStr } from "../../constants";
import { BudgetPickerDetail } from "@alife/carbon-biz-kunlun-zs/lib/components/BudgetPicker/BudgetPicker";
import * as api from "../../api";
import "./style.scss";

const Field: React.FC<any> = ({ label, children }) => {
  return (
    <div className="zs-three-subsidy-item-promotion-playdata-field">
      <label>{label}</label>
      <div>{children}</div>
    </div>
  );
};

// 生效人群对应关系
const UserScopeEnumStr = {
  0: "全部用户",
  1: "超级会员",
  2: "平台新客",
  3: "新零售新客",
  4: "门店新客",
  9: "医药新客",
};

const DeliveryChannelStr = {
  0: "通用渠道",
  9: "预订团",
};

interface Props {
  viewJson: string;
  env: any;
  activityInfo: {
    investmentActivityDetailDTO: {
      activityId: number;
      subsidyType: Subsidy;
      businessLineList: {
        businessLineName: string;
      }[];
      status: number;
    };
  };
}

interface ThreeSubsidyItemPromotionPlaydata {
  activityType: number;
  activityLimitRule: {
    userTotalCountLimit?: number;
    userDayCountLimit?: number;
  };
  activityMarketingInfo: {
    budgetId?: string;
    budgetName?: string;
    deliveryChannel: 0 | 9;
    deliveryType: 1 | 2 | 3;
    userScope: 0 | 1 | 2 | 3 | 4 | 9;
  };
  weekday: string;
  // 详情场景下，通过 periodDTOList + weekday 来表示生效周期和生效时段
  periodDTOList: {
    openTime: string;
    closeTime: string;
  }[];
}

const PlayView: React.FC<Props> = ({ viewJson, env }) => {
  let ready = false;
  let viewJsonObj: ThreeSubsidyItemPromotionPlaydata =
    {} as ThreeSubsidyItemPromotionPlaydata;

  if (!viewJson) {
    return null;
  }

  try {
    viewJsonObj = JSON.parse(viewJson);
    if (viewJsonObj == null) return null;
    ready = true;
  } catch (error) {
    console.error(error);
  }

  if (!ready) {
    return null;
  }

  return (
    <div>
      <Field label="活动类型：">品牌商品特价</Field>
      <Field label="限制人群：">
        {UserScopeEnumStr[viewJsonObj.activityMarketingInfo.userScope]}
      </Field>
      <Field label="投放渠道：">
        {DeliveryChannelStr[viewJsonObj.activityMarketingInfo.deliveryChannel]}
      </Field>
      <Field label="生效渠道：">
        {deliveryTypeStr[viewJsonObj.activityMarketingInfo.deliveryType]}
      </Field>
      <Field label="用户购买限定：">
        <div>
          {viewJsonObj.activityLimitRule.userDayCountLimit && (
            <>
              每人每日限购 {viewJsonObj.activityLimitRule.userDayCountLimit} 件
            </>
          )}
          {viewJsonObj.activityLimitRule.userDayCountLimit &&
            viewJsonObj.activityLimitRule.userTotalCountLimit &&
            "，"}
          {viewJsonObj.activityLimitRule.userTotalCountLimit && (
            <>
              活动期间每人限购{" "}
              {viewJsonObj.activityLimitRule.userTotalCountLimit} 件
            </>
          )}
        </div>
      </Field>

      <Field label="预算信息：">
        <BudgetPickerDetail
          value={{
            budgetName: viewJsonObj?.activityMarketingInfo.budgetName!,
            budgetId: viewJsonObj?.activityMarketingInfo.budgetId!,
          }}
          env={env}
        />
      </Field>
    </div>
  );
};

const PlayViewWrap: React.FC<any> = (props) => {
  const { activityInfo, env } = props;
  const [detail, setDetail] = useState<any>();

  useEffect(() => {
    api.subActivity.getDetail(activityInfo.activityId).then((res) => {
      setDetail(res.data);
    });
  }, [activityInfo.activityId]);
  return (
    <div className="zs-act-info">
      <div className="zs-act-area">
        <div className="zs-act-area-title" style={{ marginBottom: 30 }}>
          玩法设置
        </div>
        <div className="zs-act-section">
          <PlayView
            viewJson={detail?.viewJson}
            activityInfo={detail}
            env={env}
          />
        </div>
      </div>
    </div>
  );
};

export default PlayViewWrap;
