import { Dialog, Button } from '@alifd/next';

// 二次确认弹窗
export async function confirmMktRuleError(options: {
  title: string;
  content: string | JSX.Element;
  confirmBtnText: string;
  showConfirmBtnTips?: boolean;
}): Promise<boolean> {
  const { title, content, confirmBtnText, showConfirmBtnTips } = options;
  return await new Promise((resolve) => {
    const dialog = Dialog.confirm({
      title: title,
      content: content,
      onClose: () => {
        dialog.hide();
        resolve(false);
      },
      footer: [
        <div style={{ display: 'inline-table' }}>
          <Button
            type="secondary"
            onClick={async () => {
              dialog.hide();
              resolve(true);
            }}
          >
            {confirmBtnText}
          </Button>
          {showConfirmBtnTips ? (
            <div style={{ fontSize: 12, color: 'red' }}>!如含有平台补贴</div>
          ) : null}
        </div>,
        <Button
          type="primary"
          style={{ marginLeft: 20 }}
          onClick={() => {
            dialog.hide();
            resolve(false);
          }}
        >
          修改填写
        </Button>,
      ],
    });
  });
}
