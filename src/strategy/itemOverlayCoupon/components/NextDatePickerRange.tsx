import { DatePicker } from "@alifd/next";
import React, { useRef } from "react";

interface Props {
  value?: [string, string];
  onChange: (value: any) => void;
  [key: string]: any;
}
const NextDatePickerRange: React.FC<Props> = (props) => {
  // 标记首次选择结束时间
  const autoResetEndRef = useRef(true);

  const handleOnChange = (value: any) => {
    if (autoResetEndRef.current && value?.[1]) {
      // 首次设置结束时间，将时分秒改为 23:59:59
      props.onChange([value?.[0], value?.[1].clone().endOf("day")]);
      autoResetEndRef.current = false;
    } else {
      props.onChange([value?.[0], value?.[1]]);
    }
  };

  return (
    <DatePicker.RangePicker
      // @ts-ignore
      isPreview={props.view === "detail"}
      style={{
        minWidth: 400,
      }}
      {...props}
      onChange={handleOnChange}
    />
  );
};

export default NextDatePickerRange;
