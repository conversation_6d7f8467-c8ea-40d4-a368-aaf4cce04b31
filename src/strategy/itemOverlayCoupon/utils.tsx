import moment from 'moment';

import { isValueSet, maskMS, parseJsonSafe } from '../../common';
import { ActivityTypeEnum } from '../../constants';

import {
  BaseInfoForCreationContext,
  MarketPlayForPlayData,
  ViewJsonForPlayData,
} from './constants';

const ORDINARY_CHANNEL = 1;

export const formatMarketPlay2ViewJson = ({
  formData,
}: {
  formData: MarketPlayForPlayData;
}): ViewJsonForPlayData => {
  const budget = formData.budget && parseJsonSafe(formData.budget);
  const couponStock = parseJsonSafe(formData.couponStock);
  const couponName = parseJsonSafe(formData.couponName);
  const couponValid = parseJsonSafe(formData.couponValid);
  const drawLimit = parseJsonSafe(formData.drawLimit);
  const effectiveChannel = formData.effectiveChannel;
  const effectivePeople = formData.effectivePeople;
  const externalDeliveryChannel =
    formData.externalDeliveryChannel &&
    parseJsonSafe(formData.externalDeliveryChannel);
  const inStoreDeliveryType = formData.inStoreDeliveryType;
  const logoImg = formData.logoImg;
  const outStoreDeliveryType = formData.outStoreDeliveryType;
  const rule = parseJsonSafe(formData.rule);

  // 免单券新增字段
  const discountAmount = parseJsonSafe(formData.discountAmount);  // 优惠金额
  const bizUseChannel = formData.bizUseChannel; // 核销渠道
  const trafficChannel = formData.trafficChannel; // 业务渠道
  const redirectUrl = parseJsonSafe(formData.redirectUrl); // 淘宝闪购跳转链接
  const eaRedirectUrl = parseJsonSafe(formData.eaRedirectUrl); // 支付宝闪购跳转链接

  const viewJson = {
    activityType: ActivityTypeEnum.ITEM_OVERLAY_COUPON,
    activityLimitRule: {
      totalCountLimit: couponStock?.totalLimit?.value,
      dayCountLimit: couponStock?.dayLimit?.value,
      userDayCountLimit: drawLimit?.dayLimit?.value,
      userTotalCountLimit: drawLimit?.totalLimit?.value,
    },
    activityMarketingInfo: {
      deliveryType: effectiveChannel, // 核销场景
      availableDays: couponValid?.fromDraw?.value ?? undefined, // 红包使用时间 
      availableStartTime: couponValid?.validRange?.start ? moment(couponValid?.validRange?.start)?.valueOf() : undefined,
      availableEndTime: couponValid?.validRange?.end ? maskMS(moment(couponValid?.validRange?.end)?.valueOf()) : undefined,
      bizUseChannel: bizUseChannel || ORDINARY_CHANNEL, // 1 普通渠道
      trafficChannel: trafficChannel,  // 4 et+ea闪购
      redirectUrl: redirectUrl?.value,
      eaRedirectUrl: eaRedirectUrl?.value,
      
      couponName: couponName?.value,
      couponLogo: logoImg,
      inStoreDeliveryType: inStoreDeliveryType,
      outStoreDeliveryType: outStoreDeliveryType,
      userScope: effectivePeople, // 生效人群
      activityExternalDeliveryChannelList: externalDeliveryChannel,
      budgetId: budget?.budgetId,
      budgetName: budget?.budgetName,
      bundingGroup: formData.bundingGroup,
    },
    activityRule: {
      // discount: rule?.discount, // 老活动，不支持删掉
      condition: 1,
      conditionType: 'SATISFY_X_AMOUNT',
      discountType: "REDUCE", // 'FIXED_PRICE',
      platformSubsidyMax: discountAmount?.platformSubsidyMax, // 这个组件出来单位就是 分，直接给后端
    },
    periodDTOList: [{
      openTime: "00:00:00",
      closeTime: "23:59:59",
    }],
    weekday: '0,1,2,3,4,5,6',
  };
  return viewJson;
};

export const resolveItemPoolIdList = (list: any) => {
  let dealList: any = [];
  if (!list || list.length === 0) {
    return dealList
  }
  for (let i = 0; i < list.length; i++) {
    dealList.push(list[i].value)
  }
  return dealList
}

/**
 * 详情展示， viewJson 转 玩法配置语法
 */
export const formatViewJson2MarketPlay = ({
  viewDto,
  baseInfo,
}: {
  viewDto: ViewJsonForPlayData;
  baseInfo: BaseInfoForCreationContext;
}) => {
  const { activityLimitRule, activityMarketingInfo, activityRule } = viewDto;
  console.log(viewDto, 'activityMarketingInfo');
  let budget = {};
  let creator = {};
  let couponName = {};
  let couponStock = {};
  let couponValid = {};
  let dateRange;
  let drawLimit = {};
  let effectiveChannel;
  let effectivePeople;
  let externalDeliveryChannel;
  let inStoreDeliveryType;
  let logoImg;
  let outStoreDeliveryType;
  let rule = {};
  let discountAmount = {};
  let bizUseChannel;
  let trafficChannel;
  let redirectUrl = {};
  let eaRedirectUrl = {};


  // 预算信息
  if (
    isValueSet(activityMarketingInfo.budgetId) &&
    isValueSet(activityMarketingInfo.budgetName)
  ) {
    budget = {
      budgetId: activityMarketingInfo.budgetId,
      budgetName: activityMarketingInfo.budgetName,
    };
  }

  // 创建人
  if (
    isValueSet(baseInfo.createdUserId) &&
    isValueSet(baseInfo.createdUserName)
  ) {
    creator = {
      name: `${baseInfo.createdUserName}`,
      id: baseInfo.createdUserId,
    };
  }

  // 店内分类名称
  if (isValueSet(activityMarketingInfo.couponName)) {
    couponName = {
      value: activityMarketingInfo.couponName,
    };
  }
  // 库存限制
  if (isValueSet(activityLimitRule.dayCountLimit)) {
    couponStock = {
      dayLimit: { value: activityLimitRule.dayCountLimit },
    };
  }
  if (isValueSet(activityLimitRule.totalCountLimit)) {
    couponStock = {
      ...couponStock,
      totalLimit: { value: activityLimitRule.totalCountLimit },
    };
  }

  // 红包使用时间
  if (isValueSet(activityMarketingInfo.availableDays)) {
    couponValid = {
      fromDraw: { value: activityMarketingInfo.availableDays },
    };
  } else if (
    isValueSet(activityMarketingInfo.availableStartTime) &&
    isValueSet(activityMarketingInfo.availableEndTime)
  ) {
    // TODO: 目前这个活动没有考虑复制场景，现在的代码逻辑，复制场景可能会出现问题
    // TODO: 如果要增加复制功能，这里需要将所有活动场景都要 check 和适配一遍
    couponValid = {
      validRange: {
        start: moment(activityMarketingInfo.availableStartTime).format(
          'YYYY-MM-DD HH:mm:ss'
        ),
        end: moment(activityMarketingInfo.availableEndTime).format(
          'YYYY-MM-DD HH:mm:ss'
        ),
      },
    };
  }

  // 红包发放时间
  if (baseInfo.beginTime && baseInfo.endTime) {
    dateRange = {
      start: moment(baseInfo.beginTime).format('YYYY-MM-DD HH:mm:ss'),
      end: moment(baseInfo.endTime).format('YYYY-MM-DD HH:mm:ss'),
    };
  }

  // 领券限制
  if (isValueSet(activityLimitRule.userDayCountLimit)) {
    drawLimit = {
      dayLimit: { value: activityLimitRule.userDayCountLimit },
    };
  }
  if (isValueSet(activityLimitRule.userTotalCountLimit)) {
    drawLimit = {
      ...drawLimit,
      totalLimit: { value: activityLimitRule.userTotalCountLimit },
    };
  }

  // 业务场景
  if (isValueSet(activityMarketingInfo.deliveryType)) {
    effectiveChannel = activityMarketingInfo.deliveryType + '';
  }

  // 生效人群
  if (isValueSet(activityMarketingInfo.userScope)) {
    effectivePeople = activityMarketingInfo.userScope + '';
  }

  // 限定渠道
  if (isValueSet(activityMarketingInfo.activityExternalDeliveryChannelList)) {
    externalDeliveryChannel =
      activityMarketingInfo.activityExternalDeliveryChannelList;
  }

  // 店内投放方式
  if (isValueSet(activityMarketingInfo.inStoreDeliveryType)) {
    inStoreDeliveryType = activityMarketingInfo.inStoreDeliveryType + '';
  }

  // 图片
  if (isValueSet(activityMarketingInfo.couponLogo)) {
    logoImg = activityMarketingInfo.couponLogo;
  }

  // 店外投放方式
  if (isValueSet(activityMarketingInfo.outStoreDeliveryType)) {
    outStoreDeliveryType = activityMarketingInfo.outStoreDeliveryType + '';
  }

  // 券信息
  if (
    isValueSet(activityRule.discount) &&
    isValueSet(activityRule.platformSubsidyMax)
  ) {
    rule = {
      discount: activityRule.discount,
      elemeSubsidy: activityRule.platformSubsidyMax,
    };
  }

  /**
   * 免单券新增字段
   */
  // 优惠金额
  if (isValueSet(activityRule.platformSubsidyMax)) {
    discountAmount = {
      platformSubsidyMax: activityRule.platformSubsidyMax,
      platformSubsidyNum: activityRule.platformSubsidyMax,
    };
  }

  // 核销类型
  if (isValueSet(activityMarketingInfo.bizUseChannel)) {
    bizUseChannel = activityMarketingInfo.bizUseChannel
  }
  // 业务类型
  if (isValueSet(activityMarketingInfo.trafficChannel)) {
    trafficChannel = activityMarketingInfo.trafficChannel
  }
  // 淘宝闪购跳转链接
  if (isValueSet(activityMarketingInfo.redirectUrl)) {
    redirectUrl = {
      value: activityMarketingInfo.redirectUrl
    }
  }
  // 支付宝闪购跳转链接
  if (isValueSet(activityMarketingInfo.eaRedirectUrl)) {
    eaRedirectUrl = {
      value: activityMarketingInfo.eaRedirectUrl
    }
  }


  return {
    budget: JSON.stringify(budget),
    creator: JSON.stringify(creator),
    couponName: JSON.stringify(couponName),
    couponStock: JSON.stringify(couponStock),
    couponValid: JSON.stringify(couponValid),
    dateRange: JSON.stringify(dateRange),
    drawLimit: JSON.stringify(drawLimit),
    effectiveChannel,
    effectivePeople,
    externalDeliveryChannel: JSON.stringify(externalDeliveryChannel),
    inStoreDeliveryType,
    logoImg,
    outStoreDeliveryType,
    rule: JSON.stringify(rule),
    discountAmount: JSON.stringify(discountAmount),
    bizUseChannel,
    redirectUrl: JSON.stringify(redirectUrl),
    eaRedirectUrl: JSON.stringify(eaRedirectUrl),
    trafficChannel,
    bundingGroup: activityMarketingInfo?.bundingGroup
  };
};
