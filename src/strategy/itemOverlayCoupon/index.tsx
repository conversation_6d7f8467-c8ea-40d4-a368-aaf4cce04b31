import { Message, Dialog, But<PERSON>, NumberPicker } from "@alifd/next";

import * as api from "../../api"
import { maskMS } from "../../common";
import { ActivityTypeEnum } from "../../constants";
import { ActivityStrategy, InvestmentActivityDetailDTO } from "../../models"
import { CableBase } from "../base"
import {
  checkNextCommoditySignupRuleForAlpha,
  checkNextTimeForAlpha,
  genericCreateActivity,
  gotoLegacyActivityDetailByDefault,
  isOfflineEnabledByDefault,
} from "../mixins"
import { MarketPlayForPlayData, ViewJsonForPlayData } from "./constants";
import { formatMarketPlay2ViewJson, formatViewJson2MarketPlay, resolveItemPoolIdList } from "./utils";
import BigNumber from "bignumber.js";


/**
 * 叠加券
 */
const ItemOverlayCoupon: ActivityStrategy = {
  activityType: ActivityTypeEnum.ITEM_OVERLAY_COUPON,
  cable: {
    ...CableBase,
    workspace: 'ExukovQrae6xWn2rt4NgG',
    model: '6PehwDucPTD6RUpCNmNbhn',
    async checkNext(checkNextContext, creationContext, history) {
      const { step, dataStore } = checkNextContext
      console.log('checkNext', step, dataStore)
      if (step.name === "baseinfo") {
        const timeError = await checkNextTimeForAlpha(
          { step, dataStore },
          creationContext
        );
        if (timeError) {
          return timeError;
        }
      }
      if (step.name === "scope") {
        const commoditySignupRuleError =
          await checkNextCommoditySignupRuleForAlpha(
            checkNextContext,
            creationContext
          );
        if (commoditySignupRuleError) {
          return commoditySignupRuleError;
        }
      }
      if (step.name === 'playdata') {
        const { scope, playdata, baseinfo } = dataStore.data

        const itemPoolIdList = resolveItemPoolIdList(checkNextContext.dataStore.data.scope.itemPoolIdList);
        const formData: MarketPlayForPlayData = playdata.snapshot.data;
        const viewJson = formatMarketPlay2ViewJson({ formData })
        const payload = {
          activityType: ActivityTypeEnum.ITEM_OVERLAY_COUPON,
          activitySignType: creationContext.activitySignType || "REVERSE_SIGN",
          allowCancel: baseinfo.allowCancel,
          beginTime: baseinfo.activityTime.start.toDate().getTime(),
          endTime: maskMS(baseinfo.activityTime.end.toDate().getTime()),
          commodityAuditType: scope.commodityAuditType,
          description: baseinfo.description,
          remark: baseinfo.remark,
          name: baseinfo.name,
          signUpShopType: baseinfo.signUpShopType,
          itemPoolIdList,
          investmentType: creationContext.investmentType,
          investmentSchema: creationContext.investmentSchema,
          itemPoolProductSource: 30,
          subsidyType: creationContext?.subsidyType,
          manageOrgList: baseinfo?.manageOrgList,
          viewJson: JSON.stringify(viewJson)
        }
        console.log(payload, 'payload');
        return await genericCreateActivity(payload, history, creationContext)
      }
    }
  },
  hasAgentSubsidy() {
    return false
  },
  isItemAuditStatsVisible: true,
  isSupportAgent: false,
  isOfflineEnabled: isOfflineEnabledByDefault,
  isDingtalkLinkEnabled: () => false,
  hasAuditMemberList: () => false,
  // 修改库存
  async handleModifyStock(record, options) {
    try {
      // @ts-ignore
      const { totalCount, dayCount } = this._resolveTotalStockLimit(record);
      let totalCountLimit: any;
      let dayCountLimit: any;

      const dialog = Dialog.show({
        title: '修改库存',
        footer: [
          <Button
            style={{ marginRight: 10 }}
            type="primary"
            onClick={async () => {
              if (!totalCountLimit && !dayCountLimit) {
                Message.error('请填写库存');
                return;
              }
              try {
                let res: any;
                let params:any = {
                  activityId: record.activityId,
                };
                if (totalCountLimit) {
                  params.totalCountLimit = totalCountLimit;
                }
                if (dayCountLimit) {
                  params.dayCountLimit = dayCountLimit;
                }
                res = await api.modifyActivityLimitRule(params);
                if (res.success) {
                  dialog.hide();
                  options.onSuccess();
                } else {
                  options.onFail(res.errorMessage as string);
                }
              } catch (e: any) {
                options.onFail(e.message as string);
              }
            }}
          >
            确认
          </Button>,
          <Button onClick={() => dialog.hide()}>取消</Button>,
        ],
        content: (
          <div>
            {!!totalCount && <>
              <p>活动期间总共发放: </p>
              <p>原始值{totalCount}，修改为&nbsp;
                <NumberPicker
                  innerAfter="张"
                  placeholder="请输入库存"
                  style={{ width: 200 }}
                  min={totalCount}
                  onChange={(v: any) => {
                    totalCountLimit = v;
                  }}
                  max={999999}
                />
              </p>
            </>}
            {!!dayCount && <>
              <p>活动期间每日发放: </p>
              <p>原始值{dayCount}，修改为&nbsp;
                <NumberPicker
                  innerAfter="张"
                  placeholder="请输入库存"
                  style={{ width: 200 }}
                  min={dayCount}
                  onChange={(v: any) => {
                    dayCountLimit = v;
                  }}
                  max={999999}
                />
              </p>
            </>}
            <p style={{ color: 'red' }}>注意！只能增加不能减少</p>
          </div>
        ),
      });
    } catch (e) {
      console.error(e);
    }
  },
  // @ts-ignore
  _resolveTotalStockLimit(record): any {
    const _playdata: ViewJsonForPlayData = JSON.parse(record.viewJson as string)
    return { totalCount: _playdata?.activityLimitRule?.totalCountLimit || 0, dayCount: _playdata?.activityLimitRule?.dayCountLimit || 0 };
  },
  isSupportModifyStock(record) {
    return true;
  },
  isDetailInviteStatsOnMainActivityTableVisible: true,
  marketPlayMapper: async function (viewJson, baseInfo) {
    try {
      const viewDto: ViewJsonForPlayData = JSON.parse(viewJson) || {};

      let model = {};
      const fields = formatViewJson2MarketPlay({ viewDto, baseInfo });

      model = await api.subActivity.getComponentDataModel(
        'ExukovQrae6xWn2rt4NgG',
        '5Ow1jYOLXhZ8NLjaVkY9Bt'
      );
      const marketPlay = {
        instance: { fields },
        model: model,
      };
      return marketPlay;
    } catch (e) {
      console.error('marketPlayMapper: ');

      return null;
    }
  },
  renderPlayDataInfoForTableView: function (): JSX.Element {
    return <></>
  },
  renderActivityRuleForTable(viewJson: any) {
    try {
      const playdata: ViewJsonForPlayData = JSON.parse(viewJson)
      const rule = playdata.activityRule
      return `最高减${new BigNumber(rule.platformSubsidyMax).dividedBy(
        100
      )}元；平台补贴${new BigNumber(rule.platformSubsidyMax).dividedBy(100)}元`;
    } catch (e) {
      console.error(e)
      return ''
    }
  },
  gotoLegacyDetail: function (act: InvestmentActivityDetailDTO, scene: "lookup" | "examine"): void {
    gotoLegacyActivityDetailByDefault(act, scene)
  },
  renderActivityRuleForTableLegacy: function () {
    return null;
  },
  isHideSignUpInfo: true,
  addCustomStyle(styleTag, type: 'create' | 'detail') {
    if (type === 'create') {
      styleTag.setAttribute(
        "data-inject",
        "create-custom-styles-item-overlay-coupon"
      );

      styleTag.innerHTML = `
       .std-form-item .next-range-picker .next-range-picker-panel-input .next-input {
        width: calc((100% - 50px) / 4) !important; /* 设置输入框宽度 */
        }
        [data-test-id="condition-component-validRange"] .next-range-picker {
            width: 400px !important; 
        }
      `;
    } else {
      styleTag.setAttribute('data-inject', 'custom-styles-item-overlay-coupon');

      styleTag.innerHTML = `
        [data-test-id="template-data.redirectUrl"] .std-text-component,
        [data-test-id="template-data.eaRedirectUrl"] .std-text-component{
          max-width: 90%;
          word-wrap: break-word;
          white-space: pre-wrap;
          line-height: 20px !important;
          height: unset !important;
          display: unset !important;
        }
      `;
    }
  }
}

export default ItemOverlayCoupon

