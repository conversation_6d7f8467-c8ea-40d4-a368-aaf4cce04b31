export interface BaseInfoForCreationContext {
  beginTime: string;
  createdUserId: string;
  createdUserName: string;
  endTime: string;
}

export interface MarketPlayForPlayData {
  budget?: {
    budgetId: string;
    budgetName: string;
  };
  couponName: {
    value: string;
  };
  couponStock: {
    // 库存限制
    totalLimit: {
      value: number;
    },
     dayLimit: {
      value: number;
    };
  };
  drawLimit: {
    dayLimit: {
      value: number;
    };
    totalLimit: {
      value: number;
    };
  };
  couponValid: {
    // 红包使用时间（券有效期）
    fromDraw?: {
      value: number;
    };
    validRange?: {
      start: string;
      end: string;
    }
  };
  effectiveChannel: string; // 业务场景
  effectivePeople: string; // 生效人群
  externalDeliveryChannel?: {
    externalDeliveryChannelName: string;
    externalDeliveryChannelCode: string;
    externalDeliveryChannelType: 1 | 2; // 1：独享、2：非独享
  }[];
  inStoreDeliveryType: string;
  outStoreDeliveryType: string;
  logoImg: string;
  bizUseTime?: string;
  bizUseChannel: number; 
  trafficChannel: number; 
  redirectUrl?: {
    value: string
  }; // 淘宝闪购跳转链接
  eaRedirectUrl?: {
    value: string
  }; // 支付宝闪购跳转链接
  discountAmount?: {
    platformSubsidyMax: number;
  }
  rule: {
    discount: number;
    elemeSubsidy: number;
  };
  bundingGroup?: number;
  creator: {
    id: string;
    prefix: string;
    name: string;
  };
}

export interface ViewJsonForPlayData {
  activityType: number;
  activityLimitRule: {
    totalCountLimit?: number;
    dayCountLimit?: number
    userDayCountLimit?: number;
    userTotalCountLimit?: number;
  };
  activityMarketingInfo: {
    deliveryType: string; // 核销场景
    availableDays?: number;
    availableStartTime?: number | string;
    availableEndTime?: number | string;
    bizUseChannel: number | string;
    trafficChannel: number; 
    redirectUrl?: string;
    eaRedirectUrl?: string;
    couponName: string;
    couponLogo?: string;
    inStoreDeliveryType: string;
    outStoreDeliveryType: string;
    userScope: string;
    activityExternalDeliveryChannelList?: {
      externalDeliveryChannelName: string;
      externalDeliveryChannelCode: string;
      externalDeliveryChannelType: 1 | 2; // 1：独享、2：非独享
    }[];
    budgetId?: string;
    budgetName?: string;
    bundingGroup?: number; // 关联0起0配标记，后加字段
  };
  activityRule: {
    condition: number,
    conditionType: string,
    discount?: number;
    discountType: string;
    platformSubsidyMax: number;
  };
  periodDTOList: {
    closeTime: string;
    openTime: string;
  }[];
  weekday: string;
}
