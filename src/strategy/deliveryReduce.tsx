
import { ActivityTypeEnum } from "../constants";
import { ActivityStrategy, InvestmentActivityDetailDTO } from "../models"
import { CableBase } from "./base";
import { auditService } from "./service"
import { checkNextTimeForAlpha, genericCreateActivity, gotoLegacyActivityDetailByDefault } from "./mixins";
import { maskMS } from "../common";

export function renderWeekDay(weekday: string) {
  const weeks = `${weekday}`.split(',');
  const weekText = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];

  if (weeks.length === 7) {
    return '整周';
  } else {
    return weeks.sort().map((item: any) => weekText[item]).join(',');
  }
}

// 时间段渲染
export function renderTimeRange({ start, end }: any) {
  const startRes = start.split(':').slice(0, 2).join(':');
  const endRes = end.split(':').slice(0, 2).join(':');

  if (startRes === '00:00' && endRes === '23:59') {
    return '全天';
  } else {
    return `每天${startRes}~${endRes}`;
  }
}


interface DeliveryReduceRule {
  activityRule?: Array<{
    period: {
      weekday: string;
      openTime: string;
      closeTime: string;
    }
    rules: Array<{
      condition: number;
      discount: number;
      platformSubsidy: number;
      shopSubsidy: number;
      agentSubsidy: number;
    }>
  }>;
  activityMarketingInfo?: {
    userScope: number;
    budgetId?: number;
    budgetName?: string;
  };
  activityLimitRule?: {
    userDayOrderLimit: number;
  }
}

const DeliveryCoupon: ActivityStrategy = {
  activityType: ActivityTypeEnum.DELIVERY_REDUCE,
  cable: {
    ...CableBase,
    workspace: 'ExukovQrae6xWn2rt4NgG',
    model: '4nMhyYQnjz88Y3LbxToFgF',
    async checkNext({ step, dataStore }, creationContext, history) {
      if (step.name === "baseinfo") {
        const timeError = await checkNextTimeForAlpha(
          { step, dataStore },
          creationContext
        );
        if (timeError) {
          return timeError;
        }
      }
      if (step.name === 'playdata') {
        const { baseinfo, playdata, scope } = dataStore.data;
        const payload = {
          // 活动类型信息
          templateId: creationContext.templateId,
          investmentType: creationContext.investmentType,
          investmentSchema: creationContext.investmentSchema,
          activityType: ActivityTypeEnum.DELIVERY_REDUCE,

          // 基本信息
          name: baseinfo.name,
          description: baseinfo.description,
          remark: baseinfo.remark,
          beginTime: baseinfo.activityTime.start.toDate().getTime(),
          endTime: maskMS(baseinfo.activityTime.end.toDate().getTime()),
          allowCancel: baseinfo.allowCancel,
          signUpShopType: baseinfo.signUpShopType,
          signUpStartTime: baseinfo.signupTime.start.toDate().getTime(),
          signUpEndTime: maskMS(baseinfo.signupTime.end.toDate().getTime()),
          subsidyType: creationContext?.subsidyType,
          // 管理业态
          manageOrgList: baseinfo?.manageOrgList,

          // 招商范围信息
          shopPoolId: scope.pool.value,
          shopPoolName: scope.pool.label,

          // 玩法信息
          dataMap: {
            resource: {
              ...playdata.snapshot,
              componentModelUuid: '2gUoX68qOhN8DL3JehLSYg'
            }
          },

          // 不限制库存
          stockType: 0,
        };

        return await genericCreateActivity(payload, history, creationContext)
      }
    },
    auditService
  },
  hasAgentSubsidy(playdata: DeliveryReduceRule) {
    let flag = false;
    try {
      playdata.activityRule?.forEach(item => {
        item.rules.forEach(r => {
          if (+r.agentSubsidy > 0) {
            flag = true;
          }
        });
      });
    } catch (e) {
      console.error(e);
    }
    return flag;
  },
  isOfflineEnabled: function (state: number): boolean {
    // 活动状态: 1:未开始 2:活动中 3:活动暂停 4:活动取消 5:活动结束 8: 预算熔断
    return [1, 2, 3, 8].includes(state);
  },
  isDingtalkLinkEnabled: () => true,
  hasAuditMemberList: () => false,
  isSupportModifyStock: () => false,
  isDetailInviteStatsOnMainActivityTableVisible: true,
  renderPlayDataInfoForTableView: function (): JSX.Element {
    return <></>;
  },
  isSupportAgent: true,
  renderActivityRuleForTable(viewJson) {
    try {
      const data: DeliveryReduceRule = JSON.parse(viewJson);
      const rule = data.activityRule || [];
      return (
        <>
          {rule.map((r, i) => {
            return (
              <div key={i}>
                <div>规则{i + 1}</div>
                <div>周期时段:{renderWeekDay(r.period.weekday)},{renderTimeRange({ start: r.period.openTime, end: r.period.closeTime })}</div>
                <div>
                  {(r.rules || []).map((item, j) => {
                    return (
                      <div key={j}>
                        <span>阶梯{j + 1}: </span>
                        满{item.condition / 100}减{item.discount / 100};
                        商家补贴{item.shopSubsidy / 100};
                        平台补贴{item.platformSubsidy / 100}
                        {+item.agentSubsidy > 0 &&
                          <>;代理商补贴{item.agentSubsidy / 100}</>}
                      </div>
                    );
                  })}
                </div>
              </div>
            );
          })}
        </>
      );
    } catch (e) {
      return '';
    }
  },
  gotoLegacyDetail: function (act: InvestmentActivityDetailDTO, scene: 'lookup' | 'examine') {
    gotoLegacyActivityDetailByDefault(act, scene);
  },
  renderActivityRuleForTableLegacy: function (marketPlay: { instance: { fields: any } }) {
    const fields = marketPlay.instance.fields
    const periodAndRule = JSON.parse(fields.periodAndRule)
    const _signupShopType = fields._signupShopType

    const res = periodAndRule.map((item: any, index: number) => {
      const { rule, timeRange, weekday } = item;
      const temStr = `周期时段：${renderWeekDay(weekday)},${renderTimeRange(timeRange)}\n`;
      const ruleList = rule.map((_item: any, _index: number) => {
        const condition = _item.condition / 100;
        const discount = _item.discount / 100;
        const shopSubsidy = (_item.shopSubsidy / 100);
        const elemeSubsidy = _item.elemeSubsidy / 100;
        const agentSubsidy = _item.agentSubsidy / 100;

        const result = [`阶梯${_index + 1}:满${condition}减${discount}`];
        if (shopSubsidy) result.push(`商户补贴${shopSubsidy}`);
        if (elemeSubsidy) result.push(`平台补贴${elemeSubsidy}`);
        if (+_signupShopType === 2) {
          result.push(`代理商补贴${agentSubsidy}`)
        }

        return result.join(',') + ';';
      }).map((_item: any, _index: number) => (<div key={_index}>{_item}</div>))

      return (
        <div key={index}>
          <div>规则{index + 1}</div>
          <div>{temStr}</div>
          <div>{ruleList}</div>
        </div>
      )
    });
    return res;
  }
}

export default DeliveryCoupon