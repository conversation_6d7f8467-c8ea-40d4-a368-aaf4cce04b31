import * as api from '../api'
import { aslcClient } from '../api';

export const auditService = {
  async export(q: any) {
    const res = await api.zsr({
      url: `/api/v1/subactivity/${q.activityId}/merchant/export2`,
      method: 'GET',
      params: {
        storeId: q.storeId || undefined,
        storeName: q.storeName || undefined,
        supplierId: q.supplierId || undefined,
        supplierName: q.supplierName || undefined,
        state: q.states && q.states.length > 0 ? q.states[0] : undefined,
        pageNum: q.page,
        pageSize: q.size,
        rowSelection: JSON.stringify(q.rowSelection)
      }
    });

    return {
      data: {
        url: res.data.url
      }
    };
  },
  async query(q: any) {
    const res = await api.zsr({
      url: `/api/v1/subactivity/${q.activityId}/merchant/getStoreList`,
      method: 'GET',
      params: {
        storeId: q.storeId || undefined,
        storeName: q.storeName || undefined,
        supplierId: q.supplierId || undefined,
        supplierName: q.supplierName || undefined,
        state: q.states && q.states.length > 0 ? q.states[0] : undefined,
        pageNum: q.page,
        pageSize: q.size
      }
    });
    const { total, resultData } = res.data;
    return {
      data: {
        total,
        data: resultData.map((item: any) => {
          // 合并 state 和 auditState
          if (item.state === 'CHECKED') {
            item.state = item.auditState;
          }
          return item;
        })
      }
    };
  },
  async offline(req: any) {
    await api.zsr({
      method: 'POST',
      url: `/api/v1/subactivity/${req.activityId}/merchant/batchOfflineStore`,
      data: {
        aliStoreIds: req.aliStoreIdx,
        remark: req.reason
      }
    })
  }
};

export const auditServiceForCoupon = {
  ...auditService,
  async query(q: any) {
    // 在店铺类活动基础直上，解析 couponInfo
    const res = await auditService.query(q);
    console.log('商家审核res', res, 'q=====', q)
    if (res.data && res.data.data) {
      res.data.data = res.data.data.map((item: any) => {
        let couponInfo = {};
        try {
          couponInfo = JSON.parse(item.couponInfo);
        } catch (e) {
          console.error(e);
        }
        return {
          ...item,
          couponInfo
        };
      });
    }
    return res;
  },
};

interface AuditRule {
  activityPlanId: number | string; // 活动计划(alpha平台大皮)id
  reviewRuleId: number; // 审核规则id
}

export async function checkActivityReviewRule(param: AuditRule){
  const res = await aslcClient.fetch({
    apiKey:
      'ele-newretail-investment.NewretailInvestmentActivityYundingService.checkActivityReviewRule',
    params: [param],
  });
  return res;
}

export async function getPlanById(
  planId: string | number
) {
  const res = await aslcClient.fetch({
    apiKey: "ele-newretail-alpha.PlanYundingService.getActivityPlanList",
    params: [{ planId }],
  });
  return { data: res?.data?.[0] || null };
}
