import { resolveEnv } from "../common"
import { ActivityTypeEnum } from "../constants"
import { ActivityStrategy, InvestmentActivityDetailDTO } from "../models"
import { CableBase, resolveCreateActivityCommonData } from "./base"
import { 
  checkNextCommoditySignupRuleForAlpha,
  checkNextTimeForAlpha,
  genericCreateActivity, 
  isOfflineEnabledByDefault,
  resolveCommodityAuditProps,
} from "./mixins";

interface NBuyPlaydata {
  deliveryReduce: {
    activityRule: Array<{
      rules: Array<{
        platformSubsidy: number;
        shopSubsidy: number;
      }>
    }>
  }
  itemReduce: {
    activityRule: {
      discountMin: number;
      discountMax: number;
      platformSubsidyMax: number;
      minDiscountPercentForItemSalePrice: number;
    }
  }
}

const NBuy: ActivityStrategy = {
  activityType: ActivityTypeEnum.N_YUAN_ACTIVITY,
  cable: {
    ...CableBase,
    workspace: 'ExukovQrae6xWn2rt4NgG',
    model: '7r1SVjc5Nt67FrCcPfXEgL',
    async checkNext(checkNextContext, creationContext, history) {
      const { step, dataStore } = checkNextContext
      if (step.name === "baseinfo") {
        const timeError = await checkNextTimeForAlpha(
          checkNextContext,
          creationContext
        );
        if (timeError) {
          return timeError;
        }
      }
      if (step.name === "scope") {
        const commoditySignupRuleError =
          await checkNextCommoditySignupRuleForAlpha(
            checkNextContext,
            creationContext
          );
        if (commoditySignupRuleError) {
          return commoditySignupRuleError;
        }
      }
      if (step.name === 'stock') {
        const { baseinfo, scope, stock } = dataStore.data
        const commonData = resolveCreateActivityCommonData(
          ActivityTypeEnum.N_YUAN_ACTIVITY,
          checkNextContext,
          creationContext
        )

        const commodityAuditProps: any = resolveCommodityAuditProps(scope)

        const payload: any = {
          ...commonData,
          
          // 自动审核规则
          ...commodityAuditProps,

          // 降套补 商品清退规则
          commodityRemoveCondition: { removeCondition: scope.removeCondition },

          auditMemberList: baseinfo.auditMemberList?.length > 0 ?  baseinfo.auditMemberList: null,

          // 报名规则
          reviewRuleId: scope.rule?.ruleId,
          reviewRuleName: scope.rule?.name,

          // 库存设置，只支持门店+商品维度库存设置类型
          stockType: stock.stockType,
          stockMin: stock.range.start,
          stockMax: stock.range.end
        }
        payload.dataMap.resource.componentModelUuid = '7chgVfwTzHF97dHOpzNpBk'

        return await genericCreateActivity(payload, history, creationContext);
      }
    }
  },
  hasAgentSubsidy() {
    return false
  },
  isSupportAgent: false,
  isItemAuditStatsVisible: true,
  isOfflineEnabled: isOfflineEnabledByDefault,
  isDingtalkLinkEnabled: () => false,
  hasAuditMemberList: () => true,
  isSupportModifyStock: () => false,
  isDetailInviteStatsOnMainActivityTableVisible: true,
  renderPlayDataInfoForTableView: function (): JSX.Element {
    return <></>
  },
  renderActivityRuleForTable(viewJson: any) {
    try {
      const result: any[] = [];
      const playdata: NBuyPlaydata = JSON.parse(viewJson)
      result.push(`活动价格区间：${playdata.itemReduce.activityRule.discountMin / 100}~${playdata.itemReduce.activityRule.discountMax / 100}元`)
      result.push(`限制最低折扣：${playdata.itemReduce.activityRule.minDiscountPercentForItemSalePrice / 100}折`)
      result.push(`平台补贴最高：${playdata.itemReduce.activityRule.platformSubsidyMax / 100}元`)

      const r = playdata.deliveryReduce.activityRule[0].rules[0]
      if (r.platformSubsidy + r.shopSubsidy > 0) {
        result.push(`配送费无门槛最高减免${((r.platformSubsidy + r.shopSubsidy) / 100).toFixed(2)}元`)
      }
      if (r.platformSubsidy && r.shopSubsidy) {
        result.push(`饿了么补贴${r.platformSubsidy / 100}，商户补贴${r.shopSubsidy / 100}，优先使用商户补贴，再使用平台补贴`)
      }
      if (r.platformSubsidy && !r.shopSubsidy) {
        result.push('饿了么全补贴')
      }
      if (!r.platformSubsidy && r.shopSubsidy) {
        result.push('商户全补贴')
      }
      result.push('包装费对用户全免，费用全部由商户承担');
      result.push('免起送价限制');
      return <>{result.map((_r: any, i: number) => (<div key={i}>{_r}</div>))}</>
    } catch (e) {
      console.error(e)
      return <>'-'</>
    }
  },
  gotoLegacyDetail: function (act: InvestmentActivityDetailDTO, scene: "lookup" | "examine"): void {
    const env = resolveEnv()
    const prefix = env === 'pre' ? 'https://pre-zs.kunlun.alibaba-inc.com' : 'https://zs.kunlun.alibaba-inc.com'
    const mapping: any = { lookup: 'lookUp', examine: 'examine' }
    const url = prefix + '/main/activity/manage/' + mapping[scene] + '/' + act.activityId + '/' + encodeURIComponent(act.name) + '/' + act.activityType
    window.open(url, '_blank')
  },
  renderActivityRuleForTableLegacy: function (marketPlay: { instance: { fields: any } }) {
    try {
      const result: any[] = [];
      const price = JSON.parse(marketPlay.instance.fields.itemPriceArea)
      const discount = JSON.parse(marketPlay.instance.fields.itemDiscount)
      const itemPlatformSubsidy = JSON.parse(marketPlay.instance.fields.itemPlatformSubsidy)
      const deliveryFeeMerchantSubsidy = JSON.parse(marketPlay.instance.fields.deliveryFeeMerchantSubsidy)
      const deliveryFeePlatformSubsidy = JSON.parse(marketPlay.instance.fields.deliveryFeePlatformSubsidy)
      result.push(`活动价格区间：${price.start}~${price.end}元`)
      result.push(`限制最低折扣：${discount.value}折`)
      result.push(`平台补贴最高：${itemPlatformSubsidy.value}元`)
      console.log(
        'deliveryFeeMerchantSubsidy', deliveryFeeMerchantSubsidy, 'deliveryFeePlatformSubsidy', deliveryFeePlatformSubsidy,

      )
      if ((+deliveryFeeMerchantSubsidy.value) + (+deliveryFeePlatformSubsidy.value) > 0) {
        result.push(`配送费无门槛最高减免${(Number(deliveryFeeMerchantSubsidy.value) + Number(deliveryFeePlatformSubsidy.value)).toFixed(2)}元`)
      }
      if (+deliveryFeeMerchantSubsidy.value && +deliveryFeePlatformSubsidy.value) {
        result.push(`饿了么补贴${deliveryFeePlatformSubsidy.value}，商户补贴${deliveryFeeMerchantSubsidy.value}，优先使用商户补贴，再使用平台补贴`)
      }
      if (+deliveryFeePlatformSubsidy.value && !+deliveryFeeMerchantSubsidy.value) {
        result.push('饿了么全补贴')
      }
      if (!+deliveryFeePlatformSubsidy.value && +deliveryFeeMerchantSubsidy.value) {
        result.push('商户全补贴')
      }
      result.push('包装费对用户全免，费用全部由商户承担');
      result.push('免起送价限制');
      return result.map((r: any, i: number) => (<div key={i}>{r}</div>))
    } catch (e) {
      console.error(e)
      return '-'
    }
  }
}

export default NBuy

