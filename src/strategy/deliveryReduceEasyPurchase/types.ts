
import { ActivityTypeEnum } from "../../constants";

export interface DeliveryReduceEasyPurchaseViewJson {
  activityType: ActivityTypeEnum.DELIVERY_REDUCE_EASY_PURCHASE;
  activityRule?: Array<{
    period: {
      weekday: string;
      openTime: string;
      closeTime: string;
    }
    rules: Array<{
      condition: number;
      discount: number;
      platformSubsidy: number;
    }>
  }>;
  activityMarketingInfo: {
    userScope: number;
    budgetId?: number;
    budgetName?: string;
    deliveryChannel: number;
  };
  activityLimitRule?: {
    userDayCountLimit: number;
  };
}