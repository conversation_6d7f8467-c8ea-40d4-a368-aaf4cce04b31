import moment from "moment";
import { isValueSet, parseJsonSafe } from "../../common";
import { ActivityTypeEnum } from "../../constants";
import { DeliveryReduceEasyPurchaseViewJson } from './types'


export const formatViewJson2MarketPlay = ({
  viewDto,
  baseInfo,
}: {
  viewDto: DeliveryReduceEasyPurchaseViewJson;
  baseInfo: {
    name?: string,
    createdUserId?: string,
    createdUserName?: string,
    beginTime?: string,
    endTime?: string,
  };
}) => {
  const {
    activityRule,
    activityMarketingInfo,
    activityLimitRule,
  } = viewDto;

  let creator = {}; // 创建人名称、创建人id
  let dateRange = {};
  const name = baseInfo.name; // 玩法名称
  let deliveryChannel = "";
  let periodAndRule: any = [];
  let userScope = ""; // 用户类型
  let userDayOrderLimit = {};
  let budget = {}; //  预算

  if (isValueSet(activityMarketingInfo.deliveryChannel)) {
    deliveryChannel = activityMarketingInfo.deliveryChannel + "";
  }

  if (isValueSet(activityMarketingInfo.userScope)) {
    userScope = activityMarketingInfo.userScope + "";
  }
  if (
    isValueSet(baseInfo.createdUserId) &&
    isValueSet(baseInfo.createdUserName)
  ) {
    creator = {
      name: `${baseInfo.createdUserName}`,
      id: baseInfo.createdUserId,
    };
  }
  if (isValueSet(baseInfo.beginTime) && isValueSet(baseInfo.endTime)) {
    dateRange = {
      start: moment(baseInfo.beginTime).format("YYYY-MM-DD HH:mm:ss"),
      end: moment(baseInfo.endTime).format("YYYY-MM-DD HH:mm:ss"),
    };
  }
  if (isValueSet(activityRule)) {
    periodAndRule = activityRule?.map((item: any) => ({
      weekday: item.period.weekday,
      timeRange: {
        start: item.period.openTime,
        end: item.period.closeTime,
      },
      rule: item.rules
    }));
  }
  if (isValueSet(activityLimitRule)) {
    userDayOrderLimit = {
      limit: activityLimitRule?.userDayCountLimit
    }
  }
  if (
    isValueSet(activityMarketingInfo.budgetName) &&
    isValueSet(activityMarketingInfo.budgetId)
  ) {
    budget = {
      budgetName: activityMarketingInfo.budgetName,
      budgetId: "" + activityMarketingInfo.budgetId,
    };
  }

  return {
    creator: JSON.stringify(creator),
    name: name,
    dateRange: JSON.stringify(dateRange),
    deliveryChannel: deliveryChannel,
    periodAndRule: JSON.stringify(periodAndRule),
    userScope,
    userDayOrderLimit: JSON.stringify(userDayOrderLimit),
    budget: JSON.stringify(budget),
  };
};

//提交参数
export const formatMarketPlay2ViewJson = ({ formData }: {
  formData: {
    deliveryChannel: string;
    periodAndRule: string;
    userScope: string;
    userDayOrderLimit: string;
    budget: string;
  };
}): DeliveryReduceEasyPurchaseViewJson => {

  const budget = formData.budget && parseJsonSafe(formData.budget);
  const userScope = Number(formData.userScope)
  const deliveryChannel = Number(formData.deliveryChannel)
  const rule = formData.periodAndRule && parseJsonSafe(formData.periodAndRule);
  const userDayOrderLimit = formData.userDayOrderLimit && parseJsonSafe(formData.userDayOrderLimit);

  const activityRule = (rule || []).map((item: any) => ({
    period: {
      weekday: item.weekday,
      openTime: item.timeRange.start,
      closeTime: item.timeRange.end,
    },
    rules: item.rule,
  }))

  return {
    activityType: ActivityTypeEnum.DELIVERY_REDUCE_EASY_PURCHASE,
    activityMarketingInfo: {
      budgetId: budget?.budgetId,
      budgetName: budget?.budgetName,
      userScope,
      deliveryChannel: deliveryChannel,
    },
    activityRule: activityRule,
    activityLimitRule: {
      userDayCountLimit: userDayOrderLimit?.limit
    }
  }
}
