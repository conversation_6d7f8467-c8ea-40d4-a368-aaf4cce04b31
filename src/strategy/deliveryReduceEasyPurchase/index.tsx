// 运费满减-顺手买 1000044
import { ActivityTypeEnum } from "../../constants";
import { ActivityStrategy } from "../../models"
import { CableBase } from "../base";
import { auditService } from "../service"
import { checkNextTimeForAlpha, genericCreateActivity } from "../mixins";
import { maskMS, renderWeekDay, renderTimeRange } from "../../common";
import { DeliveryReduceEasyPurchaseViewJson } from "./types";
import * as api from "../../api"
import { formatMarketPlay2ViewJson, formatViewJson2MarketPlay } from './formatUtils'
import { Message } from "@alifd/next";
import BigNumber from "bignumber.js";


const DeliveryReduceEasyPurchase: ActivityStrategy = {
  activityType: ActivityTypeEnum.DELIVERY_REDUCE_EASY_PURCHASE,
  cable: {
    ...CableBase,
    workspace: 'ExukovQrae6xWn2rt4NgG',
    model: '7R5TCNCap5c5TSgHabaQM4',
    async checkNext({ step, dataStore }, creationContext, history) {
      if (step.name === "baseinfo") {
        const timeError = await checkNextTimeForAlpha(
          { step, dataStore },
          creationContext
        );
        if (timeError) {
          return timeError;
        }
      }
      if (step.name === 'playdata') {
        const { baseinfo, playdata, scope } = dataStore.data;
        const viewJson = formatMarketPlay2ViewJson({
          formData: playdata.snapshot.data,
        })
        const payload = {
          // 活动类型信息
          templateId: creationContext.templateId,
          investmentType: creationContext.investmentType,
          investmentSchema: creationContext.investmentSchema,
          activityType: ActivityTypeEnum.DELIVERY_REDUCE_EASY_PURCHASE,
          activitySignType: creationContext.activitySignType || "REVERSE_SIGN",

          // 基本信息
          name: baseinfo.name,
          description: baseinfo.description,
          remark: baseinfo.remark,
          beginTime: baseinfo.activityTime.start.toDate().getTime(),
          endTime: maskMS(baseinfo.activityTime.end.toDate().getTime()),
          allowCancel: baseinfo.allowCancel,
          signUpShopType: baseinfo.signUpShopType,
          subsidyType: creationContext?.subsidyType,
          manageOrgList: baseinfo?.manageOrgList,

          // 招商范围信息
          shopPoolId: scope.pool.value,
          shopPoolName: scope.pool.label,

          // 玩法数据
          viewJson: JSON.stringify(viewJson),

          // 0不限制库存 后续迭代确认下是不是可以删了。
          stockType: 0,
        };

        // 兜底一下rules 校验
        const theActivityRule = viewJson.activityRule
        const rulesHasErr = (theActivityRule || []).some((item: any) =>
          item.rules?.some((rule: any) => rule.discount !== (
            new BigNumber(rule.platformSubsidy).plus(rule.shopSubsidy).toNumber()
          ))
        );

        if (rulesHasErr) {
          Message.error('优惠规则存在错误,请检查')
          return
        }

        return await genericCreateActivity(payload, history, creationContext)
      }
    },
    auditService
  },
  isOfflineEnabled: function (state: number): boolean {
    // 活动状态: 1:未开始 2:活动中 3:活动暂停 4:活动取消 5:活动结束 8: 预算熔断
    return [1, 2, 3, 8].includes(state);
  },
  isDingtalkLinkEnabled: () => false,
  hasAuditMemberList: () => false,
  isSupportModifyStock: () => false,
  isDetailInviteStatsOnMainActivityTableVisible: true,
  isSupportAgent: false,
  renderPlayDataInfoForTableView: function (): JSX.Element {
    return <></>;
  },
  renderActivityRuleForTable(viewJson) {
    try {
      const data: DeliveryReduceEasyPurchaseViewJson = JSON.parse(viewJson);
      const rule = data.activityRule || [];
      return (
        <>
          {rule.map((r, i) => {
            return (
              <div key={i}>
                <div>规则{i + 1}</div>
                <div>周期时段:{renderWeekDay(r.period.weekday)},{renderTimeRange({ start: r.period.openTime, end: r.period.closeTime })}</div>
                <div>
                  {(r.rules || []).map((item, j) => {
                    return (
                      <div key={j}>
                        <span>阶梯{j + 1}: </span>
                        满{item.condition / 100}减{item.discount / 100};
                        平台补贴{item.platformSubsidy / 100}
                      </div>
                    );
                  })}
                </div>
              </div>
            );
          })}
        </>
      );
    } catch (e) {
      return '';
    }
  },
  gotoLegacyDetail: function () {
    return undefined
  },
  renderActivityRuleForTableLegacy: function () {
    return <></>;
  },
  async marketPlayMapper(viewJson: string, baseInfo: any) {
    try {
      const viewDto: any = JSON.parse(viewJson);
      let model = {};
      const fields = formatViewJson2MarketPlay({
        viewDto,
        baseInfo: {
          name: baseInfo.name,
          createdUserId: baseInfo.createdUserId,
          createdUserName: baseInfo.createdUserName,
          beginTime: baseInfo.beginTime,
          endTime: baseInfo.endTime,
        },
      });
      model = await api.subActivity.getComponentDataModel(
        "3oNNWxkJYzc6eWQyKz5Lp2",
        "a0giHFg2DfU6hsO2QOWOs3"
      );
      const marketPlay = {
        instance: { fields },
        model: model,
      };
      return marketPlay;
    } catch (e) {
      return null;
    }
  },
}

export default DeliveryReduceEasyPurchase