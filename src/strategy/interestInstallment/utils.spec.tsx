import { formatViewJson2MarkPlay, formatMarketPlay2ViewJson } from "./utils";

describe("interestInstallment", () => {
  describe("formatMarketPlay2ViewJson", () => {
    test("日常模版-花呗分期免息", () => {
      const viewJson = formatMarketPlay2ViewJson({
        baseinfo: {
          activityTime: {
            weeks: [0, 1, 2, 3, 4, 5, 6],
            batchRange: {
              list: [{ start: "00:00", end: "23:59" }],
            },
          },
        },
        formData: {
          timeRange: [
            {
              start: "",
              end: "",
            },
          ],
          weekday: "0,1,2,3,4,5,6",
          userScope: "27",
          creator: { id: "329684", name: "郭宇帆" },
          userLimit: { totalLimit: { value: 1 } },
          deliveryType: "1",
          investRadio: {
            shopSubsidyPercent: 80,
            platformSubsidyPercent: 20,
          },
          interestPeriods: [
            {
              period: 3,
              rate: 1.8,
            },
          ],
        },
      });
      expect(viewJson).toStrictEqual({
        activityLimitRule: {
          userTotalCountLimit: 1,
        },
        activityMarketingInfo: {
          deliveryType: "1",
          userScope: "27",
        },
        activityRule: {
          platformSubsidyPercent: 20,
          shopSubsidyPercent: 80,
          interestPeriods: [
            {
              period: 3,
              rate: 1.8,
            },
          ],
        },
        activityType: 1000037,
        period: [
          {
            closeTime: "23:59:59",
            openTime: "00:00:00",
          },
        ],
        weekday: "0,1,2,3,4,5,6",
      });
    });
  });

  describe("formatViewJson2MarkPlay", () => {
    test("日常模版一花呗分期免息-详情展示", () => {
      const viewJson = {
        activityLimitRule: {
          userTotalCountLimit: 1,
        },
        activityMarketingInfo: {
          deliveryType: "1",
          userScope: "27",
        },
        activityRule: {
          platformSubsidyPercent: 20,
          shopSubsidyPercent: 80,
          discount: 100,
          interestPeriods: [
            {
              period: 3,
              rate: 1.8,
            },
          ],
        },
        activityType: 1000037,
        periodDTOList: [
          {
            closeTime: "23:59:59",
            openTime: "00:00:00",
          },
        ],
        weekday: "0,1,2,3,4,5,6",
      };
      const baseInfo = {
        createdUserId: "329684",
        createdUserName: "郭宇帆",
      };
      // @ts-ignore
      const formData = formatViewJson2MarkPlay({
        viewJson: JSON.stringify(viewJson),
        baseInfo,
      });
      expect(formData).toStrictEqual({
        userScope: "27",
        creator: '{"name":"郭宇帆","id":"329684"}',
        interestPeriods: '[{"period":3,"rate":1.8}]',
        investRadio: '{"platformSubsidyPercent":20,"shopSubsidyPercent":80}',
        userLimit: '{"totalLimit":{"value":"1"}}',
        deliveryType: "1",
      });
    });
    test("日常模版一花呗分期免息-复制场景", () => {
      const viewJson = {
        activityLimitRule: {
          userTotalCountLimit: 1,
        },
        activityMarketingInfo: {
          deliveryType: "1",
          userScope: "27",
        },
        activityRule: {
          platformSubsidyPercent: 20,
          shopSubsidyPercent: 80,
          discount: 100,
          interestPeriods: [
            {
              period: 3,
              rate: 1.8,
            },
          ],
        },
        activityType: 1000037,
        periodDTOList: [
          {
            closeTime: "23:59:59",
            openTime: "00:00:00",
          },
        ],
        weekday: "0,1,2,3,4,5,6",
      };
      const baseInfo = {
        createdUserId: "329684",
        createdUserName: "郭宇帆",
      };
      // @ts-ignore
      const formData = formatViewJson2MarkPlay({
        viewJson: JSON.stringify(viewJson),
        baseInfo,
        isCopy: true,
      });
      expect(formData).toStrictEqual({
        userScope: "27",
        creator: '{"name":"郭宇帆","id":"329684"}',
        interestPeriods: null,
        investRadio: '{"platformSubsidyPercent":20,"shopSubsidyPercent":80}',
        userLimit: '{"totalLimit":{"value":"1"}}',
        deliveryType: "1",
      });
    });
    test("日常模版一花呗分期免息-接口报错", () => {
      const viewJson = {};
      const baseInfo = {};
      // @ts-ignore
      const formData = formatViewJson2MarkPlay({
        viewJson: JSON.stringify(viewJson),
        baseInfo,
        isCopy: true,
      });
      expect(formData).toStrictEqual({
        creator: "{}",
        deliveryType: "",
        interestPeriods: null,
        investRadio: "{}",
        userLimit: "{}",
        userScope: "",
      });
    });
  });
});
