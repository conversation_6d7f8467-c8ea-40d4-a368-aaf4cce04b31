import { isValueSet, parseJsonSafe } from "../../common";
import { ActivityTypeEnum } from "../../constants";

interface InstallmentPlaydata {
  activityLimitRule: {
    userTotalCountLimit: number;
  };
  activityMarketingInfo: {
    deliveryType: number;
    userScope: number;
  };
  activityType: number;
  activityRule: {
    platformSubsidyPercent: number; // 平台补贴比例
    shopSubsidyPercent: number; // 商家补贴比例
    interestPeriods: {
      period: string;
      rate: number;
    }[];
  };
}

// 支持多时段，活动详情后端返回的viewJson多时段为periodDTOList
export interface InstallmentViewPlaydata extends InstallmentPlaydata {
  periodDTOList: {
    openTime: string;
    closeTime: string;
  }[];
  weekday: string;
}

// 支持多时段，创建的时候传给后端period
export interface InstallmentCreatePlaydata extends InstallmentPlaydata {
  period: {
    openTime: string;
    closeTime: string;
  }[];
  weekday: string;
}

export const formatViewJson2MarkPlay = ({
  viewJson,
  baseInfo,
  isCopy,
}: {
  viewJson: string;
  baseInfo: any;
  isCopy?: boolean;
}) => {
  const viewDto: InstallmentPlaydata = JSON.parse(viewJson);
  const { activityRule, activityLimitRule, activityMarketingInfo } = viewDto;
  let investRadio = {}; // 出资比例
  let userLimit = {};
  let userScope = ""; // 用户类型
  let deliveryType = ""; // 生效渠道
  let creator = {}; // 创建人名称、创建人id
  let interestPeriods = {}; // 分期期数
  if (isValueSet(activityRule?.platformSubsidyPercent)) {
    investRadio = {
      platformSubsidyPercent: activityRule.platformSubsidyPercent,
    };
  }

  if (isValueSet(activityRule?.interestPeriods)) {
    interestPeriods = activityRule.interestPeriods;
  }

  if (isValueSet(activityRule?.shopSubsidyPercent)) {
    investRadio = {
      ...investRadio,
      shopSubsidyPercent: activityRule.shopSubsidyPercent,
    };
  }

  if (
    isValueSet(baseInfo?.createdUserId) &&
    isValueSet(baseInfo?.createdUserName)
  ) {
    creator = {
      name: `${baseInfo.createdUserName}`,
      id: baseInfo.createdUserId,
    };
  }
  if (isValueSet(activityLimitRule?.userTotalCountLimit)) {
    userLimit = {
      totalLimit: {
        value: activityLimitRule.userTotalCountLimit + "",
      },
    };
  }

  if (isValueSet(activityMarketingInfo?.deliveryType)) {
    deliveryType = activityMarketingInfo.deliveryType + "";
  }

  if (isValueSet(activityMarketingInfo?.userScope)) {
    userScope = activityMarketingInfo.userScope + "";
  }

  return {
    investRadio: JSON.stringify(investRadio),
    creator: JSON.stringify(creator),
    userLimit: JSON.stringify(userLimit),
    userScope,
    deliveryType,
    interestPeriods: isCopy ? null : JSON.stringify(interestPeriods),
  };
};

export const formatMarketPlay2ViewJson = ({
  formData,
  baseinfo,
}: any) => {
  const investRadio = parseJsonSafe(formData.investRadio);
  const userLimit = parseJsonSafe(formData.userLimit);
  const deliveryType = formData.deliveryType;
  const userScope = formData.userScope;

  const interestPeriods = parseJsonSafe(formData.interestPeriods);
  const viewJson: InstallmentCreatePlaydata = {
    activityLimitRule: {
      userTotalCountLimit: userLimit?.totalLimit?.value,
    },
    activityMarketingInfo: {
      deliveryType: deliveryType,
      userScope: userScope,
    },
    activityRule: {
      platformSubsidyPercent: investRadio.platformSubsidyPercent,
      shopSubsidyPercent: investRadio.shopSubsidyPercent,
      interestPeriods: interestPeriods.map(
        (i: { period: string; rate: number }) => {
          return {
            period: i?.period,
            rate: i?.rate,
          };
        }
      ),
    },
    activityType: ActivityTypeEnum.PERIOD_INTEREST_FREE,
    period: (baseinfo?.activityTime.batchRange.list || []).map((r: any) => {
      return {
        openTime: r.start + ":00",
        closeTime: r.end + ":59",
      };
    }),
    weekday: (baseinfo.activityTime.weeks || []).join(","),
  };
  return viewJson;
};
