import { Message } from "@alifd/next";
import moment from "moment";
import * as api from "../../api";
import loadingDialog from "../../components/LoadingDialog";
import {
  ActivityTypeEnum,
  InvestmentSchemaEnum,
  InvestmentTypeEnum,
} from "../../constants";
import { ActivityStrategy } from "../../models";
import {
  CableBase,
  resolveCreateActivityCommonData,
  resolveTimeRangeBatchFromMarketplayValue,
} from "../base";
import {
  resolveCommodityAuditProps,
  checkValidateCopyActivityResult,
  checkAndUploadRichDescription,
  resolveRichDescription,
  checkNextTimeForAlpha,
  checkNextCommoditySignupRuleForAlpha,
} from "../mixins";
import {
  formatMarketPlay2ViewJson,
  formatViewJson2MarkPlay,
  InstallmentViewPlaydata,
} from "./utils";

const InterestInstallment: ActivityStrategy = {
  activityType: ActivityTypeEnum.PERIOD_INTEREST_FREE,
  cable: {
    ...CableBase,
    workspace: "ExukovQrae6xWn2rt4NgG",
    model: "2VFldRnqAoh6YEFe8dmLws",
    async checkNext(checkNextContext, creationContext, history) {
      const { step, dataStore } = checkNextContext;
      if (!dataStore._session) {
        dataStore._session = {};
      }
      if (step.name === "baseinfo") {
        if (dataStore.data.baseinfo?.richDescription) {
          const { hide } = loadingDialog.show({
            message: "活动信息检查中...",
          });
          try {
            const error = await checkAndUploadRichDescription({
              richDescription: dataStore.data.baseinfo?.richDescription,
              session: dataStore._session,
            });
            if (error) {
              return error;
            }
          } finally {
            hide();
          }
        }
      }
      if (step.name === "baseinfo") {
        const timeError = await checkNextTimeForAlpha(
          { step, dataStore },
          creationContext
        );
        if (timeError) {
          return timeError;
        }
      }
      if (step.name === "scope") {
        const commoditySignupRuleError =
          await checkNextCommoditySignupRuleForAlpha(
            checkNextContext,
            creationContext
          );
        if (commoditySignupRuleError) {
          return commoditySignupRuleError;
        }
      }
      if (step.name === "stock") {
        const { baseinfo, stock, scope, playdata } = dataStore.data;
        const commonData: any = resolveCreateActivityCommonData(
          ActivityTypeEnum.PERIOD_INTEREST_FREE,
          checkNextContext,
          creationContext
        );
        delete commonData.dataMap;
        const formData: any = playdata.snapshot.data;
        const viewJson: any = formatMarketPlay2ViewJson({
          formData,
          baseinfo,
          investmentSchema: creationContext?.investmentSchema,
        });
        const commodityAuditProps: any = resolveCommodityAuditProps(scope);

        const payload: any = {
          ...commonData,

          // 自动审核规则
          ...commodityAuditProps,

          // 降套补 商品清退规则
          commodityRemoveCondition: {
            removeCondition: scope.removeCondition,
          },

          auditMemberList:
            baseinfo.auditMemberList?.length > 0
              ? baseinfo.auditMemberList
              : null,

          // 报名规则
          reviewRuleId: scope.rule?.ruleId,
          reviewRuleName: scope.rule?.name,

          // 玩法设置
          viewJson: JSON.stringify(viewJson),

          // 库存设置
          stockType: 1,
          stockMin: stock?.stock,
          stockMax: stock?.stock,
          participantDayStock: stock?.dayStock,
        };

        const { hide, updateMessage } = loadingDialog.show({
          message: creationContext.copy ? "活动信息校验中..." : "活动创建中...",
        });

        if (dataStore._session?.ossURL) {
          const ossURL = dataStore._session?.ossURL;
          payload.description = baseinfo.richDescription.text;
          payload.attributes = {
            ...payload.attributes,
            hasRichDescription: "on",
            richDescriptionURL: ossURL,
          };
        }

        try {
          if (creationContext.copy) {
            const res = await api.subActivity.validateCopyActivityData({
              copyActivityId: creationContext.activity?.activityId,
              ...payload,
            });
            const copyError = checkValidateCopyActivityResult(res);
            if (copyError) {
              return copyError;
            }
            updateMessage("活动创建中...");
          }
          const res = await api.subActivity.create(payload);
          if (res.success) {
            Message.success("创建成功");
            if (creationContext.activitySceneId) {
                  history.push(
                    "/sub-activity/" +
                      res.data +
                      "?activitySceneId=" +
                      creationContext.activitySceneId +
                      "&activityPlanId=" +
                      creationContext.activityPlanId
                  );
                } else {
                  history.push("/sub-activity/" + res.data);
                }
          } else {
            return { message: res.errorMessage };
          }
        } catch (e: any) {
          return { message: e?.message || "系统异常" };
        } finally {
          hide();
        }
      }
    },
  },

  hasAgentSubsidy() {
    return false;
  },
  isSupportAgent: false,
  isOfflineEnabled: function (state: number): boolean {
    // 活动状态: 1:未开始 2:活动中 3:活动暂停 4:活动取消 5:活动结束 8: 预算熔断
    return [1, 2, 3, 8].includes(state);
  },
  isCopyEnabled({ investmentSchema, investmentType }) {
    // 目前只有日常招商-花呗分期免息
    return (
      investmentSchema === InvestmentSchemaEnum.PERIOD_INTEREST_FREE &&
      investmentType === InvestmentTypeEnum.RICHANG
    );
  },
  async resolveCopyData(creationContext) {
    let viewJson: InstallmentViewPlaydata;
    try {
      viewJson =
        creationContext.activity &&
        JSON.parse(creationContext.activity?.viewJson);
      const { periodDTOList, weekday } = viewJson || {};
      const timeRangeBatch = resolveTimeRangeBatchFromMarketplayValue(
        JSON.stringify(
          periodDTOList.map((item) => {
            return { start: item.openTime, end: item.closeTime };
          })
        )
      );
      const baseInfo = {
        beginTime: creationContext.activity?.beginTime,
        endTime: creationContext.activity?.endTime,
      };

      // 获取富文本描述信息
      const richDescription = await resolveRichDescription(creationContext);

      return {
        baseinfo: {
          name: creationContext.activity?.name,
          richDescription,
          remark: creationContext.activity?.remark,
          activityTime: {
            start: moment(creationContext.activity?.beginTime),
            end: moment(creationContext.activity?.endTime),
            weeks: weekday
              .split(",")
              .filter(Boolean)
              .map((d: string) => +d),
            batchRange: timeRangeBatch,
          },
          auditMemberList: creationContext.activity?.auditMemberList || [],
          signupTime: {
            start: moment(creationContext.activity?.signUpStartTime),
            end: moment(creationContext.activity?.signUpEndTime),
          },
          allowCancel: creationContext.activity?.allowCancel,
          signUpShopType: creationContext.activity?.signUpShopType,
        },
        scope: {
          // 复制活动，门店池和报名规则均清空
          commodityAuditType: 1,
        },
        playdata: {
          fields: formatViewJson2MarkPlay({
            viewJson: creationContext.activity?.viewJson as string,
            baseInfo,
            isCopy: true,
          }),
        },
        stock: {
          stockType: creationContext.activity?.stockType,
          dayStock: undefined,
          stock: undefined,
        },
      };
    } catch (e) {
      console.error(e);
      return {};
    }
  },
  isDingtalkLinkEnabled: () => true,
  hasAuditMemberList: () => true,
  isSupportModifyStock: () => false,
  isItemAuditStatsVisible: true,
  isDetailInviteStatsOnMainActivityTableVisible: true,
  renderPlayDataInfoForTableView: function (): JSX.Element {
    return <></>;
  },
  gotoLegacyDetail: function (): void {},
  renderActivityRuleForTable(
    viewJson: any,
  ) {
    try {
      const data: InstallmentViewPlaydata = JSON.parse(viewJson);
      let result = [];
      result.push(`平台补贴比例${data?.activityRule?.platformSubsidyPercent}%`);
      result.push(`商户补贴比例${data?.activityRule?.shopSubsidyPercent}%`);
      return (
        <>
          {result.map((r: any, i: number) => (
            <div key={i}>{r}</div>
          ))}
        </>
      );
    } catch (e) {
      console.error(e);
      return <></>;
    }
  },
  renderActivityRuleForTableLegacy: function () {
    return <></>;
  },
  marketPlayMapper: async function (
    viewJson: any,
    baseInfo: any,
  ) {
    try {
      const viewJsonView = formatViewJson2MarkPlay({ viewJson, baseInfo });
      let model = await api.subActivity.getComponentDataModel(
        "3oNNWxkJYzc6eWQyKz5Lp2",
        "91B88eVLWJda0d4gBzzkzp"
      );
      const marketPlay = {
        instance: {
          fields: {
            ...viewJsonView,
          },
        },
        model: model,
      };
      return marketPlay;
    } catch (e) {
      return null;
    }
  },
};

export default InterestInstallment;
