import {
  checkAndUploadRichDescription,
  checkNextCommoditySignupRuleForAlpha,
  checkNextTimeForAlpha,
  genericCreateActivity,
  resolveCommodityAuditProps,
} from "../mixins";
import { ActivityTypeEnum } from "../../constants";
import loadingDialog from "../../components/LoadingDialog";
import { CheckNextContext, CreationContext } from "../../models";
import { CableBase, resolveCreateActivityCommonData } from "../base";
import { MarketPlayForFoodieBeans } from "./constants";
import { formatMarketPlay2ViewJsonForFoodieBeans } from "./utils";

export const FoodieBeansCable = {
  ...CableBase,
  workspace: "ExukovQrae6xWn2rt4NgG",
  model: "98twHbLk8P1aJgBPjUXDvY",
  async checkNext(
    checkNextContext: CheckNextContext,
    creationContext: CreationContext,
    history: any
  ) {
    const { step, dataStore } = checkNextContext;
    if (!dataStore._session) {
      dataStore._session = {};
    }
    if (step.name === "baseinfo") {
      if (dataStore.data.baseinfo?.richDescription) {
        const { hide } = loadingDialog.show({
          message: "活动信息检查中...",
        });
        try {
          const error = await checkAndUploadRichDescription({
            richDescription: dataStore.data.baseinfo?.richDescription,
            session: dataStore._session,
          });
          if (error) {
            return error;
          }
        } finally {
          hide();
        }
      }
    }
    if(step.name === "baseinfo") {
      const timeError = await checkNextTimeForAlpha(checkNextContext, creationContext);
      if(timeError) {
        return timeError;
      }
    }
    if(step.name === "scope") {
      const commoditySignupRuleError = await checkNextCommoditySignupRuleForAlpha(checkNextContext, creationContext);
      if(commoditySignupRuleError) {
        return commoditySignupRuleError;
      }
    }
    if (step.name === "discountRules") {
      const { baseinfo, playdata, scope, discountRules } = dataStore.data;
      const commonData: any = resolveCreateActivityCommonData(
        ActivityTypeEnum.ITEM_SPECIAL_COUPON,
        checkNextContext,
        creationContext
      );

      delete commonData.dataMap;
      const commodityAuditProps: any = resolveCommodityAuditProps(scope);
      const formData: MarketPlayForFoodieBeans = playdata.snapshot.data;

      const viewJson: any = formatMarketPlay2ViewJsonForFoodieBeans({
        formData,
        baseinfo,
        discountRules,
      });

      const payload: any = {
        // 基本信息
        ...commonData,
        // 自动审核规则
        ...commodityAuditProps,
        // 降套补 商品清退规则
        commodityRemoveCondition: {
          removeCondition: scope.removeCondition,
        },
        // 授权审核人
        auditMemberList:
          baseinfo.auditMemberList?.length > 0
            ? baseinfo.auditMemberList
            : null,
        // 报名规则
        reviewRuleId: scope.rule?.ruleId,
        reviewRuleName: scope.rule?.name,
        // 玩法
        viewJson: JSON.stringify(viewJson),
        // 优惠规则
        uploadPath: discountRules.commodityRules.uploadPath,
      };
      if (dataStore._session?.ossURL) {
        const ossURL = dataStore._session?.ossURL;
        payload.description = baseinfo.richDescription.text;
        payload.attributes = {
          ...payload.attributes,
          hasRichDescription: 'on',
          richDescriptionURL: ossURL,
        };
      }


      return await genericCreateActivity(payload, history, creationContext);
    }
  },
};
