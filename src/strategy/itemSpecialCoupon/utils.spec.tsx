import { InvestmentSchemaEnum } from "../../constants";
import {
  formatViewJson2MarketPlay,
  formatMarketPlay2ViewJson,
  formatMarketPlay2ViewJsonForFoodieBeans,
} from "./utils";

describe("itemSpecialCoupon", () => {
  describe("helper", () => {
    describe("formatMarketPlay2ViewJson", () => {
      test("招商类型:日常招商-招商模版: 自领取日期起-无预算", () => {
        const formData = {
          outStoreDeliveryType: "2",
          externalDeliveryChannel: null,
          budget: "{}",
          ruleFree: '{"discount":0}',
          inStoreDeliveryType: "1",
          name: "回归-特价券-日常招商",
          creator: '{"id":"213137","prefix":"yc213137","name":"杨辰"}',
          dateRange:
            '{"start":"2023-07-12 00:00:00","end":"2023-07-19 23:59:59"}',
          couponStock: '{"totalLimit":{"value":1}}',
          couponName: '{"value":"测试红包"}',
          couponValid: '{"fromDraw":{"value":2}}',
          drawLimit: '{"totalLimit":{"value":3},"dayLimit":{"value":2}}',
          logoImg: "",
          _workspace_uuid_: "ExukovQrae6xWn2rt4NgG",
          effectiveChannel: "1",
          rule: '{"discount":10000,"elemeSubsidy":0,"fixDiscount":1}',
          couponValidFix: '{"fromDraw":{"value":"0"}}',
        };

        const baseinfo = {
          activityTime: {
            weeks: [0, 1, 2, 3, 4, 5, 6],
            batchRange: {
              type: "all",
              list: [
                {
                  start: "00:00",
                  end: "23:59",
                },
              ],
            },
            start: "2023-07-11T16:00:00.000Z",
            end: "2023-07-19T15:59:59.000Z",
          },
        };

        const viewJson = formatMarketPlay2ViewJson({
          // @ts-ignore
          formData,
          baseinfo,
          investmentSchema: InvestmentSchemaEnum.RICHANG,
        });

        expect(viewJson).toStrictEqual({
          activityType: 1000021,
          activityLimitRule: {
            dayCountLimit: undefined,
            totalCountLimit: 1,
            userDayCountLimit: 2,
            userTotalCountLimit: 3,
          },
          activityMarketingInfo: {
            availableDays: 2,
            availableEndTime: undefined,
            availableStartTime: undefined,
            budgetId: undefined,
            budgetName: undefined,
            couponLogo: "",
            couponName: "测试红包",
            deliveryType: "1",
            activityExternalDeliveryChannelList: null,
            inStoreDeliveryType: "1",
            outStoreDeliveryType: "2",
            userScope: 0,
          },
          activityRule: {
            discount: 10000,
            discountType: "FIXED_PRICE",
            platformSubsidy: 0,
          },
          periodDTOList: [
            {
              openTime: "00:00:00",
              closeTime: "23:59:59",
            },
          ],
          weekday: "0,1,2,3,4,5,6",
        });
      });
      test("招商类型:日常招商-招商模版: 指定生效日期-无预算", () => {
        const formData = {
          outStoreDeliveryType: "2",
          externalDeliveryChannel: null,
          budget: "{}",
          ruleFree: '{"discount":0}',
          inStoreDeliveryType: "1",
          name: "回归-特价券-日常招商",
          creator: '{"id":"213137","prefix":"yc213137","name":"杨辰"}',
          dateRange:
            '{"start":"2023-07-12 00:00:00","end":"2023-07-19 23:59:59"}',
          couponStock: '{"totalLimit":{"value":1}}',
          couponName: '{"value":"测试红包"}',
          couponValid:
            '{"validRange":{"start":"2023-08-09 00:00:00","end":"2023-08-31 23:59:59"}}',
          drawLimit: '{"totalLimit":{"value":3},"dayLimit":{"value":2}}',
          logoImg: "",
          _workspace_uuid_: "ExukovQrae6xWn2rt4NgG",
          effectiveChannel: "1",
          rule: '{"discount":10000,"elemeSubsidy":0,"fixDiscount":1}',
          couponValidFix: '{"fromDraw":{"value":"0"}}',
        };

        const baseinfo = {
          activityTime: {
            weeks: null, // 错误数据测试
            batchRange: {
              type: "all",
              list: [
                {
                  start: "00:00",
                  end: "23:59",
                },
              ],
            },
            start: "2023-07-11T16:00:00.000Z",
            end: "2023-07-19T15:59:59.000Z",
          },
        };

        const viewJson = formatMarketPlay2ViewJson({
          // @ts-ignore
          formData,
          // @ts-ignore
          baseinfo,
          investmentSchema: InvestmentSchemaEnum.RICHANG,
        });

        expect(viewJson).toStrictEqual({
          activityType: 1000021,
          activityLimitRule: {
            dayCountLimit: undefined,
            totalCountLimit: 1,
            userDayCountLimit: 2,
            userTotalCountLimit: 3,
          },
          activityMarketingInfo: {
            availableDays: undefined,
            availableEndTime: "2023-08-31 23:59:59",
            availableStartTime: "2023-08-09 00:00:00",
            budgetId: undefined,
            budgetName: undefined,
            couponLogo: "",
            couponName: "测试红包",
            deliveryType: "1",
            activityExternalDeliveryChannelList: null,
            inStoreDeliveryType: "1",
            outStoreDeliveryType: "2",
            userScope: 0,
          },
          activityRule: {
            discount: 10000,
            discountType: "FIXED_PRICE",
            platformSubsidy: 0,
          },
          periodDTOList: [
            {
              openTime: "00:00:00",
              closeTime: "23:59:59",
            },
          ],
          weekday: "",
        });
      });

      test("招商类型:日常招商-零售医药免单: 活动期间总发放-活动期间每日发放-有预算", () => {
        const formData = {
          outStoreDeliveryType: "2",
          externalDeliveryChannel: null,
          budget: '{"budgetId":101,"budgetName":"T101"}',
          ruleFree: '{"discount":0}',
          inStoreDeliveryType: "1",
          creator: "{}",
          dateRange:
            '{"start":"2023-08-02 00:00:00","end":"2023-08-02 23:59:59"}',
          couponStock: '{"totalLimit":{"value":6},"dayLimit":{"value":3}}',
          couponName: '{"value":"测试红包"}',
          couponValid: '{"fromDraw":{"value":11}}',
          drawLimit: '{"totalLimit":{"value":1}}',
          logoImg: "",
          _workspace_uuid_: "ExukovQrae6xWn2rt4NgG",
          effectiveChannel: "1",
          rule: '{"discount":600,"elemeSubsidy":100,"fixDiscount":1}',
          couponValidFix: '{"fromDraw":{"value":"0"}}',
        };

        const baseinfo = {
          activityTime: {
            weeks: [0, 1, 2, 3, 4, 5, 6],
            batchRange: {
              type: "all",
              list: [
                {
                  start: "00:00",
                  end: "23:59",
                },
              ],
            },
            start: "2023-07-11T16:00:00.000Z",
            end: "2023-07-19T15:59:59.000Z",
          },
        };

        const viewJson = formatMarketPlay2ViewJson({
          // @ts-ignore
          formData,
          baseinfo,
          investmentSchema: InvestmentSchemaEnum.DRUG_FREE,
        });

        expect(viewJson).toStrictEqual({
          activityType: 1000021,
          activityLimitRule: {
            dayCountLimit: 3,
            totalCountLimit: 6,
            userDayCountLimit: undefined,
            userTotalCountLimit: 1,
          },
          activityMarketingInfo: {
            availableDays: 11,
            availableEndTime: undefined,
            availableStartTime: undefined,
            budgetId: 101,
            budgetName: "T101",
            couponLogo: "",
            couponName: "测试红包",
            deliveryType: "1",
            activityExternalDeliveryChannelList: null,
            inStoreDeliveryType: "1",
            outStoreDeliveryType: "2",
            userScope: 0,
          },
          activityRule: {
            discount: 0,
            discountType: "FIXED_PRICE",
            platformSubsidyMax: undefined,
            platformSubsidyPercent: undefined,
            shopSubsidyPercent: undefined,
          },
          periodDTOList: [
            {
              openTime: "00:00:00",
              closeTime: "23:59:59",
            },
          ],
          weekday: "0,1,2,3,4,5,6",
        });
      });

      test("招商类型:日常招商-招商模版: 站外卡券售卖，自领取日期起-无预算", () => {
        const formData = {
          outStoreDeliveryType: "1",
          externalDeliveryChannel:
            '[{"externalDeliveryChannelType":2,"externalDeliveryChannelCode":84,"externalDeliveryChannelName":"站外卡券售卖"}]',
          budget: "{}",
          ruleFree: '{"discount":0}',
          inStoreDeliveryType: "2",
          name: "yc-特价券-站外卡券售卖-回归",
          creator: '{"id":"213137","prefix":"yc213137","name":"杨辰"}',
          dateRange:
            '{"start":"2023-11-29 00:00:00","end":"2023-12-31 23:59:59"}',
          couponStock: '{"totalLimit":{"value":1}}',
          couponName: '{"value":"22"}',
          couponValid: '{"fromDraw":{"value":1}}',
          drawLimit: '{"totalLimit":{"value":1}}',
          logoImg: "",
          _workspace_uuid_: "ExukovQrae6xWn2rt4NgG",
          effectiveChannel: "1",
          rule: '{"discount":22200,"elemeSubsidy":0,"fixDiscount":1}',
          couponValidFix: '{"fromDraw":{"value":"0"}}',
        };
        const baseinfo = {
          activityTime: {
            weeks: [0, 1, 2, 3, 4, 5, 6],
            batchRange: {
              type: "all",
              list: [
                {
                  start: "00:00",
                  end: "23:59",
                },
              ],
            },
            start: "2023-11-28T16:00:00.000Z",
            end: "2023-12-31T15:59:59.000Z",
          },
        };
        const viewJson = formatMarketPlay2ViewJson({
          // @ts-ignore
          formData,
          baseinfo,
          investmentSchema: InvestmentSchemaEnum.CARD_SALES,
        });

        expect(viewJson).toStrictEqual({
          activityType: 1000021,
          activityLimitRule: {
            dayCountLimit: undefined,
            totalCountLimit: 1,
            userDayCountLimit: undefined,
            userTotalCountLimit: 1,
          },
          activityMarketingInfo: {
            availableDays: 1,
            availableEndTime: undefined,
            availableStartTime: undefined,
            budgetId: undefined,
            budgetName: undefined,
            couponLogo: "",
            couponName: "22",
            deliveryType: "1",
            activityExternalDeliveryChannelList: [
              {
                externalDeliveryChannelType: 2,
                externalDeliveryChannelCode: 84,
                externalDeliveryChannelName: "站外卡券售卖",
              },
            ],
            inStoreDeliveryType: "2",
            outStoreDeliveryType: "1",
            userScope: 0,
          },
          activityRule: {
            discount: 22200,
            discountType: "FIXED_PRICE",
            platformSubsidy: 0,
          },
          periodDTOList: [
            {
              openTime: "00:00:00",
              closeTime: "23:59:59",
            },
          ],
          weekday: "0,1,2,3,4,5,6",
        });
      });
    });

    describe("formatViewJson2MarketPlay", () => {
      test("日常招商-日常模版: 自领取日期起-无预算", () => {
        const viewDto = {
          activityLimitRule: {
            totalCountLimit: 100,
            userDayCountLimit: 1,
            userTotalCountLimit: 3,
          },
          activityMarketingInfo: {
            activityExternalDeliveryChannelList: [
              {
                externalDeliveryChannelCode: 1,
                externalDeliveryChannelName: "全部渠道",
                externalDeliveryChannelType: 2,
              },
            ],
            availableDays: 1,
            budgetId: "400000000005300249",
            budgetName: "2023年代运营红包预算",
            couponLogo: "",
            couponName: "dd",
            deliveryType: 1,
            inStoreDeliveryType: 1,
            outStoreDeliveryType: 1,
            userScope: 0,
          },
          activityRule: { discount: 1600, platformSubsidy: 100 },
          activityType: 1000021,
          periodDTOList: [{ closeTime: "23:59:59", openTime: "00:00:00" }],
          weekday: "2,3,4",
        };
        const baseInfo = {
          createdUserId: "213137",
          createdUserName: "杨辰",
          beginTime: "2023-07-24T16:00:00.000Z",
          endTime: "2023-07-27T15:59:59.000Z",
        };
        const formData = formatViewJson2MarketPlay({
          // @ts-ignore
          viewDto,
          baseInfo,
          investmentSchema: InvestmentSchemaEnum.RICHANG,
        });
        expect(formData).toStrictEqual({
          budget:
            '{"budgetId":"400000000005300249","budgetName":"2023年代运营红包预算"}',
          couponName: '{"value":"dd"}',
          couponStock: '{"totalLimit":{"value":100}}',
          couponValid: '{"fromDraw":{"value":1}}',
          creator: '{"name":"杨辰","id":"213137"}',
          dateRange:
            '{"start":"2023-07-25 00:00:00","end":"2023-07-27 23:59:59"}',
          drawLimit: '{"dayLimit":{"value":1},"totalLimit":{"value":3}}',
          effectiveChannel: "1",
          externalDeliveryChannel:
            '[{"externalDeliveryChannelCode":1,"externalDeliveryChannelName":"全部渠道","externalDeliveryChannelType":2}]',
          inStoreDeliveryType: "1",
          logoImg: undefined,
          outStoreDeliveryType: "1",
          rule: '{"discount":1600,"elemeSubsidy":100}',
          ruleFree: "{}",
        });
      });
      test("招商类型:日常招商-招商模版:医药免单", () => {
        const viewDto = {
          activityLimitRule: { totalCountLimit: 1, userTotalCountLimit: 1 },
          activityMarketingInfo: {
            activityExternalDeliveryChannelList: [
              {
                externalDeliveryChannelCode: 1,
                externalDeliveryChannelName: "全部渠道",
                externalDeliveryChannelType: 2,
              },
            ],
            availableDays: 1,
            couponLogo: "",
            couponName: "测试",
            deliveryType: 1,
            inStoreDeliveryType: 1,
            outStoreDeliveryType: 1,
            userScope: 0,
          },
          activityRule: {
            discount: 0,
            platformSubsidyMax: 0,
            platformSubsidyPercent: 0,
            shopSubsidyPercent: 100,
          },
          activityType: 1000021,
          periodDTOList: [{ closeTime: "23:59:59", openTime: "00:00:00" }],
          weekday: "0,1,2,3,4,5,6",
        };
        const baseInfo = {
          createdUserId: "213137",
          createdUserName: "杨辰",
          beginTime: "2023-07-24T16:00:00.000Z",
          endTime: "2023-07-27T15:59:59.000Z",
        };
        const formData = formatViewJson2MarketPlay({
          // @ts-ignore
          viewDto,
          baseInfo,
          investmentSchema: InvestmentSchemaEnum.RICHANG,
        });
        expect(formData).toStrictEqual({
          budget: "{}",
          couponName: '{"value":"测试"}',
          couponStock: '{"totalLimit":{"value":1}}',
          couponValid: '{"fromDraw":{"value":1}}',
          creator: '{"name":"杨辰","id":"213137"}',
          dateRange:
            '{"start":"2023-07-25 00:00:00","end":"2023-07-27 23:59:59"}',
          drawLimit: '{"totalLimit":{"value":1}}',
          effectiveChannel: "1",
          externalDeliveryChannel:
            '[{"externalDeliveryChannelCode":1,"externalDeliveryChannelName":"全部渠道","externalDeliveryChannelType":2}]',
          inStoreDeliveryType: "1",
          logoImg: undefined,
          outStoreDeliveryType: "1",
          rule: "{}",
          ruleFree: "{}",
        });
      });
      test("招商类型:日常招商-招商模版:医药免单-活动期间每日发放-指定生效日期", () => {
        const viewDto = {
          activityLimitRule: {
            totalCountLimit: 1,
            dayCountLimit: 3,
            userTotalCountLimit: 1,
          },
          activityMarketingInfo: {
            activityExternalDeliveryChannelList: [
              {
                externalDeliveryChannelCode: 1,
                externalDeliveryChannelName: "全部渠道",
                externalDeliveryChannelType: 2,
              },
            ],
            availableDays: undefined,
            availableEndTime: "2023-08-31 23:59:59",
            availableStartTime: "2023-08-09 00:00:00",
            couponLogo: "test.img",
            couponName: "测试",
            deliveryType: 1,
            inStoreDeliveryType: 1,
            outStoreDeliveryType: 1,
            userScope: 0,
          },
          activityRule: {
            discount: 0,
            platformSubsidyMax: 0,
            platformSubsidyPercent: 0,
            shopSubsidyPercent: 100,
          },
          activityType: 1000021,
          periodDTOList: [{ closeTime: "23:59:59", openTime: "00:00:00" }],
          weekday: "0,1,2,3,4,5,6",
        };
        const baseInfo = {
          createdUserId: "213137",
          createdUserName: "杨辰",
          beginTime: "2023-07-24T16:00:00.000Z",
          endTime: "2023-07-27T15:59:59.000Z",
        };
        const formData = formatViewJson2MarketPlay({
          // @ts-ignore
          viewDto,
          baseInfo,
          investmentSchema: InvestmentSchemaEnum.DRUG_FREE,
        });
        expect(formData).toStrictEqual({
          budget: "{}",
          couponName: '{"value":"测试"}',
          couponStock: '{"totalLimit":{"value":1},"dayLimit":{"value":3}}',
          couponValid:
            '{"validRange":{"start":"2023-08-09 00:00:00","end":"2023-08-31 23:59:59"}}',
          creator: '{"name":"杨辰","id":"213137"}',
          dateRange:
            '{"start":"2023-07-25 00:00:00","end":"2023-07-27 23:59:59"}',
          drawLimit: '{"totalLimit":{"value":1}}',
          effectiveChannel: "1",
          externalDeliveryChannel:
            '[{"externalDeliveryChannelCode":1,"externalDeliveryChannelName":"全部渠道","externalDeliveryChannelType":2}]',
          inStoreDeliveryType: "1",
          logoImg: "test.img",
          outStoreDeliveryType: "1",
          rule: "{}",
          ruleFree:
            '{"discount":0,"elemeSubsidy":0,"platformSubsidyPercent":0,"shopSubsidyPercent":100}',
        });
      });
      test("招商类型:日常招商-招商模版:吃货豆", () => {
        const viewDto = {
          activityMarketingInfo: {
            activityExternalDeliveryChannelList: [
              {
                externalDeliveryChannelCode: 1,
                externalDeliveryChannelName: "全部渠道",
                externalDeliveryChannelType: 2,
              },
            ],
            couponLogo: "",
            deliveryType: 1,
            inStoreDeliveryType: 1,
            outStoreDeliveryType: 1,
            userScope: 0,
          },
          activityType: 1000021,
          periodDTOList: [{ closeTime: "23:59:59", openTime: "00:00:00" }],
          weekday: "0,1,2,3,4,5,6",
        };
        const baseInfo = {
          createdUserId: "213137",
          createdUserName: "杨辰",
          beginTime: "2023-07-24T16:00:00.000Z",
          endTime: "2023-07-27T15:59:59.000Z",
        };
        const formData = formatViewJson2MarketPlay({
          // @ts-ignore
          viewDto,
          baseInfo,
          investmentSchema: InvestmentSchemaEnum.FOODIE_BEANS,
        });
        expect(formData).toStrictEqual({
          budget: "{}",
          couponName: "{}",
          couponStock: "{}",
          couponValid: "{}",
          creator: '{"name":"杨辰","id":"213137"}',
          dateRange:
            '{"start":"2023-07-25 00:00:00","end":"2023-07-27 23:59:59"}',
          drawLimit: "{}",
          effectiveChannel: "1",
          externalDeliveryChannel:
            '[{"externalDeliveryChannelCode":1,"externalDeliveryChannelName":"全部渠道","externalDeliveryChannelType":2}]',
          inStoreDeliveryType: "1",
          logoImg: undefined,
          outStoreDeliveryType: "1",
          rule: "{}",
          ruleFree: "{}",
        });
      });
    });

    describe("formatMarketPlay2ViewJsonForFoodieBeans", () => {
      test("招商类型:日常招商-招商模版: 吃货豆-无预算", () => {
        const formData = {
          outStoreDeliveryType: "1",
          externalDeliveryChannel:
            '[{"externalDeliveryChannelType":2,"externalDeliveryChannelCode":4,"externalDeliveryChannelName":"百川-豆商城"}]',
          inStoreDeliveryType: "2",
          name: "1",
          creator: {
            id: "329684",
            prefix: "yufanguo.gyf",
            name: "郭宇帆",
          },
          dateRange:
            '{"start":"2024-02-20 00:00:00","end":"2024-02-29 23:59:59"}',
          logoImg: "",
          _workspace_uuid_: "3oNNWxkJYzc6eWQyKz5Lp2",
          effectiveChannel: "1",
        };

        const baseinfo = {
          activityTime: {
            weeks: [0, 1, 2, 3, 4, 5, 6],
            batchRange: {
              type: "all",
              list: [
                {
                  start: "00:00",
                  end: "23:59",
                },
              ],
            },
            start: "2024-02-19T16:00:00.000Z",
            end: "2024-02-29T15:59:59.000Z",
          },
        };

        const discountRules = {
          commodityRules: {
            uploadPath:
              "https://bwm-private-b.oss-cn-hangzhou.aliyuncs.com/mkt/329684-da9b490c-bc63-4298-a924-4a85aeb71065.xlsx",
            fileUploadStatus: 1,
            checkExcel: {
              checkMessage: null,
              checkResult: null,
              errorRow: 5,
              extInfoMap: {
                invitedUpcHasPlatformSubsidy: "false",
                checkResultFilePath:
                  "https://bwm-private-b.oss-cn-hangzhou.aliyuncs.com/mkt/activityCheckResult_2356198688218194688.xlsx?Expires=1708657475&OSSAccessKeyId=LTAIhP07XE91RzLJ&Signature=7%2BMPgLdLBvysjxpcBj3bvQWuoxc%3D&response-content-disposition=attachment%3Bfilename%3D%E6%A0%A1%E9%AA%8C%E5%A4%B1%E8%B4%A5%E7%BB%93%E6%9E%9C.xlsx",
              },
              repeatRow: 0,
              successRow: 0,
              totalRow: 5,
              uploadTotalUpc: null,
            },
            url: {
              uid: "lsts36bg",
            },
          },
        };

        const viewJson = formatMarketPlay2ViewJsonForFoodieBeans({
          // @ts-ignore
          formData,
          // @ts-ignore
          baseinfo,
          // @ts-ignore
          discountRules,
        });

        expect(viewJson).toStrictEqual({
          activityType: 1000021,
          activityMarketingInfo: {
            budgetId: undefined,
            budgetName: undefined,
            couponLogo: "",
            deliveryType: "1",
            activityExternalDeliveryChannelList: [
              {
                externalDeliveryChannelType: 2,
                externalDeliveryChannelCode: 4,
                externalDeliveryChannelName: "百川-豆商城",
              },
            ],
            inStoreDeliveryType: "2",
            outStoreDeliveryType: "1",
            userScope: 0,
          },
          periodDTOList: [
            {
              openTime: "00:00:00",
              closeTime: "23:59:59",
            },
          ],
          weekday: "0,1,2,3,4,5,6",
        });
      });

      test("招商类型:日常招商-吃货豆: 有预算", () => {
        const formData = {
          outStoreDeliveryType: "1",
          externalDeliveryChannel:
            '[{"externalDeliveryChannelType":2,"externalDeliveryChannelCode":4,"externalDeliveryChannelName":"百川-豆商城"}]',
          inStoreDeliveryType: "2",
          name: "1",
          creator: {
            id: "329684",
            prefix: "yufanguo.gyf",
            name: "郭宇帆",
          },
          dateRange:
            '{"start":"2024-02-20 00:00:00","end":"2024-02-29 23:59:59"}',
          logoImg: "",
          _workspace_uuid_: "3oNNWxkJYzc6eWQyKz5Lp2",
          effectiveChannel: "1",
        };

        const baseinfo = {
          activityTime: {
            weeks: [0, 1, 2, 3, 4, 5, 6],
            batchRange: {
              type: "all",
              list: [
                {
                  start: "00:00",
                  end: "23:59",
                },
              ],
            },
            start: "2024-02-19T16:00:00.000Z",
            end: "2024-02-29T15:59:59.000Z",
          },
        };

        const discountRules = {
          commodityRules: {
            uploadPath:
              "https://bwm-private-b.oss-cn-hangzhou.aliyuncs.com/mkt/329684-da9b490c-bc63-4298-a924-4a85aeb71065.xlsx",
            fileUploadStatus: 1,
            checkExcel: {
              checkMessage: null,
              checkResult: null,
              errorRow: 5,
              extInfoMap: {
                invitedUpcHasPlatformSubsidy: "false",
                checkResultFilePath:
                  "https://bwm-private-b.oss-cn-hangzhou.aliyuncs.com/mkt/activityCheckResult_2356198688218194688.xlsx?Expires=1708657475&OSSAccessKeyId=LTAIhP07XE91RzLJ&Signature=7%2BMPgLdLBvysjxpcBj3bvQWuoxc%3D&response-content-disposition=attachment%3Bfilename%3D%E6%A0%A1%E9%AA%8C%E5%A4%B1%E8%B4%A5%E7%BB%93%E6%9E%9C.xlsx",
              },
              repeatRow: 0,
              successRow: 0,
              totalRow: 5,
              uploadTotalUpc: null,
            },
            url: {
              uid: "lsts36bg",
            },
          },
          budget: { budgetId: 101, budgetName: "T101" },
        };

        const viewJson = formatMarketPlay2ViewJsonForFoodieBeans({
          // @ts-ignore
          formData,
          // @ts-ignore
          baseinfo,
          // @ts-ignore
          discountRules,
        });

        expect(viewJson).toStrictEqual({
          activityType: 1000021,
          activityMarketingInfo: {
            budgetId: 101,
            budgetName: "T101",
            couponLogo: "",
            deliveryType: "1",
            activityExternalDeliveryChannelList: [
              {
                externalDeliveryChannelType: 2,
                externalDeliveryChannelCode: 4,
                externalDeliveryChannelName: "百川-豆商城",
              },
            ],
            inStoreDeliveryType: "2",
            outStoreDeliveryType: "1",
            userScope: 0,
          },
          periodDTOList: [
            {
              openTime: "00:00:00",
              closeTime: "23:59:59",
            },
          ],
          weekday: "0,1,2,3,4,5,6",
        });
      });
    });
  });
});
