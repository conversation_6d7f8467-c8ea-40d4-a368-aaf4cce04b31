// @ts-ignore
import { handleAPI, clearAPI } from "@ali/alsc-gateway-web-client";
import moment from "moment";

import {
  ActivityTypeEnum,
  InvestmentSchemaEnum,
  InvestmentTypeEnum,
} from "../../constants";

import { formatMarketPlay2ViewJson, formatViewJson2MarketPlay } from "./utils";
import strategy from ".";

const mockHistory = { push: () => {} };

describe("特价券", () => {
  describe("单活动", () => {
    let fakeActivityId = 10001;
    let createActivityRequests: any[] = [];

    beforeEach(() => {
      handleAPI(
        "ele-newretail-investment.NewretailInvestmentForbiddenWriteYundingService.isKunlunForbiddenWrite",
        async () => ({ data: false })
      );
      handleAPI(
        "ele-newretail-investment.NewretailInvestmentActivityYundingService.createInvestmentActivity",
        async (req: { params: any[] }) => {
          createActivityRequests.push(req);
          return {
            success: true,
            data: ++fakeActivityId,
          };
        }
      );
      handleAPI(
        "ele-newretail-investment.NewretailInvestmentActivityYundingService.uploadActivityRichTextDescription",
        async () => ({ success: true, data: "https://baidu.com" })
      );
    });

    afterEach(() => {
      createActivityRequests = [];
      clearAPI();
    });

    test("日常模版-checkNext", async () => {
      const creationContext = {
        createActiivtyStartTime: 0,
        createActivitySessionId: "0",
        isSingleActivity: true,
        investmentSchema: InvestmentSchemaEnum.RICHANG,
        investmentType: InvestmentTypeEnum.RICHANG,
        activityType: ActivityTypeEnum.ITEM_SPECIAL_COUPON,
        subsidyType: "P补",
        fineGrainedActivityTimeDisabled: true,
        copy: false,
      };
      const baseinfo = {
        actManageOrgLv1Id: "yygs1_hyyy_20250002",
        manageOrgLv1IdList: ["yygs1_hyyy_20250002", "yygs1_hyyy_20250003"],
        allowCancel: 2,
        signUpShopType: 0,
        activityTime: {
          weeks: [0, 1, 2, 3, 4, 5, 6],
          batchRange: {
            type: "all",
            list: [
              {
                start: "00:00",
                end: "23:59",
              },
            ],
          },
          start: moment("2023-07-12T16:00:00.000Z"),
          end: moment("2023-07-20T15:59:59.000Z"),
        },
        applyPlatform: 1,
        activityType: 2,
        richDescription: {
          version: "v1",
          content: [
            {
              type: "paragraph",
              children: [
                {
                  text: "test",
                },
              ],
            },
          ],
          text: "test",
        },
        name: "test",
        remark: "test",
        signupTime: {
          start: moment("2023-07-12T16:00:00.000Z"),
          end: moment("2023-07-20T15:59:59.000Z"),
        },
      };
      const dataStore = { data: { baseinfo } };
      // @ts-ignore
      await strategy
        .cable({ investmentSchema: InvestmentSchemaEnum.RICHANG })
        .checkNext(
          {
            step: { name: "baseinfo", title: "" },
            dataStore,
          },
          creationContext,
          mockHistory
        );

      Object.assign(dataStore.data, {
        playdata: {
          snapshot: {
            data: {
              outStoreDeliveryType: "2",
              externalDeliveryChannel: null,
              budget: "{}",
              ruleFree: '{"discount":0}',
              inStoreDeliveryType: "1",
              name: "test",
              creator: '{"id":"213137","prefix":"yc213137","name":"杨辰"}',
              dateRange:
                '{"start":"2023-07-13 00:00:00","end":"2023-07-20 23:59:59"}',
              couponStock: '{"totalLimit":{"value":10}}',
              couponName: '{"value":"test"}',
              couponValid: '{"fromDraw":{"value":3}}',
              drawLimit: '{"totalLimit":{"value":33},"dayLimit":{"value":22}}',
              logoImg: "",
              _workspace_uuid_: "ExukovQrae6xWn2rt4NgG",
              effectiveChannel: "1",
              rule: '{"discount":10000,"elemeSubsidy":0,"fixDiscount":1}',
              couponValidFix: '{"fromDraw":{"value":"0"}}',
            },
            type: "insert",
          },
        },
        scope: {
          pool: {
            value: 258086,
            label: "0519测试门店池",
            count: 4,
          },
          rule: {
            createdUserId: "213137",
            inviteBusinessActivityIds: [
              6323232009, 6323174007, 6323058004, 6323428001, 6323148098,
              6323186004, 6323234003, 6323234004, 6323232003, 6323234002,
            ],
            typeName: "商品",
            isNew: true,
            version: 0,
            createdAt: "2022-08-04T16:43:56.000Z",
            createdUserName: "杨辰",
            name: "yc-empty",
            typeId: 1000001,
            auditRulesInfoDto: {}, // 机审规则不需要，省略数据
            id: 8685,
            ruleId: 2646001,
            class:
              "me.ele.newretail.investment.yunding.service.activity.InvestmentAuditRuleYundingDTO",
            updatedAt: "2022-08-04T16:43:56.000Z",
            status: 3,
          },
          commodityAuditType: 1,
          removeCondition: [],
        },
      });

      const checkNextContext = {
        step: { name: "playdata", title: "" },
        dataStore,
      };
      // @ts-ignore
      await strategy
        .cable({ investmentSchema: InvestmentSchemaEnum.RICHANG })
        .checkNext(checkNextContext, creationContext, mockHistory);
      expect(
        createActivityRequests[createActivityRequests.length - 1]
      ).toStrictEqual({
        apiKey:
          "ele-newretail-investment.NewretailInvestmentActivityYundingService.createInvestmentActivity",
        params: [
          {
            actManageOrgLv1Id: "yygs1_hyyy_20250002",
            manageOrgLv1IdList: ["yygs1_hyyy_20250002", "yygs1_hyyy_20250003"],
            activityPlanId: undefined,
            activitySceneId: undefined,
            copyActivityId: undefined,
            description: "test",
            templateId: undefined,
            investmentType: 2,
            investmentSchema: 0,
            activityType: 1000021,
            name: "test",
            remark: "test",
            beginTime: 1689177600000,
            subsidyType: "P补",
            endTime: 1689868799000,
            allowCancel: 2,
            signUpShopType: 0,
            signUpStartTime: 1689177600000,
            signUpEndTime: 1689868799000,
            shopPoolId: 258086,
            shopPoolName: "0519测试门店池",
            commodityAuditType: 1,
            commodityRemoveCondition: {
              removeCondition: [],
            },
            auditMemberList: null,
            reviewRuleId: 2646001,
            reviewRuleName: "yc-empty",
            viewJson:
              '{"activityType":1000021,"activityLimitRule":{"totalCountLimit":10,"userDayCountLimit":22,"userTotalCountLimit":33},"activityMarketingInfo":{"availableDays":3,"couponLogo":"","couponName":"test","deliveryType":"1","activityExternalDeliveryChannelList":null,"inStoreDeliveryType":"1","outStoreDeliveryType":"2","userScope":0},"activityRule":{"discount":10000,"discountType":"FIXED_PRICE","platformSubsidy":0},"periodDTOList":[{"openTime":"00:00:00","closeTime":"23:59:59"}],"weekday":"0,1,2,3,4,5,6"}',
            idempotentKey: "undefined",
            attributes: {
              hasRichDescription: "on",
              richDescriptionURL: "https://baidu.com",
            },
          },
        ],
      });
    });

    test("零售医药免单-checkNext", async () => {
      const creationContext = {
        createActiivtyStartTime: 0,
        createActivitySessionId: "0",
        isSingleActivity: true,
        investmentSchema: InvestmentSchemaEnum.DRUG_FREE,
        investmentType: InvestmentTypeEnum.RICHANG,
        activityType: ActivityTypeEnum.ITEM_SPECIAL_COUPON,
        fineGrainedActivityTimeDisabled: true,
        subsidyType: "P补",
        copy: false,
      };
      const baseinfo = {
        actManageOrgLv1Id: "yygs1_hyyy_20250002",
        manageOrgLv1IdList: ["yygs1_hyyy_20250002", "yygs1_hyyy_20250003"],
        allowCancel: 2,
        signUpShopType: 0,
        activityTime: {
          weeks: [0, 1, 2, 3, 4, 5, 6],
          batchRange: {
            type: "custom",
            list: [
              {
                key: 3001,
                start: "00:00",
                end: "03:00",
              },
              {
                key: 3002,
                start: "04:00",
                end: "05:00",
              },
            ],
          },
          start: moment("2023-07-18T16:00:00.000Z"),
          end: moment("2023-07-31T15:59:59.000Z"),
        },
        applyPlatform: 1,
        activityType: 2,
        richDescription: {
          version: "v1",
          content: [
            {
              type: "paragraph",
              children: [
                {
                  text: "富文本展示",
                  color: "rgba(255,145,0,1)",
                },
              ],
            },
          ],
          text: "富文本展示",
        },
        name: "test",
        signupTime: {
          start: moment("2023-07-18T16:00:00.000Z"),
          end: moment("2023-07-31T15:59:59.000Z"),
        },
        businessFormIds: [
          {
            businessFormId: 100,
            businessFormName: "test",
          },
          {
            businessFormId: 101,
            businessFormName: "test1",
          },
        ],
        businessLineIdList: [
          {
            businessLineId: 10,
            businessLineName: "test",
          },
        ],
      };
      const dataStore = { data: { baseinfo } };
      // @ts-ignore
      await strategy
        .cable({ investmentSchema: InvestmentSchemaEnum.DRUG_FREE })
        .checkNext(
          {
            step: { name: "baseinfo", title: "" },
            dataStore,
          },
          creationContext,
          mockHistory
        );

      Object.assign(dataStore.data, {
        playdata: {
          data: {
            outStoreDeliveryType: "1",
            externalDeliveryChannel: [
              {
                externalDeliveryChannelType: 2,
                externalDeliveryChannelCode: 1,
                externalDeliveryChannelName: "全部渠道",
              },
            ],
            budget: {},
            ruleFree: {
              discount: 0,
              elemeSubsidy: 0,
              shopSubsidyPercent: 100,
              platformSubsidyPercent: 0,
            },
            inStoreDeliveryType: "1",
            name: "医药免单",
            creator: {
              id: "329684",
              prefix: "yufanguo.gyf",
              name: "郭宇帆",
            },
            dateRange: ["2023-07-18T16:00:00.000Z", "2023-07-31T15:59:59.000Z"],
            couponStock: {
              totalLimit: {
                value: 1,
              },
            },
            couponName: {
              value: "测试医药免单",
            },
            couponValid: {
              validRange: [
                "2023-07-18T16:00:00.000Z",
                "2023-07-31T15:59:59.000Z",
              ],
            },
            drawLimit: {
              totalLimit: {
                value: 1,
              },
            },
            logoImg: "",
            _workspace_uuid_: "ExukovQrae6xWn2rt4NgG",
            effectiveChannel: "1",
            rule: {
              fixDiscount: 0.01,
            },
            couponValidFix: {
              fromDraw: {
                value: "0",
              },
            },
          },
          type: "insert",
          snapshot: {
            data: {
              outStoreDeliveryType: "1",
              externalDeliveryChannel:
                '[{"externalDeliveryChannelType":2,"externalDeliveryChannelCode":1,"externalDeliveryChannelName":"全部渠道"}]',
              budget: "{}",
              ruleFree:
                '{"discount":0,"elemeSubsidy":0,"shopSubsidyPercent":100,"platformSubsidyPercent":0}',
              inStoreDeliveryType: "1",
              name: "医药免单",
              creator:
                '{"id":"329684","prefix":"yufanguo.gyf","name":"郭宇帆"}',
              dateRange:
                '{"start":"2023-07-19 00:00:00","end":"2023-07-31 23:59:59"}',
              couponStock: '{"totalLimit":{"value":1}}',
              couponName: '{"value":"测试医药免单"}',
              couponValid:
                '{"validRange":{"start":"2023-07-19 00:00:00","end":"2023-07-31 23:59:59"}}',
              drawLimit: '{"totalLimit":{"value":1}}',
              logoImg: "",
              _workspace_uuid_: "ExukovQrae6xWn2rt4NgG",
              effectiveChannel: "1",
              rule: '{"fixDiscount":1}',
              couponValidFix: '{"fromDraw":{"value":"0"}}',
            },
            type: "insert",
          },
        },
        scope: {
          pool: {
            value: 250698,
            label: "pyf门店池",
            count: 468,
          },
          rule: {
            createdUserId: "222146",
            inviteBusinessActivityIds: [
              6323720003, 6323068001, 6323232012, 6323234013, 6322958001,
              6323124025, 6323232010, 6323058001, 6322856007, 6322844001,
              6323790012, 6323744069, 6323498001, 6322844002,
            ],
            typeName: "商品",
            isNew: true,
            version: 0,
            createdAt: "2022-06-10T12:50:34.000Z",
            createdUserName: "赵志刚",
            name: "zzg商品规则验证",
            typeId: 1000001,
            auditRulesInfoDto: {
              minCommodityStocks: null,
              minOriginalPrice: null,
              skuTitleKeyword: null,
              groupId: null,
              d30MaxTotalAmt: null,
              d7MaxValidOrderCnt: null,
              maxPresentPrice: null,
              d7MinPrice: null,
              minPresentPrice: null,
              itemStatus: 1,
              d7MinTotalAmt: null,
              smbMaxEffectiveStock: null,
              class:
                "me.ele.newretail.investment.yunding.service.activity.InvestmentAuditRuleInfoYundingDTO",
              upcCode: null,
              maxCommodityAmount: 10,
              d7MinValidOrderCnt: null,
              maxOriginalPrice: null,
              d7MaxTotalAmt: null,
              minStartPurchaseNum: null,
              maxDiscount: null,
              skuCategoryName:
                '[{"id":"201230206","name":"水果"},{"id":"201232304","name":"蔬菜"},{"id":"201220512","name":"酒"},{"id":"201220311","name":"饮料"},{"id":"201229610","name":"休闲食品"},{"id":"201230607","name":"鲜花绿植"}]',
              commodityAmount: 1,
              d30MaxValidOrderCnt: null,
              basicSelectType: 20,
              maxStartPurchaseNum: null,
              d15MinPrice: null,
              smbMinEffectiveStock: null,
              d30TotalAmt: null,
              minDiscount: null,
              maxCommodityStocks: null,
              d30MinValidOrderCnt: null,
              commodityName: null,
            },
            id: 6356,
            ruleId: 1602001,
            class:
              "me.ele.newretail.investment.yunding.service.activity.InvestmentAuditRuleYundingDTO",
            updatedAt: "2022-06-10T12:50:34.000Z",
            status: 3,
          },
          commodityAuditType: 0,
          commodityPassCondition: {
            categoryCorrect: true,
          },
          removeCondition: [1],
        },
      });

      const checkNextContext = {
        step: { name: "playdata", title: "" },
        dataStore,
      };
      // @ts-ignore
      await strategy
        .cable({ investmentSchema: InvestmentSchemaEnum.DRUG_FREE })
        .checkNext(checkNextContext, creationContext, mockHistory);
      expect(
        createActivityRequests[createActivityRequests.length - 1]
      ).toStrictEqual({
        apiKey:
          "ele-newretail-investment.NewretailInvestmentActivityYundingService.createInvestmentActivity",
        params: [
          {
            actManageOrgLv1Id: "yygs1_hyyy_20250002",
            manageOrgLv1IdList: ["yygs1_hyyy_20250002", "yygs1_hyyy_20250003"],
            activityPlanId: undefined,
            activitySceneId: undefined,
            copyActivityId: undefined,
            templateId: undefined,
            investmentType: 2,
            investmentSchema: 19,
            activityType: 1000021,
            name: "test",
            description: "富文本展示",
            beginTime: 1689696000000,
            endTime: 1690819199000,
            allowCancel: 2,
            remark: undefined,
            signUpShopType: 0,
            signUpStartTime: 1689696000000,
            signUpEndTime: 1690819199000,
            subsidyType: "P补",
            shopPoolId: 250698,
            shopPoolName: "pyf门店池",
            commodityAuditType: 0,
            commodityAuditCondition: {
              passCondition: {
                commodityCategoryCorrect: true,
                commodityNameKeywordCorrect: false,
                commodityNameKeywordCondition: null,
                commodityTpcCorrect: false,
                commodityAuditTpcInfoCondition: null,
              },
              refuseCondition: {
                commodityNameKeywordRefuse: false,
                commodityNameKeywordCondition: null,
              },
            },
            commodityRemoveCondition: {
              removeCondition: [1],
            },
            auditMemberList: null,
            reviewRuleId: 1602001,
            reviewRuleName: "zzg商品规则验证",
            viewJson:
              '{"activityType":1000021,"activityLimitRule":{"totalCountLimit":1,"userTotalCountLimit":1},"activityMarketingInfo":{"availableStartTime":"2023-07-19 00:00:00","availableEndTime":"2023-07-31 23:59:59","couponLogo":"","couponName":"测试医药免单","deliveryType":"1","activityExternalDeliveryChannelList":[{"externalDeliveryChannelType":2,"externalDeliveryChannelCode":1,"externalDeliveryChannelName":"全部渠道"}],"inStoreDeliveryType":"1","outStoreDeliveryType":"1","userScope":0},"activityRule":{"discount":0,"discountType":"FIXED_PRICE","platformSubsidyMax":0,"shopSubsidyPercent":100,"platformSubsidyPercent":0},"periodDTOList":[{"openTime":"00:00:00","closeTime":"03:00:59"},{"openTime":"04:00:00","closeTime":"05:00:59"}],"weekday":"0,1,2,3,4,5,6"}',
            attributes: {
              hasRichDescription: "on",
              richDescriptionURL: "https://baidu.com",
            },
            idempotentKey: "undefined",
          },
        ],
      });
    });

    test("商品通兑卡-checkNext", async () => {
      const creationContext = {
        createActiivtyStartTime: 0,
        createActivitySessionId: "0",
        isSingleActivity: true,
        investmentSchema: InvestmentSchemaEnum.COMMODITY_EXCHANGE_CARD,
        investmentType: InvestmentTypeEnum.RICHANG,
        activityType: ActivityTypeEnum.ITEM_SPECIAL_COUPON,
        fineGrainedActivityTimeDisabled: true,
        copy: false,
      };
      const baseinfo = {
        actManageOrgLv1Id: "yygs1_hyyy_20250002",
        manageOrgLv1IdList: ["yygs1_hyyy_20250002", "yygs1_hyyy_20250003"],
        allowCancel: 2,
        signUpShopType: 0,
        activityTime: {
          weeks: [6],
          batchRange: {
            type: "all",
            list: [
              {
                start: "00:00",
                end: "23:59",
              },
            ],
          },
          start: moment("2023-10-06T16:00:00.000Z"),
          end: moment("2023-10-07T15:59:59.000Z"),
        },
        applyPlatform: 1,
        activityType: 2,
        richDescription: {
          version: "v1",
          content: [
            {
              type: "paragraph",
              children: [
                {
                  text: "zyh-咖啡通兑卡100700zyh-咖啡通兑卡100700",
                },
              ],
            },
          ],
          text: "zyh-咖啡通兑卡100700zyh-咖啡通兑卡100700",
        },
        name: "zyh-咖啡通兑卡100700",
        remark: "test",
        signupTime: {
          start: moment("2023-10-06T16:00:00.000Z"),
          end: moment("2023-10-07T15:59:59.000Z"),
        },
      };
      const dataStore = { data: { baseinfo } };
      // @ts-ignore
      await strategy
        .cable({
          investmentSchema: InvestmentSchemaEnum.COMMODITY_EXCHANGE_CARD,
        })
        .checkNext(
          {
            step: { name: "baseinfo", title: "" },
            dataStore,
          },
          creationContext,
          mockHistory
        );
      Object.assign(dataStore.data, {
        playdata: {
          snapshot: {
            data: {
              outStoreDeliveryType: "1",
              externalDeliveryChannel:
                '[{"externalDeliveryChannelType":2,"externalDeliveryChannelCode":88,"externalDeliveryChannelName":"通兑卡渠道"}]',
              budget: "{}",
              inStoreDeliveryType: "2",
              name: "zyh-咖啡通兑卡100700",
              creator:
                '{"id":"WB899541","prefix":"wb-zyh899541","name":"张宇航"}',
              dateRange:
                '{"start":"2023-10-07 00:00:00","end":"2023-10-07 23:59:59"}',
              couponStock: '{"totalLimit":{"value":999999999}}',
              couponName: '{"value":"test-测试红包"}',
              couponValid: '{"fromDraw":{"value":120}}',
              drawLimit:
                '{"totalLimit":{"value":999999999},"dayLimit":{"value":999999999}}',
              logoImg: "",
              _workspace_uuid_: "3oNNWxkJYzc6eWQyKz5Lp2",
              effectiveChannel: "3",
              rule: '{"discount":1,"elemeSubsidy":0}',
            },
            type: "insert",
          },
        },
        scope: {
          pool: {
            value: 271111,
            label: "shz测试门店池",
            count: 5,
          },
          rule: {
            createdUserId: "230789",
            inviteBusinessActivityIds: [],
            typeName: "商品",
            isNew: true,
            version: 3,
            createdAt: "2023-07-07T14:45:44.000Z",
            createdUserName: "代海镇",
            name: "test线上限购1份",
            auditRulesInfoDto: {
              minCommodityStocks: null,
              minOriginalPrice: "1",
              skuTitleKeyword: null,
              groupId: null,
              d30MaxTotalAmt: null,
              d7MaxValidOrderCnt: null,
              maxPresentPrice: null,
              d7MinPrice: null,
              minPresentPrice: null,
              itemStatus: 1,
              d7MinTotalAmt: null,
              smbMaxEffectiveStock: null,
              class:
                "me.ele.newretail.investment.yunding.service.activity.InvestmentAuditRuleInfoYundingDTO",
              upcCode: null,
              maxCommodityAmount: null,
              d7MinValidOrderCnt: null,
              maxOriginalPrice: "66",
              d7MaxTotalAmt: null,
              minStartPurchaseNum: 1,
              maxDiscount: null,
              skuCategoryName:
                '[{"id":"201230206","name":"水果"},{"id":"201232304","name":"蔬菜"},{"id":"201218709","name":"肉禽蛋类"},{"id":"201220014","name":"水产"},{"id":"201219108","name":"豆制品"},{"id":"201228115","name":"冷冻食品"},{"id":"201221015","name":"半成品食材"},{"id":"201220310","name":"乳制品"},{"id":"201228017","name":"粮油米面"},{"id":"201217417","name":"鲜食/熟食"},{"id":"201229609","name":"调味品"},{"id":"201230011","name":"便利食品"},{"id":"201220311","name":"饮料"},{"id":"201220512","name":"酒"},{"id":"201228018","name":"冲调"},{"id":"201229610","name":"休闲食品"},{"id":"201230607","name":"鲜花绿植"},{"id":"201220616","name":"个护清洁"},{"id":"201222621","name":"美妆饰品"},{"id":"201221722","name":"家庭清洁"},{"id":"201231010","name":"居家日用"},{"id":"201228205","name":"家居家装"},{"id":"201229312","name":"办公用品"},{"id":"201231204","name":"运动户外用品"},{"id":"201229611","name":"宠物用品"}]',
              d30MaxValidOrderCnt: null,
              commodityAmount: null,
              basicSelectType: 20,
              d15MinPrice: null,
              maxStartPurchaseNum: 1,
              smbMinEffectiveStock: null,
              d30TotalAmt: null,
              minDiscount: null,
              maxCommodityStocks: null,
              d30MinValidOrderCnt: null,
              commodityName: null,
            },
            typeId: 1000001,
            id: 24163,
            ruleId: 10048001,
            class:
              "me.ele.newretail.investment.yunding.service.activity.InvestmentAuditRuleYundingDTO",
            updatedAt: "2023-07-07T14:47:08.000Z",
            status: 2,
          },
          commodityAuditType: 1,
          removeCondition: [],
        },
      });

      const checkNextContext = {
        step: { name: "playdata", title: "" },
        dataStore,
      };
      // @ts-ignore
      await strategy
        .cable({
          investmentSchema: InvestmentSchemaEnum.COMMODITY_EXCHANGE_CARD,
        })
        .checkNext(checkNextContext, creationContext, mockHistory);
      expect(
        createActivityRequests[createActivityRequests.length - 1]
      ).toStrictEqual({
        apiKey:
          "ele-newretail-investment.NewretailInvestmentActivityYundingService.createInvestmentActivity",
        params: [
          {
            actManageOrgLv1Id: "yygs1_hyyy_20250002",
            manageOrgLv1IdList: ["yygs1_hyyy_20250002", "yygs1_hyyy_20250003"],
            activityPlanId: undefined,
            activitySceneId: undefined,
            copyActivityId: undefined,
            description: "zyh-咖啡通兑卡100700zyh-咖啡通兑卡100700",
            templateId: undefined,
            investmentType: 2,
            investmentSchema: 24,
            activityType: 1000021,
            name: "zyh-咖啡通兑卡100700",
            remark: "test",
            beginTime: 1696608000000,
            subsidyType: undefined,
            endTime: 1696694399000,
            allowCancel: 2,
            signUpShopType: 0,
            signUpStartTime: 1696608000000,
            signUpEndTime: 1696694399000,
            shopPoolId: 271111,
            shopPoolName: "shz测试门店池",
            commodityAuditType: 1,
            commodityRemoveCondition: {
              removeCondition: [],
            },
            auditMemberList: null,
            reviewRuleId: 10048001,
            reviewRuleName: "test线上限购1份",
            viewJson:
              '{"activityType":1000021,"activityLimitRule":{"totalCountLimit":999999999,"userDayCountLimit":999999999,"userTotalCountLimit":999999999},"activityMarketingInfo":{"availableDays":120,"couponLogo":"","couponName":"test-测试红包","deliveryType":"3","activityExternalDeliveryChannelList":[{"externalDeliveryChannelType":2,"externalDeliveryChannelCode":88,"externalDeliveryChannelName":"通兑卡渠道"}],"inStoreDeliveryType":"2","outStoreDeliveryType":"1","userScope":0},"activityRule":{"discount":1,"discountType":"FIXED_PRICE","platformSubsidy":0},"periodDTOList":[{"openTime":"00:00:00","closeTime":"23:59:59"}],"weekday":"6"}',
            idempotentKey: "undefined",
            attributes: {
              hasRichDescription: "on",
              richDescriptionURL: "https://baidu.com",
            },
          },
        ],
      });
    });

    test("utils：RICHANGE-formData => viewjson", () => {
      const formData = {
        outStoreDeliveryType: "2",
        externalDeliveryChannel: null,
        budget: "{}",
        ruleFree: '{"discount":0}',
        inStoreDeliveryType: "1",
        name: "回归-特价券-日常招商",
        creator: '{"id":"213137","prefix":"yc213137","name":"杨辰"}',
        dateRange:
          '{"start":"2023-07-12 00:00:00","end":"2023-07-19 23:59:59"}',
        couponStock: '{"totalLimit":{"value":1}}',
        couponName: '{"value":"测试红包"}',
        couponValid: '{"fromDraw":{"value":2}}',
        drawLimit: '{"totalLimit":{"value":3},"dayLimit":{"value":2}}',
        logoImg: "",
        _workspace_uuid_: "ExukovQrae6xWn2rt4NgG",
        effectiveChannel: "1",
        rule: '{"discount":10000,"elemeSubsidy":0,"fixDiscount":1}',
        couponValidFix: '{"fromDraw":{"value":"0"}}',
      };

      const baseinfo = {
        activityTime: {
          weeks: [0, 1, 2, 3, 4, 5, 6],
          batchRange: {
            type: "all",
            list: [
              {
                start: "00:00",
                end: "23:59",
              },
            ],
          },
          start: "2023-07-11T16:00:00.000Z",
          end: "2023-07-19T15:59:59.000Z",
        },
      };

      const viewJson = formatMarketPlay2ViewJson({
        // @ts-ignore
        formData,
        baseinfo,
        investmentSchema: InvestmentSchemaEnum.RICHANG,
      });

      expect(viewJson).toStrictEqual({
        activityType: 1000021,
        activityLimitRule: {
          dayCountLimit: undefined,
          totalCountLimit: 1,
          userDayCountLimit: 2,
          userTotalCountLimit: 3,
        },
        activityMarketingInfo: {
          availableDays: 2,
          availableEndTime: undefined,
          availableStartTime: undefined,
          budgetId: undefined,
          budgetName: undefined,
          couponLogo: "",
          couponName: "测试红包",
          deliveryType: "1",
          activityExternalDeliveryChannelList: null,
          inStoreDeliveryType: "1",
          outStoreDeliveryType: "2",
          userScope: 0,
        },
        activityRule: {
          discount: 10000,
          discountType: "FIXED_PRICE",
          platformSubsidy: 0,
        },
        periodDTOList: [
          {
            openTime: "00:00:00",
            closeTime: "23:59:59",
          },
        ],
        weekday: "0,1,2,3,4,5,6",
      });
    });

    // TODO： 商品通兑卡10.07活动还未创建成功，活动创建成功后需要补充单元测试
    // 咖啡通兑卡和日常模板提交数据结构一致，无特殊处理
    test("商品通兑卡utils: formData => viewjson", () => {
      const formData = {
        outStoreDeliveryType: "1",
        externalDeliveryChannel:
          '[{"externalDeliveryChannelType":2,"externalDeliveryChannelCode":88,"externalDeliveryChannelName":"通兑卡渠道"}]',
        budget:
          '{"budgetId":"******************","budgetName":"招商红包预算单品叠加"}',
        inStoreDeliveryType: "2",
        name: "test",
        creator: '{"id":"329684","prefix":"yufanguo.gyf","name":"郭宇帆"}',
        dateRange:
          '{"start":"2023-07-19 00:00:00","end":"2023-07-31 23:59:59"}',
        couponStock: '{"totalLimit":{"value":1}}',
        couponName: '{"value":"测试"}',
        couponValid: '{"fromDraw":{"value":0}}',
        drawLimit: '{"totalLimit":{"value":1}}',
        logoImg: "",
        _workspace_uuid_: "ExukovQrae6xWn2rt4NgG",
        effectiveChannel: "1",
        rule: '{"elemeSubsidy":300,"discount":1}',
      };

      const baseinfo = {
        activityTime: {
          weeks: [0, 1, 2, 3, 4, 5, 6],
          range: {
            type: "all",
            start: "00:00",
            end: "23:59",
          },
          start: "2023-07-18T16:00:00.000Z",
          end: "2023-07-31T15:59:59.000Z",
        },
      };

      const viewJson = formatMarketPlay2ViewJson({
        // @ts-ignore
        formData,
        // @ts-ignore
        baseinfo,
        investmentSchema: InvestmentSchemaEnum.COMMODITY_EXCHANGE_CARD,
      });

      expect(viewJson).toStrictEqual({
        activityType: 1000021,
        activityLimitRule: {
          dayCountLimit: undefined,
          totalCountLimit: 1,
          userTotalCountLimit: 1,
          userDayCountLimit: undefined,
        },
        activityMarketingInfo: {
          availableDays: 0,
          budgetId: "******************",
          budgetName: "招商红包预算单品叠加",
          availableEndTime: undefined,
          availableStartTime: undefined,
          couponLogo: "",
          couponName: "测试",
          deliveryType: "1",
          activityExternalDeliveryChannelList: [
            {
              externalDeliveryChannelType: 2,
              externalDeliveryChannelCode: 88,
              externalDeliveryChannelName: "通兑卡渠道",
            },
          ],
          inStoreDeliveryType: "2",
          outStoreDeliveryType: "1",
          userScope: 0,
        },
        activityRule: {
          discount: 1,
          discountType: "FIXED_PRICE",
          platformSubsidy: 300,
        },
        periodDTOList: [],
        weekday: "0,1,2,3,4,5,6",
      });
    });

    test("商品通兑卡utils: viewjson => fromData", () => {
      const viewDto = {
        activityLimitRule: { totalCountLimit: 1, userTotalCountLimit: 1 },
        activityMarketingInfo: {
          activityExternalDeliveryChannelList: [
            {
              externalDeliveryChannelCode: 88,
              externalDeliveryChannelName: "通兑卡渠道",
              externalDeliveryChannelType: 2,
            },
          ],
          availableDays: 0,
          budgetId: "******************",
          budgetName: "招商红包预算单品叠加",
          couponLogo: "",
          couponName: "测试",
          deliveryType: 1,
          inStoreDeliveryType: 2,
          outStoreDeliveryType: 1,
          userScope: 0,
        },
        activityRule: { discount: 1, platformSubsidy: 300 },
        activityType: 1000021,
        attributes: {
          hasRichDescription: "on",
          version: "3.0",
          richDescriptionURL:
            "https://bwm-private-b.oss-cn-hangzhou.aliyuncs.com/mkt/fda1e592fa7da7ed1d9f44d3fc655705?Expires=2005365654&OSSAccessKeyId=LTAIhP07XE91RzLJ&Signature=7ps3FxlnZnXIT%2F%2BFuvZliYyDnOI%3D",
        },
        periodDTOList: [
          { closeTime: "23:59:59", openTime: "20:00:00", weekday: "5" },
          { closeTime: "23:59:59", openTime: "00:00:00", weekday: "6,0" },
        ],
      };
      const baseInfo = {
        createdUserName: "郭宇帆",
        createdUserId: "329684",
      };
      const investmentSchema = InvestmentSchemaEnum.COMMODITY_EXCHANGE_CARD;
      const fromData = formatViewJson2MarketPlay({
        // @ts-ignore
        viewDto,
        // @ts-ignore
        baseInfo,
        investmentSchema,
      });
      expect(fromData).toStrictEqual({
        outStoreDeliveryType: "1",
        externalDeliveryChannel:
          '[{"externalDeliveryChannelCode":88,"externalDeliveryChannelName":"通兑卡渠道","externalDeliveryChannelType":2}]',
        budget:
          '{"budgetId":"******************","budgetName":"招商红包预算单品叠加"}',
        inStoreDeliveryType: "2",
        dateRange: undefined,
        couponStock: '{"totalLimit":{"value":1}}',
        couponName: '{"value":"测试"}',
        couponValid: '{"fromDraw":{"value":0}}',
        creator: '{"name":"郭宇帆","id":"329684"}',
        drawLimit: '{"totalLimit":{"value":1}}',
        effectiveChannel: "1",
        logoImg: undefined,
        ruleFree: "{}",
        rule: '{"discount":1,"elemeSubsidy":300}',
      });
    });
  });
});
