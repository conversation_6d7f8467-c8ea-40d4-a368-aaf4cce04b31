import * as React from "react";
import * as api from "../../../api";
import { Message } from "@alifd/next";
import { BudgetPickerDetail } from "@alife/carbon-biz-kunlun-zs/lib/components/BudgetPicker/BudgetPicker";

// interface ViewJson {
//   activityMarketingInfo?: {
//     discountPriceRangeType: number; // 优惠形式 ：1是价格固定，2是价格区间
//     cityDistinguishType: number; // N选M没有该字段，城市区分： 1是活动价区分城市，2是活动价不区分城市
//     budgetName: string;
//     budgetId: string;
//   };
//   commodityListFilePath?: string;
// }

interface Props {
  activityInfo: { activityId: number | string };
  env: "daily" | "pre" | "prod";
}

const Field: React.FC<any> = ({ label, children, extra }) => {
  return (
    <div className="zs-act-field">
      <label htmlFor="">{label}</label>
      <div>{children}</div>
      {extra ? <div className="zs-act-field-extra">{extra}</div> : null}
    </div>
  );
};

function downloadFile(fileUrl: string, fileName?: string): void {
  const a = document.createElement('a');
  a.href = fileUrl;
  a.download = fileName ?? 'download';

  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
}

const DiscountInfoViewForFoodBeans: React.FC<Props> = ({ activityInfo, env }) => {
  const { activityId } = activityInfo;
  const [detail, setDetail] = React.useState<any>();

  React.useEffect(() => {
    api.subActivity.getDetail(activityId).then((res) => {
      setDetail({
        viewJson: JSON.parse(res?.data?.viewJson) || "",
        activityInfo: res?.data?.investmentActivityDetailDTO
      })
    });
  }, [activityId]);

  const downloadDiscountInfo = () => {
    api.subActivity.getCommodityExcelUrl(activityId).then(res => {
      if (res?.data) {
        downloadFile(res?.data, "优惠详情.xlsx");
      } else {
        Message.error("下载失败，请稍后再试");
      }
    }).catch(() => {
      Message.error("下载失败，请稍后再试");
    });
  };

  if (!detail?.viewJson) {
    return null;
  }

  return (
    <div className="zs-act-info">
      <div className="zs-act-area">
        <div className="zs-act-area-title" style={{ marginBottom: 30 }}>
          优惠规则
        </div>
        <div className="zs-act-section">
          <Field label="优惠规则：">
            <div style={{ color: "#409EFF", cursor: "pointer" }} onClick={downloadDiscountInfo}>
              下载详情{">"}
              {">"}
            </div>
          </Field>
          <Field label="预算：">
            <BudgetPickerDetail
              value={detail?.viewJson.activityMarketingInfo}
              env={{
                view: 'detail',
                env: env,
                detailContext: {
                  activityId: activityInfo.activityId as any,
                }
              }}
            />
          </Field>
        </div>
      </div>
    </div>
  );
};

export default DiscountInfoViewForFoodBeans;
