import moment from "moment";

import { isValueSet, parseJsonSafe } from "../../common";
import { ActivityTypeEnum, InvestmentSchemaEnum } from "../../constants";

import {
  BaseInfoForCreationContext,
  BaseInfoForMarketPlay,
  DiscountRule,
  MarketPlayForFoodieBeans,
  MarketPlayForPlayData,
  ViewJsonForFoodieBeans,
  ViewJsonForPlayData,
} from "./constants";

export const formatMarketPlay2ViewJson = ({
  formData,
  baseinfo,
  investmentSchema,
}: {
  formData: MarketPlayForPlayData;
  baseinfo: BaseInfoForMarketPlay;
  investmentSchema: InvestmentSchemaEnum;
}) => {
  const budget = formData.budget && parseJsonSafe(formData.budget);
  const couponStock = parseJsonSafe(formData.couponStock);
  const couponName = parseJsonSafe(formData.couponName);
  const couponValid = parseJsonSafe(formData.couponValid);
  const drawLimit = parseJsonSafe(formData.drawLimit);
  const effectiveChannel = formData.effectiveChannel;
  const externalDeliveryChannel =
    formData.externalDeliveryChannel &&
    parseJsonSafe(formData.externalDeliveryChannel);
  const inStoreDeliveryType = formData.inStoreDeliveryType;
  const logoImg = formData.logoImg;
  const outStoreDeliveryType = formData.outStoreDeliveryType;
  const rule = parseJsonSafe(formData.rule);
  const ruleFree = parseJsonSafe(formData.ruleFree);
  
  // 与模板相关数据
  let _activityRule;
  let _availableDays;
  let _dayCountLimit;

  if (isValueSet(couponValid?.fromDraw?.value)) {
    _availableDays = couponValid?.fromDraw?.value;
  }

  switch (investmentSchema) {
    case InvestmentSchemaEnum.DRUG_FREE:
      // 医药免单
      _activityRule = {
        discount: ruleFree?.discount,
        discountType: 'FIXED_PRICE',
        platformSubsidyMax: ruleFree?.elemeSubsidy,
        shopSubsidyPercent: ruleFree?.shopSubsidyPercent,
        platformSubsidyPercent: ruleFree?.platformSubsidyPercent,
      };
      _dayCountLimit = couponStock?.dayLimit?.value;
      break;
    case InvestmentSchemaEnum.COMMODITY_EXCHANGE_CARD:  // 商品通兑卡 与日常模板提交数据结构一致，无需处理
    case InvestmentSchemaEnum.CARD_SALES:  // 站外卡券售卖 与日常模板提交数据结构一致，无需处理
    case InvestmentSchemaEnum.RICHANG:  // 日常 case
      _activityRule = {
        discount: rule?.discount,
        discountType: 'FIXED_PRICE',
        platformSubsidy: rule?.elemeSubsidy,
      }
      _dayCountLimit = undefined;
      break;
    default:
      // 理论不会走到这个分支，目前只有三种场景、医药免单、商品通兑卡、日常模板，都已有响应的case
      break;
  }

  const viewJson = {
    activityType: ActivityTypeEnum.ITEM_SPECIAL_COUPON,
    activityLimitRule: {
      totalCountLimit: couponStock?.totalLimit?.value,
      dayCountLimit: _dayCountLimit,
      userDayCountLimit: drawLimit?.dayLimit?.value,
      userTotalCountLimit: drawLimit?.totalLimit?.value,
    },

    activityMarketingInfo: {
      availableDays: _availableDays,
      availableStartTime: couponValid?.validRange?.start || undefined,
      availableEndTime: couponValid?.validRange?.end || undefined,
      budgetId: budget?.budgetId,
      budgetName: budget?.budgetName,
      couponLogo: logoImg,
      couponName: couponName?.value,
      deliveryType: effectiveChannel, // 业务场景
      activityExternalDeliveryChannelList: externalDeliveryChannel,
      inStoreDeliveryType: inStoreDeliveryType,
      outStoreDeliveryType: outStoreDeliveryType,
      userScope: 0,
    },
    activityRule: _activityRule,
    periodDTOList: (baseinfo?.activityTime?.batchRange?.list || []).map(
      (r: any) => {
        return {
          openTime: r?.start + ":00",
          closeTime: r?.end + ":59",
        };
      }
    ),
    weekday: (baseinfo?.activityTime.weeks || []).join(","),
  };
  return viewJson;
};

/**
 * 详情展示， viewJson 转 玩法配置语法
 */
export const formatViewJson2MarketPlay = ({
  viewDto,
  baseInfo,
  investmentSchema,
}: {
  viewDto: ViewJsonForPlayData | ViewJsonForFoodieBeans;
  baseInfo: BaseInfoForCreationContext;
  investmentSchema: InvestmentSchemaEnum;
}) => {
  const activityLimitRule = viewDto?.activityLimitRule;
  const activityMarketingInfo = viewDto?.activityMarketingInfo;
  const activityRule = viewDto?.activityRule;
  let budget = {};
  let creator = {};
  let couponName = {};
  let couponStock = {};
  let couponValid = {};
  let dateRange;
  let drawLimit = {};
  let effectiveChannel;
  let externalDeliveryChannel;
  let inStoreDeliveryType;
  let logoImg;
  let outStoreDeliveryType;
  let rule = {};
  let ruleFree = {};

  // 预算信息
  if (
    isValueSet(activityMarketingInfo.budgetId) &&
    isValueSet(activityMarketingInfo.budgetName)
  ) {
    budget = {
      budgetId: activityMarketingInfo.budgetId,
      budgetName: activityMarketingInfo.budgetName,
    };
  }

  // 创建人
  if (
    isValueSet(baseInfo.createdUserId) &&
    isValueSet(baseInfo.createdUserName)
  ) {
    creator = {
      name: `${baseInfo.createdUserName}`,
      id: baseInfo.createdUserId,
    };
  }

  // 店内分类名称
  if (isValueSet(activityMarketingInfo?.couponName)) {
    couponName = {
      value: activityMarketingInfo?.couponName,
    };
  }

  // 库存限制
  if (isValueSet(activityLimitRule?.totalCountLimit)) {
    couponStock = {
      totalLimit: {
        value: activityLimitRule?.totalCountLimit,
      },
    };
  } else {
    // 吃货豆模版活动的 viewJson 当中不包含库存限制，会执行到这个分支
  }

  if (investmentSchema === InvestmentSchemaEnum.DRUG_FREE) {
    if (isValueSet(activityLimitRule?.dayCountLimit)) {
      couponStock = {
        ...couponStock,
        dayLimit: {
          value: activityLimitRule?.dayCountLimit,
        },
      };
    }
  }

  // 红包使用时间
  if (isValueSet(activityMarketingInfo?.availableDays)) {
    // 周末一分购模版只有有效天数，所以日常模版，周末一分购、零售医药免单都会走到这个分支。（周末一分购模版已经下掉）
    couponValid = {
      fromDraw: { value: activityMarketingInfo.availableDays },
    };
  } else if (
    isValueSet(activityMarketingInfo?.availableStartTime) &&
    isValueSet(activityMarketingInfo?.availableEndTime)
  ) {
    // 日常模版、零售医药免单会走到这个分支
    couponValid = {
      validRange: {
        start: moment(activityMarketingInfo.availableStartTime).format(
          "YYYY-MM-DD HH:mm:ss"
        ),
        end: moment(activityMarketingInfo.availableEndTime).format(
          "YYYY-MM-DD HH:mm:ss"
        ),
      },
    };
  } else {
    // 吃货豆模版活动的 viewJson 当中不包含券有效期信息，会执行到这个分支（这种活动有效天数、有效时间区间都应该为空）
  }

  // 红包发放时间
  if (baseInfo.beginTime && baseInfo.endTime) {
    dateRange = {
      start: moment(baseInfo.beginTime).format("YYYY-MM-DD HH:mm:ss"),
      end: moment(baseInfo.endTime).format("YYYY-MM-DD HH:mm:ss"),
    };
  }

  // 领券限制
  if (
    isValueSet(activityLimitRule?.userDayCountLimit) ||
    isValueSet(activityLimitRule?.userTotalCountLimit)
  ) {
    drawLimit = {
      dayLimit: activityLimitRule?.userDayCountLimit && {
        value: activityLimitRule.userDayCountLimit,
      },
      totalLimit: activityLimitRule?.userTotalCountLimit && {
        value: activityLimitRule.userTotalCountLimit,
      },
    };
  } else {
    // 吃货豆模版活动的 viewJson 当中不包含券有效期信息，会执行到这个分支（这种活动有效天数、有效时间区间都应该为空）
  }

  // 业务场景
  if (isValueSet(activityMarketingInfo?.deliveryType)) {
    effectiveChannel = activityMarketingInfo.deliveryType + "";
  }

  // 限定渠道
  if (isValueSet(activityMarketingInfo.activityExternalDeliveryChannelList)) {
    externalDeliveryChannel =
      activityMarketingInfo.activityExternalDeliveryChannelList;
  }

  // 店内投放方式
  if (isValueSet(activityMarketingInfo.inStoreDeliveryType)) {
    inStoreDeliveryType = activityMarketingInfo.inStoreDeliveryType + "";
  }

  // 图片
  if (isValueSet(activityMarketingInfo.couponLogo)) {
    logoImg = activityMarketingInfo.couponLogo;
  }

  // 店外投放方式
  if (isValueSet(activityMarketingInfo.outStoreDeliveryType)) {
    outStoreDeliveryType = activityMarketingInfo.outStoreDeliveryType + "";
  }

  // 券信息
  if (+investmentSchema === InvestmentSchemaEnum.DRUG_FREE) {
    ruleFree = {
      discount: activityRule?.discount,
      elemeSubsidy: activityRule?.platformSubsidyMax,
      platformSubsidyPercent: activityRule?.platformSubsidyPercent,
      shopSubsidyPercent: activityRule?.shopSubsidyPercent,
    };
  } else {
    if (
      isValueSet(activityRule?.discount) &&
      isValueSet(activityRule?.platformSubsidy)
    ) {
      rule = {
        discount: activityRule?.discount,
        elemeSubsidy: activityRule?.platformSubsidy,
      };
    }
  }

  return {
    budget: JSON.stringify(budget),
    creator: JSON.stringify(creator),
    couponName: JSON.stringify(couponName),
    couponStock: JSON.stringify(couponStock),
    couponValid: JSON.stringify(couponValid),
    dateRange: JSON.stringify(dateRange),
    drawLimit: JSON.stringify(drawLimit),
    effectiveChannel,
    externalDeliveryChannel: JSON.stringify(externalDeliveryChannel),
    inStoreDeliveryType,
    logoImg,
    outStoreDeliveryType,
    rule: JSON.stringify(rule),
    ruleFree: JSON.stringify(ruleFree),
  };
};


export const formatMarketPlay2ViewJsonForFoodieBeans = ({
  formData,
  baseinfo,
  discountRules
}: {
  formData: MarketPlayForFoodieBeans;
  baseinfo: BaseInfoForMarketPlay;
  discountRules: DiscountRule
}): ViewJsonForFoodieBeans => {
  const budget = discountRules.budget && parseJsonSafe(discountRules.budget);
  const effectiveChannel = formData.effectiveChannel;
  const externalDeliveryChannel =
    formData.externalDeliveryChannel &&
    parseJsonSafe(formData.externalDeliveryChannel);
  const inStoreDeliveryType = formData.inStoreDeliveryType;
  const logoImg = formData.logoImg;
  const outStoreDeliveryType = formData.outStoreDeliveryType;

  return {
    activityType: ActivityTypeEnum.ITEM_SPECIAL_COUPON,
    activityMarketingInfo: {
      budgetId: budget?.budgetId,
      budgetName: budget?.budgetName,
      couponLogo: logoImg,
      deliveryType: effectiveChannel, // 业务场景
      activityExternalDeliveryChannelList: externalDeliveryChannel,
      inStoreDeliveryType: inStoreDeliveryType,
      outStoreDeliveryType: outStoreDeliveryType,
      userScope: 0, // 列表不需要配置，前端写死人群
    },
    periodDTOList: (baseinfo?.activityTime?.batchRange?.list || []).map(
      (r: any) => {
        return {
          openTime: r?.start + ":00",
          closeTime: r?.end + ":59",
        };
      }
    ),
    weekday: (baseinfo?.activityTime.weeks || []).join(","),
  }
}
