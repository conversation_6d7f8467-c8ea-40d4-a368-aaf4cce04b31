import React from "react";
import { <PERSON><PERSON>, Field, Form, Message } from "@alifd/next";
import {
  BudgetPicker,
  CommodityRulesWithUpLoad,
} from "@alife/carbon-biz-kunlun-zs";
import { Budget, CommodityRules } from "../constants";
import "./style.scss";
interface Value {
  commodityRules: CommodityRules;
  budget?: Budget; // 预算
}

interface BudgetParams {
  budgetType?: "ACTIVITY" | "COUPON";
  bizDate?: number;
  templateId?: string;
  subsidyType?: 'b' | 'p' | 'c' | 'd'; // 补贴类型
}

interface Props {
  value?: Value;
  onChange: (v: Value | undefined) => void;
  onEvent?: (event: any) => void;
  env: "daily" | "pre" | "prod";
  activityType: number;
  investmentSchema: number;
  templateUrl?: string; // 下载模版
  budgetParams?: BudgetParams; // 预算相关参数
}
const formItemLayout = {
  labelCol: { span: 7 },
  wrapperCol: { span: 17 },
};

function wrapCallback(_callback: any) {
  let callback = _callback;
  if (!callback) {
    callback = (msg: string) => {
      if (msg) {
        throw new Error(msg);
      }
    };
  }
  return callback;
}

function isEmpty(v: any) {
  return v === "" || v === null || v === undefined;
}

const DiscountInfoSettingForFoodBeans: React.FC<Props> = ({
  value,
  onChange,
  onEvent,
  env,
  activityType,
  templateUrl,
  budgetParams,
  investmentSchema,
}) => {
  const field = Field.useField({
    values: value,
    onChange(name, values) {
      if (name === "commodityRules") {
        if (
          values?.checkExcel?.extInfoMap?.invitedUpcHasPlatformSubsidy !==
          "true"
        ) {
          field.setValue("budget", undefined);
        }
      }
      onChange(field.getValues());
    },
  });
  return (
    <div className="zs-discount-info-setting">
      <div className="zs-discount-info-setting-header">差异化优惠信息</div>
      <div className="zs-discount-info-setting-content">
        <Form field={field} {...formItemLayout}>
          <Form.Item label="优惠规则:" required={true}>
            <CommodityRulesWithUpLoad
              uploadFileOperatorType="add"
              env={env}
              activityType={activityType}
              templateUrl={templateUrl}
              investmentSchema={investmentSchema}
              {...(field.init("commodityRules", {
                rules: [
                  { required: true, message: "请上传商品信息" },
                  {
                    validator: (r: any, v: any, _callback) => {
                      let callback = wrapCallback(_callback);
                      if (v.fileUploadStatus === 2) {
                        callback("文件正在解析中，请等待...");
                      }
                      if (v.fileUploadStatus === 3) {
                        callback("请重新上传文件");
                      }
                      const valid =
                        !isEmpty(v.checkExcel) && !isEmpty(v.uploadPath);
                      if (!valid) {
                        callback("请上传商品信息");
                      } else {
                        callback();
                      }
                    },
                  },
                ],
              }) as any)}
            />
          </Form.Item>
          <Form.Item
            label="预算:"
            required={
              value?.commodityRules?.checkExcel?.extInfoMap
                .invitedUpcHasPlatformSubsidy === "true"
            }
          >
            <BudgetPicker
              style={{ width: 400 }}
              {...field.init("budget", {
                rules: [
                  {
                    required:
                      value?.commodityRules?.checkExcel?.extInfoMap
                        .invitedUpcHasPlatformSubsidy === "true",
                    message: "请选择预算",
                  },
                  {
                    validator: (r: any, v: any, _callback) => {
                      let callback = wrapCallback(_callback);
                      if (
                        !isEmpty(v?.budget?.budgetId) &&
                        value?.commodityRules?.checkExcel?.extInfoMap
                          .invitedUpcHasPlatformSubsidy === "true"
                      ) {
                        callback("请选择预算");
                      } else {
                        callback();
                      }
                    },
                  },
                ],
              })}
              disabled={
                !(
                  value?.commodityRules?.checkExcel?.extInfoMap
                    ?.invitedUpcHasPlatformSubsidy === "true"
                )
              }
              value={value?.budget ? value.budget : undefined}
              env={{
                env: env,
                view: "edit",
                creationContext: {
                  subsidyType: budgetParams?.subsidyType,
                  activityType: activityType
                }
              }}
              view="edit"
              bizDate={budgetParams?.bizDate}
              budgetType={budgetParams?.budgetType}
              templateId={budgetParams?.templateId}
            />
          </Form.Item>
          <Form.Item label=" ">
            <div style={{ padding: "30px 0px 20px" }}>
              <Button
                data-test-id="scope-info-step-cancel-btn"
                onClick={() => onEvent && onEvent({ type: "cancel" })}
              >
                取消
              </Button>
              &nbsp;&nbsp;
              <Button
                data-test-id="scope-info-step-prev-btn"
                onClick={() => onEvent && onEvent({ type: "prev" })}
              >
                上一步
              </Button>
              &nbsp;&nbsp;
              <Button
                type="primary"
                data-test-id="scope-info-step-next-btn"
                onClick={async () => {
                  const r = await field.validatePromise();
                  console.log("validate result", r);
                  if (r.errors && Object.keys(r.errors).length > 0) {
                    const key = Object.keys(r.errors)[0];
                    // @ts-ignore
                    const msg = r.errors[key].errors[0];
                    Message.error(msg);
                  } else {
                    onEvent && onEvent({ type: "next" });
                  }
                }}
              >
                发布
              </Button>
            </div>
          </Form.Item>
        </Form>
      </div>
    </div>
  );
};

export default DiscountInfoSettingForFoodBeans;
