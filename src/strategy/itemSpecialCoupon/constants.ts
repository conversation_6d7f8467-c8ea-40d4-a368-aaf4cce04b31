import { ActivityTypeEnum } from '../../constants';
export interface BaseInfoForCreationContext {
  beginTime: string;
  createdUserId: string;
  createdUserName: string;
  endTime: string;
  subsidyType?: string;
    businessLineList?: {
      businessLineName?: string; // 业务线名称
      businessLineId?: number; // 业务线Id,
    }[],
}

export interface BaseInfoForMarketPlay {
  activityTime: {
    batchRange: {
      list: Array<{ start: string; end: string }>;
    };
    weeks: (string | number)[];
  };
}

export interface MarketPlayForPlayData {
  outStoreDeliveryType: string;
  externalDeliveryChannel?: {
    externalDeliveryChannelName: string;
    externalDeliveryChannelCode: string;
    externalDeliveryChannelType: 1 | 2; // 1：独享、2：非独享
  }[];
  budget?: {
    budgetId: string;
    budgetName: string;
  };
  weekday: string;
  userScope: string;
  inStoreDeliveryType: string;
  name: string;
  creator: {
    id: string;
    prefix: string;
    name: string;
  };
  dateRange: string[];
  timeRange: Array<string[]>;
  couponStock: {
    // 库存限制
    totalLimit: {
      value: number;
    };
  };
  couponName: {
    value: string;
  };
  childType: string;
  couponValidFix: {
    // 周末一分购写死生效日期是自领取日期起0天
    fromDraw?: {
      value: number;
    };
  };
  couponValid: {
    // 红包使用时间（券有效期）
    fromDraw?: {
      value: number;
    };
    validRange?: {
      start: string;
      end: string;
    };
  };
  drawLimit: {
    dayLimit: {
      value: number;
    };
    totalLimit: {
      value: number;
    };
  };
  logoImg: string;
  effectiveChannel: string; // 业务场景
  rule?: {
    discount: number;
    elemeSubsidy: number;
  };
  ruleFree?: {
    discount: number;
    elemeSubsidy: number;
    platformSubsidyPercent: number;
    shopSubsidyPercent: number;
  }
}

export interface ViewJsonForPlayData {
  activityType: ActivityTypeEnum.ITEM_SPECIAL_COUPON;
  activityLimitRule: {
    totalCountLimit: number;
    dayCountLimit?: number;
    userDayCountLimit?: number;
    userTotalCountLimit?: number;
  };
  activityMarketingInfo: {
    availableDays?: number;
    availableStartTime?: string;
    availableEndTime?: string;
    budgetId?: string;
    budgetName?: string;
    couponName: string;
    couponLogo?: string;
    deliveryType: number; // 业务场景
    deliveryChannel?: number; // 投放渠道，老活动有该字段
    activityExternalDeliveryChannelList?: {
      externalDeliveryChannelName: string;
      externalDeliveryChannelCode: string;
      externalDeliveryChannelType: 1 | 2; // 1：独享、2：非独享
    }[];
    inStoreDeliveryType: number;
    outStoreDeliveryType: number;
    userScope: string;
  };
  activityRule: {
    discount: number;
    discountType: 'FIXED_PRICE';
    platformSubsidy?: number;
    // 零售医药免单需求新增的补贴字段
    platformSubsidyPercent?: number;
    shopSubsidyPercent?: number;
    platformSubsidyMax?: number;
  };
  periodDTOList?: {
    closeTime: string;
    openTime: string;
    weekday: string; // 周末一分购模版活动时间独有字段，代表活动时间是周中某一天的时间段
  }[];
  weekday: string;
}

export interface MarketPlayForFoodieBeans {
  outStoreDeliveryType: string;
  externalDeliveryChannel?: {
    externalDeliveryChannelName: string;
    externalDeliveryChannelCode: string;
    externalDeliveryChannelType: 1 | 2; // 1：独享、2：非独享
  }[];
  weekday: string;
  userScope: string;
  inStoreDeliveryType: string;
  name: string;
  creator: {
    id: string;
    prefix: string;
    name: string;
  };
  dateRange: string[];
  timeRange: Array<string[]>;
  logoImg: string;
  effectiveChannel: string; // 业务场景
}

export interface ViewJsonForFoodieBeans {
  activityType: ActivityTypeEnum.ITEM_SPECIAL_COUPON;
  activityLimitRule?: null; // 吃货豆没有这个字段, 为了ts类型检查
  activityRule?: null; // 吃货豆没有这个字段
  activityMarketingInfo: {
    availableDays?: undefined; // 吃货豆没有这个字段
    availableStartTime?: undefined; // 吃货豆没有这个字段
    availableEndTime?: undefined; // 吃货豆没有这个字段
    couponName?: undefined; // 吃货豆没有这个字段
    budgetId?: string;
    budgetName?: string;
    couponLogo?: string;
    deliveryType: string; // 业务场景
    deliveryChannel?: number; // 投放渠道，老活动有该字段
    activityExternalDeliveryChannelList?: {
      externalDeliveryChannelName: string;
      externalDeliveryChannelCode: string;
      externalDeliveryChannelType: 1 | 2; // 1：独享、2：非独享
    }[];
    inStoreDeliveryType: string;
    outStoreDeliveryType: string;
    userScope: number;
  };
  periodDTOList?: {
    closeTime: string;
    openTime: string;
  }[];
  weekday: string;
}

export interface CommodityRules {
  uploadPath?: string;
  url?: string;
  fileUploadStatus?: UploadStatusEnum;
  budgetId?: string;
  budgetName?: string;
  checkExcel?: CheckExcel;
  uploadTotalUpc?: number;
}
export interface CheckExcel {
  checkResult: string;
  checkMessage: string;
  totalRow: number;
  successRow: number;
  errorRow: number;
  repeatRow: number;
  extInfoMap: {
      invitedUpcHasPlatformSubsidy: string;
      checkResultFilePath: string;
  };
}
export declare enum UploadStatusEnum {
  SUCCESS = 1,
  LOAD = 2,
  FAIL = 3
}

export interface Budget {
  budgetId: string;
  budgetName: string;
}
export interface DiscountRule {
    commodityRules: CommodityRules,
    budget?: Budget; // 预算
}