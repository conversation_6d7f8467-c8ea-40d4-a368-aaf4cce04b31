import { Message, Dialog, Button, NumberPicker } from "@alifd/next";

import * as api from "../../api";
import {
  ActivityTypeEnum,
  InvestmentSchemaEnum,
  InvestmentTypeEnum,
} from "../../constants";
import loadingDialog from "../../components/LoadingDialog";
import {
  ActivityStrategy,
  InvestmentActivityDetailDTO,
} from "../../models";
import { CableBase, resolveCreateActivityCommonData } from "../base";
import {
  checkAndUploadRichDescription,
  checkNextCommoditySignupRuleForAlpha,
  checkNextTimeForAlpha,
  genericCreateActivity,
  gotoLegacyActivityDetailByDefault,
  isOfflineEnabledByDefault,
  resolveCommodityAuditProps,
} from "../mixins";

import { MarketPlayForPlayData, ViewJsonForPlayData } from "./constants";
import { formatMarketPlay2ViewJson, formatViewJson2MarketPlay } from "./utils";
import { FoodieBeansCable } from "./FoodieBeansCable";
import ModifyStockForFoodBeans from "./ModifyStockForFoodBeans";

/**
 * 特价券
 */
const ItemSpecialCoupon: ActivityStrategy = {
  activityType: ActivityTypeEnum.ITEM_SPECIAL_COUPON,
  cable: (options) => {
    if (options.investmentSchema === InvestmentSchemaEnum.FOODIE_BEANS) {
      return FoodieBeansCable;
    }
    let _model = '9B8ocguH4vM9QPiNKDA6s7';
    const _workspace = 'ExukovQrae6xWn2rt4NgG';
    if (options.investmentSchema === InvestmentSchemaEnum.COMMODITY_EXCHANGE_CARD) {
      _model = '5PPP4ViY9pw6QpXZ34aAx9';
      // _workspace 同上，目前使用的是同一个。不单独设置
    }
    return {
      ...CableBase,
      workspace: _workspace,
      model: _model,
      async checkNext(checkNextContext, creationContext, history) {
        const { step, dataStore } = checkNextContext;
        // console.log('checkNext', step, dataStore);
        if (!dataStore._session) {
          dataStore._session = {};
        }
        if (step.name === 'baseinfo') {
          if (dataStore.data.baseinfo?.richDescription) {
            const { hide } = loadingDialog.show({
              message: '活动信息检查中...',
            });
            try {
              const error = await checkAndUploadRichDescription({
                richDescription: dataStore.data.baseinfo?.richDescription,
                session: dataStore._session,
              });
              if (error) {
                return error;
              }
            } finally {
              hide();
            }
          }
        }
        if(step.name === "baseinfo") {
          const timeError = await checkNextTimeForAlpha(checkNextContext, creationContext);
          if(timeError) {
            return timeError;
          }
        }
        if(step.name === "scope") {
          const commoditySignupRuleError = await checkNextCommoditySignupRuleForAlpha(checkNextContext, creationContext);
          if(commoditySignupRuleError) {
            return commoditySignupRuleError;
          }
        }
        if (step.name === 'playdata') {
          const { baseinfo, playdata, scope } = dataStore.data;
          const commonData: any = resolveCreateActivityCommonData(
            ActivityTypeEnum.ITEM_SPECIAL_COUPON,
            checkNextContext,
            creationContext
          );

          delete commonData.dataMap;
          const commodityAuditProps: any = resolveCommodityAuditProps(scope);
          const formData: MarketPlayForPlayData = playdata.snapshot.data;

          // 判断是否为周末一分购，周末一分购已下线，不再支持
          if (creationContext?.investmentSchema === InvestmentSchemaEnum.WEEKEND_SPLIT_PURCHASE) {
            throw new Error("周末一分购活动已不再支持");
          }

          const viewJson: any = formatMarketPlay2ViewJson({
            formData,
            baseinfo,
            investmentSchema: creationContext.investmentSchema,
          });

          const payload: any = {
            // 基本信息
            ...commonData,
            // 自动审核规则
            ...commodityAuditProps,
            // 降套补 商品清退规则
            commodityRemoveCondition: {
              removeCondition: scope.removeCondition,
            },
            // 授权审核人
            auditMemberList:
              baseinfo.auditMemberList?.length > 0
                ? baseinfo.auditMemberList
                : null,
            // 报名规则
            reviewRuleId: scope.rule?.ruleId,
            reviewRuleName: scope.rule?.name,
            // 转换模板数据为viewJson
            viewJson: JSON.stringify(viewJson),
          };
          if (dataStore._session?.ossURL) {
            const ossURL = dataStore._session?.ossURL;
            payload.description = baseinfo.richDescription.text;
            payload.attributes = {
              ...payload.attributes,
              hasRichDescription: 'on',
              richDescriptionURL: ossURL,
            };
          }

          return await genericCreateActivity(payload, history, creationContext);
        }
      },
    };
  },
  hasAgentSubsidy() {
    return false;
  },
  isItemAuditStatsVisible: true,
  isSupportAgent: false,
  isOfflineEnabled: isOfflineEnabledByDefault,
  isDingtalkLinkEnabled: () => false,
  async resolvePartialCreateData(createContext) {
    if (
      createContext.investmentType === InvestmentTypeEnum.RICHANG &&
      createContext.investmentSchema === InvestmentSchemaEnum.CARD_SALES
    ) {
      return {
        playdata: {
          fields: {
            inStoreDeliveryType: '2', // 店内不支持投放
            outStoreDeliveryType: '1', // 店外支持投放
          }
        }
      }
    } else {
      // 非站外卡券售卖场景，无需通过这个能力设置初始值
      return undefined
    }
  },
  hasAuditMemberList: () => true,
  // 修改库存
  async handleModifyStock(record, options) {
    try {
      if (record.investmentSchema === InvestmentSchemaEnum.FOODIE_BEANS) {
        let modifyStock: any;
        let upcCode: any;
        const dialog = Dialog.show({
          title: "修改库存",
          footer: [
            <Button
              style={{ marginRight: 10 }}
              type="primary"
              onClick={async () => {
                if(!upcCode) {
                  Message.error("请先点击查询指定商品库存信息");
                  return;
                }
                if (!modifyStock) {
                  Message.error("请填写修改库存");
                  return;
                }
                try {
                   const res = await api.subActivity.updateStockForViewJson(
                    record.activityId,
                    modifyStock,
                    undefined,
                    upcCode
                  );

                  if (res?.success) {
                    dialog.hide();
                    options.onSuccess();
                  } else {
                    options.onFail(res?.errorMessage as string);
                  }
                } catch (e: any) {
                  options.onFail(e.message as string);
                }
              }}
            >
              确认
            </Button>,
            <Button onClick={() => dialog.hide()}>取消</Button>,
          ],
          content: (
            <ModifyStockForFoodBeans
              onChange={(upc, stock) => {upcCode = upc; modifyStock = stock}}
              activityId={record.activityId}
            />
          ),
        });
      } else {
        // @ts-ignore
        const stock = this._resolveTotalStockLimit(record);
        // @ts-ignore
        const dayStock = this._resolveDayStockLimit(record);
        let value: any;
        let dayValue: any;
        const dialog = Dialog.show({
          title: "修改库存",
          footer: [
            <Button
              style={{ marginRight: 10 }}
              type="primary"
              onClick={async () => {
                if (stock && !value) {
                  Message.error("请填写总库存");
                  return;
                }
                if (dayStock && !dayValue) {
                  Message.error("请填写每日库存");
                  return;
                }
                if (dayStock && stock && dayValue > value) {
                  Message.error("每日库存数量应该小于等于总库存数量");
                  return;
                }
                try {
                  let res: any;
                  if (record.isNewActivity) {
                    // 新招商链路的活动
                    let viewJson: ViewJsonForPlayData = {} as ViewJsonForPlayData;
                    try {
                      viewJson = record.viewJson && JSON.parse(record.viewJson);
                    } catch (e) {
                      console.error(e);
                    }

                    // 不基于组件链路创建的活动，viewJson 中没有 deliveryChannel字段。 有 deliveryChannel字段说明是基于组件链路创建的活动
                    if (viewJson?.activityMarketingInfo?.deliveryChannel) {
                      // 基于组件链路创建的活动，都已经结束，理论上不会走到这个分支当中
                      Message.error("当前活动不支持修改库存");
                      return;
                    } else {
                      res = await api.subActivity.updateStockForViewJson(
                        record.activityId,
                        value,
                        dayValue
                      );
                    }
                  } else {
                    // 老招商链路的活动，不再支持，活动都已结束，理论上不会走到这个分支
                    Message.error("当前活动不支持修改库存");
                    return;
                  }
                  if (res?.success) {
                    dialog.hide();
                    options.onSuccess();
                  } else {
                    options.onFail(res?.errorMessage as string);
                  }
                } catch (e: any) {
                  options.onFail(e.message as string);
                }
              }}
            >
              确认
            </Button>,
            <Button onClick={() => dialog.hide()}>取消</Button>,
          ],
          content: (
            <div>
              {stock ? (
                <>
                  <p>总库存: {stock}</p>
                  <p>
                    修改总库存 &nbsp; &nbsp;
                    <NumberPicker
                      placeholder="请输入库存"
                      style={{ width: 200 }}
                      min={stock}
                      onChange={(v: any) => {
                        value = v;
                      }}
                      max={99999999}
                    />
                  </p>
                </>
              ) : null}

              {dayStock ? (
                <>
                  <p>每日库存 : {dayStock}</p>
                  <p>
                    修改每日库存 &nbsp; &nbsp;
                    <NumberPicker
                      placeholder="请输入库存"
                      style={{ width: 200 }}
                      min={dayStock}
                      onChange={(v: any) => {
                        dayValue = v;
                      }}
                      max={99999999}
                    />
                  </p>
                </>
              ) : null}

              <p style={{ color: "red" }}>注意！只能增加不能减少</p>
            </div>
          ),
        });
      }
    } catch (e) {
      console.error(e);
    }
  },
    // @ts-ignore
    _resolveDayStockLimit(record): number | undefined {
      const _playdata: ViewJsonForPlayData = JSON.parse(
        record.viewJson as string
      );
      return _playdata.activityLimitRule.dayCountLimit;
    },
    // @ts-ignore
    _resolveTotalStockLimit(record): number | undefined {
      const _playdata: ViewJsonForPlayData = JSON.parse(
        record.viewJson as string
      );
      return _playdata.activityLimitRule.totalCountLimit;
    },
  isSupportModifyStock(record) {
    try {
      if (record.investmentSchema === InvestmentSchemaEnum.FOODIE_BEANS) {
        return true;
      }
      return (
        // @ts-ignore
        !!this._resolveTotalStockLimit(record) ||
        // @ts-ignore
        !!this._resolveDayStockLimit(record)
      );
    } catch (e) {
      console.error(e);
      return false;
    }
  },
  isDetailInviteStatsOnMainActivityTableVisible: true,
  // viewJson 转组件语法
  marketPlayMapper: async function (viewJson, baseInfo, option) {
    try {
      const viewDto: ViewJsonForPlayData = JSON.parse(viewJson) || {};
      const { activityMarketingInfo } = viewDto;

      if (activityMarketingInfo.deliveryChannel) {
        // 基于组件链路创建的活动
        return;
      }

      let model = {};
      let fields = formatViewJson2MarketPlay({
        viewDto,
        baseInfo,
        investmentSchema: option?.investmentSchema,
      });

      if (option?.investmentSchema === InvestmentSchemaEnum.COMMODITY_EXCHANGE_CARD) {
        // 商品通兑卡模板
        model = await api.subActivity.getComponentDataModel(
          "3oNNWxkJYzc6eWQyKz5Lp2",
          "2FQXyZXR1eC9QRZcI47CDe"
        );
      } else if (option?.investmentSchema === InvestmentSchemaEnum.FOODIE_BEANS) {
        // 吃货豆
        model = await api.subActivity.getComponentDataModel(
          "3oNNWxkJYzc6eWQyKz5Lp2",
          "aO1sTPPeCOY8yy5xZsPPMk"
        );
      } else {
        model = await api.subActivity.getComponentDataModel(
          "ExukovQrae6xWn2rt4NgG",
          "2uP76tXYBiW5KVpWgD3SkZ"
        );
      }

      const marketPlay = {
        instance: { fields },
        model: model,
      };
      return marketPlay;
    } catch (e) {
      console.error("marketPlayMapper: ");

      return null;
    }
  },
  renderPlayDataInfoForTableView: function (): JSX.Element {
    return <></>;
  },
  renderActivityRuleForTable(
    viewJson: any,
    options: { investmentSchema: number }
  ) {
    try {
      const playdata: ViewJsonForPlayData = JSON.parse(viewJson);
      const rule = playdata.activityRule;
      if (options.investmentSchema === InvestmentSchemaEnum.DRUG_FREE) {
        return `券后价 ${rule.discount / 100} 元，平台补贴上限 ${
          rule.platformSubsidyMax! / 100
        } 元`;
      } else if (options.investmentSchema === InvestmentSchemaEnum.FOODIE_BEANS) {
        return "";
      } else {
        return `券后价 ${rule.discount / 100} 元，平台补贴 ${
          rule.platformSubsidy! / 100
        } 元`;
      }
    } catch (e) {
      console.error(e);
      return "";
    }
  },
  gotoLegacyDetail: function (
    act: InvestmentActivityDetailDTO,
    scene: "lookup" | "examine"
  ): void {
    gotoLegacyActivityDetailByDefault(act, scene);
  },
  renderActivityRuleForTableLegacy: function () {
    return "";
  },
};

export default ItemSpecialCoupon;
