import { Button, Input, Message, NumberPicker } from "@alifd/next";
import * as React from "react";
import { useState } from "react";
import { aslcClient } from "../../api";

interface Props {
  activityId: number;
  onChange: (upc: number | undefined, stock: number | undefined) => void;
}

const queryUpcRule =  async (activityId: number, upcCode: number) => {
  return await aslcClient.fetch({
    apiKey:
      "ele-newretail-investment.NewretailInvestmentActivityInviteYundingService.queryUpcRule",
    params: [{activityId, upcCode}],
  });
}

const ModifyStockForFoodBeans: React.FC<Props> = ({ activityId, onChange }) => {
const [upcCode, setUpcCode] = useState();
  const [upcInfo, setUpcInfo] = useState<{ totalCountLimit: number, upcCode: number}>();
  return (
    <div>
      <div style={{ display: "flex", alignItems: "center" }}>
        <p>
          upcCode &nbsp; &nbsp;
          <Input
            placeholder="请输入 upcCode"
            style={{ width: 200 }}
            onChange={(v: any) => {
                onChange(undefined, undefined);
                setUpcInfo(undefined);
                setUpcCode(v);
            }}
          />
        </p>
        <Button
          style={{ marginLeft: 10, marginTop: 5 }}
          type="primary"
          onClick={() => {
            if (!upcCode) {
              Message.error("请先填写upcCode查询库存信息");
              return;
            }
            queryUpcRule(activityId, upcCode).then((res: any) => {
                if(!res?.data?.totalCountLimit) {
                    Message.error("该upcCode未查询到库存信息");
                    return
                }
              setUpcInfo(res.data)
              onChange(res.data.upcCode, undefined);
            });
          }}
        >
          点击查询库存
        </Button>
      </div>
      {upcInfo?.totalCountLimit && (
        <div>
          <p>总库存: {upcInfo?.totalCountLimit}</p>
          <p>
            修改总库存 &nbsp; &nbsp;
            <NumberPicker
              placeholder="请输入库存"
              style={{ width: 200 }}
              min={upcInfo?.totalCountLimit}
              onChange={(v) => onChange(upcInfo?.upcCode, v)}
              max={99999999}
            />
          </p>
          <p style={{ color: "red" }}>注意！只能增加不能减少</p>
        </div>
      )}
    </div>
  );
};

export default ModifyStockForFoodBeans;
