import axios from "axios";
import { Message } from "@alifd/next";
import { formatCommodityAuditConditionData } from "@alife/carbon-biz-kunlun-zs";

import { maskMS, parseJsonSafe, resolveEnv } from "../common";
import loadingDialog from "../components/LoadingDialog";
import * as api from "../api";
import {
  CheckNextContext,
  CreationContext,
  InvestmentActivityDetailDTO,
  ManageOrgSelectDateTs,
} from "../models";
import { checkActivityReviewRule, getPlanById } from "./service";
import { confirmMktRuleError } from "./utils";

export function gotoLegacyActivityDetailByDefault(
  act: InvestmentActivityDetailDTO,
  scene: "lookup" | "examine"
) {
  const env = resolveEnv();
  const prefix =
    env === "pre"
      ? "https://pre-zs.kunlun.alibaba-inc.com"
      : "https://zs.kunlun.alibaba-inc.com";
  const mapping: any = { lookup: "lookUp", examine: "examine" };
  const url =
    prefix +
    "/main/subactivity/manage/" +
    mapping[scene] +
    "/" +
    act.activityId +
    "/" +
    encodeURIComponent(act.name) +
    "/" +
    act.activityType;
  window.open(url, "_blank");
}

export function isOfflineEnabledByDefault(state: number) {
  // 活动状态: 1:未开始 2:活动中 4:活动取消 5:活动结束 8: 预算熔断
  return [1, 2, 8].includes(state);
}

/**
 * 获取自动审核相关字段，字段严格按文档要求来传：
 *  老文档，弃用：~~ https://yuque.alibaba-inc.com/docs/share/d4df07f0-f6b5-4817-b603-e0aa6744c60b?# 《创建活动接口》 ~~
 *  新文档：https://alidocs.dingtalk.com/i/nodes/qXomz1wAyjKVXog76PabW3Y9pRBx5OrE
 * 使用组件透出的方法，保证创建和修改的参数统一。
 *
 */
export function resolveCommodityAuditProps(scope: any) {
  return formatCommodityAuditConditionData(scope);
}

/**
 * 通用基础的活动创建逻辑
 * 注意！如果需要增加其他逻辑需要慎重，必须充分讨论并严格通过 Code Review 才能往里面追加代码。
 */
export async function genericCreateActivity(
  payload: any,
  history: { push: (path: string) => any },
  creationContext: CreationContext
): Promise<{ message: string } | undefined> {
  const { hide } = loadingDialog.show({ message: "活动创建中..." });
  try {
    const res = await api.subActivity.create(payload);
    if (res.success) {
      Message.success("创建成功");
      if (creationContext.activitySceneId) {
        history.push(
          "/sub-activity/" +
            res.data +
            "?activitySceneId=" +
            creationContext.activitySceneId +
            "&activityPlanId=" +
            creationContext.activityPlanId
        );
      } else {
        history.push("/sub-activity/" + res.data);
      }
    } else {
      return { message: res.errorMessage };
    }
  } finally {
    hide();
  }
}

export function checkValidateCopyActivityResult(res: {
  errorCode?: string;
  errorMessage?: string;
  data?: {
    result: boolean;
    message?: string;
  } | null;
}): { message: string } | null {
  if (res?.errorCode) {
    return { message: res?.errorMessage || "活动信息校验失败" };
  }
  if (res?.data && !res?.data.result) {
    return { message: res?.data?.message || "活动信息校验失败" };
  }
  return null;
}

export async function checkAndUploadRichDescription(options: {
  richDescription: any;
  session: any;
}) {
  const res = await api.uploadRichText({
    richText: JSON.stringify(options.richDescription),
    originalUrl: options.session?.ossURL,
  });
  if (!res.success) {
    return {
      message: res.errorMessage || "活动描述内容异常，请检查",
    };
  }
  options.session.ossURL = res.data;
}

export async function resolveRichDescription(creationContext: CreationContext) {
  // 获取富文本描述信息
  let ret = null;
  const playdata = parseJsonSafe(creationContext.activity?.viewJson);
  if (
    playdata?.attributes?.hasRichDescription === "on" &&
    playdata?.attributes?.richDescriptionURL
  ) {
    try {
      const res = await axios({
        method: "GET",
        url: playdata?.attributes?.richDescriptionURL,
      });
      ret = res.data;
    } catch (e) {
      console.error(e);
    }
  } else if (creationContext.activity?.description) {
    const content = creationContext.activity.description
      .split("\n")
      .map((r) => {
        return {
          type: "paragraph",
          children: [{ text: r }],
        };
      });
    ret = {
      version: "v1",
      content,
      text: creationContext.activity?.description,
    };
  }

  return ret;
}

export async function checkNextTimeForAlpha(
  checkNextContext: CheckNextContext,
  creationContext: CreationContext
) {
  // alpha 平台校验活动时间函数
  const { step, dataStore } = checkNextContext;
  if (!creationContext?.activityPlanId) {
    return;
  }
  if (step.name === "baseinfo") {
    const { baseinfo } = dataStore.data;
    const { hide } = loadingDialog.show({
      message: "活动信息检查中...",
    });
    const planInfo = await getPlanById(creationContext?.activityPlanId);
    const planActivityRule = planInfo?.data?.planRule?.planActivityRule;
    try {
      const beginTime = baseinfo?.activityTime?.start?.toDate()?.getTime();
      const endTime = maskMS(baseinfo?.activityTime?.end?.toDate()?.getTime());
      const signUpStartTime = baseinfo?.signupTime
        ? baseinfo.signupTime.start.toDate().getTime()
        : null;
      const signUpEndTime = baseinfo?.signupTime
        ? maskMS(baseinfo?.signupTime.end.toDate().getTime())
        : null;
      if (
        (planActivityRule.activityBeginTime > beginTime ||
          planActivityRule.activityEndTime < endTime) &&
        beginTime
      ) {
        return {
          message: {
            title: "活动时间需要在所属活动计划活动正式时间范围内，请检查",
            duration: 4000,
          },
        };
      }
      if (
        (planActivityRule.activitySignUpStartTime > signUpStartTime ||
          planActivityRule.activitySignUpEndTime < signUpEndTime!) &&
        signUpStartTime
      ) {
        return {
          message: {
            title: "商家报名时间需要在所属活动计划商家报名时间范围内，请检查",
            duration: 4000,
          },
        };
      }
    } catch (err) {
      console.log(err);
      // 校验接口报错之后跳过该校验，在创建活动的时候后端会重新校验
    } finally {
      hide();
    }
  }
}

export async function checkNextCommoditySignupRuleForAlpha(
  checkNextContext: CheckNextContext,
  creationContext: CreationContext
) {
  // alpha 平台商品报名规则函数
  const { step, dataStore } = checkNextContext;
  if (!creationContext?.activityPlanId) {
    return;
  }
  if (step.name === "scope") {
    const { scope } = dataStore.data;
    const { hide } = loadingDialog.show({
      message: "活动信息检查中...",
    });
    try {
      const planInfo = await getPlanById(creationContext?.activityPlanId);
      const planActivityRule = planInfo?.data?.planRule?.planActivityRule;
      const planHasCommodityRule = planActivityRule?.commodityCategoryInfo
        ? JSON.parse(planActivityRule?.commodityCategoryInfo).length > 0
        : false;
      if (planHasCommodityRule && !scope?.rule?.ruleId) {
        return {
          message: "请根据活动计划要求招商商品类目填写商品报名规则",
        };
      }
      if (scope?.rule?.ruleId) {
        const res = await checkActivityReviewRule({
          activityPlanId: creationContext?.activityPlanId,
          reviewRuleId: scope?.rule?.ruleId,
        });
        const data = res?.data;
        if (!data?.result) {
          return {
            message: {
              title: data?.message,
              duration: 3000,
            },
          };
        }
      }
    } catch (err) {
      // 校验接口报错之后跳过该校验，在创建活动的时候后端会重新校验
    } finally {
      hide();
    }
  }
}
interface BaseInfoTS {
  manageOrgList: ManageOrgSelectDateTs[];
  activityTime: any;
}
// 获取需要校验的规则的基础信息
function getMktRuleBaseCondition(
  context: CreationContext,
  baseinfo: BaseInfoTS
) {
  return {
    activityType: context?.activityType,
    investmentType: context?.investmentType,
    investmentSchema: context?.investmentSchema,
    investmentActivityType: context?.isSingleActivity ? 0 : 1, // 单活动0
    manageOrgList: baseinfo.manageOrgList,
    beginTime: baseinfo.activityTime.start.toDate().getTime(),
    endTime: maskMS(baseinfo.activityTime.end.toDate().getTime()), // 活动结束时间
  };
}

// 查询是否跨业态,只有部分在营销中控配置行业规则的活动需要用到
export async function checkMktRulesForMultiBusinessFormAndConfirmIfNeed(
  creationContext: CreationContext,
  baseinfo: BaseInfoTS
) {
  const params = {
    // 校验条件
    condition: {
      ...getMktRuleBaseCondition(creationContext, baseinfo),
    },
  };
  const res = await api.subActivity.checkActDiscountByMarketingRule(params);
  const { result: success, marketingRuleCheckErrorType: errType } =
    res?.data || {};
  if (success || errType !== "multi_rule") {
    // 和后端确认过了，只要不是这个错误，接口报错也忽略
    return { conflicted: false };
  }
  const userConfirm = await confirmMktRuleError({
    title: "当前跨主营类目不符合行业规则",
    content: res?.data.failReason as string,
    confirmBtnText: "进行下一步，提交后升级审批",
    showConfirmBtnTips: true,
  });
  return { conflicted: true, userConfirm };
}


// 查询填写值是否不符合中控推荐规则
async function checkMktRulesForDiscountBusinessFormAndConfirmIfNeed(
  creationContext: CreationContext,
  viewJson: string,
  baseinfo: BaseInfoTS,
) {
  const params = {
    // 校验条件
    condition: {
      ...getMktRuleBaseCondition(creationContext, baseinfo),
      weekDays: baseinfo?.activityTime?.weeks?.join(','), // 星期时段——用来计算单用户平台补贴金额
      activityRuleViewJson: viewJson,
    },
  };
  const res = await api.subActivity.checkActDiscountByMarketingRule(params);
  const { result: success, marketingRuleCheckErrorType: errType } =
    res?.data || {};
  if (success || !errType) {
    return { errType: false };
  }
  if (errType === "multi_rule") {
    // 跨业态
    return { errType: "multi_rule" };
  }
  if (errType === "inconformity_rule") {
    // 玩法信息不满足行业规则，需要二次提醒
    const errorList =
      res?.data?.ruleValueCheckResultList?.filter(
        (item) => item.success === false
      ) || [];
    const userConfirm = await confirmMktRuleError({
      title: "当前填写值不符合行业规则",
      content: (
        <div>
          {errorList?.map((item) => {
            return (
              <div key={item?.mktVerifyRuleCheckType as string}>
                {item?.errorMsg}
              </div>
            );
          })}
        </div>
      ),
      confirmBtnText: "仍提交，升级审批",
    });
    return { errType, userConfirm };
  }
  // impossible
  return { errType: null };
}
export async function checkMarketRules(
  dataStore: {
    [stepName: string]: any; 
  },
  creationContext: CreationContext,
  viewJson: string
) {
  let auditUpgradeForCheckMktRuleUnPass: boolean | undefined = undefined;
  let auditUpgradeForMultiMktRule: boolean | undefined = undefined;

  if (dataStore?._session?.auditUpgradeForMultiMktRule) {
    auditUpgradeForMultiMktRule = true;
  } else {
    const { userConfirm, errType } =
      await checkMktRulesForDiscountBusinessFormAndConfirmIfNeed(
        creationContext,
        viewJson,
        dataStore?.data?.baseinfo
      );

    if (errType && errType === "inconformity_rule") {
      if (userConfirm) {
        // 玩法不满足行业规则，用户二次确认了，继续向下执行创建流程
        auditUpgradeForCheckMktRuleUnPass = true;
      } else {
        // 玩法不满足行业规则，用户未二次确认，页面停留在当前步骤，中断创建流程
        return {
          interrupt: true,
          auditUpgradeForCheckMktRuleUnPass,
          auditUpgradeForMultiMktRule,
        };
      }
    }
  }

  return {
    interrupt: false,
    auditUpgradeForCheckMktRuleUnPass,
    auditUpgradeForMultiMktRule,
  };
}

