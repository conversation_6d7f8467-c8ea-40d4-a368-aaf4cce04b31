import { MonitorFusion } from '@ali/monitor-fusion';
import * as React from 'react';
import { ErrorBoundary } from 'react-error-boundary';

function sandbox(fn: () => void) {
  try {
    fn();
  } catch (e: any) { }
}

const ErrorFallback: React.FC<any> = () => {
  const style = {
    height: '100vh',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    fontSize: 18
  }

  return (
    <div style={style}>
      页面异常，请刷新页面重试
    </div>
  )
};

interface Props {
  children: any;
  source: string;
}

const ZsErrorBoundary: React.FC<Props> = ({ children, source }) => {
  return (
    <ErrorBoundary
      FallbackComponent={ErrorFallback}
      onError={(error: Error, info: { componentStack: string; }) => {
        sandbox(() => {
          MonitorFusion.logJsError(error);
        });
        sandbox(() => {
          MonitorFusion.logCustomEvent({
            code: 'page_crash',
            msg: error.message,
            c1: window.location.href,
            c2: source,
            c3: info.componentStack,
          });
        });
      }}>
      {children}
    </ErrorBoundary>
  );
};

export function withErrorBoundary(Component: React.ComponentType<any>, source: string) {
  const Wrap: React.FC<any> = props => {
    return (
      <ZsErrorBoundary source={source}>
        <Component {...props} />
      </ZsErrorBoundary>
    );
  };

  return Wrap;
}

export default ZsErrorBoundary;
