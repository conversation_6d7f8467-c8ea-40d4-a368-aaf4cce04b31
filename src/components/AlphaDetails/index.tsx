import './index.scss';

import debugFn from 'debug';
import {
  useEffect,
  useState,
  useImperativeHandle,
  forwardRef,
} from 'react';
import { toggleMenuVisible, updateSOPBar } from '@alife/starship-alpha-sdk';

import * as Api from '../../api';
import { ActivityTypeEnumStr } from '../../constants';

const debug = debugFn('zs:alpha-details');

interface SceneInfo {
  name: string;
  sceneId: string;
  remark: string;
  activityTypes: (string | number)[];
  commodityCategoryInfo: { categoryName: string; categoryId: string }[][];
}

interface AlphaDetailsTS {
  sceneId: string;
}

const AlphaDetails = ({ sceneId }: AlphaDetailsTS, ref: any) => {
  const [ready, setReady] = useState(false);
  const [sceneInfo, setSceneInfo] = useState<SceneInfo>({} as SceneInfo);

  useImperativeHandle(ref, () => sceneInfo, [sceneInfo]);

  useEffect(() => {
    if (!sceneId) {
      return;
    }
    // TODO：获取场景详情数据，更新ready状态
    Api.alpha
      .getSceneBySceneId(sceneId)
      .then((res) => {
        const data = { ...res.data };
        if (res?.data?.activityTypes) {
          try {
            data.activityTypes = JSON.parse(res?.data?.activityTypes);
          } catch (e) {
            data.activityTypes = [];
            debug('activityTypes', e, res?.data?.activityTypes);
          }

          try {
            // {"scehma":"v1","value":[[{"categoryId":201230206,"categoryName":"水果"}],[{"categoryId":201218709,"categoryName":"肉禽蛋类"},{"categoryId":201230212,"categoryName":"鹅"}],[{"categoryId":201219108,"categoryName":"豆制品"},{"categoryId":201218721,"categoryName":"豆腐类"},{"categoryId":201223352,"categoryName":"油炸豆腐"}],[{"categoryId":201219108,"categoryName":"豆制品"},{"categoryId":201218721,"categoryName":"豆腐类"},{"categoryId":201218937,"categoryName":"日本豆腐"}]]}
            data.commodityCategoryInfo = JSON.parse(
              res?.data?.commodityCategoryInfo
            )?.value;
          } catch (e) {
            data.commodityCategoryInfo = [];
            debug('commodityCategoryInfo', e, res?.data?.commodityCategoryInfo);
          }
        }
        setSceneInfo(data);
      })
      .catch((e) => {
        console.log('e=======', e);
      });
    setReady(true);
  }, [sceneId]);

  useEffect(() => {
    toggleMenuVisible(false);
    updateSOPBar({
      visible: true,
      step: 'create-zs-activity',
      type: 'workflow',
    });
    debug('toggleMenuVisible(true)');
    return () => {
      debug('toggleMenuVisible(false)');
      toggleMenuVisible(true);
      updateSOPBar({ visible: false });
    };
  }, []);

  if (!ready) {
    return <></>;
  }

  return (
    <div className="zs-alpha-detail">
      <div className="zs-h4">所属活动场景</div>
      <div className="zs-h5 zs-mt16">活动场景名称：</div>
      <div className="zs-content-text zs-mt8">{sceneInfo?.name}</div>

      <div className="zs-h5 zs-mt20">活动场景描述：</div>
      <div className="zs-desc-block zs-mt8">{sceneInfo?.remark}</div>

      <div className="zs-h5 zs-mt20">活动场景ID：</div>
      <div className="zs-content-text zs-mt8">{sceneInfo?.sceneId}</div>

      <div className="zs-h4 zs-mt32">招商建议</div>
      <div className="zs-h5 zs-mt16">招商玩法类型：</div>
      <div className="zs-act-type-items">
        {sceneInfo?.activityTypes?.map((item: any) => {
          return (
            <div className="zs-act-type-item">{ActivityTypeEnumStr[item]}</div>
          );
        })}
      </div>

      <div className="zs-h5 zs-mt20">招商商品类目：</div>
      <div>
        {sceneInfo?.commodityCategoryInfo?.map((category) => {
          if (!category) return null;
          return (
            <div className="zs-act-category-row">
              {category?.map((item, i) => {
                return (
                  <span>
                    {item?.categoryName}
                    {category?.length - 1 !== i ? ' > ' : ''}
                  </span>
                );
              })}
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default forwardRef(AlphaDetails);
