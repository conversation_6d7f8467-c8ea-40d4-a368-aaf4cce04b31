.zs-alpha-detail {
  width: 390px;
  padding: 40px 20px;
  margin-right: 16px;
  background-color: white;

  .zs-h4 {
    font-weight: 500;
    font-size: 16px;
    color: rgba(0, 0, 0, 0.84);
    letter-spacing: 0;
    line-height: 24px;
  }

  .zs-h5 {
    font-weight: 500;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.72);
    letter-spacing: 0;
    line-height: 22px;
  }

  .zs-mt32 {
    margin-top: 32px;
  }

  .zs-mt20 {
    margin-top: 20px;
  }

  .zs-mt16 {
    margin-top: 16px;
  }

  .zs-mt8 {
    margin-top: 8px;
  }

  .zs-desc-block {
    background-color: rgba(0, 0, 0, 0.02);
    border-radius: 2px;
    padding: 12px;
  }

  .zs-content-text {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.64);
    letter-spacing: 0;
  }

  .zs-act-type-items {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 10px;
  }
  .zs-act-type-item {
    display: inline;
    margin-right: 8px;
    margin-top: 4px;
    padding: 2px 4px;
    background-color: rgba(0, 0, 0, 0.04);
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.84);
    letter-spacing: 0;
  }

  .zs-act-category-row {
    margin-top: 8px;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.64);
    letter-spacing: 0;
  }
}
