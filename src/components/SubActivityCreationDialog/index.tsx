import * as React from "react";
import { useEffect, useMemo, useState } from "react";
import { Dialog, Form, Field, Select, Message } from "@alifd/next";
import * as api from "../../api";

import {
  ActivityTypeEnum,
  ActivityTypeEnumStr,
  SubsidyTypeMap,
} from "../../constants";
import ZsTip from "../ZsTip";

/**
 * 暂时不支持商品折扣
 */

function distinct(arr: number[]) {
  return Array.from(new Set(arr));
}

const formItemLayout = {
  labelCol: {
    fixedSpan: 4,
  },
  wrapperCol: {
    span: 20,
  },
};

export interface Value {
  activityType: ActivityTypeEnum;
  subsidyType: "p" | "b" | "c" | "d";
  subsidyInvestmentModel: "A" | "Y"
}

const SubActivityCreationDialog: React.FC<any> = ({
  visible,
  onClose,
  onCancel,
  templateId,
  onCreate,
}) => {
  const [investmentTemplateSubsidyType, setInvestmentTemplateSubsidyType] =
    useState<any[]>([]);
  const field = Field.useField({
    onChange: (name: string) => {
      if (name === "activityType") {
        field.setValues({
          subsidyType: undefined,
        });
      }
    },
  });
  useEffect(() => {
    if (visible) {
      api.subActivity
        .getInvestmentTemplateSubsidyType(templateId)
        .then((res) => {
          setInvestmentTemplateSubsidyType(res.data);
        });
    }
  }, [templateId, visible]);

  const subsidyTypeOptions = useMemo(() => {
    const activityType = field.getValue("activityType");
    if (typeof activityType !== "number") {
      return [];
    }
    let subsidyTypes = (investmentTemplateSubsidyType || [])
      .filter((r) => r.activityType === activityType)
      .map((r) => r.subsidyType)
      .filter(Boolean);
    subsidyTypes = distinct(subsidyTypes);
    return subsidyTypes.map((value: "p" | "b" | "c" | "d") => {
      return {
        value,
        label: SubsidyTypeMap[value],
      };
    });
  }, [field.getValue("activityType"), investmentTemplateSubsidyType]);

  const subSidyInvestmentModelOptions = useMemo(() => {
    const activityType = field.getValue("activityType");
    if (typeof activityType !== "number") {
      return [];
    }
    let subSidyInvestmentModelData = (investmentTemplateSubsidyType || [])
      .filter((r) => r.activityType === activityType)
      .filter(Boolean);
    if(subSidyInvestmentModelData.length === 0) {
      return []
    } else {
      const model = subSidyInvestmentModelData[0]?.subsidyInvestmentDTO
     
      if(model?.a && model?.y) {
        return [{value: "A", label: "商家基础出资A"}, {value: "Y", label: "商家追加出资Y"}]
      } else if( model?.y ) {
        return [{value: "Y", label: "商家追加出资Y"}]
      } else if(model?.a) {
        return [{value: "A", label: "商家基础出资A"}]
      } else {
        return []
      }
    }
  }, [field.getValue("activityType"), investmentTemplateSubsidyType]);

  return (
    <Dialog
      title="选择子活动玩法类型"
      visible={visible}
      onClose={onClose}
      onCancel={onCancel}
      afterClose={() => {
        field.setValues({});
        field.setErrors({});
      }}
      onOk={async () => {
        const ret = await field.validatePromise();
        if (ret.errors) {
          Message.error(ret.errors[0]);
          return;
        }
        const values = field.getValues();
        onCreate(values as Value);
      }}
      footerActions={["cancel", "ok"]}
      okProps={{
        disabled: !field.getValue("subsidyType"),
        children: "立即创建",
      }}
    >
      <div style={{ width: 480, padding: "40px 20px" }}>
        <Form {...formItemLayout} style={{ marginTop: 20 }} field={field}>
          <Form.Item label="玩法类型" required>
            <Select
              data-test-id="creation-dialog-activity-type"
              style={{ width: 300 }}
              {...field.init("activityType", {
                rules: [
                  {
                    required: true,
                    message: "请选择玩法类型",
                  },
                ],
              })}
              placeholder="请选择"
            >
              {investmentTemplateSubsidyType.map((t) => {
                return (
                  <Select.Option value={t.activityType} key={t.activityType}>
                    {ActivityTypeEnumStr[t.activityType]}
                  </Select.Option>
                );
              })}
            </Select>
          </Form.Item>
          <Form.Item
            label="补贴类型"
            required
            style={{
              display: (subsidyTypeOptions || []).length > 0 ? "flex" : "none",
            }}
          >
            <Select
              data-test-id="creation-dialog-subsidy-type"
              style={{ width: 300 }}
              {...field.init("subsidyType", {
                rules: [
                  {
                    required: true,
                    message: "请选择补贴类型",
                  },
                ],
              })}
              placeholder="请选择"
            >
              {subsidyTypeOptions.map((t) => {
                return (
                  <Select.Option value={t.value} key={t.value}>
                    {t.label}
                  </Select.Option>
                );
              })}
            </Select>
          </Form.Item>
          <Form.Item
            label="招商模式"
            required
            style={{
              display: subSidyInvestmentModelOptions?.length !== 0 ? "flex" : "none",
            }}
          >
            <Select
              data-test-id="creation-dialog-subsidy-type"
              style={{ width: 300 }}
              {...field.init("subsidyInvestmentModel", {
                rules: [
                  {
                    required: subSidyInvestmentModelOptions?.length !== 0,
                    message: "请选择招商模式",
                  },
                ],
              })}
              placeholder="请选择"
            >
              {(subSidyInvestmentModelOptions || []).map((t) => {
                return (
                  <Select.Option value={t.value} key={t.value}>
                    {t.label}
                  </Select.Option>
                );
              })}
            </Select>
          </Form.Item>
        </Form>

        <ZsTip tip="对应⼦活动玩法类型只可选择主活动包含的玩法类型" />
      </div>
    </Dialog>
  );
};

export default SubActivityCreationDialog;
