import cls from "./index.module.scss";

import { Select } from "@alifd/next";
import { useDebounceEffect } from "ahooks";
import classNames from "classnames";
import { CSSProperties, forwardRef, Ref, useRef, useState } from "react";

import { alscClient } from "api";

interface Plan {
  planId: number;
  planName: string;
  isRelation: boolean;
  notRelationReason: string | null;
}

interface Service {
  getPlanList(params: { search: string }): Promise<{ data?: Plan[] }>;
}

class DefaultService implements Service {
  async getPlanList(params: { search: string }): Promise<{ data?: Plan[] }> {
    return await alscClient.fetch({
      apiKey:
        "ele-newretail-investment.QualityProductYundingService.queryQualityProductPlan",
      params: [null, params.search],
    });
  }
}

interface Props {
  value?: number;
  onChange: (value?: number) => void;
  placeholder?: string;
  style?: CSSProperties;
}

export const QualityProductPlanSelect = forwardRef(
  (
    { value, onChange, placeholder, style }: Props,
    ref: Ref<HTMLDivElement>
  ) => {
    const serviceRef = useRef(new DefaultService());
    const [dataSource, setDataSource] = useState<Plan[]>([]);
    const [search, setSearch] = useState<string>();

    useDebounceEffect(() => {
      if (!search) {
        return;
      }
      serviceRef.current.getPlanList({ search }).then((res) => {
        setDataSource(res?.data || []);
      });
    }, [search]);

    return (
      <div ref={ref} className={cls.select} style={style}>
        <Select
          style={{ width: "100%" }}
          // @ts-ignore
          popupContainer={(target) => target.parentElement}
          placeholder={placeholder}
          value={value}
          onChange={(_value) => onChange(_value as number | undefined)}
          dataSource={dataSource.map((p) => ({
            label: p.planName,
            value: p.planId,
            disabled: !p.isRelation,
            reason: p.notRelationReason,
          }))}
          itemRender={(item) => (
            <div
              className={classNames(
                cls.item,
                item.disabled ? cls.disabled : null
              )}
            >
              <div>{item.label}</div>
              {item.disabled && item.reason && (
                <div className={cls.reason}>{item.reason}</div>
              )}
            </div>
          )}
          hasClear
          showSearch
          filterLocal={false}
          onSearch={(s) => setSearch(s)}
        />
      </div>
    );
  }
);
