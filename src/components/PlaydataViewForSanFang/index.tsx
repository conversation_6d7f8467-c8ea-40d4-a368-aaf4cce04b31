import './style.scss';

import React, { useEffect, useState } from 'react';
import moment from 'moment';
import { Form } from '@alifd/next';
import { BigNumber } from 'bignumber.js';

import * as api from '../../api';
import { BudgetPickerDetail } from '@alife/carbon-biz-kunlun-zs/lib/components/BudgetPicker/BudgetPicker';

const FormItem = Form.Item;

const formItemLayout = {
  labelCol: { span: 5 },
  wrapperCol: { span: 19 },
};

enum deliveryTypeEnum {
  '仅外卖' = 1,
  '仅到店自提' = 2,
  '全部渠道' = 3,
}

enum deliveryChannelEnum {
  '普通渠道',
  '店内投放渠道',
  '店外投放渠道',
  'N元购渠道',
  '店内+店外投放渠道',
  '卡券售卖渠道',
  '搭售活动渠道',
  '支付宝渠道',
  '同店换购渠道',
}

const SanFangPlayDetail: React.FC<any> = ({ activityDetail, viewJson, env, activityId }) => {
  try {
    const { beginTime, endTime, createdUserName, createdUserId } =
      activityDetail;
    const data = JSON.parse(viewJson);
    const { activityMarketingInfo, couponGroupRuleList = [] } = data;
    couponGroupRuleList.map((item: any) => {
      item.activityRule.condition = new BigNumber(item?.activityRule?.condition)
        .dividedBy(100)
        .toNumber();
      item.activityRule.discount = new BigNumber(item?.activityRule?.discount)
        .dividedBy(100)
        .toNumber();
      item.activityRule.platformSubsidy = new BigNumber(
        item?.activityRule?.platformSubsidy
      )
        .dividedBy(100)
        .toNumber();
      item.activityRule.brandSubsidy = new BigNumber(
        item?.activityRule?.brandSubsidy
      )
        .dividedBy(100)
        .toNumber();
      item.activityRule.shopSubsidy = new BigNumber(
        item?.activityRule?.shopSubsidy
      )
        .dividedBy(100)
        .toNumber();
    });
    const {
      couponName,
      availableDays,
      storeCategoryForApp,
      overlying,
      deliveryType,
      deliveryChannel,
      couponLogo,
      budgetId,
      budgetName,
      availableStartTime,
      availableEndTime,
    } = activityMarketingInfo;

    let timeDes = '';
    if (availableDays) {
      timeDes = `自领取日期起${availableDays}天`;
    } else if (availableDays === 0) {
      timeDes = '活动当天生效';
    } else {
      let desStartTime = moment(availableStartTime).format('YYYY-MM-DD');
      let desEndTime = moment(availableEndTime).format('YYYY-MM-DD');
      timeDes = `${desStartTime} - ${desEndTime}`;
    }
    let beginTimeStr = moment(beginTime).format('YYYY-MM-DD HH:mm:ss');
    let endTimeStr = moment(endTime).format('YYYY-MM-DD HH:mm:ss');

    return (
      <div className="zs-act-section-sanfang">
        <h2>基础信息</h2>
        <Form {...formItemLayout}>
          <FormItem label="红包发放时间：">
            <span>
              {beginTimeStr} - {endTimeStr}
            </span>
          </FormItem>
          <FormItem label="红包外部名称：">
            <span>{couponName}</span>
          </FormItem>
          <FormItem label="红包使用时间：">
            <span>{timeDes}</span>
          </FormItem>
          <FormItem label="店内分类名称：">
            <span>{storeCategoryForApp}</span>
          </FormItem>
          <FormItem label="店内分类名称：" help="同享券：与其他活动均可同享；专享券：与店铺券、全店满减、业态红包、单品特价券不可叠加，与其他活动均同享">
            <span>{overlying ? '同享券' : '专享券'}</span>
          </FormItem>
          <FormItem label="核销渠道：" style={{ marginTop: "30px" }}>
            <span>{deliveryTypeEnum[deliveryType]}</span>
          </FormItem>
          <FormItem label="投放渠道：">
            <span>{deliveryChannelEnum[deliveryChannel]}</span>
          </FormItem>
          <FormItem label="创建人：">
            <span>
              {createdUserName}({createdUserId})
            </span>
          </FormItem>
          <FormItem label="领券图片：">
            <img src={couponLogo} width={120} />
          </FormItem>
          <FormItem label="分档位信息：">
            {couponGroupRuleList.map((levelItem: any, index: number) => {
              return (
                <div className="sanfang-level">
                  <p>
                    档位{index + 1}：满{levelItem?.activityRule?.condition}－
                    {levelItem?.activityRule?.discount}，平台补贴
                    {levelItem?.activityRule?.platformSubsidy}元，品牌补贴
                    {levelItem?.activityRule?.brandSubsidy}元，商户补贴
                    {levelItem?.activityRule?.shopSubsidy}元
                  </p>
                  <div className="sanfang-subsidy">
                    <p>
                      限领规则：每人活动期间限领
                      {levelItem?.activityLimitRule?.userTotalCountLimit}
                      张，每人每日限领
                      {levelItem?.activityLimitRule?.userDayCountLimit}张
                    </p>
                    <p>
                      活动库存：总发行量
                      {levelItem?.activityLimitRule?.totalCountLimit}
                      张，每日发行量
                      {levelItem?.activityLimitRule?.dayCountLimit}张
                    </p>
                  </div>
                </div>
              );
            })}
          </FormItem>
        </Form>
        <h2>预算信息</h2>
        <Form {...formItemLayout}>
          <FormItem label="预算：">
            <BudgetPickerDetail
              value={{
                budgetId,
                budgetName,
              }}
              env={{
                view: 'detail',
                env: env,
                detailContext: {
                  activityId,
                }
              }}
            />
          </FormItem>
        </Form>
      </div>
    );
  } catch (e) {
    console.error(e);
    return null;
  }
};

const SanFangPlayDetailWrap: React.FC<any> = ({ activityInfo, env }) => {
  const [detail, setDetail] = useState<any>();

  useEffect(() => {
    api.subActivity.getDetail(activityInfo.activityId).then((res) => {
      setDetail(res.data);
    });
  }, [activityInfo.activityId]);

  if (!detail) {
    return null;
  }

  return (
    <div className="zs-act-info">
      <div className="zs-act-area">
        <div className="zs-act-area-title" style={{ marginBottom: 30 }}>
          玩法设置
        </div>
        <div className="zs-act-section">
          <SanFangPlayDetail
            activityDetail={detail?.investmentActivityDetailDTO}
            viewJson={detail?.viewJson}
            activityId={activityInfo.activityId}
            env={env}
          />
        </div>
      </div>
    </div>
  );
};

export default SanFangPlayDetailWrap;
