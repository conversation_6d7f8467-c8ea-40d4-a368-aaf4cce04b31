import * as React from "react";
import { useEffect, useRef, useState } from "react";
import { updateSOPBar } from "@alife/starship-alpha-sdk";
import { mountModule, unmoutModule } from "@ice/stark-module";

import { resolveEnv } from "../../common";

const getForm: any = () => {
  const env = resolveEnv();
  let url = `https://nr.alibaba-inc.com/app/op-fe/alpha-context-info-view/micro-resource.json`;
  if (env === "pre") {
    url =
      "https://pre-nr.alibaba-inc.com/app/op-fe/alpha-context-info-view/micro-resource.json";
  }
  return fetch(url).then(async (res) => (await res?.json())?.index);
};

interface Props {
  sceneId?: string | number;
  planId?: string | number;
}

const AlphaSceneInfo: React.FC<Props> = (props) => {
  const [moduleInfo, setModuleInfo] = useState<any>();
  const alphaContextInfoRef = useRef<any>(null);

  useEffect(() => {
    updateSOPBar({
      visible: true,
      step: "create-zs-activity",
      type: "workflow",
    });
    getForm().then((res: string[]) => {
      setModuleInfo({
        name: "moduleName",
        url: res,
        runtime: [
          {
            id: "React@17",
            url: "https://g.alicdn.com/code/lib/react/17.0.2/umd/react.production.min.js",
          },
          {
            id: "ReactDOM@17",
            url: "https://g.alicdn.com/code/lib/react-dom/17.0.2/umd/react-dom.production.min.js",
          },
        ],
      });
    });
  }, []);

  useEffect(() => {
    if (!moduleInfo) {
      return;
    }
    try {
      mountModule(
        moduleInfo,
        alphaContextInfoRef.current,
        {
          env: resolveEnv(),
          pageType: "scene-like-page",
          sceneId: props.sceneId,
          planId: props.planId,
        },
        { multiMode: true }
      );
      return () => {
        unmoutModule(moduleInfo, alphaContextInfoRef.current);
      };
    } catch (error) {
      // @ts-ignore
      window.tracker && window.tracker.logError(e);
    }
  }, [moduleInfo]);

  return <div style={{ marginRight: "12px" }} ref={alphaContextInfoRef} />;
};

export default AlphaSceneInfo;
