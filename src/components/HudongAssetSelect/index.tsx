import * as React from "react";
import { useDebounceEffect } from 'ahooks';
import { useState } from "react";
import { Select } from "@alifd/next";
import { resolveEnv } from "../../common";
import { AlscGatewayWebClient, EnvType, RequestSource } from "@ali/alsc-gateway-web-client";

class AssetService {
  env: string;

  constructor(env: string) {
    this.env = env;
  }

  async query(search: string): Promise<any> {
    const aslcEnvMapping: any = {
      daily: EnvType.DAILY,
      pre: EnvType.PRE,
      prod: EnvType.PROD,
    };
    const aslcClient = new AlscGatewayWebClient({
      env: aslcEnvMapping[this.env],
      source: RequestSource.BUC,
    });
    const res = await aslcClient.fetch({
      apiKey: 'ele-newretail-investment.NewretailInvestmentActivityShopPoolYundingService.getPropertyPoolList',
      params: [search],
    });
    return res;
  }
}

interface Value {
  id: number;
  name: string;
}

interface Props {
  value?: Value;
  disabled?: boolean;
  style: any;
  onChange: (v: Value | null | undefined) => void;
}

const HudongAssetSelect: React.FC<Props> = ({
  value,
  onChange,
  disabled = false,
  style,
}) => {
  const [dataSource, setDataSource] = useState<Value[]>([]);
  const [searchValue, setSearchValue] = useState("");

  useDebounceEffect(() => {
    if (searchValue) {
      new AssetService(resolveEnv()).query(searchValue).then((res) => {
        const options = (res.data || []).map((item: any) => {
          return {
            id: item.poolId,
            name: item.poolName,
          };
        });
        setDataSource(options);
      });
    }
  }, [searchValue]);

  return (
    <Select
      value={value?.id}
      disabled={disabled}
      filterLocal={false}
      showSearch
      style={{
        width: 200,
        ...style,
      }}
      hasClear
      placeholder="请输入互动资产ID"
      onChange={(id) => {
        if (!id) {
          onChange(undefined);
          return;
        }
        const asset = dataSource.find((v) => v.id === id);
        onChange(asset);
      }}
      onSearch={(v) => {
        setSearchValue(v);
      }}
    >
      {dataSource.map((item) => {
        return (
          <Select.Option key={item.id} value={item.id}>
            {item.name}
          </Select.Option>
        );
      })}
    </Select>
  );
};

export default HudongAssetSelect;
