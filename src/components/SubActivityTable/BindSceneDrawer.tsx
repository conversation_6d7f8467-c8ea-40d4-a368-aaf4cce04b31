import './BindSceneDrawer.scss';

import React, { useCallback, useEffect, useState } from 'react';
import {
  Button,
  Drawer,
  Field,
  Form,
  Input,
  Message,
  Pagination,
  Table,
} from '@alifd/next';
import moment from 'moment';

import * as Api from '../../api';

interface BindSceneDrawerTS {
  activityId: string | number;
  visible: boolean;
  onClose: () => void;
}

interface PaginationTS {
  page: number;
  pageSize: number;
}

const defaultPagination = {
  page: 1,
  pageSize: 10,
};

const BindSceneDrawer: React.FC<BindSceneDrawerTS> = ({
  activityId,
  visible = true,
  onClose,
}) => {
  const field = Field.useField({});
  const [query, setQuery] = useState({});
  const [dataSource, setDataSource] = useState([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState<PaginationTS>(defaultPagination);
  const [total, setTotal] = useState(0);
  const [selects, setSelects] = useState<Array<number>>([]);

  const onChange = (selectedRowKeys: Array<number>) => {
    setSelects(selectedRowKeys);
  };

  useEffect(() => {
    if (!activityId) return;
    if (!visible) return;

    setLoading(true);
    const params = {
      activityId,
      ...query,
      ...pagination,
    };

    Api.alpha
      .getInvestmentActivitySignUpSceneList(params)
      .then((res) => {
        if (res?.data) {
          setDataSource(res?.data || []);
          setTotal(res?.total);
        } else {
          Message.error(res?.errorMessage || '获取列表失败');
        }
      })
      .catch((e) => {
        Message.error(e?.errorMessage || '获取列表失败');
      })
      .finally(() => {
        setLoading(false);
      });
  }, [activityId, pagination, query]);

  const onBindClick = useCallback(() => {
    const params: Api.BindSceneParams = {} as Api.BindSceneParams;
    params.investmentActivityId = activityId;
    params.sceneIdList = selects;
    setLoading(true);
    Api.alpha
      .investmentActivityBindScene(params)
      .then((res) => {
        if (res?.data) {
          Message.show('关联成功');
          setPagination({ ...pagination });
        } else {
          Message.error(res?.errorMessage || '关联失败，请稍后重试');
        }
      })
      .catch((err) => {
        Message.error(err?.errorMessage || '关联失败，请稍后重试');
      })
      .finally(() => setLoading(false));
  }, [selects]);

  const _onClose = () => {
    field.reset();
    setQuery({});
    setPagination(defaultPagination);
    onClose();
  };

  const onSceneIdChange = (value: string) => {
    const reg = /^-?\d*(\.\d*)?$/;
    if (reg.test(value) || value === '' || value === '-') {
      field.setValue('sceneId', value);
    }
  };

  return (
    <Drawer
      className="zs-drawer-wrapper"
      headerStyle={{
        backgroundColor: '#FFFFFF',
        boxShadow: 'inset 0 -1px 0 0 rgba(0,0,0,0.06)',
        borderWidth: 0,
      }}
      visible={visible}
      width={'auto'}
      title="关联活动场景"
      onClose={_onClose}
    >
      <Form field={field} style={{ display: 'flex' }}>
        <Form.Item className="zs-drawer-input">
          <Input
            {...field.init('sceneId')}
            placeholder="输入活动场景Id"
            onChange={onSceneIdChange}
          />
        </Form.Item>
        <Form.Item className="zs-drawer-input">
          <Input placeholder="输入活动场景名称" {...field.init('sceneName')} />
        </Form.Item>
        <Form.Item className="zs-drawer-input">
          <Input placeholder="输入活动场景负责人" {...field.init('owner')} />
        </Form.Item>
        <Form.Submit
          className="zs-drawer-search-btn"
          type="secondary"
          onClick={(value: any) => {
            setQuery({ ...value });
            setPagination(defaultPagination);
          }}
        >
          查询
        </Form.Submit>
        <Form.Reset
          onClick={() => {
            field.reset();
            setQuery({});
            setPagination(defaultPagination);
          }}
        >
          重置
        </Form.Reset>
      </Form>

      <Table
        className="zs-sub-activity-table"
        hasBorder={false}
        primaryKey={'sceneId'}
        rowSelection={{
          onChange: onChange,
        }}
        loading={loading}
        dataSource={dataSource}
      >
        <Table.Column dataIndex="sceneId" title="活动场景 ID" width={120} />
        <Table.Column dataIndex="name" title="活动场景名称" width={200} />
        <Table.Column dataIndex="planName" title="所属活动计划" width={200} />
        <Table.Column
          dataIndex="createUser"
          title="活动场景创建人"
          width={250}
          cell={(v: string) => {
            let creator: any = {};
            try {
              creator = JSON.parse(v);
            } catch (e) {
              // debug('');
              console.log('Json化失败=======');
            }
            if (creator?.realName && creator?.workId) {
              return `${creator?.realName}(${creator?.workId})`;
            } else {
              return '-';
            }
          }}
        />
        <Table.Column
          dataIndex="time"
          title="活动场景周期"
          width={350}
          cell={(v: string, i: number, record: any) => {
            const { beginTime, endTime } = record || {};
            if (beginTime && endTime) {
              return `${moment(beginTime).format(
                'YYYY.MM.DD HH:mm'
              )} - ${moment(endTime).format('YYYY.MM.DD HH:mm')}`;
            } else {
              return '-';
            }
          }}
        />
      </Table>
      <Pagination
        current={pagination.page}
        pageNumberRender={(page: number) => (
          <div className="zs-drawer-page-num">{page}</div>
        )}
        pageSize={pagination.pageSize}
        onChange={(curPage: number) =>
          setPagination({ ...pagination, page: curPage })
        }
        style={{ textAlign: 'end', marginTop: 24 }}
        shape="no-border"
        showJump
        total={total}
        totalRender={(t: number) => (
          <div className="zs-drawer-page-total">共 {t} 条</div>
        )}
      />

      <div className="zs-drawer-footer-btns">
        <Button style={{ marginRight: 8 }} onClick={_onClose}>
          取消
        </Button>
        <Button onClick={onBindClick} type="primary">
          关联
        </Button>
      </div>
    </Drawer>
  );
};

export default BindSceneDrawer;
