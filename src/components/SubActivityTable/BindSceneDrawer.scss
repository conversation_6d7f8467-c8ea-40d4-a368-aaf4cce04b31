.zs-drawer-wrapper {
  .zs-drawer-input {
    width: 262px;
    min-width: 200px;
    margin-right: 12px;
  }
  .zs-drawer-search-btn {
    margin-right: 12px;
  }
  .zs-drawer-page-num {
    padding: 4px 11px;
    border-radius: 2px;
    font-weight: 500;
    font-size: 12px;
    line-height: 20px;
    margin-bottom: 60px;
  }
  .zs-drawer-page-total {
    font-size: 12px;
    color: #505050;
    text-align: center;
  }
  .zs-drawer-footer-btns {
    position: absolute;
    bottom: 0;
    width: 100%;
    border-top: 1px solid #e8e8e8;
    padding: 10px 16px;
    text-align: right;
    left: 0;
    background: #fff;
    border-radius: 0 0 4px 4px;
  }
  .next-pagination .next-pagination-item {
    border-width: 0;
    padding: 0;
    color: #505050;
    background-color: white;
  }
  .next-pagination .next-pagination-item.next-current {
    color: #f77234;
    background-color: #fff3e8;
  }
}
