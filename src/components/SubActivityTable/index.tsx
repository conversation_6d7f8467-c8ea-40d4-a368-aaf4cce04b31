import "./style.scss";

import React, { useState, useEffect } from "react";

import { useCallback } from "react";
import { useHistory } from "react-router";
import { Message, Dialog, Table, Button, Dropdown, Menu, Select } from "@alifd/next";

import BindSceneDrawer from "./BindSceneDrawer";
import * as api from "../../api";
import InfoItem from "../InfoItem";
import { InvestmentActivityDTO } from "../../models";
import {
  ActivityTypeEnum,
  ActivityTypeEnumStr,
  InvestmentTypeEnum,
  InviteActivityTypeEnum,
  UserInfo,
} from "../../constants";
import { getStrategy } from "../../strategy";
import { percentage } from "../../common";
import {
  checkForbiddenAndAlertIfNeed,
  handleGotoActivityDetail,
  handleNoPermissionForActivity,
} from "../../pages/common";
import { MonitorFusion } from "@ali/monitor-fusion";

const NOT_STARTED = 1;
const ON_GOING = 2;
const moment = require("moment");
// const debug = require('debug')('zs:homepage:SubActivityTable')

interface Props {
  onCopyActivity?: (
    activityId: number,
    activityType: InvestmentTypeEnum,
    subsidyType: string,
    templateId?: number,
  ) => void;
  loading: boolean;
  dataSource: InvestmentActivityDTO[];
  onRefresh: () => void;
}

const SubActivityTable: React.FC<Props> = ({
  loading,
  onCopyActivity,
  dataSource,
  onRefresh,
}) => {
  const history = useHistory();
  const [visible, setVisible] = useState(false);
  const [curActId, setCurActId] = useState<string | number>('');
  const [userInfo, setUserInfo] = useState<UserInfo>({} as UserInfo);

  useEffect(() => {
    api.getUserInfo().then(res => {
      setUserInfo(res.data);
    })
  }, []);

  const handleAction = useCallback(
    async (
      record: InvestmentActivityDTO,
      action:
        | "lookup"
        | "examine"
        | "dingtalkLink"
        | "offline"
        | "modifyStock"
        | "copy"
        | "bindScene"
    ) => {
      switch (action) {
        case "copy":
          if (record.investmentActivityDetailDTO.fromString !== "NEW") {
            console.error("copy is supported only for new subActivity");
            return;
          }
          const selectSubsidyType = record.investmentActivityDetailDTO.selectSubsidyType || [];
          let subsidyType: string;
          if(selectSubsidyType.length > 1 ) {
            Dialog.show({
              title: "请选择补贴类型",
              style: { width: 300 },
              content: (
                <div> 
                  补贴类型：
                  <Select
                    data-test-id="creation-dialog-template-type"
                    onChange={(value) => {
                      subsidyType = value;
                    }}
                    placeholder="请选择补贴类型"
                  >
                   {selectSubsidyType.map(item => <Select.Option value={item}>{item}</Select.Option>)}
                  </Select>
                </div>
              ),
              footer: [
                <Button
                  type="primary"
                  onClick={() =>
                    {
                      if (!subsidyType) {
                        Message.error("请选择补贴类型");
                        return;
                      }
                      onCopyActivity &&
                        onCopyActivity(
                          record.investmentActivityDetailDTO.activityId,
                          record.investmentActivityDetailDTO.activityType,
                          subsidyType,
                          record.investmentActivityDetailDTO
                            .investmentActivityType ===
                            InviteActivityTypeEnum.SINGLE_ACTIVITY
                            ? undefined
                            : record.investmentActivityDetailDTO.templateId
                        );
                    }
                  }
                >
                复制活动
              </Button>,
              ],
            });
          } else {
            subsidyType = selectSubsidyType[0];
            onCopyActivity &&
              onCopyActivity(
                record.investmentActivityDetailDTO.activityId,
                record.investmentActivityDetailDTO.activityType,
                subsidyType,
                record.investmentActivityDetailDTO.investmentActivityType ===
                  InviteActivityTypeEnum.SINGLE_ACTIVITY
                  ? undefined
                  : record.investmentActivityDetailDTO.templateId
              );
          }
          
          break;
        case "lookup":
        case "examine":
          handleGotoActivityDetail(
            record.investmentActivityDetailDTO,
            history,
            action
          );
          break;
        case "dingtalkLink":
          let url = `https://nr.ele.me/op-fe/ebai-zs-h5-landing?activityId=${record.investmentActivityDetailDTO.activityId}`;
          if (record.investmentActivityDetailDTO.fromString === "NEW") {
            url += "&isNewActivity=true";
          }
          Dialog.show({
            title: "提示",
            content: <>报名链接：{url}</>,
            footer: false,
          });
          break;
        case "modifyStock":
          if (await checkForbiddenAndAlertIfNeed()) {
            return;
          }
          const strategy = getStrategy(
            record.investmentActivityDetailDTO?.activityType
          );
          if (!strategy || !strategy.handleModifyStock) {
            return;
          }
          strategy.handleModifyStock(
            {
              activityId: record.investmentActivityDetailDTO.activityId,
              isNewActivity:
                record.investmentActivityDetailDTO.fromString === "NEW",
              viewJson: record.viewJson,
              marketPlay: record.investmentActivityDetailDTO.marketPlay,
              investmentSchema: record.investmentActivityDetailDTO.investmentSchema
            },
            {
              onSuccess: () => {
                Message.success("修改成功");
                onRefresh();
              },
              onFail: (msg: string) => {
                Message.error(msg || "修改失败，请稍后重试");
              },
            }
          );
          break;
        case "offline":
          if (await checkForbiddenAndAlertIfNeed()) {
            return;
          }
          Dialog.show({
            title: "提示",
            style: { width: 300 },
            content: "确认要下线活动吗？",
            onOk: async () => {
              const activityId =
                record.investmentActivityDetailDTO.activityId;
              const activityType =
                record.investmentActivityDetailDTO.activityType;
              const isNewActivity =
                record.investmentActivityDetailDTO.fromString === "NEW";
              let res: any;
              try {
                if (isNewActivity) {
                  res = await api.subActivity.offline(activityId);
                } else {
                  if (activityType === ActivityTypeEnum.N_YUAN_ACTIVITY) {
                    res = await api.legacy.offlineNBuyActivity(activityId);
                  } else {
                    res = await api.legacy.offlineSubActivity(activityId);
                  }
                }
                if (!res.success) {
                  const noPermission =
                    res.errorCode === "NO_AUDIT_FOR_ACTIVITY";
                  if (noPermission) {
                    // 用户无权限
                    await handleNoPermissionForActivity(activityId, {
                      activityType,
                      isNewActivity,
                    });
                  } else {
                    Message.error(res.errorMessage);
                    throw new Error(res.errorMessage);
                  }
                }
              } catch(err: any) {
                // 子活动下线异常 arms监控
                MonitorFusion?.logCustomEvent({
                  code: "offline_activity_err",
                  msg: err.message || err,
                  c1: `sub activity -> isNewActivity： ${isNewActivity}, `,
                  c2: `${activityType}`,
                  c3: `${activityId}`
                });
              } finally {
                onRefresh();
              }
            },
          });
          break;
        case "bindScene": 
          // 弹出绑定抽屉
          setVisible(true);
          setCurActId(record.investmentActivityDetailDTO?.activityId || '');
          break;
        default:
          throw new Error("unsupported action type:" + action);
      }
    },
    [onCopyActivity, onRefresh, history]
  );

  return (
    <div>
      <Table
        className="zs-sub-activity-table"
        loading={loading}
        dataSource={dataSource}
      >
        <Table.Column
          title="活动信息"
          width={420}
          cell={(_: any, index: number, record: InvestmentActivityDTO) => {
            const { activityId, activityType, name, beginTime, endTime } =
              record.investmentActivityDetailDTO;
            const strategy = getStrategy(activityType);

            return (
              <>
                <InfoItem label="活动ID" labelWidth={100}>
                  {activityId}
                </InfoItem>
                <InfoItem label="活动名称" labelWidth={100}>
                  {name}
                </InfoItem>
                {record.investmentActivityDetailDTO.remark ? (
                  <InfoItem label="活动备注" labelWidth={100}>
                    {record.investmentActivityDetailDTO.remark}
                  </InfoItem>
                ) : null}
                <InfoItem label="活动时间" labelWidth={100}>
                  {record?.investmentActivityDetailDTO?.isLongTermActivity ? (
                    <td>长期有效</td>
                  ) : (
                    <>
                      {moment(beginTime).format("YYYY-MM-DD HH:mm:ss")} ~{" "}
                      {moment(endTime).format("YYYY-MM-DD HH:mm:ss")}
                    </>
                  )}
                </InfoItem>
                {strategy && strategy.renderExtraInfoForSubActivityTable
                  ? strategy.renderExtraInfoForSubActivityTable(record)
                  : null}
              </>
            );
          }}
        />
        <Table.Column
          title="招商信息"
          width={300}
          cell={(_: any, index: number, record: InvestmentActivityDTO) => {
            const { activityType, investmentSchema } = record.investmentActivityDetailDTO;
            const strategy = getStrategy(activityType);
            let ruleContent: any;
            if (strategy) {
              if (
                record.investmentActivityDetailDTO.fromString === "NEW" &&
                record.viewJson
              ) {
                try {
                  ruleContent = strategy.renderActivityRuleForTable(
                    record.viewJson,
                    { investmentSchema }
                  );
                } catch (e) {
                  console.error(e);
                }
              } else if (
                record.investmentActivityDetailDTO.fromString === "OLD"
              ) {
                try {
                  ruleContent = strategy.renderActivityRuleForTableLegacy(
                    record?.investmentActivityDetailDTO?.marketPlay as any,
                    record.investmentActivityDetailDTO
                  );
                } catch (e) {
                  console.error(e);
                }
              }
            }
            return (
              <>
                {ruleContent ?
                  <InfoItem label="优惠内容">{ruleContent}</InfoItem> :
                  null}
                <InfoItem label="活动类型">
                  {ActivityTypeEnumStr[activityType]}
                </InfoItem>
              </>
            );
          }}
        />
        <Table.Column
          title="活动报名统计"
          width={280}
          cell={(_: any, index: number, record: InvestmentActivityDTO) => {
            const { activityType, signUpShopType } =
              record.investmentActivityDetailDTO;
            const {
              approveNum,
              waitApproveNum,
              signupNum,
              inviteSuccessNum,
              inviteTotalNum,
              agentApproveNum,
              agentHangNum,
              agentInviteNum,
            } = record.investmentActivityExtendDTO || {};
            const { approveStatus } = record.investmentActivityDetailDTO;
            const acceptPrecent = percentage(signupNum, inviteSuccessNum);
            const approvePrecent = percentage(
              approveNum,
              (waitApproveNum as number) + (approveNum as number)
            );
            const strategy = getStrategy(activityType);
            const isHideSignUpInfo =
                  (typeof strategy?.isHideSignUpInfo === "function"
                    ? strategy?.isHideSignUpInfo(
                       {subsidyInvestmentModel: record?.investmentActivityDetailDTO?.attributes?.subsidyInvestmentModel}
                      )
                    : strategy?.isHideSignUpInfo) || false;
            const showAgentInfo =
              strategy?.isSupportAgent && signUpShopType === 2;
            const agentApprovePercent = showAgentInfo
              ? percentage(agentApproveNum, agentInviteNum)
              : null;
            const showItemAuditStats = strategy?.isItemAuditStatsVisible;

            function fallback(v: any) {
              if (v === "" || v == undefined || v === null) {
                return "-";
              } else {
                return v;
              }
            }
            return (
              <>
                {showAgentInfo && (
                  <div style={{ marginBottom: 5 }}>
                    <b>商户报名信息</b>
                  </div>
                )}
                {!isHideSignUpInfo && <>
                  <InfoItem labelWidth={120} label="已报名/已邀请">
                    {signupNum}/{inviteSuccessNum}
                  </InfoItem>
                  <InfoItem labelWidth={120} label="报名率">
                    {acceptPrecent}
                  </InfoItem>
                  <InfoItem labelWidth={120} label="共邀请">
                    {inviteTotalNum}{" "}
                    {+approveStatus !== 20 && +approveStatus !== 40 ? (
                      <>
                        （邀请失败
                        {(inviteTotalNum as number) -
                          (inviteSuccessNum as number)}
                        ）
                      </>
                    ) : null}
                  </InfoItem>
                </>}
                {showItemAuditStats ? (
                  <>
                    <InfoItem labelWidth={120} label="待审核/已审核">
                      {waitApproveNum}/{approveNum}
                    </InfoItem>
                    <InfoItem labelWidth={120} label="审核通过率">
                      {approvePrecent}
                    </InfoItem>
                  </>
                ) : null}

                {showAgentInfo && (
                  <>
                    <div style={{ marginBottom: 5 }}>
                      <b>代理商确认信息</b>
                    </div>
                    <InfoItem labelWidth={120} label="已确认/已挂起/共邀请">
                      {fallback(agentApproveNum)}/{fallback(agentHangNum)}/
                      {fallback(agentInviteNum)}
                    </InfoItem>
                    <InfoItem labelWidth={120} label="确认率">
                      {agentApprovePercent}
                    </InfoItem>
                  </>
                )}
              </>
            );
          }}
        />
        <Table.Column
          title="状态"
          width={265}
          cell={(_: any, index: number, record: InvestmentActivityDTO) => {
            const {
              status,
              statusStr,
              inviteStatusStr,
              approveStatusStr,
              failReason,
            } = record.investmentActivityDetailDTO;
            return (
              <>
                <InfoItem label="活动状态">{statusStr}</InfoItem>
                <InfoItem label="报名状态">{inviteStatusStr}</InfoItem>
                <InfoItem label="审批状态">{approveStatusStr}</InfoItem>
                {status === 7 && (
                  <InfoItem label="失败信息">
                    <span style={{ color: "#b20" }}>{failReason}</span>
                  </InfoItem>
                )}
              </>
            );
          }}
        />
        <Table.Column
          title="创建信息"
          width={265}
          cell={(_: any, index: number, record: InvestmentActivityDTO) => {
            const { createdUserName, gmtCreate } =
              record.investmentActivityDetailDTO;
            return (
              <>
                <InfoItem labelWidth={100} label="创建人">
                  {createdUserName}
                </InfoItem>
                <InfoItem labelWidth={100} label="创建时间">
                  {moment(gmtCreate).format("YYYY-MM-DD HH:mm:ss")}
                </InfoItem>
              </>
            );
          }}
        />
        <Table.Column
          title="操作"
          lock="right"
          width={170}
          cell={(_: any, index: any, record: InvestmentActivityDTO) => {
            const { activityType, status } = record.investmentActivityDetailDTO;
            const strategy = getStrategy(activityType);
            const isOfflineEnabled = strategy?.isOfflineEnabled(status);
            const isSupportModifyStock = strategy?.isSupportModifyStock({
              isNewActivity:
                record.investmentActivityDetailDTO.fromString === "NEW",
              activityId: record.investmentActivityDetailDTO
                ?.activityId as number,
              viewJson: record.viewJson,
              marketPlay: record.investmentActivityDetailDTO?.marketPlay,
              investmentSchema: record.investmentActivityDetailDTO.investmentSchema
            });
            const copyEnabled =
              (record.investmentActivityDetailDTO?.selectSubsidyType || [])
                .length > 0 &&
              strategy?.isCopyEnabled &&
              strategy?.isCopyEnabled({
                investmentSchema:
                  record.investmentActivityDetailDTO.investmentSchema,
                investmentType:
                  record.investmentActivityDetailDTO.investmentType,
                viewJson: record.viewJson,
              });

            return (
              <div style={{ padding: "0px 10px" }}>
                <div style={{ marginBottom: 10 }}>
                  <Button
                    type="primary"
                    text
                    onClick={() => handleAction(record, "lookup")}
                  >
                    查看
                  </Button>
                  {!strategy?.checkBtnVisible &&
                    <>
                      <span style={{ color: "#FF7C4D" }}>
                        &nbsp;&nbsp;|&nbsp;&nbsp;
                      </span>
                      <Button
                        type="primary"
                        text
                        onClick={() => handleAction(record, "examine")}
                      >
                        审核
                      </Button>
                    </>}
                </div>
                <div style={{ marginBottom: 10 }}>
                  <Button
                    type="primary"
                    text
                    onClick={() => handleAction(record, "dingtalkLink")}
                  >
                    钉钉报名链接
                  </Button>
                </div>

                {[NOT_STARTED, ON_GOING].includes(status) &&
                userInfo?.workid?.toLowerCase() ===
                  record?.investmentActivityDetailDTO?.createdUserId?.toLowerCase() ? (
                  <div>
                    <Button
                      type="primary"
                      text
                      onClick={() => handleAction(record, 'bindScene')}
                    >
                      关联活动场景
                    </Button>
                  </div>
                ) : null}
                
                {isSupportModifyStock ||
                  isOfflineEnabled ||
                  copyEnabled ? (
                  <Dropdown
                    trigger={
                      <div
                        style={{ display: "inline-block", color: "#FF7C4D", marginBottom: 10 }}
                      >
                        更多
                        <i className="next-icon next-icon-arrow-down" />
                      </div>
                    }
                    triggerType="click"
                  >
                    <Menu>
                      {isOfflineEnabled ? (
                        <Menu.Item key="offlineSubActiivty">
                          <Button
                            type="primary"
                            text
                            onClick={() => handleAction(record, "offline")}
                          >
                            下线
                          </Button>
                        </Menu.Item>
                      ) : null}
                      {isSupportModifyStock ? (
                        <Menu.Item key="modifyStock">
                          <Button
                            type="primary"
                            text
                            onClick={() => handleAction(record, "modifyStock")}
                          >
                            修改库存
                          </Button>
                        </Menu.Item>
                      ) : null}
                      {copyEnabled ? (
                        <Menu.Item key="copy">
                          <Button
                            type="primary"
                            text
                            onClick={() => handleAction(record, "copy")}
                          >
                            复制
                          </Button>
                        </Menu.Item>
                      ) : null}
                    </Menu>
                  </Dropdown>
                ) : null}

              </div>
            );
          }}
        />
      </Table>

      <BindSceneDrawer
        visible={visible}
        onClose={() => setVisible(false)}
        activityId={curActId}
      />
    </div>
  );
};

export default SubActivityTable;
