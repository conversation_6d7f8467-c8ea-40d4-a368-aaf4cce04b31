import React, { useEffect, useState } from 'react';

import * as api from '../../api';
import { getStrategy } from '../../strategy';
import { resolveEnv } from '../../common';

interface Props {
  ActivityForm: any;
  activityInfo: {
    activityId: number | string;
  };
  // 从 ViewJson 解析 MarketPlay
  resolveMarketPlayFromViewJson?: boolean;
}

const PlaydataView: React.FC<Props> = ({
  ActivityForm,
  activityInfo,
  resolveMarketPlayFromViewJson = true,
}) => {
  const [detail, setDetail] = useState<any>();
  const [marketPlay, setMarketPlay] = useState<any>();
  const [ready, setReady] = useState(false);

  useEffect(() => {
    api.subActivity.getDetail(activityInfo.activityId).then((res) => {
      setDetail(res.data);
      const { viewJson, investmentActivityDetailDTO } = res.data || {};
      const _marketPlay = investmentActivityDetailDTO?.marketPlay;
      const { activityType, investmentSchema } =
        investmentActivityDetailDTO || {};
      if (resolveMarketPlayFromViewJson) {
        const strategy = getStrategy(activityType);
        strategy
          ?.marketPlayMapper?.(viewJson, investmentActivityDetailDTO, {
            investmentSchema,
            marketPlay: _marketPlay
          })
          .then((newMarketPlay: any) => {
            setMarketPlay(newMarketPlay);
            setTimeout(() => setReady(true), 300);
          });
      } else {
        setMarketPlay(investmentActivityDetailDTO.marketPlay);
        setTimeout(() => setReady(true), 300);
      }
    });
  }, [activityInfo.activityId, resolveMarketPlayFromViewJson]);

  if (!ready) {
    return null;
  }
  if (!marketPlay) {
    return null;
  }
  if (!detail) {
    return null;
  }

  return (
    <div className="zs-act-info">
      <div className="zs-act-area">
        <div className="zs-act-area-title" style={{ marginBottom: 30 }}>
          玩法设置
        </div>
        <div className="zs-act-section">
          <ActivityForm
            view="detail"
            instance={marketPlay.instance}
            env={{
              investmentTemplate:
                detail?.investmentActivityDetailDTO?.investmentSchema,
              env: resolveEnv(),
              detailContext:
                {
                  activityType:
                    detail?.investmentActivityDetailDTO?.activityType,
                  activityId: detail?.investmentActivityDetailDTO?.activityId,
                  creatorId: detail?.investmentActivityDetailDTO?.createdUserId,
                } || {},
            }}
            model={marketPlay.model}
          />
        </div>
      </div>
    </div>
  );
};

export default PlaydataView;
