import React, { useEffect, useState } from 'react';

import * as api from '../../api';

interface PropsForWrap {
  activityInfo: {
    activityId: number | string;
  };
  version?: 'v2';
}

interface PropsForDetail {
  playDetail?: {
    playLimitRuleDTO: {
      dayCountLimit: number;
      dayCountLimitMax: number;
    };
    playDataRuleDTOList: {
      subsidy: {
        shopSubsidy: number;
        shopSubsidyMax: number;
      };
    }[];
  };
  viewJson?: string;
  version?: 'v2';
}

const YetaiPlayDetail: React.FC<PropsForDetail> = ({
  playDetail,
  viewJson,
  version,
}) => {
  let shopSubsidy, dayCountLimit;
  let ready = false;
  if (playDetail && playDetail.playLimitRuleDTO) {
    const subsidy = playDetail.playDataRuleDTOList?.[0]?.subsidy;
    const Limit = playDetail.playLimitRuleDTO;
    shopSubsidy = `商家出资金额: ${subsidy?.shopSubsidy / 100} - ${
      subsidy?.shopSubsidyMax / 100
    }`;
    dayCountLimit = `单门店每日可消耗数量: ${Limit?.dayCountLimit} - ${Limit?.dayCountLimitMax}`;
    ready = true;
  } else if (version === 'v2' && viewJson) {
    try {
      const data = JSON.parse(viewJson);
      shopSubsidy = `商家出资金额: ${data?.shopSubsidy / 100} - ${
        data?.shopSubsidyMax / 100
      }`;
      dayCountLimit = `单门店每日可消耗数量: ${data?.dayCountLimit} - ${data?.dayCountLimitMax}`;
      ready = true;
    } catch (e) {
      console.error(e);
    }
  }

  if (!ready) {
    return null;
  }
  return (
    <div>
      <p>{shopSubsidy}</p>
      <p>{dayCountLimit}</p>
    </div>
  );
};

const YetaiPlayDetailWrap: React.FC<PropsForWrap> = ({
  activityInfo,
  version,
}) => {
  const [detail, setDetail] = useState<any>();

  useEffect(() => {
    api.subActivity.getDetail(activityInfo.activityId).then((res) => {
      setDetail(res.data);
    });
  }, [activityInfo.activityId]);
  return (
    <div className="zs-act-info">
      <div className="zs-act-area">
        <div className="zs-act-area-title" style={{ marginBottom: 30 }}>
          玩法设置
        </div>
        <div className="zs-act-section">
          <YetaiPlayDetail
            playDetail={
              detail?.investmentActivityDetailDTO?.responsePlayDataDTO
            }
            viewJson={detail?.viewJson}
            version={version}
          />
        </div>
      </div>
    </div>
  );
};

export default YetaiPlayDetailWrap;
