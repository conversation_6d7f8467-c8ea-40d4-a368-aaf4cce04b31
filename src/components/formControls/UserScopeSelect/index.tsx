import { Balloon, Icon, Radio } from "@alifd/next";

// TODO: 待补充全部已知的常用人群枚举值
export enum UserScopeEnum {
  ALL = 0, // 全部顾客
  STORE_NEW = 4, // 门店新客
  BRAND_NEW = 11, // 商家品牌新客
  STUDENT = 31, // 学生人群
}

// TODO: 待补充全部已知的常用人群枚举值
export const UserScopeOptions = [
  { label: "全部顾客", value: UserScopeEnum.ALL },
  { label: "门店新客", value: UserScopeEnum.STORE_NEW },
  { label: "商家品牌新客", value: UserScopeEnum.BRAND_NEW },
  { label: "学生人群", value: UserScopeEnum.STUDENT },
];

export function OptionLabel({
  label,
  tip,
}: {
  label: string;
  tip?: React.ReactNode;
}) {
  return (
    <>
      {label}
      {tip ? (
        <Balloon.Tooltip
          trigger={
            <Icon
              type="help"
              style={{ marginLeft: 4, color: "#999", cursor: "pointer" }}
            />
          }
          align="t"
        >
          {tip}
        </Balloon.Tooltip>
      ) : null}
    </>
  );
}

interface Props {
  value?: number;
  onChange?: (value?: UserScopeEnum) => void;
  disabled?: boolean;
  // 注意：可选的人群类型众多，每种活动需要展示出来的又都不一样
  // 所以人群选择组件不会内置选项，需要使用方自己传入需要展示的选项
  options?: {
    value: UserScopeEnum;
    label: string;
    disabled?: boolean;
    tip?: React.ReactNode;
  }[];
}

/**
 * 限制人群组件
 */
export function UserScopeSelect({
  value,
  onChange,
  options,
  disabled = false,
}: Props) {
  return (
    <Radio.Group
      disabled={disabled}
      value={value}
      onChange={(v) => onChange?.(v as number | undefined)}
    >
      {options?.map((op) => {
        return (
          <Radio value={op.value} disabled={op.disabled} key={op.value}>
            <OptionLabel label={op.label} tip={op.tip} />
          </Radio>
        );
      })}
    </Radio.Group>
  );
}

export function UserScopeView({ value }: { value: UserScopeEnum }) {
  const op = UserScopeOptions.find((item) => item.value === value);
  return <>{op?.label || ""}</>;
}
