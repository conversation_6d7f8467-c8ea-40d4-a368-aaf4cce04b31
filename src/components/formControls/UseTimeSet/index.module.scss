.timeRangeSelector {
  
    .timeRanges {
      margin-top: 12px;
    }
  
    .timeRangeItem {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 12px;
    }
  
    .addTimeRange {
      color: #0064C8;
      cursor: pointer;
      margin-top: 8px;
  
      &:hover {
        color: #0052A3;
      }
    }
  
    .errorMessage {
      color: #ff3000;
      font-size: 12px;
      margin-top: 4px;
    }
  }
  