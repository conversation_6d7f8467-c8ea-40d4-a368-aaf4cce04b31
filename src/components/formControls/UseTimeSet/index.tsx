import React from "react";
import { Icon, Radio, TimePicker } from "@alifd/next";
import { v4 as uuid4 } from 'uuid';
import moment from "moment";
import styles from "./index.module.scss";

export interface TimeRange {
  start: moment.Moment | null;
  end: moment.Moment | null;
  key: string;
}

export interface TimeRangeSelectorValue {
  timeType: "allDay" | "custom";
  timeRanges: TimeRange[];
}

interface TimeRangeSelectorProps {
  value?: TimeRangeSelectorValue;
  onChange?: (value: TimeRangeSelectorValue) => void;
  error?: string;
}

const UseTimeSet: React.FC<TimeRangeSelectorProps> = ({
  value = { timeType: "allDay", timeRanges: [] },
  onChange,
  error,
}) => {
  const handleTypeChange = (timeType: "allDay" | "custom") => {
    if (timeType === "allDay") {
      // 切换到全天模式时,清空 timeRanges
      onChange?.({
        timeType,
        timeRanges: [],
      });
    } else {
      // 切换到自定义模式时,创建一个新的 timeRange
      onChange?.({
        timeType,
        timeRanges: [{ start: null, end: null, key: uuid4() }],
      });
    }
  };

  const handleTimeChange = (
    index: number,
    field: "start" | "end",
    time: moment.Moment | null
  ) => {
    if (value.timeType !== "custom") return;

    const timeRangesV2 = [...value.timeRanges];
    timeRangesV2[index] = {
      ...timeRangesV2[index],
      [field]: time,
    };

    onChange?.({
      timeType: value.timeType,
      timeRanges: timeRangesV2,
    });
  };

  const addTimeRange = () => {
    if (value.timeType !== "custom" || value.timeRanges.length >= 2) return;

    onChange?.({
      timeType: value.timeType,
      timeRanges: [...value.timeRanges, { start: null, end: null, key: uuid4() }],
    });
  };

  return (
    <div className={styles.timeRangeSelector}>
      <div className={styles.content}>
        <Radio.Group value={value.timeType} onChange={handleTypeChange}>
          <Radio value="allDay">全天</Radio>
          <Radio value="custom">自定义红包使用时段</Radio>
        </Radio.Group>

        {value.timeType === "custom" && (
          <div className={styles.timeRanges}>
            {value.timeRanges.map((range) => (
              <div key={range.key} className={styles.timeRangeItem}>
                <TimePicker
                  value={range.start}
                  onChange={(time) => handleTimeChange(value.timeRanges.findIndex(r => r.key === range.key), "start", time as moment.Moment | null)}
                  format="HH:mm"
                />
                <TimePicker
                  value={range.end}
                  onChange={(time) => handleTimeChange(value.timeRanges.findIndex(r => r.key === range.key), "end", time as moment.Moment | null)}
                  format="HH:mm"
                />
                {value.timeRanges.length === 2 && (
                  <Icon
                    type="ashbin"
                    onClick={() => {
                      const newTimeRanges = value.timeRanges.filter(r => r.key !== range.key);
                      onChange?.({
                        timeType: value.timeType,
                        timeRanges: newTimeRanges,
                      });
                    }}
                  />
                )}
              </div>
            ))}
            {value.timeRanges.length < 2 && (
              <div className={styles.addTimeRange} onClick={addTimeRange}>
                + 添加时间段
              </div>
            )}
            {error && <div className={styles.errorMessage}>{error}</div>}
          </div>
        )}
      </div>
    </div>
  );
};

export default UseTimeSet;
