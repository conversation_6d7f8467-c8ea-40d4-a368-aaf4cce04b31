import { Radio } from "@alifd/next";

export enum DeliveryChannelEnum {
  GENERAL = 0, // 通用渠道
}

export const DeliveryChannelOptions = [
  {
    label: "通用渠道",
    value: DeliveryChannelEnum.GENERAL,
  },
];

interface Props {
  value?: number;
  onChange?: (value?: number) => void;
  disabled?: boolean;
  // 投放渠道类型比较多，并且不同活动支持的投放渠道不一样，所以必须使用方明确传入要投出的投放渠道
  options: {
    value: DeliveryChannelEnum;
    label: React.ReactNode;
    disabled?: boolean;
  }[];
}

/**
 * 投放渠道组件
 */
export function DeliveryChannelSelect({
  value,
  onChange,
  disabled = false,
  options,
}: Props) {
  return (
    <Radio.Group
      disabled={disabled}
      value={value}
      onChange={(v) => onChange?.(v as number | undefined)}
    >
      {options?.map((op) => {
        return (
          <Radio value={op.value} disabled={op.disabled} key={op.value}>
            {op.label}
          </Radio>
        );
      })}
    </Radio.Group>
  );
}

export function DeliveryChannelView({
  value,
}: {
  value?: DeliveryChannelEnum;
}) {
  const op = DeliveryChannelOptions.find((item) => item.value === value);
  return <>{op?.label || ""}</>;
}
