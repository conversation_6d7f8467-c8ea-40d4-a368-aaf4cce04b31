import { Radio } from "@alifd/next";

export enum DeliveryTypeEnum {
  WAIMAI = 1, // 外卖
  DAODIAN = 2, // 到店自提
  ALL = 3,
}

export const DeliveryTypeOptions = [
  {
    label: "仅外卖生效",
    value: DeliveryTypeEnum.WAIMAI,
  },
  {
    label: "仅到店自提生效",
    value: DeliveryTypeEnum.DAODIAN,
  },
  {
    label: "全部渠道生效",
    value: DeliveryTypeEnum.ALL,
  },
];

interface Props {
  value?: number;
  onChange?: (value?: number) => void;
  disabled?: boolean;
  options?: {
    value: DeliveryTypeEnum;
    label: React.ReactNode;
    disabled?: boolean;
  }[];
}

/**
 * 生效渠道选择组件
 */
export function DeliveryTypeSelect({
  value,
  onChange,
  disabled = false,
  options = DeliveryTypeOptions,
}: Props) {
  return (
    <Radio.Group
      disabled={disabled}
      value={value}
      onChange={(v) => onChange?.(v as number | undefined)}
    >
      {options.map((op) => {
        return (
          <Radio value={op.value} disabled={op.disabled} key={op.value}>
            {op.label}
          </Radio>
        );
      })}
    </Radio.Group>
  );
}

export function DeliveryTypeView({ value }: { value?: DeliveryTypeEnum }) {
  const op = DeliveryTypeOptions.find((item) => item.value === value);
  return <>{op?.label}</>;
}
