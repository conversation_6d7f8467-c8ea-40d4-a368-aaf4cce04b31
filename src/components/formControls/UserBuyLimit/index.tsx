import CheckboxWithInput from "../CheckboxWithInput";

interface Limit {
  userTotalCountLimit?: number;
  userDayCountLimit?: number;
}

interface Props {
  value: { [key: string]: string | number };
  onChange: (newValue: { [key: string]: string | number }) => void;
}

/**
 * 用户购买限定组件
 */
export function UserBuyLimit({ value, onChange }: Props) {
  return (
    <CheckboxWithInput
      value={value}
      onChange={onChange}
      options={[
        { label: "每人每天限购", key: "totalLimit" },
        { label: "每人活动限购", key: "dayLimit" },
      ]}
      unit="件"
    />
  );
}

export function UserBuyLimitView({
  value,
  unit = "件",
  userDayCountLimitText = "每人每天限购",
  userTotalCountLimitText = "每人活动限购",
}: {
  value?: Limit;
  unit?: string;
  userDayCountLimitText?: string;
  userTotalCountLimitText?: string;
}) {
  const { userDayCountLimit, userTotalCountLimit } = value || {};
  return (
    <>
      {userDayCountLimit && userTotalCountLimit ? (
        <div>
          {userDayCountLimitText} {userDayCountLimit} {unit}，{userTotalCountLimitText}{" "}
          {userTotalCountLimit} {unit}
        </div>
      ) : userDayCountLimit ? (
        <div>
          {userDayCountLimitText} {userDayCountLimit} {unit}
        </div>
      ) : userTotalCountLimit ? (
        <div>
          {userTotalCountLimitText} {userTotalCountLimit} {unit}
        </div>
      ) : null}
    </>
  );
}
