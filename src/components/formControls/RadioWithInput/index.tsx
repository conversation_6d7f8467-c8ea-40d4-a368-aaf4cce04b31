import * as React from "react";
import { NumberPicker, Radio } from "@alifd/next";

interface CheckboxInputProps {
  value: { [key: string]: string | number | undefined };
  onChange: (newValue: { [key: string]: string | number | undefined }) => void;
  options: { label: string; key: string, min?: number, max?: number }[];
  unit: string;
}

const RadioWithInput: React.FC<CheckboxInputProps> = ({
  value,
  onChange,
  options,
  unit,
}) => {
  const handleInputChange = (key: string, inputValue?: string | number) => {
    const valueV2 = { [key]: inputValue };
    onChange(valueV2);
  };

  return (
    <Radio.Group
      value={Object.keys(value || {})?.[0]}
      onChange={(v) => {
        if(!v) {
          onChange({});
          return;
        } else {
          onChange( { [v as string]: undefined });
        }
      }}
    >
      {options.map((option) => {
        return (
          <div style={{ display: "flex" }} key={option.key}>
            <Radio
              style={{ display: "block", marginBottom: "10px" }}
              key={option.key}
              value={option.key}
            >{option.label}</Radio>
             
            {option.key in value && (
              <>
                <NumberPicker
                  style={{ width: "100px" }}
                  min={option?.min}
                  max={option?.max}
                  value={value?.[option?.key] || ""}
                  onChange={(e) => handleInputChange(option.key, e)}
                  placeholder="请输入"
                />
                &nbsp;{unit}
              </>
            )}
          </div>
        );
      })}
    </Radio.Group>
  );
};
export default RadioWithInput;
