import * as React from "react";
import { Checkbox, Input } from "@alifd/next";

interface CheckboxInputProps {
  value: { [key: string]: string | number };
  onChange: (newValue: { [key: string]: string | number }) => void;
  options: { label: string; key: string }[];
  unit: string;
}

const CheckboxWithInput: React.FC<CheckboxInputProps> = ({
  value,
  onChange,
  options,
  unit,
}) => {
  const handleInputChange = (key: string, inputValue: string | number) => {
    const newValue = { ...value, [key]: inputValue };
    onChange(newValue);
  };

  return (
    <Checkbox.Group
      value={Object.keys(value)}
      onChange={(v) => {
        if(!v) {
          onChange({});
          return;
        }
        if (v.length === 0) {
          onChange({});
        } else {
          onChange(v.reduce((acc, cur) => {
            return {...acc, [String(cur)]: value[String(cur)] }
          }, {}));
        }
      }}
    >
      {options.map((option) => {
        return (
          <div key={option.key} style={{ display: "flex" }}>
            <Checkbox
              style={{ display: "block", marginBottom: "10px" }}
              key={option.key}
              value={option.key}
            >{option.label}</Checkbox>
             
            {option.key in value && (
              <>
                <Input
                  style={{ width: "100px" }}
                  type="number"
                  value={value?.[option?.key] || ""}
                  onChange={(e) => handleInputChange(option.key, e)}
                  placeholder="请输入"
                />
                &nbsp;{unit}
              </>
            )}
          </div>
        );
      })}
    </Checkbox.Group>
  );
};
export default CheckboxWithInput;
