interface Props {
  view: "edit" | "detail";
  type: "warning";
  content: string;
}
const color = {
  warning: "#fff1f0",
};

function PromptMessageEditing({ type, content }: Props) {
  return (
    <div
      style={{
        backgroundColor: color[type],
        color: "#333",
        padding: "8px 12px",
        borderRadius: "4px",
      }}
    >
      {content}
    </div>
  );
}

export const PromptMessage: React.FC<Props> = (props) => {
  if (props.view === "edit") {
    return <PromptMessageEditing {...props} />;
  } else {
    // 详情不展示本组件
    return null;
  }
};
