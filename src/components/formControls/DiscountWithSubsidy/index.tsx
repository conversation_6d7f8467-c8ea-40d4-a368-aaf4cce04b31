import { NumberPicker } from "@alifd/next";
import style from "./index.module.scss";

interface Props {
  value?: number;
  onChange?: (value?: number) => void;
  prompt?: string;
  precision?: number;
  numberMin?: number;
  numberMax?: number;
}

/**
 * 减 __ 元，平台补贴 __ 元
 * 平台补贴会跟着 减钱变化
 */
export function DiscountWithSubsidy({
  value,
  onChange,
  prompt,
  precision = 2,
  numberMin = 1,
  numberMax = 9999,
}: Props) {
  return (
    <div className={style.discountWithSubsidy}>
      <div className={style.prompt}>{prompt}</div>
      <div className={style.content}>
        减 &nbsp;
        <NumberPicker
          min={numberMin}
          max={numberMax}
          precision={precision}
          value={value}
          onChange={(v) => onChange?.(v as number)}
        />
        &nbsp; 元， 平台补贴 &nbsp;
        <NumberPicker value={value} disabled={true} /> &nbsp;元
      </div>
    </div>
  );
}

export function DiscountWithSubsidyView({ value }: { value?: number }) {
  return (
    <>
      减 {value} 元，平台补贴 {value} 元
    </>
  );
}
