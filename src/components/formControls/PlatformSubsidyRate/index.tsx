import { Balloon, Icon, Radio } from "@alifd/next";

const FIFTY_PERCENT = 50;
const NO_LiMIT = -1;

export function OptionLabel({
  label,
  tip,
}: {
  label: string;
  tip?: React.ReactNode;
}) {
  return (
    <>
      {label}
      {tip ? (
        <Balloon.Tooltip
          trigger={
            <Icon
              type="help"
              style={{ marginLeft: 4, color: "#999", cursor: "pointer" }}
            />
          }
          align="t"
        >
          {tip}
        </Balloon.Tooltip>
      ) : null}
    </>
  );
}

const options: { label: string; value: number, tip?: React.ReactNode }[] = [
  {
    label: "平台补贴比例上限50%",
    value: FIFTY_PERCENT,
  },
  {
    label: "平台补贴比例不限制",
    value: NO_LiMIT,
  },
];

interface Props {
  value?: number;
  onChange?: (value?: number) => void;
  disabled?: boolean;
}

/**
 * 出资比例组件
 */
export function PlatformSubsidyRate({
  value,
  onChange,
}: Props) {
  return (
    <Radio.Group
      value={value}
      onChange={(v) => onChange?.(v as number | undefined)}
    >
      {options?.map((op) => {
        return (
          <Radio value={op.value} key={op.value}>
            <OptionLabel label={op.label} tip={op?.tip} />
          </Radio>
        );
      })}
    </Radio.Group>
  );
}

export function PlatformSubsidyRateView({ value }: { value: number }) {
  const op = options.find((item) => item.value === value);
  return <>{op?.label || ""}</>;
}
