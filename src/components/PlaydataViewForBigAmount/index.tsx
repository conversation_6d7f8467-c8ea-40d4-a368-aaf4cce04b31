import './style.scss';

import React, { useEffect, useState } from 'react';
import { BigNumber } from 'bignumber.js';

import * as api from '../../api';
import { InvestmentSchemaEnum } from '../../constants';

const Field: React.FC<any> = ({ label, children }) => {
  return (
    <div className="zs-act-field">
      <label>{label}</label>
      <div>{children}</div>
    </div>
  );
};

// 生效人群对应关系
export const UserScopeEnumStr = {
  10: '购物金首冲用户',
  11: '商家新客用户',
  12: '商家一单用户',
  13: '商家二单用户',
  14: '商家流失用户',
  15: '商家活跃用户',
  16: '频道新客用户',
  17: '频道一单用户',
  18: '频道二单用户',
  19: '频道流失用户',
  20: '频道活跃用户',
};

// 核销场景
export const deliveryTypeStr = {
  1: '仅外卖可使用',
  2: '仅到店可使用',
  3: '外卖和自提都可使用',
};

// 玩法介绍类型对应关系
export const playRuleDescTypeStr = {
  1: '主投饿了么站内',
  2: '主投饿了么站外',
  3: '主投饿了么站内爆红包频道',
  4: '饿了么全能超市',
};

// 生效人群
type UserScopeType = 10 | 11 | 12 | 13 | 14 | 15 | 16 | 17 | 18 | 19 | 20;

interface LargeCouponViewJsonTs {
  activityType: number;
  attributes: {
    displayPlatformSubsidyMin: number; // 商家展示平台补贴最小值
    displayPlatformSubsidyMax: number; // 商家展示平台补贴最大值
    playRuleDescType: 1 | 2 | 3;
  };
  couponRuleViewDTOList: {
    activitySupplyPoolId: number; // 门店池 id
    dayCountLimit: number; // 每日库存最小值
    dayCountLimitMax: number; //  每日库存最大值
    shopSubsidy: number; //  商家出资
    userScope: UserScopeType; //  生效人群
  }[];
  deliveryChannel: 1 | 2; // 投放渠道
  deliveryType: 1 | 2; //  核销渠道
  userScopeContributionType: 1 | 2; // 分人群出资方式
}

const userScopeContributionTypeStr = {
  1: '仅商家新客红包',
  2: '多人群分档位红包',
  3: '仅频道新客红包（召回实时踢店）',
};

const deliveryChannelStr = {
  1: '店内投放',
  2: '店外投放',
};

interface Props {
  customPlayRuleName?: string;
  customDeliveryChannelText?: string;
  viewJson: string;
  isSuperMember: boolean;
}

const LargeCouponPlayDetail: React.FC<Props> = ({
  customPlayRuleName,
  customDeliveryChannelText,
  viewJson,
  isSuperMember
}) => {
  let ready = false;
  let viewJsonObj: LargeCouponViewJsonTs = {} as LargeCouponViewJsonTs;

  try {
    viewJsonObj = JSON.parse(viewJson);
    if (viewJsonObj === null) return null;
    ready = true;
  } catch (error) {
    console.error(error);
  }

  const playRuleDescVisbile = !!customPlayRuleName || !isSuperMember;

  if (!ready) {
    return null;
  }

  return (
    <div className="zs-large-coupon-play-data">
      <div className="zs-large-coupon-play-data-header">
        商家展示平台补贴：
        {viewJsonObj?.attributes?.displayPlatformSubsidyMin} -{' '}
        {viewJsonObj?.attributes?.displayPlatformSubsidyMax}
      </div>
      <div className="zs-large-coupon-play-data-header">
        分人群出资方式：
        {userScopeContributionTypeStr[viewJsonObj.userScopeContributionType]}
      </div>

      {viewJsonObj.couponRuleViewDTOList.map((item) => {
        return (
          <div className="list" key={item.userScope}>
            <div className="list-header">
              生效人群: {UserScopeEnumStr[item.userScope]}{' '}
              <span style={{ marginLeft: 80 }}>
                供给池ID：{item.activitySupplyPoolId}
              </span>
            </div>
            <div className="list-item">
              商家出资金额：
              {new BigNumber(item.shopSubsidy || 0).dividedBy(100).toNumber()}元
            </div>
            <div className="list-item">
              单店每日库存：{item.dayCountLimit} - {item.dayCountLimitMax}
            </div>
          </div>
        );
      })}

      <div className="zs-large-coupon-play-data-footer">
        {customDeliveryChannelText ? (
          <Field label="投放渠道：">{customDeliveryChannelText}</Field>
        ) : (
          <Field label="投放渠道：">
            {/* 超会模板 与 日常模板展示内容不同 */}
            {isSuperMember
              ? `${deliveryChannelStr[viewJsonObj.deliveryChannel]}（超级吃货卡）`
              : deliveryChannelStr[viewJsonObj.deliveryChannel]}
          </Field>
        )}

        {playRuleDescVisbile ? (
          <Field label="玩法介绍：">
            {customPlayRuleName || playRuleDescTypeStr[viewJsonObj?.attributes?.playRuleDescType]}
          </Field>
        ) : null}

        <Field label="核销场景：">
          {/* 超会模板 与 日常模板展示内容不同 */}
          {deliveryTypeStr[viewJsonObj.deliveryType]}
        </Field>

        <Field label="叠斥规则：">
          本活动红包与全店满减红包不可同时使用（包括分享领红包 / 吃货豆兑换红包
          / 品牌三单礼等）
        </Field>
      </div>
    </div>
  );
};

const LargeCouponPlayDetailWrap: React.FC<any> = ({ customPlayRuleName, customDeliveryChannelText, activityInfo }) => {
  const [detail, setDetail] = useState<any>();

  useEffect(() => {
    api.subActivity.getDetail(activityInfo.activityId).then((res) => {
      setDetail(res.data);
    });
  }, [activityInfo.activityId]);
  return (
    <div className="zs-act-info">
      <div className="zs-act-area">
        <div className="zs-act-area-title" style={{ marginBottom: 30 }}>
          玩法设置
        </div>
        <div className="zs-act-section">
          <LargeCouponPlayDetail
            viewJson={detail?.viewJson}
            customDeliveryChannelText={customDeliveryChannelText}
            customPlayRuleName={customPlayRuleName}
            isSuperMember={
              detail?.investmentActivityDetailDTO?.investmentSchema ===
              InvestmentSchemaEnum.SUPER_MEMBER_COUPON
            }
          />
        </div>
      </div>
    </div>
  );
};

export default LargeCouponPlayDetailWrap;
