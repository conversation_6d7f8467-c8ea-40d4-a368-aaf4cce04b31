import './style.scss';

import React, { useState } from 'react';
import { Dialog, Loading } from '@alifd/next';

function show(props: { message: string }) {
  let setMessage: (m: string) => void;

  const Content: React.FC<any> = () => {
    let message: string;
    [message, setMessage] = useState<string>(props.message);

    // 嵌入Alpha中，因为dialog挂在到global 上，加载不到class样式，需要用style行内样式来处理
    return (
      <div
        style={{
          width: 200,
          height: 120,
          paddingTop: 16,
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <div>
          <Loading visible size="large" />
        </div>
        <div
          style={{
            fontSize: 16,
            textAlign: 'center',
            color: '#999',
            marginTop: 40,
          }}
        >
          {message}
        </div>
      </div>
    );
  };
  const ret = Dialog.show({
    closeable: false,
    footer: false,
    title: null,
    content: <Content />,
  });

  return {
    ...ret,
    updateMessage: (message: string) => {
      setMessage(message);
    },
  };
}

export default {
  show,
};
