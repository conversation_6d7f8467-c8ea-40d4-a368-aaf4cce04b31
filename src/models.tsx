import { ActivitySignType, InvestmentSchemaEnum } from "./constants";
export interface MarketPlay {
  instance: {
    fields: {
      [field: string]: any
    }
  }
}

/**
 * 单/子活动，创建活动上下文
 */
export interface CreationContext {
  createActivitySessionId: string;
  createActiivtyStartTime: number; // 活动创建开始时间，格式是时间戳

  /* ==== 最基础的活动上下文信息 ==== */
  isSingleActivity: boolean; // 是否为单活动，如果为 true 的话，templateId 一定为空
  templateId?: number; // 主活动 id
  investmentSchema: number; // 招商模板: 日常模板、百亿补贴、时令好货等
  investmentType: number; // 招商类型: 日常招商、栏目招商、算法招商
  activityType: number; // 活动类型：业态红包、运费满减等
  activitySignType?: ActivitySignType; // 招商方式：正招(FORWARD_SIGN)，反圈(REVERSE_SIGN)
  subsidyType: string; // 补贴类型
  subsidyInvestmentModel?: string; // 补贴投资类型，目前只有爆单红包（子活动）会用到
  copy: boolean; // 是否是复制活动，目前只有商品特价子活动支持复制，所以 copy 等于 true 的时候，investmentTemplate 和 activity 一定不为空

  /* ==== alpha 活动上下文 ==== */
  activitySceneId?: string | number;  // 活动场景Id， 从阿尔法创建活动创建活动 url上会带有这个id
  activitySceneName?: string | number;  // 活动场景Name， 从阿尔法创建活动创建活动 用来展示
  activitySceneDateRangeLimit?: {beginTime: number, endTime: number}; // 从阿尔法创建活动 需要match 活动场景设置的时间
  activityPlanId?: string | number; // 活动计划Id
  // 被复制的活动详情信息，这里只定义必要的字段
  activity?: {
    activityId: number | string;

    // 活动创建者信息
    createdUserId: string; // 工号
    createdUserName: string; // 名称

    name: string;
    description: string;
    remark: string;
    beginTime: string; // utc 时间字符串，比如："2021-12-21T16:00:00.000Z"
    endTime: string; // utc 时间字符串，比如："2021-12-21T16:00:00.000Z"
    allowCancel: 1 | 2;
    signUpShopType: number;
    signUpStartTime: string; // utc 时间字符串，比如："2021-12-21T16:00:00.000Z"
    signUpEndTime: string; // utc 时间字符串，比如："2021-12-21T16:00:00.000Z"
    shopPoolId: number | null | undefined;
    shopPoolName: string | null | undefined;
    reviewRuleId: number | null | undefined;
    reviewRuleName: string | null | undefined;
    signUpMinCycleTime?: number | null;
    signUpMaxCycleTime?: number | null;
    auditMemberList?: { empId: string; lastName: string; }[];
    stockType?: number; // 复制活动场景，只复制库存维度，不复制库存设置
    businessLineList: {
      businessLineName?: string; // 业务线名称
      businessLineId?: number; // 业务线Id,
    }[],
    businessFormList?: {
      businessFormName?: string; // 业态类型名称
      businessFormId?: number; // 业态类型Id
    }[],
    marketPlay: {
      instance: {
        fields: {
          [prop: string]: any
        }
      }
    } | null,
    viewJson: string,
  },

  /* ==== 一些细粒度的功能开关 ==== */
  // 是否支持细粒度的活动时间组件禁用配置，@alife/carbon-biz-kunlun-zs v1.1.12 之后开始支持
  fineGrainedActivityTimeDisabled?: boolean;
  customAttributes?: { [key: string]: any }; // 自定义拓展属性

  /* ==== 主活动信息 ==== */
  investmentTemplate?: { // 创建子活动的时候才会有 investmentTemplate
    beginTime: number;
    endTime: number;
    minCycleTime?: number;
    maxCycleTime?: number;
    activityTimeEditable: boolean; // 活动时间是否允许修改
    reviewRuleId?: number;
    reviewRuleName?: string;
    reviewRuleInfo?: { // 机审规则的部分详细规则字段
      upcCode: string;
      skuCategoryName: string;
    };
    budget?: {
      id: string;
      name: string;
    }
  }

  extra?: Readonly<Record<string, any>>;
}

export interface CheckNextContext {
  step: {
    name: string;
    title: string;
  },
  dataStore: {
    [stepName: string]: any
  }
}

export interface ZsCableProps {
  data?: any; // 复制场景 data 才不为空
  env: {
    view: 'edit' | 'detail',
    investmentTemplate?: number; // 之前的玩法配置会使用 env.investmentTemplate，所以这里要做兼容
    creationContext?: CreationContext
  };
  channel: 'kunlun';
  user: {
    id: string;
    name: string;
    prefix: string
  },
  options?: {
    views?: {
      [viewId: string]: {
        [prop: string]: any
      }
    },
    steps?: {
      [stepId: string]: {
        [prop: string]: any
      }
    }
  }
  onCancel: () => void;
  onEvent: (event: { type: string, [prop: string]: any }) => void
  checkNext: (ctx: CheckNextContext) => Promise<any>
}

export interface ModifyStocTotalLimitkContext {
  isNewActivity: boolean;
  activityId: number;
  viewJson?: string;
  marketPlay?: any;
  investmentSchema: number
}

export interface ModifyPlatformSubsidyContext {
  activityId: number;
  viewJson?: string;
}

export interface CableInst {
  workspace: string;
  model: string;
  checkNext: (ctx: CheckNextContext, creationContext: CreationContext, history: any) => Promise<any>;
  onEvent(event: { type: string }): any; // 创建流程和详情页面自定义事件回调函数
  /**
   * 注意：CreateSubActivity 是有内置的 stepOptions 的，CableInst 如果本身有 stepOptions 的话
   * CreateSubActivity 内置的 stepOptions 就会被完全替换掉，不会将两个 stepOptions 合并到一起
   * 
   * 所以，不是必要情况下，stepOptions 不应该定制!!!
   */
  stepOptions?: Record<string, Record<string, any>>;
  [prop: string]: any;
}

export interface ActivityStrategy {
  renderExtraInfoForMainActivityTable?(record: InvestmentTemplateDTO): JSX.Element;
  renderExtraInfoForSubActivityTable?(record: InvestmentActivityDTO): JSX.Element;

  gotoLegacyDetail(act: InvestmentActivityDetailDTO, scene: 'lookup' | 'examine'): void;
  activityType: number;
  cable:
  | CableInst
  | ((option: {
      investmentSchema: number;
      investmentType: number;
      activitySignType?: ActivitySignType;
      subsidyInvestmentModel?: string; // 补贴投资类型，目前只有爆单红包（子活动）会用到
      extra?: CreationContext["extra"]
    }) => CableInst);
  generateExtraInfoForCreationContext?(
    creationContext: CreationContext,
    query: Record<string, string | undefined>
  ): Promise<CreationContext["extra"]>;
  renderActivityRuleForTableLegacy(marketPlay: { instance: { fields: any } }, activity?: any): any; // 展示活动列表的优惠内容，针对老活动
  renderActivityRuleForTable(viewJson: any, options: { investmentSchema: number }): JSX.Element | string; // 展示活动列表的优惠内容
  renderGroupItemExtView?(record: any, activityId: number, shopStatus: string, activityStatus: number): JSX.Element | string;
  renderOfflineGroupItemExtView?(params: { record: any, activityId: number, activityStatus?: number }): JSX.Element | string;// 下线管理 门店列扩展信息
  isItemAuditStatsVisible?: boolean; // 是否在单/子活动列表页展示商品审核数
  isDetailInviteStatsOnMainActivityTableVisible: boolean; // 主活动列表上是否支持展示详细的邀请审核数据
  isSupportAgent: boolean; // 是否支持代理商补贴
  // 是否支持复制
  isCopyEnabled?(options: {
    investmentSchema: number;
    investmentType: number;
    viewJson?: string | null;
  }): boolean;

  // 为复制活动场景计算回填数据
  resolveCopyData?(creationContext: CreationContext): Promise<any>;
  /**
   * 为创建活动场景计算初始数据
   * 
   * 这个能力要【慎重使用】：
   * - 对于普通活动常见场景，原本就有为字段设置初始值的能力，
   *   各种 step 组件都有设置指定字段初始值的细粒度能力
   *   所以，大部分情况下不需要使用这个能力
   * - 这里之所以在 Strategy 上提供 resolveCreateData 能力，主要是为了解决 boreas 玩法规则表单部分字段的初始值设置的问题
   *   boreas 玩法规则表单也具有设置初始值的能力，但是有不合理的地方，有可能产生 bug
   * 
   * 【UPDATE-2024-12-18】
   * 招商运营端创建流程是各个 Step 业务组件内置的默认属性，或者通过 props.defaultXxx 来设置默认值。
   * 这个方案使用起来也许会更简单，但是对产品功能的控制力不足，也比较脆弱，在品牌侧目前已经转为 Strategy 全权负责默认值设置。
   * 但是，招商系统这个能力机制已经根深蒂固了，**所以这个机制得持续保持（除极个别场景外），保证系统机制的一致性**。
   */
  resolvePartialCreateData?(creationContext: CreationContext): Promise<any | undefined>;

  isOfflineEnabled(state: number): boolean; // 活动是否可以被下线
  isDingtalkLinkEnabled(): boolean; // 是否支持钉钉报名链接
  noAssociatedActScene?: boolean; // 不展示 关联活动场景
  hasAuditMemberList(): boolean; // 是否可以设置授权人审核（仅商品类活动为true）
  auditMemberLimit?: number; // 授权人审核限制人数
  isSupportModifyStock(record: ModifyStocTotalLimitkContext): boolean; // 是否允许修改库存
  isModifyPlatformSubsidy?(viewJSON: any): boolean; // 是否允许修改平台补贴和发放数量
  handleModifyStock?(record: ModifyStocTotalLimitkContext, options: {
    onSuccess: () => void;
    onFail: (msg: string) => void
  }): any;
  handleModifyPlatformSubsidy?(record: ModifyPlatformSubsidyContext, options: {
    onSuccess: () => void;
    onFail: (msg: string) => void
  }): any;
  hasAgentSubsidy?(playdata: any): boolean;
  renderPlayDataInfoForTableView(marketPlay: any): JSX.Element;
  checkBtnVisible?: boolean;   // 是否隐藏审核按钮
  // v2 表示招商重构后新增活动类型，v1 表示招商重构之前就存在的活动类型，如果 generation 为空，则认定是 v1
  generation?: 'v1' | 'v2'; 
  marketPlayMapper?(viewJson: any, baseInfo: any, option: { investmentSchema: any, marketPlay?: any }): any; // 活动详情展示将viewJson转为marketPlay渲染玩法信息
  isHideSignUpInfo?: boolean | ((option?: {subsidyInvestmentModel?: string  }) => boolean); // 是否在列表中隐藏报名率相关信息，默认false不隐藏
  customAttributesForCreationContext?: ((option: { activityType: number; investmentType: number; investmentSchema: InvestmentSchemaEnum; }) => Promise<{ [key: string]: any }>); // 自定义拓展属性，每个活动随自己需求配置
  addCustomStyle?: (style: HTMLStyleElement, mode: 'create' | 'detail') => void;
}

export interface InvestmentActivityDetailDTO {
  isLongTermActivity: any;
  selectSubsidyType?: string[]; // 补贴类型
  investmentActivityType: 0 | 1; // 0 单活动，1 子活动
  templateId?: number;// 主活动 id

  // marketingId 信息
  marketingNum: number;
  marketingIds: number[];

  activityId: number;
  name: string;
  remark?: string;
  description: string;
  status: number; // 活动状态
  beginTime: string;
  endTime: string;
  activityType: number;
  createdUserName: string;
  createdUserId: string;
  signUpShopType: number;
  allowCancel: number;

  signUpMinCycleTime?: number;
  signUpMaxCycleTime?: number;

  gmtCreate: string;
  gmtModified: string;

  shopPoolName: string;
  shopPoolId: number;

  investmentType: number;
  investmentSchema: any;

  signUpEndTime: number;
  signUpStartTime: number;

  failReason?: string;
  statusStr: string;
  inviteStatusStr: string;
  approveStatus: number;
  approveStatusStr: string;

  attributes: {
    subsidyInvestmentModel: string;
  };

  // 业态红包供给池 id
  poolId: number;

  marketPlay?: {
    instance: any;
    model: any;
  }

  fromString: 'OLD' | 'NEW'
}

export interface InvestmentAlphaInfoNodeDTO  {
  activityId: (number | string);
  planId: (number | string);
  planName: string;
  planType: string;
  sceneId: (number | string);
  sceneName: string;
  sceneType: string;
  appImgUrl: string;
  sceneCreateTime: Date;
  level: string;
}

export interface InvestmentActivityExtendDTO {
  activityNum?: number; //子活动数
  signupNum?: number; // 已报名
  inviteSuccessNum?: number; // 已邀请
  inviteTotalNum?: number; // 共邀请
  waitApproveNum?: number; // 待审核
  approveNum?: number; // 已审核
  goodsNum: number; // 商品数量
  agentApproveNum?: number; //代理商已审核数量
  agentHangNum?: number; //代理商挂起数量
  agentInviteNum?: number; // 代理商邀请数量
}

export interface InvestmentTemplateDTO {
  viewJson?: string;
  investmentTemplateDetailDTO: {
    templateId: number;
    name: string;
    description: string;
    beginTime: number;
    endTime: number;
    timeMode: number;
    investmentActivityType: number;
    investmentType: number;
    investmentSchema: number;
    activityTypes: number[];
    approveStatus: number;
    approveStatusStr: string;
    status: number;
    statusStr: string;
    failReason?: string;
    createdUserName: string;
    createdUserId: number;

    // 栏目招商活动时间长度限制
    signUpMinCycleTime: number;
    signUpMaxCycleTime: number;

    gmtCreate: string; // 主活动创建时间，utc 时间字符串
    gmtModified: string; // 主活动更新时间，utc 时间字符串

    budgetId?: string;
    budgetName?: string;

    reviewRuleId?: number;
    reviewRuleName?: string;

    fromString: 'OLD' | 'NEW';
    isLongTermActivity?: boolean; // 是否长期活动
  };

  investmentActivityExtendDTO: InvestmentActivityExtendDTO;
  investmentActivityDetailDTO?: InvestmentActivityDetailDTO;
  investmentAlphaInfoNodeDTOList?: InvestmentAlphaInfoNodeDTO[];
}

export interface InvestmentActivityDTO {
  viewJson?: string;
  investmentActivityDetailDTO: InvestmentActivityDetailDTO;
  investmentActivityExtendDTO: InvestmentActivityExtendDTO;
  investmentActivityBrandInfoDTOList: Array<{
    brandId: number;
    brandName: string;
  }>;
  investmentAlphaInfoNodeDTOList?: InvestmentAlphaInfoNodeDTO[];
}


export interface UserInfo {
  workid: string; // 工号，比如 213137
  userid: string; // 邮件地址前缀，比如 yc213137
  realName: string; // 用户真实名称
}
export interface ManageOrgSelectDateTs {
  subList?: ManageOrgSelectDateTs[];
  code: string;
  name: string;
}

export interface CheckActDiscountParams {
  // 后端根据condition校验条件的数据来检查value数据是否符合中控配置的规则
  // 校验条件
  condition: {
    activityType: number;
    investmentType: number;
    investmentSchema: number;
    investmentActivityType: number; // 单活动
    userScope?: number; // 用户类型
    manageOrgList: ManageOrgSelectDateTs[]; // 管理业态
    discountRange?: number[]; //  优惠范围   --元
    conditionRange?: number[]; // 门槛范围  --元
    maxPlatformSubsidy?: number; // 平台最高补贴
    beginTime: number; // 活动开始时间
    endTime: number; // 活动结束时间
  };
  // 校验值
  value?: {
    maxPlatformSubsidy?: number; // 平台最高补贴
    userDayCountLimitRange?: number[]; // 用户每日限购
    userTotalCountLimitRange?: number[]; // 用户总限购
    conditionRange?: number[]; // 门槛范围  --元
  };
}

export interface CheckActDiscountParamsV2 {
// 这个入参是在不影响原来接口的情况下，和后端协商不再识别每个活动的值，直接把整个viewJson加上基础字段传过去
  // 校验条件
  condition: {
    activityType: number;
    investmentType: number;
    investmentSchema: number;
    investmentActivityType: number; // 单活动
    manageOrgList: ManageOrgSelectDateTs[]; // 管理业态
    beginTime: number; // 活动开始时间
    endTime: number; // 活动结束时间
    
    activityRuleViewJson?: string 
  };
}
